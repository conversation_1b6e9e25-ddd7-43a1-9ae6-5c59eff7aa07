"""
独立配置管理模块

提供独立的配置加载和管理功能，可以读取主程序配置但不依赖主程序的配置管理器。
"""

import os
import json
from typing import Dict, Any, Optional, List
from pathlib import Path
from dataclasses import dataclass, field


@dataclass
class ASRServiceConfig:
    """ASR服务配置"""
    service_name: str
    api_key: str
    enabled: bool = True
    max_retries: int = 5
    timeout: int = 300
    additional_params: Dict[str, Any] = field(default_factory=dict)


@dataclass
class LLMServiceConfig:
    """LLM服务配置"""
    name: str
    base_url: str
    model: str
    api_key: str
    enabled: bool = True
    temperature: float = 0.7
    max_tokens: int = 4000
    additional_params: Dict[str, Any] = field(default_factory=dict)


@dataclass
class RefineConfig:
    """EvaTrans 幻觉修复工具配置"""
    # 基础配置
    threshold_seconds: float = 10.0
    temp_dir: Optional[str] = None
    log_level: str = "INFO"
    
    # ASR服务配置
    asr_services: List[ASRServiceConfig] = field(default_factory=list)
    
    # LLM服务配置  
    llm_services: List[LLMServiceConfig] = field(default_factory=list)
    
    # 音频处理配置
    audio_sample_rate: int = 16000
    audio_channels: int = 1
    audio_format: str = "wav"
    
    # 处理配置
    max_concurrent_requests: int = 3
    request_timeout: int = 300
    
    @classmethod
    def load_from_env(cls) -> 'RefineConfig':
        """从环境变量加载配置"""
        config = cls()
        
        # 基础配置
        config.threshold_seconds = float(os.getenv('REFINE_THRESHOLD_SECONDS', '10.0'))
        config.temp_dir = os.getenv('REFINE_TEMP_DIR')
        config.log_level = os.getenv('REFINE_LOG_LEVEL', 'INFO')
        
        # ASR服务配置
        asr_services = []
        
        # ElevenLabs
        elevenlabs_key = os.getenv('ELEVENLABS_API_KEY', '')
        # 环境变量配置默认使用免费模式
        asr_services.append(ASRServiceConfig(
            service_name='elevenlabs',
            api_key=elevenlabs_key,
            additional_params={'mode': 'free', 'model_id': 'scribe_v1'}
        ))
        
        # AssemblyAI
        assemblyai_key = os.getenv('ASSEMBLYAI_API_KEY')
        if assemblyai_key:
            asr_services.append(ASRServiceConfig(
                service_name='assemblyai',
                api_key=assemblyai_key
            ))

        # Deepgram
        deepgram_key = os.getenv('DEEPGRAM_API_KEY')
        if deepgram_key:
            asr_services.append(ASRServiceConfig(
                service_name='deepgram',
                api_key=deepgram_key
            ))
        
        config.asr_services = asr_services
        
        # LLM服务配置
        llm_services = []
        
        llm_base_url = os.getenv('LLM_BASE_URL')
        llm_model = os.getenv('LLM_MODEL')
        llm_api_key = os.getenv('LLM_API_KEY')
        
        if llm_base_url and llm_model and llm_api_key:
            llm_services.append(LLMServiceConfig(
                name='custom',
                base_url=llm_base_url,
                model=llm_model,
                api_key=llm_api_key
            ))
        
        config.llm_services = llm_services
        
        return config
    
    @classmethod
    def load_from_main_config(cls, config_path: Optional[str] = None) -> 'RefineConfig':
        """从主程序配置文件加载配置（只读，不修改）"""
        config = cls()
        
        # 默认配置路径
        if not config_path:
            config_path = os.path.join(os.path.expanduser("~"), ".evatrans_gui", "config.json")
        
        if not os.path.exists(config_path):
            return config
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                main_config = json.load(f)
            
            # 提取ASR服务配置
            asr_configs = main_config.get('asr_service_configs', [])
            asr_services = []

            for asr_config in asr_configs:
                service_name = asr_config.get('service_name', '').lower()
                enabled = asr_config.get('enabled', False)
                has_api_key = bool(asr_config.get('api_key'))
                is_free_mode = asr_config.get('mode') == 'free'

                # 对于免费模式服务，不要求API key
                if enabled and (has_api_key or is_free_mode):
                    # 提取基本配置
                    basic_config = {
                        'service_name': service_name,
                        'api_key': asr_config.get('api_key', ''),
                        'enabled': True,
                        'max_retries': asr_config.get('max_retries', 3),
                        'timeout': asr_config.get('timeout', 300)
                    }

                    # 提取服务特有的配置到additional_params
                    additional_params = {}

                    # 排除基本字段，将其他所有字段都放入additional_params
                    basic_fields = {'service_name', 'api_key', 'enabled', 'max_retries', 'timeout', 'id'}
                    for key, value in asr_config.items():
                        if key not in basic_fields:
                            additional_params[key] = value

                    basic_config['additional_params'] = additional_params

                    asr_services.append(ASRServiceConfig(**basic_config))

            config.asr_services = asr_services
            
            # 提取LLM服务配置
            llm_configs = main_config.get('llm_api_configs', [])
            llm_services = []
            
            for llm_config in llm_configs:
                if llm_config.get('enabled', False) and llm_config.get('api_key'):
                    llm_services.append(LLMServiceConfig(
                        name=llm_config.get('name', 'custom'),
                        base_url=llm_config.get('base_url', ''),
                        model=llm_config.get('model', ''),
                        api_key=llm_config.get('api_key', ''),
                        enabled=True,
                        temperature=llm_config.get('temperature', 0.7),
                        max_tokens=llm_config.get('max_tokens', 4000)
                    ))
            
            config.llm_services = llm_services
            
        except Exception as e:
            print(f"警告：加载主程序配置失败: {e}")
        
        return config
    
    def get_asr_service_config(self, service_name: str) -> Optional[ASRServiceConfig]:
        """获取指定ASR服务配置"""
        for service in self.asr_services:
            if service.service_name.lower() == service_name.lower() and service.enabled:
                return service
        return None
    
    def get_llm_service_config(self, service_name: Optional[str] = None) -> Optional[LLMServiceConfig]:
        """获取LLM服务配置（默认返回第一个可用的）"""
        enabled_services = [s for s in self.llm_services if s.enabled]
        
        if not enabled_services:
            return None
        
        if service_name:
            for service in enabled_services:
                if service.name.lower() == service_name.lower():
                    return service
        
        return enabled_services[0]
    
    def has_valid_asr_service(self) -> bool:
        """检查是否有有效的ASR服务配置"""
        return any(s.enabled and s.api_key for s in self.asr_services)
    
    def has_valid_llm_service(self) -> bool:
        """检查是否有有效的LLM服务配置"""
        return any(s.enabled and s.api_key for s in self.llm_services)
