{"success": true, "service": "elevenlabs", "transcription_id": "elevenlabs_1754319535", "text": "Don Ingarsia ha fatto buona pesca stavolta. Guarda che donna si è portata appresso. <PERSON><PERSON>, proprio il tipo che ci voleva per uno come Ingarsia. Ci mancava pure che si portasse una moglie dal continente. Mi sembra che sia molto elegante. Speriamo che quell'eleganza don Pietro non voglia pagarsela con le nostre fatiche. Dai, molla! È teso, non tira. Per me vorrei che fosse teso intorno al collo di don Pietro Ingarsia. Quattro soldi che ci dà servono appena per non morire di fame. Quando l'ho visto scendere dal postale ho detto: ecco il pescecane dell'isola. Quello finirà per mangiarci tutti quanti. Avete visto che bella donna allo sbarco? Sì, che bella femmina e che occhi che tiene, eh. Negli occhi della donna è la perfidia del mondo. <PERSON> dovresti saperlo, <PERSON>, quella donna è il demonio. Allora io ci dormissi volentieri col diavolo. Rosario, Rosario. Maledizione. La rete si è impigliata nel porto. Maledizione. Macchina indietro, tutta sinistra, gira. Ora accollargalo di poppa. Stai tranquillo, ci penso io. Ma fermati, Abul, è inutile. Non fare fesserie, Abul. Hai visto qualcosa? La rete si è impigliata sul banco. Dov'è il fondale? Al corallo. Almeno quindici metri d'acqua. Proverò a tagliare la rete col coltello. È pericoloso. Meglio perdere tutto e tagliare i cavi a bordo. Non puoi farcela. ", "words": [{"text": "Don", "start": 31.319, "end": 31.5, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 31.5, "end": 31.5, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Ingarsia", "start": 31.5, "end": 31.84, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 31.84, "end": 31.84, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ha", "start": 31.84, "end": 31.92, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 31.92, "end": 31.92, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "fatto", "start": 31.92, "end": 32.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 32.119, "end": 32.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "buona", "start": 32.139, "end": 32.36, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 32.36, "end": 32.399, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "pesca", "start": 32.399, "end": 32.719, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 32.719, "end": 32.719, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "stavolta.", "start": 32.719, "end": 33.36, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 33.36, "end": 33.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Guarda", "start": 33.979, "end": 34.279, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 34.279, "end": 34.279, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 34.279, "end": 34.4, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 34.4, "end": 34.439, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "donna", "start": 34.439, "end": 34.7, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 34.7, "end": 34.7, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "si", "start": 34.7, "end": 34.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 34.799, "end": 34.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "è", "start": 34.799, "end": 34.86, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 34.86, "end": 34.88, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "portata", "start": 34.88, "end": 35.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 35.239, "end": 35.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "appresso.", "start": 35.239, "end": 35.78, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 35.78, "end": 36.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Sì,", "start": 36.119, "end": 36.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 36.479, "end": 36.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "proprio", "start": 36.479, "end": 36.86, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 36.86, "end": 36.899, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "il", "start": 36.899, "end": 36.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 36.919, "end": 36.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tipo", "start": 36.919, "end": 37.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 37.139, "end": 37.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 37.139, "end": 37.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 37.239, "end": 37.259, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ci", "start": 37.259, "end": 37.38, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 37.38, "end": 37.399, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>eva", "start": 37.399, "end": 37.7, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 37.7, "end": 37.719, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "per", "start": 37.719, "end": 37.86, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 37.86, "end": 37.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "uno", "start": 37.879, "end": 38.02, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 38.02, "end": 38.04, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "come", "start": 38.04, "end": 38.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 38.219, "end": 38.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Ingarsia.", "start": 38.219, "end": 38.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 38.919, "end": 39.719, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Ci", "start": 39.719, "end": 39.84, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 39.84, "end": 39.84, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "mancava", "start": 39.84, "end": 40.179, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 40.179, "end": 40.18, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "pure", "start": 40.18, "end": 40.36, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 40.36, "end": 40.36, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 40.36, "end": 40.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 40.459, "end": 40.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "si", "start": 40.479, "end": 40.56, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 40.56, "end": 40.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "portasse", "start": 40.599, "end": 40.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 40.959, "end": 40.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "una", "start": 40.959, "end": 41.08, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 41.08, "end": 41.099, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "moglie", "start": 41.099, "end": 41.34, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 41.34, "end": 41.36, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "dal", "start": 41.36, "end": 41.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 41.479, "end": 41.52, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "continente.", "start": 41.52, "end": 42.22, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 42.22, "end": 42.7, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>", "start": 42.7, "end": 42.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 42.859, "end": 42.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sembra", "start": 42.879, "end": 43.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 43.159, "end": 43.18, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 43.18, "end": 43.259, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 43.259, "end": 43.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sia", "start": 43.299, "end": 43.439, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 43.439, "end": 43.439, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "molto", "start": 43.439, "end": 43.7, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 43.7, "end": 43.7, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "elegante.", "start": 43.7, "end": 44.279, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 44.279, "end": 45.0, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 45.0, "end": 45.52, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 45.52, "end": 45.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 45.559, "end": 45.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 45.659, "end": 45.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "quell'eleganza", "start": 45.659, "end": 46.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 46.319, "end": 46.34, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "don", "start": 46.34, "end": 46.5, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 46.5, "end": 46.52, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>", "start": 46.52, "end": 46.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 46.779, "end": 46.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "non", "start": 46.779, "end": 46.86, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 46.86, "end": 46.899, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "voglia", "start": 46.899, "end": 47.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 47.119, "end": 47.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "paga<PERSON><PERSON>", "start": 47.139, "end": 47.639, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 47.639, "end": 47.639, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "con", "start": 47.639, "end": 47.739, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 47.739, "end": 47.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "le", "start": 47.739, "end": 47.86, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 47.86, "end": 47.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "nostre", "start": 47.879, "end": 48.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 48.139, "end": 48.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "fatiche.", "start": 48.159, "end": 48.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 48.699, "end": 49.379, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>,", "start": 49.379, "end": 49.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 49.759, "end": 49.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "molla!", "start": 49.779, "end": 50.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 50.319, "end": 68.859, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "È", "start": 68.859, "end": 68.859, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 68.859, "end": 68.859, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "teso,", "start": 68.859, "end": 69.239, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 69.239, "end": 69.419, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "non", "start": 69.419, "end": 69.639, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 69.639, "end": 69.639, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "tira.", "start": 69.639, "end": 70.119, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 70.119, "end": 70.4, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Per", "start": 70.4, "end": 70.58, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 70.58, "end": 70.599, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "me", "start": 70.599, "end": 70.739, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 70.739, "end": 70.76, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "vorrei", "start": 70.76, "end": 71.019, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 71.019, "end": 71.019, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "che", "start": 71.019, "end": 71.18, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 71.18, "end": 71.18, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "fosse", "start": 71.18, "end": 71.439, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 71.439, "end": 71.439, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "teso", "start": 71.439, "end": 71.68, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 71.68, "end": 71.68, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "intorno", "start": 71.68, "end": 71.979, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 71.979, "end": 72.0, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "al", "start": 72.0, "end": 72.119, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 72.119, "end": 72.119, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "collo", "start": 72.119, "end": 72.359, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 72.359, "end": 72.36, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "di", "start": 72.36, "end": 72.459, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 72.459, "end": 72.459, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "don", "start": 72.459, "end": 72.58, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 72.58, "end": 72.619, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON>", "start": 72.619, "end": 72.819, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 72.819, "end": 72.819, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Ingarsia.", "start": 72.819, "end": 73.58, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 73.58, "end": 74.699, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Quattro", "start": 74.699, "end": 75.0, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 75.0, "end": 75.04, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "soldi", "start": 75.04, "end": 75.319, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 75.319, "end": 75.319, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "che", "start": 75.319, "end": 75.419, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 75.419, "end": 75.419, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "ci", "start": 75.419, "end": 75.519, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 75.519, "end": 75.559, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "dà", "start": 75.559, "end": 75.699, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 75.699, "end": 75.699, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "servono", "start": 75.699, "end": 75.999, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 75.999, "end": 76.0, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "appena", "start": 76.0, "end": 76.319, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 76.319, "end": 76.319, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "per", "start": 76.319, "end": 76.439, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 76.439, "end": 76.459, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "non", "start": 76.459, "end": 76.579, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 76.579, "end": 76.599, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "morire", "start": 76.599, "end": 76.9, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 76.9, "end": 76.9, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "di", "start": 76.9, "end": 77.019, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 77.019, "end": 77.059, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "fame.", "start": 77.059, "end": 77.5, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 77.5, "end": 78.799, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Quando", "start": 78.799, "end": 79.019, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 79.019, "end": 79.019, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "l'ho", "start": 79.019, "end": 79.139, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 79.139, "end": 79.159, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "visto", "start": 79.159, "end": 79.339, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 79.339, "end": 79.379, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "scendere", "start": 79.379, "end": 79.699, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 79.699, "end": 79.699, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "dal", "start": 79.699, "end": 79.839, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 79.839, "end": 79.839, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "postale", "start": 79.839, "end": 80.159, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 80.159, "end": 80.18, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "ho", "start": 80.18, "end": 80.259, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 80.259, "end": 80.279, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "detto:", "start": 80.279, "end": 80.659, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 80.659, "end": 81.04, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "ecco", "start": 81.04, "end": 81.279, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 81.279, "end": 81.279, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "il", "start": 81.279, "end": 81.36, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 81.36, "end": 81.36, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "pescecane", "start": 81.36, "end": 81.839, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 81.839, "end": 81.839, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "dell'isola.", "start": 81.839, "end": 82.419, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 82.419, "end": 83.159, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 83.159, "end": 83.4, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 83.4, "end": 83.419, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "finirà", "start": 83.419, "end": 83.62, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 83.62, "end": 83.739, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "per", "start": 83.739, "end": 83.879, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 83.879, "end": 83.879, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 83.879, "end": 84.299, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 84.299, "end": 84.339, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "tutti", "start": 84.339, "end": 84.539, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 84.539, "end": 84.619, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "quanti.", "start": 84.619, "end": 85.079, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 85.079, "end": 85.22, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Avete", "start": 85.22, "end": 85.5, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 85.5, "end": 85.519, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "visto", "start": 85.519, "end": 85.699, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 85.699, "end": 85.72, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "che", "start": 85.72, "end": 85.819, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 85.819, "end": 85.86, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "bella", "start": 85.86, "end": 86.059, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 86.059, "end": 86.059, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "donna", "start": 86.059, "end": 86.279, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 86.279, "end": 86.279, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "allo", "start": 86.279, "end": 86.399, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 86.399, "end": 86.4, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "sbarco?", "start": 86.4, "end": 86.879, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 86.879, "end": 86.979, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Sì,", "start": 86.979, "end": 87.219, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 87.219, "end": 87.279, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "che", "start": 87.279, "end": 87.459, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 87.459, "end": 87.54, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "bella", "start": 87.54, "end": 87.86, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 87.86, "end": 87.919, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "femmina", "start": 87.919, "end": 88.379, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 88.379, "end": 88.459, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "e", "start": 88.459, "end": 88.599, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 88.599, "end": 88.659, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "che", "start": 88.659, "end": 88.76, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 88.76, "end": 88.86, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "occhi", "start": 88.86, "end": 89.199, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 89.199, "end": 89.239, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "che", "start": 89.239, "end": 89.319, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 89.319, "end": 89.4, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "tiene,", "start": 89.4, "end": 89.799, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 89.799, "end": 89.799, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "eh.", "start": 89.799, "end": 90.099, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 90.099, "end": 91.959, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 91.959, "end": 92.159, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 92.159, "end": 92.199, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "occhi", "start": 92.199, "end": 92.44, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 92.44, "end": 92.459, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "della", "start": 92.459, "end": 92.619, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 92.619, "end": 92.68, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "donna", "start": 92.68, "end": 92.919, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 92.919, "end": 92.919, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "è", "start": 92.919, "end": 92.959, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 92.959, "end": 92.959, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "la", "start": 92.959, "end": 93.079, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 93.079, "end": 93.079, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "perfidia", "start": 93.079, "end": 93.54, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 93.54, "end": 93.559, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "del", "start": 93.559, "end": 93.679, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 93.679, "end": 93.739, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "mondo.", "start": 93.739, "end": 94.099, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 94.099, "end": 94.459, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "E", "start": 94.459, "end": 94.58, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 94.58, "end": 94.599, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 94.599, "end": 94.94, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 94.94, "end": 94.959, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "saper<PERSON>,", "start": 94.959, "end": 95.399, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 95.399, "end": 95.4, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON>,", "start": 95.4, "end": 95.919, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 95.919, "end": 96.22, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "quella", "start": 96.22, "end": 96.459, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 96.459, "end": 96.5, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "donna", "start": 96.5, "end": 96.72, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 96.72, "end": 96.759, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "è", "start": 96.759, "end": 96.779, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 96.779, "end": 96.779, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "il", "start": 96.779, "end": 96.839, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 96.839, "end": 96.879, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "demonio.", "start": 96.879, "end": 97.379, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 97.379, "end": 97.5, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 97.5, "end": 97.879, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 97.879, "end": 97.879, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "io", "start": 97.879, "end": 98.019, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 98.019, "end": 98.019, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "ci", "start": 98.019, "end": 98.099, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 98.099, "end": 98.18, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "dormissi", "start": 98.18, "end": 98.559, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 98.559, "end": 98.619, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 98.619, "end": 99.159, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 99.159, "end": 99.22, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "col", "start": 99.22, "end": 99.379, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 99.379, "end": 99.419, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "diavolo.", "start": 99.419, "end": 99.979, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 99.979, "end": 112.72, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Rosario,", "start": 112.72, "end": 120.04, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 120.04, "end": 120.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Rosario.", "start": 120.739, "end": 121.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 121.659, "end": 128.44, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Maledizione.", "start": 128.44, "end": 128.46, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 128.46, "end": 128.46, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "La", "start": 128.46, "end": 128.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 128.559, "end": 128.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "rete", "start": 128.559, "end": 128.82, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 128.82, "end": 128.86, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "si", "start": 128.86, "end": 128.88, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 128.88, "end": 128.88, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "è", "start": 128.88, "end": 128.88, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 128.88, "end": 129.0, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "impigliata", "start": 129.0, "end": 129.339, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 129.339, "end": 129.36, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "nel", "start": 129.36, "end": 129.539, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 129.539, "end": 129.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "porto.", "start": 129.559, "end": 129.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 129.979, "end": 131.52, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Maledizione.", "start": 131.52, "end": 132.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 132.559, "end": 132.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Macchina", "start": 132.879, "end": 133.259, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 133.259, "end": 133.259, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "indietro,", "start": 133.259, "end": 133.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 133.779, "end": 133.839, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tutta", "start": 133.839, "end": 134.1, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 134.1, "end": 134.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sinistra,", "start": 134.119, "end": 134.68, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 134.68, "end": 134.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "gira.", "start": 134.779, "end": 135.4, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 135.4, "end": 141.52, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>a", "start": 141.52, "end": 141.74, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 141.74, "end": 141.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "accollar<PERSON><PERSON>", "start": 141.819, "end": 142.439, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 142.439, "end": 142.44, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 142.44, "end": 142.539, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 142.539, "end": 142.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "poppa.", "start": 142.599, "end": 143.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 143.139, "end": 143.58, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Stai", "start": 143.58, "end": 143.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 143.759, "end": 143.759, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tranquillo,", "start": 143.759, "end": 144.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 144.119, "end": 144.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ci", "start": 144.139, "end": 144.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 144.239, "end": 144.259, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "penso", "start": 144.259, "end": 144.52, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 144.52, "end": 144.58, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "io.", "start": 144.58, "end": 144.839, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 144.839, "end": 145.02, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Ma", "start": 145.02, "end": 145.059, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 145.059, "end": 145.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "fermati,", "start": 145.139, "end": 145.52, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 145.52, "end": 145.52, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Abul,", "start": 145.52, "end": 145.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 145.759, "end": 145.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "è", "start": 145.819, "end": 145.839, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 145.839, "end": 145.839, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "inutile.", "start": 145.839, "end": 146.4, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 146.4, "end": 147.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Non", "start": 147.319, "end": 147.44, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 147.44, "end": 147.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "fare", "start": 147.479, "end": 147.679, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 147.679, "end": 147.679, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "fesserie,", "start": 147.679, "end": 148.099, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 148.099, "end": 148.099, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON>.", "start": 148.099, "end": 148.339, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 148.339, "end": 174.66, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>", "start": 174.66, "end": 174.839, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 174.839, "end": 174.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "visto", "start": 174.879, "end": 175.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 175.119, "end": 175.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "qualcosa?", "start": 175.139, "end": 175.739, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 175.739, "end": 176.22, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "La", "start": 176.22, "end": 176.3, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 176.3, "end": 176.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "rete", "start": 176.479, "end": 176.74, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 176.74, "end": 176.759, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "si", "start": 176.759, "end": 176.839, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 176.839, "end": 176.86, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "è", "start": 176.86, "end": 176.879, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 176.879, "end": 176.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "impigliata", "start": 176.879, "end": 177.379, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 177.379, "end": 177.419, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sul", "start": 177.419, "end": 177.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 177.579, "end": 177.58, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "banco.", "start": 177.58, "end": 178.059, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 178.059, "end": 178.179, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>v'<PERSON>", "start": 178.179, "end": 178.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 178.919, "end": 178.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "il", "start": 178.959, "end": 179.0, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 179.0, "end": 179.0, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "fondale?", "start": 179.0, "end": 179.58, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 179.58, "end": 179.679, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Al", "start": 179.679, "end": 179.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 179.779, "end": 179.8, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "corallo.", "start": 179.8, "end": 180.519, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 180.519, "end": 181.3, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Almeno", "start": 181.3, "end": 181.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 181.579, "end": 181.639, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "quindici", "start": 181.639, "end": 181.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 181.959, "end": 182.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "metri", "start": 182.019, "end": 182.3, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 182.3, "end": 182.3, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "d'acqua.", "start": 182.3, "end": 182.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 182.659, "end": 182.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Proverò", "start": 182.659, "end": 182.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 182.979, "end": 183.039, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 183.039, "end": 183.059, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 183.059, "end": 183.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tagliare", "start": 183.059, "end": 183.38, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 183.38, "end": 183.44, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "la", "start": 183.44, "end": 183.46, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 183.46, "end": 183.46, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "rete", "start": 183.46, "end": 183.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 183.699, "end": 183.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "col", "start": 183.699, "end": 183.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 183.899, "end": 183.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON>.", "start": 183.919, "end": 184.54, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 184.54, "end": 184.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "È", "start": 184.619, "end": 184.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 184.899, "end": 184.899, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "pericoloso.", "start": 184.899, "end": 185.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 185.579, "end": 185.58, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 185.58, "end": 185.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 185.819, "end": 185.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "perdere", "start": 185.819, "end": 186.18, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 186.18, "end": 186.199, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tutto", "start": 186.199, "end": 186.36, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 186.36, "end": 186.36, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "e", "start": 186.36, "end": 186.44, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 186.44, "end": 186.44, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tagliare", "start": 186.44, "end": 186.719, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 186.719, "end": 186.72, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "i", "start": 186.72, "end": 186.74, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 186.74, "end": 186.8, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "cavi", "start": 186.8, "end": 186.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 186.979, "end": 186.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 186.979, "end": 187.059, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 187.059, "end": 187.08, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "bordo.", "start": 187.08, "end": 187.539, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 187.539, "end": 187.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Non", "start": 187.539, "end": 187.72, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 187.72, "end": 187.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "puoi", "start": 187.739, "end": 187.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 187.979, "end": 188.0, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "farcela.", "start": 188.0, "end": 188.6, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 188.6, "end": 188.6, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}], "language_detected": null, "confidence": null, "processing_time": 11.089862585067749, "metadata": {"service": "elevenlabs", "mode": "free", "model": "scribe_v1", "raw_result": {"language_code": "ita", "language_probability": 0.9988753795623779, "text": "Don Ingarsia ha fatto buona pesca stavolta. Guarda che donna si è portata appresso. <PERSON><PERSON>, proprio il tipo che ci voleva per uno come Ingarsia. Ci mancava pure che si portasse una moglie dal continente. Mi sembra che sia molto elegante. Speriamo che quell'eleganza don Pietro non voglia pagarsela con le nostre fatiche. Dai, molla! È teso, non tira. Per me vorrei che fosse teso intorno al collo di don Pietro Ingarsia. Quattro soldi che ci dà servono appena per non morire di fame. Quando l'ho visto scendere dal postale ho detto: ecco il pescecane dell'isola. Quello finirà per mangiarci tutti quanti. Avete visto che bella donna allo sbarco? Sì, che bella femmina e che occhi che tiene, eh. Negli occhi della donna è la perfidia del mondo. <PERSON> dovresti saperlo, <PERSON>, quella donna è il demonio. Allora io ci dormissi volentieri col diavolo. Rosario, Rosario. Maledizione. La rete si è impigliata nel porto. Maledizione. Macchina indietro, tutta sinistra, gira. Ora accollargalo di poppa. Stai tranquillo, ci penso io. Ma fermati, Abul, è inutile. Non fare fesserie, Abul. Hai visto qualcosa? La rete si è impigliata sul banco. Dov'è il fondale? Al corallo. Almeno quindici metri d'acqua. Proverò a tagliare la rete col coltello. È pericoloso. Meglio perdere tutto e tagliare i cavi a bordo. Non puoi farcela. ", "words": [{"text": "Don", "start": 31.319, "end": 31.5, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 31.5, "end": 31.5, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Ingarsia", "start": 31.5, "end": 31.84, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 31.84, "end": 31.84, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ha", "start": 31.84, "end": 31.92, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 31.92, "end": 31.92, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "fatto", "start": 31.92, "end": 32.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 32.119, "end": 32.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "buona", "start": 32.139, "end": 32.36, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 32.36, "end": 32.399, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "pesca", "start": 32.399, "end": 32.719, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 32.719, "end": 32.719, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "stavolta.", "start": 32.719, "end": 33.36, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 33.36, "end": 33.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Guarda", "start": 33.979, "end": 34.279, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 34.279, "end": 34.279, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 34.279, "end": 34.4, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 34.4, "end": 34.439, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "donna", "start": 34.439, "end": 34.7, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 34.7, "end": 34.7, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "si", "start": 34.7, "end": 34.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 34.799, "end": 34.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "è", "start": 34.799, "end": 34.86, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 34.86, "end": 34.88, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "portata", "start": 34.88, "end": 35.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 35.239, "end": 35.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "appresso.", "start": 35.239, "end": 35.78, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 35.78, "end": 36.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Sì,", "start": 36.119, "end": 36.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 36.479, "end": 36.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "proprio", "start": 36.479, "end": 36.86, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 36.86, "end": 36.899, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "il", "start": 36.899, "end": 36.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 36.919, "end": 36.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tipo", "start": 36.919, "end": 37.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 37.139, "end": 37.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 37.139, "end": 37.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 37.239, "end": 37.259, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ci", "start": 37.259, "end": 37.38, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 37.38, "end": 37.399, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>eva", "start": 37.399, "end": 37.7, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 37.7, "end": 37.719, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "per", "start": 37.719, "end": 37.86, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 37.86, "end": 37.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "uno", "start": 37.879, "end": 38.02, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 38.02, "end": 38.04, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "come", "start": 38.04, "end": 38.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 38.219, "end": 38.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Ingarsia.", "start": 38.219, "end": 38.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 38.919, "end": 39.719, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Ci", "start": 39.719, "end": 39.84, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 39.84, "end": 39.84, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "mancava", "start": 39.84, "end": 40.179, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 40.179, "end": 40.18, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "pure", "start": 40.18, "end": 40.36, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 40.36, "end": 40.36, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 40.36, "end": 40.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 40.459, "end": 40.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "si", "start": 40.479, "end": 40.56, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 40.56, "end": 40.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "portasse", "start": 40.599, "end": 40.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 40.959, "end": 40.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "una", "start": 40.959, "end": 41.08, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 41.08, "end": 41.099, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "moglie", "start": 41.099, "end": 41.34, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 41.34, "end": 41.36, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "dal", "start": 41.36, "end": 41.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 41.479, "end": 41.52, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "continente.", "start": 41.52, "end": 42.22, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 42.22, "end": 42.7, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>", "start": 42.7, "end": 42.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 42.859, "end": 42.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sembra", "start": 42.879, "end": 43.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 43.159, "end": 43.18, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 43.18, "end": 43.259, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 43.259, "end": 43.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sia", "start": 43.299, "end": 43.439, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 43.439, "end": 43.439, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "molto", "start": 43.439, "end": 43.7, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 43.7, "end": 43.7, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "elegante.", "start": 43.7, "end": 44.279, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 44.279, "end": 45.0, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 45.0, "end": 45.52, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 45.52, "end": 45.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 45.559, "end": 45.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 45.659, "end": 45.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "quell'eleganza", "start": 45.659, "end": 46.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 46.319, "end": 46.34, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "don", "start": 46.34, "end": 46.5, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 46.5, "end": 46.52, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>", "start": 46.52, "end": 46.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 46.779, "end": 46.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "non", "start": 46.779, "end": 46.86, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 46.86, "end": 46.899, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "voglia", "start": 46.899, "end": 47.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 47.119, "end": 47.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "paga<PERSON><PERSON>", "start": 47.139, "end": 47.639, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 47.639, "end": 47.639, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "con", "start": 47.639, "end": 47.739, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 47.739, "end": 47.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "le", "start": 47.739, "end": 47.86, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 47.86, "end": 47.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "nostre", "start": 47.879, "end": 48.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 48.139, "end": 48.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "fatiche.", "start": 48.159, "end": 48.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 48.699, "end": 49.379, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>,", "start": 49.379, "end": 49.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 49.759, "end": 49.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "molla!", "start": 49.779, "end": 50.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 50.319, "end": 68.859, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "È", "start": 68.859, "end": 68.859, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 68.859, "end": 68.859, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "teso,", "start": 68.859, "end": 69.239, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 69.239, "end": 69.419, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "non", "start": 69.419, "end": 69.639, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 69.639, "end": 69.639, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "tira.", "start": 69.639, "end": 70.119, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 70.119, "end": 70.4, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Per", "start": 70.4, "end": 70.58, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 70.58, "end": 70.599, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "me", "start": 70.599, "end": 70.739, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 70.739, "end": 70.76, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "vorrei", "start": 70.76, "end": 71.019, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 71.019, "end": 71.019, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "che", "start": 71.019, "end": 71.18, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 71.18, "end": 71.18, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "fosse", "start": 71.18, "end": 71.439, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 71.439, "end": 71.439, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "teso", "start": 71.439, "end": 71.68, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 71.68, "end": 71.68, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "intorno", "start": 71.68, "end": 71.979, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 71.979, "end": 72.0, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "al", "start": 72.0, "end": 72.119, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 72.119, "end": 72.119, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "collo", "start": 72.119, "end": 72.359, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 72.359, "end": 72.36, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "di", "start": 72.36, "end": 72.459, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 72.459, "end": 72.459, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "don", "start": 72.459, "end": 72.58, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 72.58, "end": 72.619, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON>", "start": 72.619, "end": 72.819, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 72.819, "end": 72.819, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Ingarsia.", "start": 72.819, "end": 73.58, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 73.58, "end": 74.699, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Quattro", "start": 74.699, "end": 75.0, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 75.0, "end": 75.04, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "soldi", "start": 75.04, "end": 75.319, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 75.319, "end": 75.319, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "che", "start": 75.319, "end": 75.419, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 75.419, "end": 75.419, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "ci", "start": 75.419, "end": 75.519, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 75.519, "end": 75.559, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "dà", "start": 75.559, "end": 75.699, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 75.699, "end": 75.699, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "servono", "start": 75.699, "end": 75.999, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 75.999, "end": 76.0, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "appena", "start": 76.0, "end": 76.319, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 76.319, "end": 76.319, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "per", "start": 76.319, "end": 76.439, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 76.439, "end": 76.459, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "non", "start": 76.459, "end": 76.579, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 76.579, "end": 76.599, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "morire", "start": 76.599, "end": 76.9, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 76.9, "end": 76.9, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "di", "start": 76.9, "end": 77.019, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 77.019, "end": 77.059, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "fame.", "start": 77.059, "end": 77.5, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 77.5, "end": 78.799, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Quando", "start": 78.799, "end": 79.019, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 79.019, "end": 79.019, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "l'ho", "start": 79.019, "end": 79.139, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 79.139, "end": 79.159, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "visto", "start": 79.159, "end": 79.339, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 79.339, "end": 79.379, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "scendere", "start": 79.379, "end": 79.699, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 79.699, "end": 79.699, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "dal", "start": 79.699, "end": 79.839, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 79.839, "end": 79.839, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "postale", "start": 79.839, "end": 80.159, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 80.159, "end": 80.18, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "ho", "start": 80.18, "end": 80.259, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 80.259, "end": 80.279, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "detto:", "start": 80.279, "end": 80.659, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 80.659, "end": 81.04, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "ecco", "start": 81.04, "end": 81.279, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 81.279, "end": 81.279, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "il", "start": 81.279, "end": 81.36, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 81.36, "end": 81.36, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "pescecane", "start": 81.36, "end": 81.839, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 81.839, "end": 81.839, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "dell'isola.", "start": 81.839, "end": 82.419, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 82.419, "end": 83.159, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 83.159, "end": 83.4, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 83.4, "end": 83.419, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "finirà", "start": 83.419, "end": 83.62, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 83.62, "end": 83.739, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "per", "start": 83.739, "end": 83.879, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 83.879, "end": 83.879, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 83.879, "end": 84.299, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 84.299, "end": 84.339, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "tutti", "start": 84.339, "end": 84.539, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 84.539, "end": 84.619, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "quanti.", "start": 84.619, "end": 85.079, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 85.079, "end": 85.22, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Avete", "start": 85.22, "end": 85.5, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 85.5, "end": 85.519, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "visto", "start": 85.519, "end": 85.699, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 85.699, "end": 85.72, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "che", "start": 85.72, "end": 85.819, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 85.819, "end": 85.86, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "bella", "start": 85.86, "end": 86.059, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 86.059, "end": 86.059, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "donna", "start": 86.059, "end": 86.279, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 86.279, "end": 86.279, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "allo", "start": 86.279, "end": 86.399, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 86.399, "end": 86.4, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "sbarco?", "start": 86.4, "end": 86.879, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 86.879, "end": 86.979, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Sì,", "start": 86.979, "end": 87.219, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 87.219, "end": 87.279, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "che", "start": 87.279, "end": 87.459, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 87.459, "end": 87.54, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "bella", "start": 87.54, "end": 87.86, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 87.86, "end": 87.919, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "femmina", "start": 87.919, "end": 88.379, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 88.379, "end": 88.459, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "e", "start": 88.459, "end": 88.599, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 88.599, "end": 88.659, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "che", "start": 88.659, "end": 88.76, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 88.76, "end": 88.86, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "occhi", "start": 88.86, "end": 89.199, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 89.199, "end": 89.239, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "che", "start": 89.239, "end": 89.319, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 89.319, "end": 89.4, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "tiene,", "start": 89.4, "end": 89.799, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 89.799, "end": 89.799, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "eh.", "start": 89.799, "end": 90.099, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 90.099, "end": 91.959, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 91.959, "end": 92.159, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 92.159, "end": 92.199, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "occhi", "start": 92.199, "end": 92.44, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 92.44, "end": 92.459, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "della", "start": 92.459, "end": 92.619, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 92.619, "end": 92.68, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "donna", "start": 92.68, "end": 92.919, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 92.919, "end": 92.919, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "è", "start": 92.919, "end": 92.959, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 92.959, "end": 92.959, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "la", "start": 92.959, "end": 93.079, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 93.079, "end": 93.079, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "perfidia", "start": 93.079, "end": 93.54, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 93.54, "end": 93.559, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "del", "start": 93.559, "end": 93.679, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 93.679, "end": 93.739, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "mondo.", "start": 93.739, "end": 94.099, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 94.099, "end": 94.459, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "E", "start": 94.459, "end": 94.58, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 94.58, "end": 94.599, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 94.599, "end": 94.94, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 94.94, "end": 94.959, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "saper<PERSON>,", "start": 94.959, "end": 95.399, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 95.399, "end": 95.4, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON>,", "start": 95.4, "end": 95.919, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 95.919, "end": 96.22, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "quella", "start": 96.22, "end": 96.459, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 96.459, "end": 96.5, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "donna", "start": 96.5, "end": 96.72, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 96.72, "end": 96.759, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "è", "start": 96.759, "end": 96.779, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 96.779, "end": 96.779, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "il", "start": 96.779, "end": 96.839, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 96.839, "end": 96.879, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "demonio.", "start": 96.879, "end": 97.379, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 97.379, "end": 97.5, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 97.5, "end": 97.879, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 97.879, "end": 97.879, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "io", "start": 97.879, "end": 98.019, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 98.019, "end": 98.019, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "ci", "start": 98.019, "end": 98.099, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 98.099, "end": 98.18, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "dormissi", "start": 98.18, "end": 98.559, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 98.559, "end": 98.619, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 98.619, "end": 99.159, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 99.159, "end": 99.22, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "col", "start": 99.22, "end": 99.379, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 99.379, "end": 99.419, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "diavolo.", "start": 99.419, "end": 99.979, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 99.979, "end": 112.72, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Rosario,", "start": 112.72, "end": 120.04, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 120.04, "end": 120.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Rosario.", "start": 120.739, "end": 121.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 121.659, "end": 128.44, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Maledizione.", "start": 128.44, "end": 128.46, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 128.46, "end": 128.46, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "La", "start": 128.46, "end": 128.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 128.559, "end": 128.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "rete", "start": 128.559, "end": 128.82, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 128.82, "end": 128.86, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "si", "start": 128.86, "end": 128.88, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 128.88, "end": 128.88, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "è", "start": 128.88, "end": 128.88, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 128.88, "end": 129.0, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "impigliata", "start": 129.0, "end": 129.339, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 129.339, "end": 129.36, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "nel", "start": 129.36, "end": 129.539, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 129.539, "end": 129.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "porto.", "start": 129.559, "end": 129.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 129.979, "end": 131.52, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Maledizione.", "start": 131.52, "end": 132.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 132.559, "end": 132.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Macchina", "start": 132.879, "end": 133.259, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 133.259, "end": 133.259, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "indietro,", "start": 133.259, "end": 133.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 133.779, "end": 133.839, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tutta", "start": 133.839, "end": 134.1, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 134.1, "end": 134.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sinistra,", "start": 134.119, "end": 134.68, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 134.68, "end": 134.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "gira.", "start": 134.779, "end": 135.4, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 135.4, "end": 141.52, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>a", "start": 141.52, "end": 141.74, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 141.74, "end": 141.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "accollar<PERSON><PERSON>", "start": 141.819, "end": 142.439, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 142.439, "end": 142.44, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 142.44, "end": 142.539, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 142.539, "end": 142.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "poppa.", "start": 142.599, "end": 143.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 143.139, "end": 143.58, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Stai", "start": 143.58, "end": 143.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 143.759, "end": 143.759, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tranquillo,", "start": 143.759, "end": 144.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 144.119, "end": 144.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ci", "start": 144.139, "end": 144.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 144.239, "end": 144.259, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "penso", "start": 144.259, "end": 144.52, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 144.52, "end": 144.58, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "io.", "start": 144.58, "end": 144.839, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 144.839, "end": 145.02, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Ma", "start": 145.02, "end": 145.059, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 145.059, "end": 145.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "fermati,", "start": 145.139, "end": 145.52, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 145.52, "end": 145.52, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Abul,", "start": 145.52, "end": 145.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 145.759, "end": 145.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "è", "start": 145.819, "end": 145.839, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 145.839, "end": 145.839, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "inutile.", "start": 145.839, "end": 146.4, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 146.4, "end": 147.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Non", "start": 147.319, "end": 147.44, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 147.44, "end": 147.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "fare", "start": 147.479, "end": 147.679, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 147.679, "end": 147.679, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "fesserie,", "start": 147.679, "end": 148.099, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 148.099, "end": 148.099, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON>.", "start": 148.099, "end": 148.339, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 148.339, "end": 174.66, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>", "start": 174.66, "end": 174.839, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 174.839, "end": 174.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "visto", "start": 174.879, "end": 175.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 175.119, "end": 175.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "qualcosa?", "start": 175.139, "end": 175.739, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 175.739, "end": 176.22, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "La", "start": 176.22, "end": 176.3, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 176.3, "end": 176.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "rete", "start": 176.479, "end": 176.74, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 176.74, "end": 176.759, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "si", "start": 176.759, "end": 176.839, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 176.839, "end": 176.86, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "è", "start": 176.86, "end": 176.879, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 176.879, "end": 176.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "impigliata", "start": 176.879, "end": 177.379, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 177.379, "end": 177.419, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sul", "start": 177.419, "end": 177.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 177.579, "end": 177.58, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "banco.", "start": 177.58, "end": 178.059, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 178.059, "end": 178.179, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>v'<PERSON>", "start": 178.179, "end": 178.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 178.919, "end": 178.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "il", "start": 178.959, "end": 179.0, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 179.0, "end": 179.0, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "fondale?", "start": 179.0, "end": 179.58, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 179.58, "end": 179.679, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Al", "start": 179.679, "end": 179.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 179.779, "end": 179.8, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "corallo.", "start": 179.8, "end": 180.519, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 180.519, "end": 181.3, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Almeno", "start": 181.3, "end": 181.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 181.579, "end": 181.639, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "quindici", "start": 181.639, "end": 181.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 181.959, "end": 182.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "metri", "start": 182.019, "end": 182.3, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 182.3, "end": 182.3, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "d'acqua.", "start": 182.3, "end": 182.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 182.659, "end": 182.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Proverò", "start": 182.659, "end": 182.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 182.979, "end": 183.039, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 183.039, "end": 183.059, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 183.059, "end": 183.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tagliare", "start": 183.059, "end": 183.38, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 183.38, "end": 183.44, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "la", "start": 183.44, "end": 183.46, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 183.46, "end": 183.46, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "rete", "start": 183.46, "end": 183.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 183.699, "end": 183.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "col", "start": 183.699, "end": 183.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 183.899, "end": 183.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON>.", "start": 183.919, "end": 184.54, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 184.54, "end": 184.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "È", "start": 184.619, "end": 184.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 184.899, "end": 184.899, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "pericoloso.", "start": 184.899, "end": 185.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 185.579, "end": 185.58, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 185.58, "end": 185.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 185.819, "end": 185.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "perdere", "start": 185.819, "end": 186.18, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 186.18, "end": 186.199, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tutto", "start": 186.199, "end": 186.36, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 186.36, "end": 186.36, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "e", "start": 186.36, "end": 186.44, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 186.44, "end": 186.44, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tagliare", "start": 186.44, "end": 186.719, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 186.719, "end": 186.72, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "i", "start": 186.72, "end": 186.74, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 186.74, "end": 186.8, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "cavi", "start": 186.8, "end": 186.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 186.979, "end": 186.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 186.979, "end": 187.059, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 187.059, "end": 187.08, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "bordo.", "start": 187.08, "end": 187.539, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 187.539, "end": 187.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Non", "start": 187.539, "end": 187.72, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 187.72, "end": 187.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "puoi", "start": 187.739, "end": 187.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 187.979, "end": 188.0, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "farcela.", "start": 188.0, "end": 188.6, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 188.6, "end": 188.6, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}]}}, "created_at": 1754319546.7231565}