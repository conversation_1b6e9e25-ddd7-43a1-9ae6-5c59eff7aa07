"""
LLM API工具类

提供LLM API的模型列表获取、连接测试等工具功能。
支持OpenAI和Gemini格式的API调用。
"""

import time
import requests
from typing import List, Dict, Optional, Any, Tuple
from dataclasses import dataclass

from .api import APIFormat


@dataclass
class ModelTestResult:
    """模型测试结果"""
    model_name: str
    status: str
    response_time: float = 0.0
    returned_model: str = ""
    error_message: str = ""


class LLMAPIUtils:
    """LLM API工具类

    提供模型列表获取、连接测试等静态工具方法。
    支持OpenAI和Gemini两种API格式。
    """
    
    @staticmethod
    def _normalize_api_url(api_url: str) -> str:
        """标准化API URL"""
        return api_url.rstrip('/')
    
    @staticmethod
    def _get_models_endpoint(base_url: str, api_format: APIFormat) -> str:
        """获取模型列表端点"""
        base_url = LLMAPIUtils._normalize_api_url(base_url)
        
        if api_format == APIFormat.OPENAI:
            return f"{base_url}/v1/models"
        elif api_format == APIFormat.GEMINI:
            return f"{base_url}/v1beta/models"
        else:
            return f"{base_url}/v1/models"
    
    @staticmethod
    def _get_chat_endpoint(base_url: str, api_format: APIFormat, model: str = "") -> str:
        """获取聊天端点"""
        base_url = LLMAPIUtils._normalize_api_url(base_url)
        
        if api_format == APIFormat.OPENAI:
            return f"{base_url}/v1/chat/completions"
        elif api_format == APIFormat.GEMINI:
            return f"{base_url}/v1beta/models/{model}:generateContent"
        else:
            return f"{base_url}/v1/chat/completions"
    
    @staticmethod
    def _get_headers(api_key: str, api_format: APIFormat) -> Dict[str, str]:
        """获取请求头

        Args:
            api_key: API密钥
            api_format: API格式

        Returns:
            Dict[str, str]: 请求头字典
        """
        clean_api_key = api_key.strip().replace('\r', '').replace('\n', '')

        if api_format == APIFormat.OPENAI:
            return {
                "Authorization": f"Bearer {clean_api_key}",
                "Content-Type": "application/json"
            }
        elif api_format == APIFormat.GEMINI:
            return {
                "Content-Type": "application/json"
            }
        else:
            return {
                "Authorization": f"Bearer {clean_api_key}",
                "Content-Type": "application/json"
            }
    
    @staticmethod
    def _get_request_params(api_key: str, api_format: APIFormat) -> Dict[str, str]:
        """获取请求参数

        Args:
            api_key: API密钥
            api_format: API格式

        Returns:
            Dict[str, str]: 请求参数字典
        """
        if api_format == APIFormat.GEMINI:
            return {"key": api_key}
        return {}
    
    @staticmethod
    def fetch_models(base_url: str, api_key: str, api_format: APIFormat, timeout: int = 180, logger=None, debug: bool = False) -> Tuple[bool, List[str], str]:
        """获取模型列表

        Args:
            base_url: API基础URL
            api_key: API密钥
            api_format: API格式
            timeout: 超时时间（秒）
            logger: 日志记录器
            debug: 是否启用调试模式

        Returns:
            Tuple[bool, List[str], str]: (成功标志, 模型列表, 错误信息)
        """
        if api_format == APIFormat.OPENAI:
            return LLMAPIUtils._fetch_openai_models(base_url, api_key, timeout, logger, debug)
        elif api_format == APIFormat.GEMINI:
            return LLMAPIUtils._fetch_gemini_models(base_url, api_key, timeout, logger, debug)
        else:
            return False, [], f"不支持的API格式: {api_format}"

    @staticmethod
    def _fetch_openai_models(base_url: str, api_key: str, timeout: int = 180, logger=None, debug: bool = False) -> Tuple[bool, List[str], str]:
        """获取OpenAI格式的模型列表

        Args:
            base_url: API基础URL
            api_key: API密钥
            timeout: 超时时间（秒）
            logger: 日志记录器
            debug: 是否启用调试模式

        Returns:
            Tuple[bool, List[str], str]: (成功标志, 模型列表, 错误信息)
        """
        try:
            api_url_value = base_url.rstrip('/')
            endpoint = f"{api_url_value}/v1/models"
            clean_api_key = api_key.strip().replace('\r', '').replace('\n', '')

            headers = {
                "Authorization": f"Bearer {clean_api_key}",
                "Content-Type": "application/json"
            }

            if logger and debug:
                logger(f"[DEBUG] OpenAI请求端点: {endpoint}")
                logger(f"[DEBUG] OpenAI请求头: {headers}")

            response = requests.get(endpoint, headers=headers, timeout=timeout)

            if logger and debug:
                logger(f"[DEBUG] OpenAI响应状态码: {response.status_code}")

            if response.status_code == 200:
                data = response.json()

                if logger and debug:
                    logger(f"[DEBUG] OpenAI响应数据: {data}")

                # 解析模型列表
                models = []
                if "data" in data and isinstance(data["data"], list):
                    for item in data["data"]:
                        model_id = item.get("id", "")
                        if model_id and model_id.strip():
                            models.append(model_id.strip())

                # 过滤空值并排序
                models = [m for m in models if m]
                models.sort()

                if logger and debug:
                    logger(f"[DEBUG] OpenAI解析到的模型: {models}")

                return True, models, ""
            else:
                error_text = response.text[:200]
                if logger and debug:
                    logger(f"[DEBUG] OpenAI错误响应: {error_text}")
                return False, [], f"HTTP {response.status_code}: {error_text}"

        except requests.exceptions.Timeout:
            error_msg = f"请求超时 ({timeout}秒)"
            if logger and debug:
                logger(f"[DEBUG] OpenAI {error_msg}")
            return False, [], error_msg
        except Exception as e:
            error_msg = f"请求失败: {str(e)}"
            if logger and debug:
                logger(f"[DEBUG] OpenAI {error_msg}")
            return False, [], error_msg

    @staticmethod
    def _fetch_gemini_models(base_url: str, api_key: str, timeout: int = 180, logger=None, debug: bool = False) -> Tuple[bool, List[str], str]:
        """获取Gemini格式的模型列表

        Args:
            base_url: API基础URL
            api_key: API密钥
            timeout: 超时时间（秒）
            logger: 日志记录器
            debug: 是否启用调试模式

        Returns:
            Tuple[bool, List[str], str]: (成功标志, 模型列表, 错误信息)
        """
        try:
            clean_api_key = api_key.strip().replace('\r', '').replace('\n', '')
            api_url_value = base_url.rstrip('/')
            endpoint = f"{api_url_value}/v1beta/models?key={clean_api_key}"

            headers = {
                "Content-Type": "application/json"
            }

            if logger and debug:
                logger(f"[DEBUG] Gemini请求端点: {endpoint}")
                logger(f"[DEBUG] Gemini请求头: {headers}")

            response = requests.get(endpoint, headers=headers, timeout=timeout)

            if logger and debug:
                logger(f"[DEBUG] Gemini响应状态码: {response.status_code}")

            if response.status_code == 200:
                data = response.json()

                if logger and debug:
                    logger(f"[DEBUG] Gemini响应数据: {data}")

                # 解析模型列表
                models = []
                if "models" in data and isinstance(data["models"], list):
                    for item in data["models"]:
                        model_name = item.get("name", "")
                        if model_name and model_name.strip():
                            # 移除前缀
                            if model_name.startswith("models/"):
                                model_id = model_name[7:]
                            else:
                                model_id = model_name

                            if model_id:
                                models.append(model_id.strip())

                # 过滤空值并排序
                models = [m for m in models if m]
                models.sort()

                if logger and debug:
                    logger(f"[DEBUG] Gemini解析到的模型: {models}")

                return True, models, ""
            else:
                error_text = response.text[:200]
                if logger and debug:
                    logger(f"[DEBUG] Gemini错误响应: {error_text}")
                return False, [], f"HTTP {response.status_code}: {error_text}"

        except requests.exceptions.Timeout:
            error_msg = f"请求超时 ({timeout}秒)"
            if logger and debug:
                logger(f"[DEBUG] Gemini {error_msg}")
            return False, [], error_msg
        except Exception as e:
            error_msg = f"请求失败: {str(e)}"
            if logger and debug:
                logger(f"[DEBUG] Gemini {error_msg}")
            return False, [], error_msg

    @staticmethod
    def test_model_connection(
        base_url: str,
        api_key: str,
        api_format: APIFormat,
        model: str,
        timeout: int = 180,
        temperature: float = 0.7,
        logger=None,
        debug: bool = False
    ) -> 'ModelTestResult':
        """测试模型连接

        Args:
            base_url: API基础URL
            api_key: API密钥
            api_format: API格式
            model: 模型名称
            timeout: 超时时间（秒）
            temperature: 温度参数
            logger: 日志记录器
            debug: 是否启用调试模式

        Returns:
            ModelTestResult: 测试结果
        """
        if api_format == APIFormat.OPENAI:
            return LLMAPIUtils._test_openai_connection(base_url, api_key, model, timeout, temperature, logger, debug)
        elif api_format == APIFormat.GEMINI:
            return LLMAPIUtils._test_gemini_connection(base_url, api_key, model, timeout, temperature, logger, debug)
        else:
            result = ModelTestResult(model_name=model, status="error")
            result.error_message = f"不支持的API格式: {api_format}"
            return result

    @staticmethod
    def _test_openai_connection(
        base_url: str,
        api_key: str,
        model: str,
        timeout: int = 180,
        temperature: float = 0.7,
        logger=None,
        debug: bool = False
    ) -> 'ModelTestResult':
        """测试OpenAI格式的模型连接

        Args:
            base_url: API基础URL
            api_key: API密钥
            model: 模型名称
            timeout: 超时时间（秒）
            temperature: 温度参数
            logger: 日志记录器
            debug: 是否启用调试模式

        Returns:
            ModelTestResult: 测试结果
        """
        result = ModelTestResult(model_name=model, status="error")

        try:
            api_url_value = base_url.rstrip('/')
            endpoint = f"{api_url_value}/v1/chat/completions"
            clean_api_key = api_key.strip().replace('\r', '').replace('\n', '')

            headers = {
                "Authorization": f"Bearer {clean_api_key}",
                "Content-Type": "application/json"
            }

            # 构建请求体
            request_body = {
                "model": model,
                "messages": [{"role": "user", "content": "写一个10个字的冷笑话"}],
                "temperature": temperature
            }

            if logger and debug:
                logger(f"[DEBUG] OpenAI测试模型: {model}")
                logger(f"[DEBUG] OpenAI请求端点: {endpoint}")
                logger(f"[DEBUG] OpenAI超时时间: {timeout}秒")
                logger(f"[DEBUG] OpenAI请求体: {request_body}")

            start_time = time.time()
            response = requests.post(endpoint, headers=headers, json=request_body, timeout=timeout)
            end_time = time.time()
            result.response_time = round(end_time - start_time, 2)

            if logger and debug:
                logger(f"[DEBUG] OpenAI响应状态码: {response.status_code}")
                logger(f"[DEBUG] OpenAI响应时间: {result.response_time}秒")

            if response.status_code == 200:
                data = response.json()

                if logger and debug:
                    logger(f"[DEBUG] OpenAI响应数据: {data}")

                # 解析返回的模型名称
                returned_model = data.get("model", "no returned model")
                result.returned_model = returned_model

                # 检查模型一致性
                if returned_model == model:
                    result.status = "success"
                    if logger and debug:
                        logger(f"[DEBUG] OpenAI模型一致性检查通过")
                else:
                    result.status = "inconsistent"
                    result.error_message = f"返回模型: {returned_model}"
                    if logger and debug:
                        logger(f"[DEBUG] OpenAI模型不一致: 期望 {model}, 返回 {returned_model}")
            else:
                result.status = "error"
                try:
                    error_data = response.json()
                    result.error_message = error_data.get("error", {}).get("message", f"HTTP {response.status_code}")
                    if logger and debug:
                        logger(f"[DEBUG] OpenAI API错误: {result.error_message}")
                except:
                    result.error_message = f"HTTP {response.status_code}"
                    if logger and debug:
                        logger(f"[DEBUG] OpenAI HTTP错误: {result.error_message}")

        except requests.exceptions.Timeout:
            result.status = "timeout"
            result.error_message = f"超时 ({timeout}秒)"
            if logger and debug:
                logger(f"[DEBUG] OpenAI超时: {result.error_message}")
        except Exception as e:
            result.status = "error"
            result.error_message = str(e)
            if logger and debug:
                logger(f"[DEBUG] OpenAI异常: {result.error_message}")

        return result

    @staticmethod
    def _test_gemini_connection(
        base_url: str,
        api_key: str,
        model: str,
        timeout: int = 180,
        temperature: float = 0.7,
        logger=None,
        debug: bool = False
    ) -> 'ModelTestResult':
        """测试Gemini格式的模型连接

        Args:
            base_url: API基础URL
            api_key: API密钥
            model: 模型名称
            timeout: 超时时间（秒）
            temperature: 温度参数
            logger: 日志记录器
            debug: 是否启用调试模式

        Returns:
            ModelTestResult: 测试结果
        """
        result = ModelTestResult(model_name=model, status="error")

        try:
            clean_api_key = api_key.strip().replace('\r', '').replace('\n', '')
            api_url_value = base_url.rstrip('/')
            endpoint = f"{api_url_value}/v1beta/models/{model}:generateContent?key={clean_api_key}"

            headers = {
                "Content-Type": "application/json"
            }

            # 构建请求体
            request_body = {
                "contents": [{"parts": [{"text": "写一个10个字的冷笑话"}]}],
                "generationConfig": {
                    "maxOutputTokens": 50,
                    "temperature": temperature
                }
            }

            if logger and debug:
                logger(f"[DEBUG] Gemini测试模型: {model}")
                logger(f"[DEBUG] Gemini请求端点: {endpoint}")
                logger(f"[DEBUG] Gemini请求体: {request_body}")

            start_time = time.time()
            response = requests.post(endpoint, headers=headers, json=request_body, timeout=timeout)
            end_time = time.time()
            result.response_time = round(end_time - start_time, 2)

            if logger and debug:
                logger(f"[DEBUG] Gemini响应状态码: {response.status_code}")
                logger(f"[DEBUG] Gemini响应时间: {result.response_time}秒")

            if response.status_code == 200:
                data = response.json()

                if logger and debug:
                    logger(f"[DEBUG] Gemini响应数据: {data}")

                # 假设模型一致
                result.returned_model = model
                result.status = "success"
                if logger and debug:
                    logger(f"[DEBUG] Gemini测试成功")
            else:
                result.status = "error"
                try:
                    error_data = response.json()
                    result.error_message = error_data.get("error", {}).get("message", f"HTTP {response.status_code}")
                    if logger and debug:
                        logger(f"[DEBUG] Gemini API错误: {result.error_message}")
                except:
                    result.error_message = f"HTTP {response.status_code}"
                    if logger and debug:
                        logger(f"[DEBUG] Gemini HTTP错误: {result.error_message}")

        except requests.exceptions.Timeout:
            result.status = "timeout"
            result.error_message = f"超时 ({timeout}秒)"
            if logger and debug:
                logger(f"[DEBUG] Gemini超时: {result.error_message}")
        except Exception as e:
            result.status = "error"
            result.error_message = str(e)
            if logger and debug:
                logger(f"[DEBUG] Gemini异常: {result.error_message}")

        return result
