"""
数据模型定义

定义幻觉修复工具使用的数据结构，复用主程序的数据模型概念但保持独立。
"""

from dataclasses import dataclass
from typing import List, Optional, Dict, Any
from enum import Enum


@dataclass
class SubtitleEntry:
    """字幕条目数据结构 - 复用主程序的设计但保持独立"""
    index: int
    start_time: str  # SRT格式时间戳 HH:MM:SS,mmm
    end_time: str    # SRT格式时间戳 HH:MM:SS,mmm
    text: str


@dataclass
class LongEntryRefineRequest:
    """长条目精炼请求"""
    task_folder: str
    subtitle_file: str
    audio_file: str
    threshold_seconds: float = 10.0
    test_mode: bool = False


@dataclass
class LongEntryRefineResult:
    """长条目精炼结果"""
    success: bool
    processed_entries: int
    refined_entries: int
    output_file: Optional[str] = None
    error_message: Optional[str] = None
    processing_time: float = 0.0


class ASRProvider(Enum):
    """ASR服务提供商枚举"""
    ELEVENLABS = "elevenlabs"
    ASSEMBLYAI = "assemblyai" 
    DEEPGRAM = "deepgram"


class LLMProvider(Enum):
    """LLM服务提供商枚举"""
    OPENAI = "openai"
    CLAUDE = "claude"
    GEMINI = "gemini"
    CUSTOM = "custom"


@dataclass
class ProcessingStats:
    """处理统计信息"""
    total_entries: int = 0
    long_entries_found: int = 0
    successfully_processed: int = 0
    failed_entries: int = 0
    total_duration: float = 0.0
    processing_time: float = 0.0


@dataclass
class AudioSegment:
    """音频片段信息"""
    start_time: float  # 秒
    end_time: float    # 秒
    duration: float    # 秒
    original_entry_index: int
    file_path: Optional[str] = None
