{"success": true, "service": "elevenlabs", "transcription_id": "elevenlabs_1754319495", "text": "Mi dai una sigaretta? Tieni. Hai visto la baracca che stanno costruendo al porto? Ah, poche tavele marci in piedi e quattro soldi nelle mani di un ragazzo. Se sperano di mettermi paura con quella pagliacciata della cooperativa, si sbagliano e finiranno per rimetterci anche le reti. Quel giovanotto è più abile di quanto tu creda. Non dovresti sottovalutarlo. Chi, Rosario? È un giovane energico e i pescatori lo seguono. È pericoloso, ti dico. Ha ceduto il battello per un anno. E se riesco a combinare con la gente di Palermo, quello non vende una spugna nemmeno con l'aiuto di Santa Rosalia. Ma preferisco che si rovini con le sue mani. Hai pensato bene a quello che fai? Che cosa vuoi dire? Voglio dire che tua figlia non sarà dello stesso parere, per quello che riguarda il giovanotto. Se tu ti decidessi a mandarla via dall'isola per un po' di tempo... Ci ho già pensato. Qualche mese a Palermo non le farà male. Ne sono certa. Maria e quel pescatore insieme non potrebbero crearti che dei fastidi, credimi. Maria. Sì, papà. Prepara le valigie per qualche giorno di assenza, ti condurrò a Palermo. A Palermo? E perché? Ho già preso i biglietti, si parte alle otto. Non posso andare, papà. Per non abbandonare il rosario, forse? Quell'uomo mi è già stancato abbastanza. Non voglio diventare la favola dell'isola, io. Tu verrai a Palermo in collegio e ci rimarrai fino a quando non avrai dimenticato questa stupida storia. Potrai divertirti in città, negozi, cinematografi... Certo, che in fondo ha bisogno di cambiare, di conoscere gente come si deve. Come quella che frequenta questa casa, forse? Che cosa vuoi dire? È per me che lo dice. Tua figlia mi guarda davvero con molta simpatia. Mi meraviglio che con Margherita non si siano accordate per avvelenarmi. Non è vero, Maria? Non preoccuparti, Pietro. Alla ragazza farà bene, vedrai. E poi quando ritornerai da Palermo, saremo più soli. Così voi partiste per Palermo e vostra moglie rimase sola. Per pochi giorni però. Vi sembrerà impossibile, ma ogni minuto lontano da lei era per me peggio di una coltellata. La trovai nervosa, mi fece uno strano effetto. E non vi domandaste il perché di questo cambiamento? No. Credevo che fosse per quanto era successo nell'isola. Il suicidio di Carmela? Sì. La sorella di Rosario era stata trovata impiccata. L'aveva fatto per amore, per quel disgraziato di Francesco. Proprio vero che le donne si perdono per i farabutti. Di quel suicidio ne parlavano tutti nell'isola. Il negro l'ha vista correre per la scogliera e poi l'hanno trovata impiccata nel fondo di Padron Grava. Povero Rosario. Chissà che colpo terribile sarà per lui quando tornerà da Palermo. Certo non andrà troppo liscia. Salute, don Pietro. Salute. Salute. Dame un anice. Scusate, don Pietro. Che c'è? È per il battello, sapete? Che battello? Il Fortuna II, quello che mi avete affittato voi. Beh? Domani dovrei pagare. E tu pagami. Ma non ho i soldi. Be' adesso non è il caso di parlare di questo. Me li darai quando potrai. Grazie don Pietro, grazie. Avete sentito don Pietro? Già, povera ragazza. E non si è saputo perché la fa. Dicono che verranno la polizia e il medico legale. Ho saputo che hanno interrogato il negro, ma da lui purtroppo sono riusciti a cavar fuori ben poco. È una brutta storia. Sarebbe stato meglio che foste venuto allora. E siete proprio sicuro? Da quel momento le cose precipitarono. Voi siete un poliziotto e vi piace sapere i fatti nell'ordine. E invece tutto fu così confuso che...", "words": [{"text": "<PERSON>", "start": 12.92, "end": 13.059, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 13.059, "end": 13.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "dai", "start": 13.059, "end": 13.259, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 13.259, "end": 13.259, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "una", "start": 13.259, "end": 13.379, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 13.379, "end": 13.399, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>?", "start": 13.399, "end": 14.06, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 14.06, "end": 16.02, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Tieni.", "start": 16.02, "end": 16.499, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 16.499, "end": 25.539, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>", "start": 25.539, "end": 27.179, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 27.179, "end": 27.199, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "visto", "start": 27.199, "end": 27.419, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 27.419, "end": 27.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "la", "start": 27.459, "end": 27.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 27.559, "end": 27.579, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "baracca", "start": 27.579, "end": 27.92, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 27.92, "end": 27.92, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 27.92, "end": 28.02, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 28.02, "end": 28.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "stanno", "start": 28.059, "end": 28.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 28.299, "end": 28.34, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "costruendo", "start": 28.34, "end": 28.78, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 28.78, "end": 28.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "al", "start": 28.799, "end": 28.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 28.899, "end": 28.92, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "porto?", "start": 28.92, "end": 29.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 29.319, "end": 29.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Ah,", "start": 29.539, "end": 29.899, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 29.899, "end": 30.039, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "poche", "start": 30.039, "end": 30.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 30.299, "end": 30.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "tavele", "start": 30.299, "end": 30.619, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 30.619, "end": 30.659, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "marci", "start": 30.659, "end": 30.919, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 30.919, "end": 30.92, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "in", "start": 30.92, "end": 31.019, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 31.019, "end": 31.019, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "piedi", "start": 31.019, "end": 31.279, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 31.279, "end": 31.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "e", "start": 31.299, "end": 31.379, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 31.379, "end": 31.399, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "quattro", "start": 31.399, "end": 31.619, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 31.619, "end": 31.679, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "soldi", "start": 31.679, "end": 31.939, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 31.939, "end": 31.939, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "nelle", "start": 31.939, "end": 32.119, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 32.119, "end": 32.139, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mani", "start": 32.139, "end": 32.34, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 32.34, "end": 32.34, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 32.34, "end": 32.419, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 32.419, "end": 32.419, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "un", "start": 32.419, "end": 32.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 32.479, "end": 32.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ragazzo.", "start": 32.479, "end": 32.959, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 32.959, "end": 34.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Se", "start": 34.299, "end": 34.439, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 34.439, "end": 34.439, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sperano", "start": 34.439, "end": 34.86, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 34.86, "end": 34.86, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 34.86, "end": 34.959, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 34.959, "end": 34.959, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mettermi", "start": 34.959, "end": 35.319, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 35.319, "end": 35.34, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "paura", "start": 35.34, "end": 35.619, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 35.619, "end": 35.619, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "con", "start": 35.619, "end": 35.779, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 35.779, "end": 35.779, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "quella", "start": 35.779, "end": 36.0, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 36.0, "end": 36.0, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "paglia<PERSON><PERSON>", "start": 36.0, "end": 36.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 36.559, "end": 36.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "della", "start": 36.559, "end": 36.74, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 36.74, "end": 36.759, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "cooperativa,", "start": 36.759, "end": 37.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 37.299, "end": 37.319, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "si", "start": 37.319, "end": 37.439, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 37.439, "end": 37.439, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 37.439, "end": 37.9, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 37.9, "end": 38.079, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "e", "start": 38.079, "end": 38.179, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 38.179, "end": 38.2, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "finiranno", "start": 38.2, "end": 38.619, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 38.619, "end": 38.619, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "per", "start": 38.619, "end": 38.739, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 38.739, "end": 38.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 38.739, "end": 39.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 39.159, "end": 39.159, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "anche", "start": 39.159, "end": 39.34, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 39.34, "end": 39.34, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "le", "start": 39.34, "end": 39.459, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 39.459, "end": 39.459, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "reti.", "start": 39.459, "end": 39.8, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 39.8, "end": 41.18, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Quel", "start": 41.18, "end": 41.36, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 41.36, "end": 41.399, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "start": 41.399, "end": 41.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 41.919, "end": 41.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "è", "start": 41.919, "end": 42.039, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 42.039, "end": 42.04, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "più", "start": 42.04, "end": 42.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 42.199, "end": 42.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "abile", "start": 42.219, "end": 42.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 42.559, "end": 42.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 42.559, "end": 42.68, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 42.68, "end": 42.68, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "quanto", "start": 42.68, "end": 42.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 42.959, "end": 43.02, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tu", "start": 43.02, "end": 43.179, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 43.179, "end": 43.2, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "creda.", "start": 43.2, "end": 43.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 43.659, "end": 44.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Non", "start": 44.559, "end": 44.74, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 44.74, "end": 44.759, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 44.759, "end": 45.2, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 45.2, "end": 45.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sottovalutarlo.", "start": 45.239, "end": 46.339, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 46.339, "end": 46.719, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>,", "start": 46.719, "end": 47.0, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 47.0, "end": 47.02, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Rosario?", "start": 47.02, "end": 47.579, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 47.579, "end": 47.86, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "È", "start": 47.86, "end": 48.0, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 48.0, "end": 48.02, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "un", "start": 48.02, "end": 48.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 48.119, "end": 48.18, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "giovane", "start": 48.18, "end": 48.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 48.559, "end": 48.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "energico", "start": 48.559, "end": 49.24, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 49.24, "end": 49.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "e", "start": 49.479, "end": 49.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 49.579, "end": 49.579, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "i", "start": 49.579, "end": 49.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 49.619, "end": 49.68, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "pescatori", "start": 49.68, "end": 50.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 50.159, "end": 50.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "lo", "start": 50.159, "end": 50.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 50.319, "end": 50.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "segu<PERSON>.", "start": 50.319, "end": 50.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 50.779, "end": 51.0, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "È", "start": 51.0, "end": 51.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 51.139, "end": 51.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "per<PERSON><PERSON><PERSON>,", "start": 51.159, "end": 51.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 51.779, "end": 51.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ti", "start": 51.799, "end": 51.92, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 51.92, "end": 51.939, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "dico.", "start": 51.939, "end": 52.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 52.299, "end": 53.279, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Ha", "start": 53.279, "end": 53.399, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 53.399, "end": 53.399, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ceduto", "start": 53.399, "end": 53.739, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 53.739, "end": 53.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "il", "start": 53.739, "end": 53.84, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 53.84, "end": 53.84, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "battel<PERSON>", "start": 53.84, "end": 54.22, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 54.22, "end": 54.239, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "per", "start": 54.239, "end": 54.38, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 54.38, "end": 54.399, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "un", "start": 54.399, "end": 54.5, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 54.5, "end": 54.539, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "anno.", "start": 54.539, "end": 54.84, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 54.84, "end": 55.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "E", "start": 55.479, "end": 55.599, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 55.599, "end": 55.619, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "se", "start": 55.619, "end": 55.72, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 55.72, "end": 55.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "r<PERSON>co", "start": 55.739, "end": 56.0, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 56.0, "end": 56.0, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "a", "start": 56.0, "end": 56.08, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 56.08, "end": 56.099, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "combinare", "start": 56.099, "end": 56.54, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 56.54, "end": 56.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "con", "start": 56.559, "end": 56.699, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 56.699, "end": 56.699, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "la", "start": 56.699, "end": 56.819, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 56.819, "end": 56.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "gente", "start": 56.819, "end": 57.099, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 57.099, "end": 57.099, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 57.099, "end": 57.18, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 57.18, "end": 57.239, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Palermo,", "start": 57.239, "end": 57.68, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 57.68, "end": 58.159, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "quello", "start": 58.159, "end": 58.42, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 58.42, "end": 58.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "non", "start": 58.479, "end": 58.599, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 58.599, "end": 58.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "vende", "start": 58.639, "end": 58.92, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 58.92, "end": 58.92, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "una", "start": 58.92, "end": 59.04, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 59.04, "end": 59.04, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "spugna", "start": 59.04, "end": 59.379, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 59.379, "end": 59.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 59.379, "end": 59.679, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 59.679, "end": 59.699, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "con", "start": 59.699, "end": 59.819, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 59.819, "end": 59.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "l'aiuto", "start": 59.819, "end": 60.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 60.139, "end": 60.16, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 60.16, "end": 60.259, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 60.259, "end": 60.279, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Santa", "start": 60.279, "end": 60.499, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 60.499, "end": 60.539, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Rosalia.", "start": 60.539, "end": 61.019, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 61.019, "end": 61.319, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Ma", "start": 61.319, "end": 61.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 61.479, "end": 61.499, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "preferisco", "start": 61.499, "end": 61.959, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 61.959, "end": 61.999, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "che", "start": 61.999, "end": 62.08, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 62.08, "end": 62.12, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "si", "start": 62.12, "end": 62.219, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 62.219, "end": 62.219, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "rovini", "start": 62.219, "end": 62.519, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 62.519, "end": 62.519, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "con", "start": 62.519, "end": 62.64, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 62.64, "end": 62.64, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "le", "start": 62.64, "end": 62.719, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 62.719, "end": 62.719, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sue", "start": 62.719, "end": 62.899, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 62.899, "end": 62.939, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mani.", "start": 62.939, "end": 63.28, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 63.28, "end": 63.439, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>", "start": 63.439, "end": 63.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 63.579, "end": 63.62, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "pensato", "start": 63.62, "end": 64.019, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 64.019, "end": 64.039, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "bene", "start": 64.039, "end": 64.259, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 64.259, "end": 64.259, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 64.259, "end": 64.339, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 64.339, "end": 64.359, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "quello", "start": 64.359, "end": 64.519, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 64.519, "end": 64.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 64.559, "end": 64.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 64.699, "end": 64.719, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "fai?", "start": 64.719, "end": 65.059, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 65.059, "end": 65.64, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Che", "start": 65.64, "end": 65.759, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 65.759, "end": 65.779, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "cosa", "start": 65.779, "end": 66.0, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 66.0, "end": 66.02, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "vuoi", "start": 66.02, "end": 66.18, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 66.18, "end": 66.199, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "dire?", "start": 66.199, "end": 66.519, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 66.519, "end": 67.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 67.299, "end": 67.56, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 67.56, "end": 67.64, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "dire", "start": 67.64, "end": 67.839, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 67.839, "end": 67.859, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 67.859, "end": 67.96, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 67.96, "end": 67.98, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tua", "start": 67.98, "end": 68.18, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 68.18, "end": 68.199, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "figlia", "start": 68.199, "end": 68.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 68.459, "end": 68.499, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "non", "start": 68.499, "end": 68.64, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 68.64, "end": 68.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sarà", "start": 68.659, "end": 68.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 68.859, "end": 68.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "dello", "start": 68.879, "end": 69.04, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 69.04, "end": 69.1, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "stesso", "start": 69.1, "end": 69.359, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 69.359, "end": 69.419, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "parere,", "start": 69.419, "end": 69.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 69.959, "end": 70.419, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "per", "start": 70.419, "end": 70.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 70.579, "end": 70.6, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "quello", "start": 70.6, "end": 70.839, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 70.839, "end": 70.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 70.879, "end": 70.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 70.959, "end": 70.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 70.979, "end": 71.379, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 71.379, "end": 71.379, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "il", "start": 71.379, "end": 71.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 71.459, "end": 71.499, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "g<PERSON><PERSON><PERSON><PERSON>.", "start": 71.499, "end": 72.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 72.219, "end": 73.279, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Se", "start": 73.279, "end": 73.4, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 73.4, "end": 73.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tu", "start": 73.459, "end": 73.539, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 73.539, "end": 73.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ti", "start": 73.539, "end": 73.66, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 73.66, "end": 73.68, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 73.68, "end": 74.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 74.159, "end": 74.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 74.159, "end": 74.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 74.239, "end": 74.259, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "man<PERSON><PERSON>", "start": 74.259, "end": 74.739, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 74.739, "end": 74.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "via", "start": 74.739, "end": 74.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 74.919, "end": 74.94, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "dall'isola", "start": 74.94, "end": 75.539, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 75.539, "end": 75.6, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "per", "start": 75.6, "end": 75.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 75.759, "end": 75.78, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "un", "start": 75.78, "end": 75.9, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 75.9, "end": 75.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "po'", "start": 75.919, "end": 76.039, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 76.039, "end": 76.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 76.059, "end": 76.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 76.199, "end": 76.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tempo...", "start": 76.239, "end": 76.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 76.819, "end": 76.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Ci", "start": 76.819, "end": 76.879, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 76.879, "end": 76.879, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ho", "start": 76.879, "end": 76.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 76.979, "end": 76.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "già", "start": 76.979, "end": 77.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 77.139, "end": 77.139, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "pensato.", "start": 77.139, "end": 77.719, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 77.719, "end": 78.02, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 78.02, "end": 78.32, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 78.32, "end": 78.36, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mese", "start": 78.36, "end": 78.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 78.559, "end": 78.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "a", "start": 78.559, "end": 78.619, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 78.619, "end": 78.619, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Palermo", "start": 78.619, "end": 78.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 78.979, "end": 78.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "non", "start": 78.979, "end": 79.1, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 79.1, "end": 79.1, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "le", "start": 79.1, "end": 79.219, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 79.219, "end": 79.219, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "far<PERSON>", "start": 79.219, "end": 79.4, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 79.4, "end": 79.419, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "male.", "start": 79.419, "end": 79.78, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 79.78, "end": 81.78, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Ne", "start": 81.78, "end": 81.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 81.919, "end": 81.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sono", "start": 81.979, "end": 82.28, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 82.28, "end": 82.36, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "certa.", "start": 82.36, "end": 83.04, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 83.04, "end": 83.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>", "start": 83.659, "end": 83.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 83.959, "end": 83.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "e", "start": 83.959, "end": 84.039, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 84.039, "end": 84.039, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "quel", "start": 84.039, "end": 84.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 84.219, "end": 84.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "pescatore", "start": 84.219, "end": 84.719, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 84.719, "end": 84.719, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "insieme", "start": 84.719, "end": 85.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 85.139, "end": 85.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "non", "start": 85.159, "end": 85.279, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 85.279, "end": 85.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 85.299, "end": 85.719, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 85.719, "end": 85.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "crea<PERSON>i", "start": 85.739, "end": 86.18, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 86.18, "end": 86.18, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 86.18, "end": 86.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 86.299, "end": 86.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "dei", "start": 86.299, "end": 86.439, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 86.439, "end": 86.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>,", "start": 86.479, "end": 87.079, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 87.079, "end": 87.079, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "credimi.", "start": 87.079, "end": 87.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 87.759, "end": 97.579, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>.", "start": 97.579, "end": 98.12, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 98.12, "end": 99.2, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Sì,", "start": 99.2, "end": 99.499, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 99.499, "end": 99.499, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "papà.", "start": 99.499, "end": 99.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 99.919, "end": 100.499, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Prepara", "start": 100.499, "end": 100.839, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 100.839, "end": 100.839, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "le", "start": 100.839, "end": 100.92, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 100.92, "end": 100.939, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "valigie", "start": 100.939, "end": 101.22, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 101.22, "end": 101.22, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "per", "start": 101.22, "end": 101.319, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 101.319, "end": 101.36, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "qualche", "start": 101.36, "end": 101.58, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 101.58, "end": 101.619, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>ior<PERSON>", "start": 101.619, "end": 101.779, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 101.779, "end": 101.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 101.799, "end": 101.88, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 101.88, "end": 101.899, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "assenza,", "start": 101.899, "end": 102.279, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 102.279, "end": 102.319, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ti", "start": 102.319, "end": 102.439, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 102.439, "end": 102.439, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "condurr<PERSON>", "start": 102.439, "end": 102.819, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 102.819, "end": 102.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "a", "start": 102.819, "end": 102.9, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 102.9, "end": 102.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Palermo.", "start": 102.919, "end": 103.379, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 103.379, "end": 104.18, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "A", "start": 104.18, "end": 104.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 104.299, "end": 104.339, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Palermo?", "start": 104.339, "end": 104.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 104.819, "end": 104.86, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "E", "start": 104.86, "end": 104.999, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 104.999, "end": 104.999, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "perché?", "start": 104.999, "end": 105.439, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 105.439, "end": 105.86, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>", "start": 105.86, "end": 105.999, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 105.999, "end": 105.999, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "già", "start": 105.999, "end": 106.119, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 106.119, "end": 106.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "preso", "start": 106.119, "end": 106.339, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 106.339, "end": 106.36, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "i", "start": 106.36, "end": 106.42, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 106.42, "end": 106.439, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>,", "start": 106.439, "end": 106.819, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 106.819, "end": 106.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "si", "start": 106.819, "end": 106.959, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 106.959, "end": 106.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "parte", "start": 106.979, "end": 107.18, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 107.18, "end": 107.18, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "alle", "start": 107.18, "end": 107.319, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 107.319, "end": 107.319, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "otto.", "start": 107.319, "end": 107.64, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 107.64, "end": 109.04, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Non", "start": 109.04, "end": 109.22, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 109.22, "end": 109.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "posso", "start": 109.299, "end": 109.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 109.559, "end": 109.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "andare,", "start": 109.559, "end": 109.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 109.959, "end": 109.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "papà.", "start": 109.959, "end": 110.359, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 110.359, "end": 111.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Per", "start": 111.299, "end": 111.459, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 111.459, "end": 111.52, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "non", "start": 111.52, "end": 111.659, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 111.659, "end": 111.659, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "abbandonare", "start": 111.659, "end": 112.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 112.159, "end": 112.159, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "il", "start": 112.159, "end": 112.2, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 112.2, "end": 112.2, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "rosario,", "start": 112.2, "end": 112.619, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 112.619, "end": 112.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "forse?", "start": 112.639, "end": 113.019, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 113.019, "end": 113.579, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Quell'uomo", "start": 113.579, "end": 114.02, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 114.02, "end": 114.02, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mi", "start": 114.02, "end": 114.119, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 114.119, "end": 114.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "è", "start": 114.119, "end": 114.2, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 114.2, "end": 114.2, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "già", "start": 114.2, "end": 114.279, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 114.279, "end": 114.279, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "stancato", "start": 114.279, "end": 114.639, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 114.639, "end": 114.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "a<PERSON><PERSON><PERSON>.", "start": 114.639, "end": 115.3, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 115.3, "end": 115.36, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Non", "start": 115.36, "end": 115.499, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 115.499, "end": 115.499, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "voglio", "start": 115.499, "end": 115.74, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 115.74, "end": 115.759, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "diventare", "start": 115.759, "end": 116.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 116.139, "end": 116.159, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "la", "start": 116.159, "end": 116.259, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 116.259, "end": 116.259, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "favola", "start": 116.259, "end": 116.52, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 116.52, "end": 116.52, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "dell'isola,", "start": 116.52, "end": 116.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 116.979, "end": 116.999, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "io.", "start": 116.999, "end": 117.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 117.159, "end": 117.859, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Tu", "start": 117.859, "end": 117.999, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 117.999, "end": 117.999, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "verrai", "start": 117.999, "end": 118.319, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 118.319, "end": 118.319, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "a", "start": 118.319, "end": 118.379, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 118.379, "end": 118.399, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Palermo", "start": 118.399, "end": 118.719, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 118.719, "end": 118.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "in", "start": 118.739, "end": 118.819, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 118.819, "end": 118.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "collegio", "start": 118.819, "end": 119.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 119.139, "end": 119.139, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "e", "start": 119.139, "end": 119.199, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 119.199, "end": 119.199, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ci", "start": 119.199, "end": 119.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 119.299, "end": 119.319, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 119.319, "end": 119.619, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 119.619, "end": 119.619, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "fino", "start": 119.619, "end": 119.759, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 119.759, "end": 119.759, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "a", "start": 119.759, "end": 119.839, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 119.839, "end": 119.839, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "quando", "start": 119.839, "end": 120.039, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 120.039, "end": 120.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "non", "start": 120.059, "end": 120.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 120.139, "end": 120.139, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "avrai", "start": 120.139, "end": 120.379, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 120.379, "end": 120.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "dimenticato", "start": 120.379, "end": 121.019, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 121.019, "end": 121.219, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "questa", "start": 121.219, "end": 121.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 121.479, "end": 121.499, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "stupida", "start": 121.499, "end": 121.859, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 121.859, "end": 121.859, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "storia.", "start": 121.859, "end": 122.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 122.299, "end": 122.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Potrai", "start": 122.819, "end": 123.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 123.199, "end": 123.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 123.219, "end": 123.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 123.819, "end": 123.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "in", "start": 123.819, "end": 123.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 123.959, "end": 123.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "città,", "start": 123.979, "end": 124.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 124.459, "end": 124.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>,", "start": 124.619, "end": 125.519, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 125.519, "end": 125.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "cinematografi...", "start": 125.699, "end": 127.059, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 127.059, "end": 127.079, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>,", "start": 127.079, "end": 127.539, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 127.539, "end": 128.039, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "che", "start": 128.039, "end": 128.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 128.139, "end": 128.159, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "in", "start": 128.159, "end": 128.259, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 128.259, "end": 128.279, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "fondo", "start": 128.279, "end": 128.539, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 128.539, "end": 128.539, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ha", "start": 128.539, "end": 128.659, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 128.659, "end": 128.699, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "bisogno", "start": 128.699, "end": 129.039, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 129.039, "end": 129.039, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 129.039, "end": 129.179, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 129.179, "end": 129.199, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "cambiare,", "start": 129.199, "end": 129.739, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 129.739, "end": 130.079, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 130.079, "end": 130.179, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 130.179, "end": 130.179, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "conoscere", "start": 130.179, "end": 130.659, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 130.659, "end": 130.679, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "gente", "start": 130.679, "end": 131.019, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 131.019, "end": 131.019, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "come", "start": 131.019, "end": 131.219, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 131.219, "end": 131.239, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "si", "start": 131.239, "end": 131.319, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 131.319, "end": 131.339, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "deve.", "start": 131.339, "end": 131.699, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 131.699, "end": 132.259, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Come", "start": 132.259, "end": 132.499, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 132.499, "end": 132.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "quella", "start": 132.539, "end": 132.739, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 132.739, "end": 132.759, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 132.759, "end": 132.839, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 132.839, "end": 132.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "frequenta", "start": 132.879, "end": 133.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 133.319, "end": 133.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "questa", "start": 133.319, "end": 133.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 133.559, "end": 133.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "casa,", "start": 133.599, "end": 133.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 133.959, "end": 133.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "forse?", "start": 133.959, "end": 134.419, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 134.419, "end": 135.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Che", "start": 135.299, "end": 135.439, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 135.439, "end": 135.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "cosa", "start": 135.479, "end": 135.659, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 135.659, "end": 135.659, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "vuoi", "start": 135.659, "end": 135.819, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 135.819, "end": 135.859, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "dire?", "start": 135.859, "end": 136.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 136.139, "end": 136.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "È", "start": 136.739, "end": 136.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 136.899, "end": 136.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "per", "start": 136.959, "end": 137.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 137.139, "end": 137.199, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "me", "start": 137.199, "end": 137.359, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 137.359, "end": 137.379, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 137.379, "end": 137.519, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 137.519, "end": 137.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "lo", "start": 137.539, "end": 137.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 137.619, "end": 137.679, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "dice.", "start": 137.679, "end": 138.099, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 138.099, "end": 140.859, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 140.859, "end": 141.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 141.139, "end": 141.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "figlia", "start": 141.159, "end": 141.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 141.459, "end": 141.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "mi", "start": 141.479, "end": 141.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 141.579, "end": 141.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "guarda", "start": 141.619, "end": 141.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 141.919, "end": 141.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "da<PERSON><PERSON><PERSON>", "start": 141.959, "end": 142.359, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 142.359, "end": 142.419, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "con", "start": 142.419, "end": 142.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 142.559, "end": 142.639, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "molta", "start": 142.639, "end": 142.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 142.959, "end": 143.039, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "simpatia.", "start": 143.039, "end": 143.839, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 143.839, "end": 144.719, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>", "start": 144.719, "end": 144.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 144.799, "end": 144.859, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "meraviglio", "start": 144.859, "end": 145.359, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 145.359, "end": 145.379, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 145.379, "end": 145.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 145.479, "end": 145.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "con", "start": 145.559, "end": 145.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 145.659, "end": 145.679, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Margh<PERSON><PERSON>", "start": 145.679, "end": 146.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 146.199, "end": 146.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "non", "start": 146.219, "end": 146.339, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 146.339, "end": 146.379, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "si", "start": 146.379, "end": 146.499, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 146.499, "end": 146.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "siano", "start": 146.559, "end": 146.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 146.779, "end": 146.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "accordate", "start": 146.779, "end": 147.379, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 147.379, "end": 147.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "per", "start": 147.459, "end": 147.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 147.619, "end": 147.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.", "start": 147.619, "end": 148.499, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 148.499, "end": 148.899, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Non", "start": 148.899, "end": 149.039, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 149.039, "end": 149.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "è", "start": 149.059, "end": 149.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 149.119, "end": 149.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "vero,", "start": 149.159, "end": 149.419, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 149.419, "end": 149.439, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>?", "start": 149.439, "end": 149.879, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 149.879, "end": 154.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Non", "start": 154.599, "end": 154.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 154.759, "end": 154.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "preoccuparti,", "start": 154.819, "end": 155.739, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 155.739, "end": 155.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>.", "start": 155.739, "end": 156.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 156.219, "end": 156.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>a", "start": 156.619, "end": 156.839, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 156.839, "end": 156.859, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ragazza", "start": 156.859, "end": 157.259, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 157.259, "end": 157.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "far<PERSON>", "start": 157.299, "end": 157.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 157.559, "end": 157.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "bene,", "start": 157.599, "end": 157.939, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 157.939, "end": 157.939, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "vedrai.", "start": 157.939, "end": 158.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 158.479, "end": 158.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "E", "start": 158.819, "end": 158.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 158.919, "end": 159.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "poi", "start": 159.019, "end": 159.259, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 159.259, "end": 159.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "quando", "start": 159.299, "end": 159.599, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 159.599, "end": 159.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ritornerai", "start": 159.619, "end": 160.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 160.159, "end": 160.179, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "da", "start": 160.179, "end": 160.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 160.299, "end": 160.359, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Palermo,", "start": 160.359, "end": 160.999, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 160.999, "end": 161.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "saremo", "start": 161.599, "end": 161.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 161.979, "end": 162.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "più", "start": 162.019, "end": 162.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 162.299, "end": 162.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "soli.", "start": 162.319, "end": 162.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 162.819, "end": 164.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Così", "start": 164.599, "end": 164.879, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 164.879, "end": 164.939, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "voi", "start": 164.939, "end": 165.179, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 165.179, "end": 165.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "partiste", "start": 165.299, "end": 165.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 165.799, "end": 165.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "per", "start": 165.799, "end": 165.939, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 165.939, "end": 165.999, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Palermo", "start": 165.999, "end": 166.399, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 166.399, "end": 166.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "e", "start": 166.739, "end": 166.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 166.919, "end": 167.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "vostra", "start": 167.019, "end": 167.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 167.319, "end": 167.379, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "moglie", "start": 167.379, "end": 167.679, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 167.679, "end": 167.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "rimase", "start": 167.799, "end": 168.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 168.299, "end": 168.419, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sola.", "start": 168.419, "end": 168.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 168.819, "end": 169.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Per", "start": 169.559, "end": 169.719, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 169.719, "end": 169.759, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "pochi", "start": 169.759, "end": 170.019, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 170.019, "end": 170.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>ior<PERSON>", "start": 170.059, "end": 170.359, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 170.359, "end": 170.399, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "per<PERSON>.", "start": 170.399, "end": 170.739, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 170.739, "end": 171.359, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Vi", "start": 171.359, "end": 171.519, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 171.519, "end": 171.539, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sembre<PERSON><PERSON>", "start": 171.539, "end": 171.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 171.979, "end": 171.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "impossibile,", "start": 171.979, "end": 172.639, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 172.639, "end": 172.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ma", "start": 172.639, "end": 172.859, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 172.859, "end": 174.199, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ogni", "start": 174.199, "end": 174.439, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 174.439, "end": 174.499, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "minuto", "start": 174.499, "end": 174.819, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 174.819, "end": 174.839, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "lontano", "start": 174.839, "end": 175.199, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 175.199, "end": 175.219, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "da", "start": 175.219, "end": 175.339, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 175.339, "end": 175.339, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "lei", "start": 175.339, "end": 175.599, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 175.599, "end": 175.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "era", "start": 175.799, "end": 176.039, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 176.039, "end": 176.079, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "per", "start": 176.079, "end": 176.279, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 176.279, "end": 176.339, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "me", "start": 176.339, "end": 176.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 176.479, "end": 176.519, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "peggio", "start": 176.519, "end": 176.859, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 176.859, "end": 176.859, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 176.859, "end": 176.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 176.979, "end": 176.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "una", "start": 176.979, "end": 177.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 177.159, "end": 177.179, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "coltellata.", "start": 177.179, "end": 177.839, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 177.839, "end": 178.579, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "La", "start": 178.579, "end": 178.699, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 178.699, "end": 178.699, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "trovai", "start": 178.699, "end": 179.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 179.139, "end": 179.279, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "nervosa,", "start": 179.279, "end": 179.859, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 179.859, "end": 179.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mi", "start": 179.919, "end": 180.079, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 180.079, "end": 180.099, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "fece", "start": 180.099, "end": 180.319, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 180.319, "end": 180.359, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "uno", "start": 180.359, "end": 180.519, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 180.519, "end": 180.539, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "strano", "start": 180.539, "end": 180.839, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 180.839, "end": 180.839, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "effetto.", "start": 180.839, "end": 181.439, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 181.439, "end": 181.659, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "E", "start": 181.659, "end": 181.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 181.819, "end": 181.839, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "non", "start": 181.839, "end": 181.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 181.959, "end": 181.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "vi", "start": 181.979, "end": 182.079, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 182.079, "end": 182.079, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "domandaste", "start": 182.079, "end": 182.639, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 182.639, "end": 182.679, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "il", "start": 182.679, "end": 182.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 182.799, "end": 182.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "perché", "start": 182.879, "end": 183.339, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 183.339, "end": 183.379, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 183.379, "end": 183.539, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 183.539, "end": 183.579, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "questo", "start": 183.579, "end": 183.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 183.819, "end": 183.839, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "cambiamento?", "start": 183.839, "end": 184.499, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 184.499, "end": 185.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "No.", "start": 185.139, "end": 185.499, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 185.499, "end": 186.079, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Credevo", "start": 186.079, "end": 186.499, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 186.499, "end": 186.519, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "che", "start": 186.519, "end": 186.619, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 186.619, "end": 186.659, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "fosse", "start": 186.659, "end": 186.939, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 186.939, "end": 186.939, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "per", "start": 186.939, "end": 187.059, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 187.059, "end": 187.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "quanto", "start": 187.119, "end": 187.359, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 187.359, "end": 187.359, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "era", "start": 187.359, "end": 187.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 187.559, "end": 187.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "successo", "start": 187.559, "end": 187.999, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 187.999, "end": 188.019, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "nell'isola.", "start": 188.019, "end": 188.659, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 188.659, "end": 189.239, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Il", "start": 189.239, "end": 189.359, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 189.359, "end": 189.419, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "suicidio", "start": 189.419, "end": 189.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 189.979, "end": 190.079, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 190.079, "end": 190.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 190.159, "end": 190.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON>?", "start": 190.219, "end": 190.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 190.779, "end": 190.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Sì.", "start": 190.959, "end": 191.279, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 191.279, "end": 192.019, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "La", "start": 192.019, "end": 192.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 192.139, "end": 192.159, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sorella", "start": 192.159, "end": 192.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 192.479, "end": 192.499, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 192.499, "end": 192.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 192.559, "end": 192.599, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Rosario", "start": 192.599, "end": 192.959, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 192.959, "end": 192.959, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "era", "start": 192.959, "end": 193.119, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 193.119, "end": 193.139, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "stata", "start": 193.139, "end": 193.379, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 193.379, "end": 193.439, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "trovata", "start": 193.439, "end": 193.759, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 193.759, "end": 193.759, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "impiccata.", "start": 193.759, "end": 194.439, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 194.439, "end": 195.659, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "L'aveva", "start": 195.659, "end": 196.079, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 196.079, "end": 196.099, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "fatto", "start": 196.099, "end": 196.419, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 196.419, "end": 196.419, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "per", "start": 196.419, "end": 196.579, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 196.579, "end": 196.579, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "amore,", "start": 196.579, "end": 197.099, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 197.099, "end": 197.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "per", "start": 197.559, "end": 197.759, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 197.759, "end": 197.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "quel", "start": 197.799, "end": 198.039, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 198.039, "end": 198.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "disgraziato", "start": 198.059, "end": 198.819, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 198.819, "end": 198.839, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 198.839, "end": 198.939, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 198.939, "end": 198.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>.", "start": 198.979, "end": 199.679, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 199.679, "end": 201.039, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 201.039, "end": 201.419, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 201.419, "end": 201.439, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "vero", "start": 201.439, "end": 201.699, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 201.699, "end": 201.699, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "che", "start": 201.699, "end": 201.819, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 201.819, "end": 201.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "le", "start": 201.819, "end": 201.919, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 201.919, "end": 201.959, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "donne", "start": 201.959, "end": 202.279, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 202.279, "end": 202.519, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "si", "start": 202.519, "end": 202.639, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 202.639, "end": 202.719, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "perdono", "start": 202.719, "end": 203.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 203.159, "end": 203.199, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "per", "start": 203.199, "end": 203.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 203.299, "end": 203.319, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "i", "start": 203.319, "end": 203.399, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 203.399, "end": 203.399, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>.", "start": 203.399, "end": 204.059, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 204.059, "end": 205.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Di", "start": 205.379, "end": 205.499, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 205.499, "end": 205.519, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "quel", "start": 205.519, "end": 205.679, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 205.679, "end": 205.679, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "suicidio", "start": 205.679, "end": 206.319, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 206.319, "end": 206.339, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ne", "start": 206.339, "end": 206.459, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 206.459, "end": 206.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 206.479, "end": 206.959, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 206.959, "end": 206.999, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "tutti", "start": 206.999, "end": 207.339, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 207.339, "end": 207.419, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "nell'isola.", "start": 207.419, "end": 208.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 208.159, "end": 211.599, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Il", "start": 211.599, "end": 211.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 211.799, "end": 211.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "negro", "start": 211.819, "end": 212.099, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 212.099, "end": 212.099, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "l'ha", "start": 212.099, "end": 212.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 212.219, "end": 212.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "vista", "start": 212.219, "end": 212.439, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 212.439, "end": 212.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "co<PERSON>e", "start": 212.479, "end": 212.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 212.779, "end": 212.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "per", "start": 212.779, "end": 212.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 212.899, "end": 212.899, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "la", "start": 212.899, "end": 212.999, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 212.999, "end": 212.999, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sco<PERSON><PERSON>", "start": 212.999, "end": 213.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 213.479, "end": 213.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "e", "start": 213.559, "end": 213.639, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 213.639, "end": 213.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "poi", "start": 213.659, "end": 213.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 213.819, "end": 213.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "l'hanno", "start": 213.819, "end": 214.039, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 214.039, "end": 214.079, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "trovata", "start": 214.079, "end": 214.339, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 214.339, "end": 214.339, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "impiccata", "start": 214.339, "end": 214.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 214.779, "end": 214.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "nel", "start": 214.799, "end": 214.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 214.959, "end": 214.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "fondo", "start": 214.979, "end": 215.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 215.199, "end": 215.199, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 215.199, "end": 215.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 215.299, "end": 215.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Padron", "start": 215.299, "end": 215.599, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 215.599, "end": 215.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Grava.", "start": 215.619, "end": 215.999, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 215.999, "end": 216.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Povero", "start": 216.299, "end": 216.639, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 216.639, "end": 216.659, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Rosario.", "start": 216.659, "end": 217.279, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 217.279, "end": 217.399, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Chissà", "start": 217.399, "end": 217.699, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 217.699, "end": 217.739, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "che", "start": 217.739, "end": 217.879, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 217.879, "end": 217.879, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "colpo", "start": 217.879, "end": 218.119, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 218.119, "end": 218.139, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "terribile", "start": 218.139, "end": 218.599, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 218.599, "end": 218.599, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "sarà", "start": 218.599, "end": 218.819, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 218.819, "end": 218.839, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "per", "start": 218.839, "end": 218.979, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 218.979, "end": 218.999, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "lui", "start": 218.999, "end": 219.159, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 219.159, "end": 219.159, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "quando", "start": 219.159, "end": 219.399, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 219.399, "end": 219.439, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "tornerà", "start": 219.439, "end": 219.779, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 219.779, "end": 219.819, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "da", "start": 219.819, "end": 219.959, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 219.959, "end": 219.959, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Palermo.", "start": 219.959, "end": 220.459, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 220.459, "end": 220.579, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Certo", "start": 220.579, "end": 220.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 220.859, "end": 220.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "non", "start": 220.879, "end": 220.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 220.979, "end": 220.999, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 220.999, "end": 221.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 221.239, "end": 221.279, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "troppo", "start": 221.279, "end": 221.539, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 221.539, "end": 221.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "liscia.", "start": 221.559, "end": 221.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 221.959, "end": 222.639, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Salute,", "start": 222.639, "end": 222.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 222.979, "end": 222.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "don", "start": 222.979, "end": 223.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 223.119, "end": 223.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>.", "start": 223.139, "end": 223.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 223.579, "end": 225.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Salute.", "start": 225.059, "end": 225.459, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 225.459, "end": 225.459, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Salute.", "start": 225.459, "end": 225.599, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 225.599, "end": 227.899, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>", "start": 227.899, "end": 228.179, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 228.179, "end": 228.179, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "un", "start": 228.179, "end": 228.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 228.299, "end": 228.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "anice.", "start": 228.299, "end": 228.859, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 228.859, "end": 231.699, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Scusate,", "start": 231.699, "end": 232.259, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 232.259, "end": 232.259, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "don", "start": 232.259, "end": 232.379, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 232.379, "end": 232.419, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON>.", "start": 232.419, "end": 232.759, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 232.759, "end": 232.759, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Che", "start": 232.759, "end": 232.879, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 232.879, "end": 232.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "c'è?", "start": 232.979, "end": 233.259, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 233.259, "end": 233.259, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "È", "start": 233.259, "end": 233.279, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 233.279, "end": 233.999, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "per", "start": 233.999, "end": 234.239, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 234.239, "end": 234.239, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "il", "start": 234.239, "end": 234.359, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 234.359, "end": 234.419, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>,", "start": 234.419, "end": 234.919, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 234.919, "end": 234.939, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "sapete?", "start": 234.939, "end": 235.459, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 235.459, "end": 235.899, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Che", "start": 235.899, "end": 236.039, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 236.039, "end": 236.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "battel<PERSON>?", "start": 236.059, "end": 236.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 236.559, "end": 237.099, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Il", "start": 237.099, "end": 237.259, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 237.259, "end": 237.319, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Fortuna", "start": 237.319, "end": 237.659, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 237.659, "end": 237.679, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "II,", "start": 237.679, "end": 238.039, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 238.039, "end": 238.499, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "quello", "start": 238.499, "end": 238.739, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 238.739, "end": 238.759, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "che", "start": 238.759, "end": 238.839, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 238.839, "end": 238.879, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "mi", "start": 238.879, "end": 238.959, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 238.959, "end": 238.979, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "avete", "start": 238.979, "end": 239.159, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 239.159, "end": 239.159, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "affittato", "start": 239.159, "end": 239.579, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 239.579, "end": 239.639, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "voi.", "start": 239.639, "end": 240.119, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 240.119, "end": 240.359, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Beh?", "start": 240.359, "end": 240.679, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 240.679, "end": 241.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 241.299, "end": 241.619, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 241.619, "end": 241.619, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "do<PERSON><PERSON>", "start": 241.619, "end": 241.899, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 241.899, "end": 241.919, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "pagare.", "start": 241.919, "end": 242.399, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 242.399, "end": 242.539, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "E", "start": 242.539, "end": 242.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 242.559, "end": 242.659, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "tu", "start": 242.659, "end": 242.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 242.979, "end": 242.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "pagami.", "start": 242.979, "end": 243.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 243.479, "end": 243.519, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Ma", "start": 243.519, "end": 244.259, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 244.259, "end": 244.279, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "non", "start": 244.279, "end": 244.419, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 244.419, "end": 244.419, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "ho", "start": 244.419, "end": 244.519, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 244.519, "end": 244.519, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "i", "start": 244.519, "end": 244.579, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 244.579, "end": 244.639, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "soldi.", "start": 244.639, "end": 245.139, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 245.139, "end": 246.199, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Be'", "start": 246.199, "end": 246.459, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 246.459, "end": 246.519, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "adesso", "start": 246.519, "end": 246.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 246.979, "end": 247.659, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "non", "start": 247.659, "end": 247.819, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 247.819, "end": 247.839, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "è", "start": 247.839, "end": 247.859, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 247.859, "end": 247.859, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "il", "start": 247.859, "end": 247.959, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 247.959, "end": 247.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "caso", "start": 247.979, "end": 248.239, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 248.239, "end": 248.239, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 248.239, "end": 248.359, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 248.359, "end": 248.359, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "parlare", "start": 248.359, "end": 248.719, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 248.719, "end": 248.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 248.739, "end": 248.859, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 248.859, "end": 248.879, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "questo.", "start": 248.879, "end": 249.279, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 249.279, "end": 249.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Me", "start": 249.979, "end": 250.119, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 250.119, "end": 250.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "li", "start": 250.119, "end": 250.219, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 250.219, "end": 250.219, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "darai", "start": 250.219, "end": 250.759, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 250.759, "end": 250.839, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "quando", "start": 250.839, "end": 251.219, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 251.219, "end": 251.239, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "potrai.", "start": 251.239, "end": 251.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 251.799, "end": 252.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>raz<PERSON>", "start": 252.559, "end": 252.919, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 252.919, "end": 252.939, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "don", "start": 252.939, "end": 253.059, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 253.059, "end": 253.099, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON>,", "start": 253.099, "end": 253.499, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 253.499, "end": 253.539, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "grazie.", "start": 253.539, "end": 253.919, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 253.919, "end": 254.399, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Avete", "start": 254.399, "end": 254.679, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 254.679, "end": 254.699, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "sentito", "start": 254.699, "end": 255.019, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 255.019, "end": 255.039, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "don", "start": 255.039, "end": 255.179, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 255.179, "end": 255.199, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON>?", "start": 255.199, "end": 255.599, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 255.599, "end": 255.899, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Già,", "start": 255.899, "end": 256.399, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 256.399, "end": 257.359, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "povera", "start": 257.359, "end": 257.739, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 257.739, "end": 257.759, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ragazza.", "start": 257.759, "end": 258.259, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 258.259, "end": 258.939, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "E", "start": 258.939, "end": 259.119, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 259.119, "end": 259.139, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "non", "start": 259.139, "end": 259.259, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 259.259, "end": 259.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "si", "start": 259.299, "end": 259.399, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 259.399, "end": 259.399, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "è", "start": 259.399, "end": 259.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 259.479, "end": 259.499, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "saputo", "start": 259.499, "end": 259.899, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 259.899, "end": 259.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "perché", "start": 259.919, "end": 260.179, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 260.179, "end": 260.179, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "la", "start": 260.179, "end": 260.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 260.299, "end": 260.319, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "fa.", "start": 260.319, "end": 260.599, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 260.599, "end": 260.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Dicono", "start": 260.639, "end": 261.019, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 261.019, "end": 261.039, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "che", "start": 261.039, "end": 261.139, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 261.139, "end": 261.159, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "verranno", "start": 261.159, "end": 261.539, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 261.539, "end": 261.539, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "la", "start": 261.539, "end": 261.639, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 261.639, "end": 261.659, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "polizia", "start": 261.659, "end": 262.119, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 262.119, "end": 262.139, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "e", "start": 262.139, "end": 262.199, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 262.199, "end": 262.219, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "il", "start": 262.219, "end": 262.279, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 262.279, "end": 262.319, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "medico", "start": 262.319, "end": 262.619, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 262.619, "end": 262.639, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "legale.", "start": 262.639, "end": 263.099, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 263.099, "end": 263.859, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON>", "start": 263.859, "end": 263.999, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 263.999, "end": 264.019, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "saputo", "start": 264.019, "end": 264.299, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 264.299, "end": 264.299, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "che", "start": 264.299, "end": 264.399, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 264.399, "end": 264.419, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "hanno", "start": 264.419, "end": 264.559, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 264.559, "end": 264.579, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "interrogato", "start": 264.579, "end": 265.079, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 265.079, "end": 265.079, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "il", "start": 265.079, "end": 265.159, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 265.159, "end": 265.199, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "negro,", "start": 265.199, "end": 265.539, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 265.539, "end": 265.539, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "ma", "start": 265.539, "end": 265.659, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 265.659, "end": 265.679, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "da", "start": 265.679, "end": 265.759, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 265.759, "end": 265.779, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "lui", "start": 265.779, "end": 265.919, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 265.919, "end": 265.959, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "pu<PERSON><PERSON><PERSON>", "start": 265.959, "end": 266.419, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 266.419, "end": 266.419, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "sono", "start": 266.419, "end": 266.659, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 266.659, "end": 266.679, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 266.679, "end": 267.119, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 267.119, "end": 267.119, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "a", "start": 267.119, "end": 267.199, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 267.199, "end": 267.199, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "cavar", "start": 267.199, "end": 267.479, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 267.479, "end": 267.499, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "fuori", "start": 267.499, "end": 267.779, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 267.779, "end": 267.819, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "ben", "start": 267.819, "end": 268.059, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 268.059, "end": 268.059, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "poco.", "start": 268.059, "end": 268.439, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 268.439, "end": 268.439, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "È", "start": 268.439, "end": 269.339, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 269.339, "end": 269.679, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "una", "start": 269.679, "end": 269.899, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 269.899, "end": 269.939, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "brutta", "start": 269.939, "end": 270.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 270.299, "end": 270.339, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "storia.", "start": 270.339, "end": 270.919, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 270.919, "end": 273.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 273.739, "end": 274.099, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 274.099, "end": 274.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "stato", "start": 274.119, "end": 274.379, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 274.379, "end": 274.399, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "meglio", "start": 274.399, "end": 274.659, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 274.659, "end": 274.659, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "che", "start": 274.659, "end": 274.759, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 274.759, "end": 274.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "foste", "start": 274.799, "end": 275.039, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 275.039, "end": 275.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "venuto", "start": 275.059, "end": 275.339, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 275.339, "end": 275.339, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "allora.", "start": 275.339, "end": 275.759, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 275.759, "end": 276.079, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "E", "start": 276.079, "end": 276.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 276.239, "end": 276.259, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "siete", "start": 276.259, "end": 276.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 276.579, "end": 276.579, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "proprio", "start": 276.579, "end": 276.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 276.959, "end": 276.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sicuro?", "start": 276.959, "end": 277.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 277.459, "end": 277.679, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Da", "start": 277.679, "end": 277.839, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 277.839, "end": 277.859, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "quel", "start": 277.859, "end": 277.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 277.979, "end": 277.999, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "momento", "start": 277.999, "end": 278.319, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 278.319, "end": 278.339, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "le", "start": 278.339, "end": 278.459, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 278.459, "end": 278.459, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "cose", "start": 278.459, "end": 278.699, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 278.699, "end": 278.719, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "precipitarono.", "start": 278.719, "end": 279.519, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 279.519, "end": 280.579, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Voi", "start": 280.579, "end": 280.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 280.799, "end": 280.839, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "siete", "start": 280.839, "end": 281.019, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 281.019, "end": 281.039, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "un", "start": 281.039, "end": 281.119, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 281.119, "end": 281.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "poliziotto", "start": 281.119, "end": 281.599, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 281.599, "end": 281.599, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "e", "start": 281.599, "end": 281.639, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 281.639, "end": 281.659, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "vi", "start": 281.659, "end": 281.759, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 281.759, "end": 281.779, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "piace", "start": 281.779, "end": 282.019, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 282.019, "end": 282.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sapere", "start": 282.059, "end": 282.359, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 282.359, "end": 282.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "i", "start": 282.379, "end": 282.459, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 282.459, "end": 282.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "fatti", "start": 282.479, "end": 282.699, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 282.699, "end": 282.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "nell'or<PERSON>.", "start": 282.739, "end": 283.519, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 283.519, "end": 284.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "E", "start": 284.379, "end": 284.499, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 284.499, "end": 284.519, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "invece", "start": 284.519, "end": 284.859, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 284.859, "end": 284.899, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "tutto", "start": 284.899, "end": 285.119, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 285.119, "end": 285.159, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "fu", "start": 285.159, "end": 285.279, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 285.279, "end": 285.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "così", "start": 285.299, "end": 285.519, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 285.519, "end": 285.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "confuso", "start": 285.559, "end": 286.059, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 286.059, "end": 286.099, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "che...", "start": 286.099, "end": 286.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}], "language_detected": null, "confidence": null, "processing_time": 20.344498872756958, "metadata": {"service": "elevenlabs", "mode": "free", "model": "scribe_v1", "raw_result": {"language_code": "ita", "language_probability": 0.9880191087722778, "text": "Mi dai una sigaretta? Tieni. Hai visto la baracca che stanno costruendo al porto? Ah, poche tavele marci in piedi e quattro soldi nelle mani di un ragazzo. Se sperano di mettermi paura con quella pagliacciata della cooperativa, si sbagliano e finiranno per rimetterci anche le reti. Quel giovanotto è più abile di quanto tu creda. Non dovresti sottovalutarlo. Chi, Rosario? È un giovane energico e i pescatori lo seguono. È pericoloso, ti dico. Ha ceduto il battello per un anno. E se riesco a combinare con la gente di Palermo, quello non vende una spugna nemmeno con l'aiuto di Santa Rosalia. Ma preferisco che si rovini con le sue mani. Hai pensato bene a quello che fai? Che cosa vuoi dire? Voglio dire che tua figlia non sarà dello stesso parere, per quello che riguarda il giovanotto. Se tu ti decidessi a mandarla via dall'isola per un po' di tempo... Ci ho già pensato. Qualche mese a Palermo non le farà male. Ne sono certa. Maria e quel pescatore insieme non potrebbero crearti che dei fastidi, credimi. Maria. Sì, papà. Prepara le valigie per qualche giorno di assenza, ti condurrò a Palermo. A Palermo? E perché? Ho già preso i biglietti, si parte alle otto. Non posso andare, papà. Per non abbandonare il rosario, forse? Quell'uomo mi è già stancato abbastanza. Non voglio diventare la favola dell'isola, io. Tu verrai a Palermo in collegio e ci rimarrai fino a quando non avrai dimenticato questa stupida storia. Potrai divertirti in città, negozi, cinematografi... Certo, che in fondo ha bisogno di cambiare, di conoscere gente come si deve. Come quella che frequenta questa casa, forse? Che cosa vuoi dire? È per me che lo dice. Tua figlia mi guarda davvero con molta simpatia. Mi meraviglio che con Margherita non si siano accordate per avvelenarmi. Non è vero, Maria? Non preoccuparti, Pietro. Alla ragazza farà bene, vedrai. E poi quando ritornerai da Palermo, saremo più soli. Così voi partiste per Palermo e vostra moglie rimase sola. Per pochi giorni però. Vi sembrerà impossibile, ma ogni minuto lontano da lei era per me peggio di una coltellata. La trovai nervosa, mi fece uno strano effetto. E non vi domandaste il perché di questo cambiamento? No. Credevo che fosse per quanto era successo nell'isola. Il suicidio di Carmela? Sì. La sorella di Rosario era stata trovata impiccata. L'aveva fatto per amore, per quel disgraziato di Francesco. Proprio vero che le donne si perdono per i farabutti. Di quel suicidio ne parlavano tutti nell'isola. Il negro l'ha vista correre per la scogliera e poi l'hanno trovata impiccata nel fondo di Padron Grava. Povero Rosario. Chissà che colpo terribile sarà per lui quando tornerà da Palermo. Certo non andrà troppo liscia. Salute, don Pietro. Salute. Salute. Dame un anice. Scusate, don Pietro. Che c'è? È per il battello, sapete? Che battello? Il Fortuna II, quello che mi avete affittato voi. Beh? Domani dovrei pagare. E tu pagami. Ma non ho i soldi. Be' adesso non è il caso di parlare di questo. Me li darai quando potrai. Grazie don Pietro, grazie. Avete sentito don Pietro? Già, povera ragazza. E non si è saputo perché la fa. Dicono che verranno la polizia e il medico legale. Ho saputo che hanno interrogato il negro, ma da lui purtroppo sono riusciti a cavar fuori ben poco. È una brutta storia. Sarebbe stato meglio che foste venuto allora. E siete proprio sicuro? Da quel momento le cose precipitarono. Voi siete un poliziotto e vi piace sapere i fatti nell'ordine. E invece tutto fu così confuso che...", "words": [{"text": "<PERSON>", "start": 12.92, "end": 13.059, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 13.059, "end": 13.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "dai", "start": 13.059, "end": 13.259, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 13.259, "end": 13.259, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "una", "start": 13.259, "end": 13.379, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 13.379, "end": 13.399, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>?", "start": 13.399, "end": 14.06, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 14.06, "end": 16.02, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Tieni.", "start": 16.02, "end": 16.499, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 16.499, "end": 25.539, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>", "start": 25.539, "end": 27.179, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 27.179, "end": 27.199, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "visto", "start": 27.199, "end": 27.419, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 27.419, "end": 27.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "la", "start": 27.459, "end": 27.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 27.559, "end": 27.579, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "baracca", "start": 27.579, "end": 27.92, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 27.92, "end": 27.92, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 27.92, "end": 28.02, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 28.02, "end": 28.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "stanno", "start": 28.059, "end": 28.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 28.299, "end": 28.34, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "costruendo", "start": 28.34, "end": 28.78, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 28.78, "end": 28.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "al", "start": 28.799, "end": 28.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 28.899, "end": 28.92, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "porto?", "start": 28.92, "end": 29.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 29.319, "end": 29.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Ah,", "start": 29.539, "end": 29.899, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 29.899, "end": 30.039, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "poche", "start": 30.039, "end": 30.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 30.299, "end": 30.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "tavele", "start": 30.299, "end": 30.619, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 30.619, "end": 30.659, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "marci", "start": 30.659, "end": 30.919, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 30.919, "end": 30.92, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "in", "start": 30.92, "end": 31.019, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 31.019, "end": 31.019, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "piedi", "start": 31.019, "end": 31.279, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 31.279, "end": 31.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "e", "start": 31.299, "end": 31.379, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 31.379, "end": 31.399, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "quattro", "start": 31.399, "end": 31.619, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 31.619, "end": 31.679, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "soldi", "start": 31.679, "end": 31.939, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 31.939, "end": 31.939, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "nelle", "start": 31.939, "end": 32.119, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 32.119, "end": 32.139, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mani", "start": 32.139, "end": 32.34, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 32.34, "end": 32.34, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 32.34, "end": 32.419, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 32.419, "end": 32.419, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "un", "start": 32.419, "end": 32.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 32.479, "end": 32.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ragazzo.", "start": 32.479, "end": 32.959, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 32.959, "end": 34.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Se", "start": 34.299, "end": 34.439, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 34.439, "end": 34.439, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sperano", "start": 34.439, "end": 34.86, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 34.86, "end": 34.86, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 34.86, "end": 34.959, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 34.959, "end": 34.959, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mettermi", "start": 34.959, "end": 35.319, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 35.319, "end": 35.34, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "paura", "start": 35.34, "end": 35.619, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 35.619, "end": 35.619, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "con", "start": 35.619, "end": 35.779, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 35.779, "end": 35.779, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "quella", "start": 35.779, "end": 36.0, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 36.0, "end": 36.0, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "paglia<PERSON><PERSON>", "start": 36.0, "end": 36.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 36.559, "end": 36.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "della", "start": 36.559, "end": 36.74, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 36.74, "end": 36.759, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "cooperativa,", "start": 36.759, "end": 37.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 37.299, "end": 37.319, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "si", "start": 37.319, "end": 37.439, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 37.439, "end": 37.439, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 37.439, "end": 37.9, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 37.9, "end": 38.079, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "e", "start": 38.079, "end": 38.179, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 38.179, "end": 38.2, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "finiranno", "start": 38.2, "end": 38.619, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 38.619, "end": 38.619, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "per", "start": 38.619, "end": 38.739, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 38.739, "end": 38.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 38.739, "end": 39.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 39.159, "end": 39.159, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "anche", "start": 39.159, "end": 39.34, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 39.34, "end": 39.34, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "le", "start": 39.34, "end": 39.459, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 39.459, "end": 39.459, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "reti.", "start": 39.459, "end": 39.8, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 39.8, "end": 41.18, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Quel", "start": 41.18, "end": 41.36, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 41.36, "end": 41.399, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "start": 41.399, "end": 41.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 41.919, "end": 41.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "è", "start": 41.919, "end": 42.039, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 42.039, "end": 42.04, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "più", "start": 42.04, "end": 42.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 42.199, "end": 42.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "abile", "start": 42.219, "end": 42.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 42.559, "end": 42.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 42.559, "end": 42.68, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 42.68, "end": 42.68, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "quanto", "start": 42.68, "end": 42.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 42.959, "end": 43.02, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tu", "start": 43.02, "end": 43.179, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 43.179, "end": 43.2, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "creda.", "start": 43.2, "end": 43.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 43.659, "end": 44.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Non", "start": 44.559, "end": 44.74, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 44.74, "end": 44.759, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 44.759, "end": 45.2, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 45.2, "end": 45.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sottovalutarlo.", "start": 45.239, "end": 46.339, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 46.339, "end": 46.719, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>,", "start": 46.719, "end": 47.0, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 47.0, "end": 47.02, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Rosario?", "start": 47.02, "end": 47.579, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 47.579, "end": 47.86, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "È", "start": 47.86, "end": 48.0, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 48.0, "end": 48.02, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "un", "start": 48.02, "end": 48.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 48.119, "end": 48.18, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "giovane", "start": 48.18, "end": 48.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 48.559, "end": 48.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "energico", "start": 48.559, "end": 49.24, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 49.24, "end": 49.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "e", "start": 49.479, "end": 49.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 49.579, "end": 49.579, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "i", "start": 49.579, "end": 49.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 49.619, "end": 49.68, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "pescatori", "start": 49.68, "end": 50.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 50.159, "end": 50.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "lo", "start": 50.159, "end": 50.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 50.319, "end": 50.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "segu<PERSON>.", "start": 50.319, "end": 50.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 50.779, "end": 51.0, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "È", "start": 51.0, "end": 51.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 51.139, "end": 51.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "per<PERSON><PERSON><PERSON>,", "start": 51.159, "end": 51.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 51.779, "end": 51.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ti", "start": 51.799, "end": 51.92, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 51.92, "end": 51.939, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "dico.", "start": 51.939, "end": 52.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 52.299, "end": 53.279, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Ha", "start": 53.279, "end": 53.399, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 53.399, "end": 53.399, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ceduto", "start": 53.399, "end": 53.739, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 53.739, "end": 53.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "il", "start": 53.739, "end": 53.84, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 53.84, "end": 53.84, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "battel<PERSON>", "start": 53.84, "end": 54.22, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 54.22, "end": 54.239, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "per", "start": 54.239, "end": 54.38, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 54.38, "end": 54.399, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "un", "start": 54.399, "end": 54.5, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 54.5, "end": 54.539, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "anno.", "start": 54.539, "end": 54.84, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 54.84, "end": 55.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "E", "start": 55.479, "end": 55.599, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 55.599, "end": 55.619, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "se", "start": 55.619, "end": 55.72, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 55.72, "end": 55.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "r<PERSON>co", "start": 55.739, "end": 56.0, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 56.0, "end": 56.0, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "a", "start": 56.0, "end": 56.08, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 56.08, "end": 56.099, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "combinare", "start": 56.099, "end": 56.54, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 56.54, "end": 56.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "con", "start": 56.559, "end": 56.699, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 56.699, "end": 56.699, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "la", "start": 56.699, "end": 56.819, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 56.819, "end": 56.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "gente", "start": 56.819, "end": 57.099, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 57.099, "end": 57.099, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 57.099, "end": 57.18, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 57.18, "end": 57.239, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Palermo,", "start": 57.239, "end": 57.68, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 57.68, "end": 58.159, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "quello", "start": 58.159, "end": 58.42, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 58.42, "end": 58.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "non", "start": 58.479, "end": 58.599, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 58.599, "end": 58.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "vende", "start": 58.639, "end": 58.92, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 58.92, "end": 58.92, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "una", "start": 58.92, "end": 59.04, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 59.04, "end": 59.04, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "spugna", "start": 59.04, "end": 59.379, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 59.379, "end": 59.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 59.379, "end": 59.679, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 59.679, "end": 59.699, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "con", "start": 59.699, "end": 59.819, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 59.819, "end": 59.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "l'aiuto", "start": 59.819, "end": 60.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 60.139, "end": 60.16, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 60.16, "end": 60.259, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 60.259, "end": 60.279, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Santa", "start": 60.279, "end": 60.499, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 60.499, "end": 60.539, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Rosalia.", "start": 60.539, "end": 61.019, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 61.019, "end": 61.319, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Ma", "start": 61.319, "end": 61.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 61.479, "end": 61.499, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "preferisco", "start": 61.499, "end": 61.959, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 61.959, "end": 61.999, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "che", "start": 61.999, "end": 62.08, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 62.08, "end": 62.12, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "si", "start": 62.12, "end": 62.219, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 62.219, "end": 62.219, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "rovini", "start": 62.219, "end": 62.519, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 62.519, "end": 62.519, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "con", "start": 62.519, "end": 62.64, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 62.64, "end": 62.64, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "le", "start": 62.64, "end": 62.719, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 62.719, "end": 62.719, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sue", "start": 62.719, "end": 62.899, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 62.899, "end": 62.939, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mani.", "start": 62.939, "end": 63.28, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 63.28, "end": 63.439, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>", "start": 63.439, "end": 63.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 63.579, "end": 63.62, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "pensato", "start": 63.62, "end": 64.019, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 64.019, "end": 64.039, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "bene", "start": 64.039, "end": 64.259, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 64.259, "end": 64.259, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 64.259, "end": 64.339, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 64.339, "end": 64.359, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "quello", "start": 64.359, "end": 64.519, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 64.519, "end": 64.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 64.559, "end": 64.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 64.699, "end": 64.719, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "fai?", "start": 64.719, "end": 65.059, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 65.059, "end": 65.64, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Che", "start": 65.64, "end": 65.759, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 65.759, "end": 65.779, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "cosa", "start": 65.779, "end": 66.0, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 66.0, "end": 66.02, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "vuoi", "start": 66.02, "end": 66.18, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 66.18, "end": 66.199, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "dire?", "start": 66.199, "end": 66.519, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 66.519, "end": 67.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 67.299, "end": 67.56, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 67.56, "end": 67.64, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "dire", "start": 67.64, "end": 67.839, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 67.839, "end": 67.859, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 67.859, "end": 67.96, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 67.96, "end": 67.98, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tua", "start": 67.98, "end": 68.18, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 68.18, "end": 68.199, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "figlia", "start": 68.199, "end": 68.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 68.459, "end": 68.499, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "non", "start": 68.499, "end": 68.64, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 68.64, "end": 68.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sarà", "start": 68.659, "end": 68.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 68.859, "end": 68.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "dello", "start": 68.879, "end": 69.04, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 69.04, "end": 69.1, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "stesso", "start": 69.1, "end": 69.359, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 69.359, "end": 69.419, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "parere,", "start": 69.419, "end": 69.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 69.959, "end": 70.419, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "per", "start": 70.419, "end": 70.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 70.579, "end": 70.6, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "quello", "start": 70.6, "end": 70.839, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 70.839, "end": 70.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 70.879, "end": 70.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 70.959, "end": 70.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 70.979, "end": 71.379, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 71.379, "end": 71.379, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "il", "start": 71.379, "end": 71.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 71.459, "end": 71.499, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "g<PERSON><PERSON><PERSON><PERSON>.", "start": 71.499, "end": 72.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 72.219, "end": 73.279, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Se", "start": 73.279, "end": 73.4, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 73.4, "end": 73.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tu", "start": 73.459, "end": 73.539, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 73.539, "end": 73.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ti", "start": 73.539, "end": 73.66, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 73.66, "end": 73.68, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 73.68, "end": 74.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 74.159, "end": 74.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 74.159, "end": 74.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 74.239, "end": 74.259, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "man<PERSON><PERSON>", "start": 74.259, "end": 74.739, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 74.739, "end": 74.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "via", "start": 74.739, "end": 74.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 74.919, "end": 74.94, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "dall'isola", "start": 74.94, "end": 75.539, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 75.539, "end": 75.6, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "per", "start": 75.6, "end": 75.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 75.759, "end": 75.78, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "un", "start": 75.78, "end": 75.9, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 75.9, "end": 75.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "po'", "start": 75.919, "end": 76.039, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 76.039, "end": 76.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 76.059, "end": 76.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 76.199, "end": 76.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tempo...", "start": 76.239, "end": 76.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 76.819, "end": 76.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Ci", "start": 76.819, "end": 76.879, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 76.879, "end": 76.879, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ho", "start": 76.879, "end": 76.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 76.979, "end": 76.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "già", "start": 76.979, "end": 77.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 77.139, "end": 77.139, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "pensato.", "start": 77.139, "end": 77.719, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 77.719, "end": 78.02, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 78.02, "end": 78.32, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 78.32, "end": 78.36, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mese", "start": 78.36, "end": 78.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 78.559, "end": 78.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "a", "start": 78.559, "end": 78.619, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 78.619, "end": 78.619, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Palermo", "start": 78.619, "end": 78.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 78.979, "end": 78.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "non", "start": 78.979, "end": 79.1, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 79.1, "end": 79.1, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "le", "start": 79.1, "end": 79.219, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 79.219, "end": 79.219, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "far<PERSON>", "start": 79.219, "end": 79.4, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 79.4, "end": 79.419, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "male.", "start": 79.419, "end": 79.78, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 79.78, "end": 81.78, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Ne", "start": 81.78, "end": 81.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 81.919, "end": 81.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sono", "start": 81.979, "end": 82.28, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 82.28, "end": 82.36, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "certa.", "start": 82.36, "end": 83.04, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 83.04, "end": 83.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>", "start": 83.659, "end": 83.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 83.959, "end": 83.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "e", "start": 83.959, "end": 84.039, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 84.039, "end": 84.039, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "quel", "start": 84.039, "end": 84.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 84.219, "end": 84.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "pescatore", "start": 84.219, "end": 84.719, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 84.719, "end": 84.719, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "insieme", "start": 84.719, "end": 85.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 85.139, "end": 85.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "non", "start": 85.159, "end": 85.279, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 85.279, "end": 85.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 85.299, "end": 85.719, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 85.719, "end": 85.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "crea<PERSON>i", "start": 85.739, "end": 86.18, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 86.18, "end": 86.18, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 86.18, "end": 86.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 86.299, "end": 86.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "dei", "start": 86.299, "end": 86.439, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 86.439, "end": 86.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>,", "start": 86.479, "end": 87.079, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 87.079, "end": 87.079, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "credimi.", "start": 87.079, "end": 87.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 87.759, "end": 97.579, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>.", "start": 97.579, "end": 98.12, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 98.12, "end": 99.2, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Sì,", "start": 99.2, "end": 99.499, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 99.499, "end": 99.499, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "papà.", "start": 99.499, "end": 99.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 99.919, "end": 100.499, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Prepara", "start": 100.499, "end": 100.839, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 100.839, "end": 100.839, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "le", "start": 100.839, "end": 100.92, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 100.92, "end": 100.939, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "valigie", "start": 100.939, "end": 101.22, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 101.22, "end": 101.22, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "per", "start": 101.22, "end": 101.319, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 101.319, "end": 101.36, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "qualche", "start": 101.36, "end": 101.58, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 101.58, "end": 101.619, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>ior<PERSON>", "start": 101.619, "end": 101.779, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 101.779, "end": 101.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 101.799, "end": 101.88, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 101.88, "end": 101.899, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "assenza,", "start": 101.899, "end": 102.279, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 102.279, "end": 102.319, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ti", "start": 102.319, "end": 102.439, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 102.439, "end": 102.439, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "condurr<PERSON>", "start": 102.439, "end": 102.819, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 102.819, "end": 102.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "a", "start": 102.819, "end": 102.9, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 102.9, "end": 102.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Palermo.", "start": 102.919, "end": 103.379, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 103.379, "end": 104.18, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "A", "start": 104.18, "end": 104.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 104.299, "end": 104.339, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Palermo?", "start": 104.339, "end": 104.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 104.819, "end": 104.86, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "E", "start": 104.86, "end": 104.999, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 104.999, "end": 104.999, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "perché?", "start": 104.999, "end": 105.439, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 105.439, "end": 105.86, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>", "start": 105.86, "end": 105.999, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 105.999, "end": 105.999, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "già", "start": 105.999, "end": 106.119, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 106.119, "end": 106.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "preso", "start": 106.119, "end": 106.339, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 106.339, "end": 106.36, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "i", "start": 106.36, "end": 106.42, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 106.42, "end": 106.439, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>,", "start": 106.439, "end": 106.819, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 106.819, "end": 106.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "si", "start": 106.819, "end": 106.959, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 106.959, "end": 106.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "parte", "start": 106.979, "end": 107.18, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 107.18, "end": 107.18, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "alle", "start": 107.18, "end": 107.319, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 107.319, "end": 107.319, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "otto.", "start": 107.319, "end": 107.64, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 107.64, "end": 109.04, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Non", "start": 109.04, "end": 109.22, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 109.22, "end": 109.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "posso", "start": 109.299, "end": 109.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 109.559, "end": 109.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "andare,", "start": 109.559, "end": 109.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 109.959, "end": 109.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "papà.", "start": 109.959, "end": 110.359, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 110.359, "end": 111.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Per", "start": 111.299, "end": 111.459, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 111.459, "end": 111.52, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "non", "start": 111.52, "end": 111.659, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 111.659, "end": 111.659, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "abbandonare", "start": 111.659, "end": 112.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 112.159, "end": 112.159, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "il", "start": 112.159, "end": 112.2, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 112.2, "end": 112.2, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "rosario,", "start": 112.2, "end": 112.619, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 112.619, "end": 112.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "forse?", "start": 112.639, "end": 113.019, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 113.019, "end": 113.579, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Quell'uomo", "start": 113.579, "end": 114.02, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 114.02, "end": 114.02, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mi", "start": 114.02, "end": 114.119, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 114.119, "end": 114.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "è", "start": 114.119, "end": 114.2, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 114.2, "end": 114.2, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "già", "start": 114.2, "end": 114.279, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 114.279, "end": 114.279, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "stancato", "start": 114.279, "end": 114.639, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 114.639, "end": 114.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "a<PERSON><PERSON><PERSON>.", "start": 114.639, "end": 115.3, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 115.3, "end": 115.36, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Non", "start": 115.36, "end": 115.499, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 115.499, "end": 115.499, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "voglio", "start": 115.499, "end": 115.74, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 115.74, "end": 115.759, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "diventare", "start": 115.759, "end": 116.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 116.139, "end": 116.159, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "la", "start": 116.159, "end": 116.259, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 116.259, "end": 116.259, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "favola", "start": 116.259, "end": 116.52, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 116.52, "end": 116.52, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "dell'isola,", "start": 116.52, "end": 116.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 116.979, "end": 116.999, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "io.", "start": 116.999, "end": 117.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 117.159, "end": 117.859, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Tu", "start": 117.859, "end": 117.999, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 117.999, "end": 117.999, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "verrai", "start": 117.999, "end": 118.319, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 118.319, "end": 118.319, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "a", "start": 118.319, "end": 118.379, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 118.379, "end": 118.399, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Palermo", "start": 118.399, "end": 118.719, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 118.719, "end": 118.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "in", "start": 118.739, "end": 118.819, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 118.819, "end": 118.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "collegio", "start": 118.819, "end": 119.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 119.139, "end": 119.139, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "e", "start": 119.139, "end": 119.199, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 119.199, "end": 119.199, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ci", "start": 119.199, "end": 119.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 119.299, "end": 119.319, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 119.319, "end": 119.619, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 119.619, "end": 119.619, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "fino", "start": 119.619, "end": 119.759, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 119.759, "end": 119.759, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "a", "start": 119.759, "end": 119.839, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 119.839, "end": 119.839, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "quando", "start": 119.839, "end": 120.039, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 120.039, "end": 120.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "non", "start": 120.059, "end": 120.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 120.139, "end": 120.139, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "avrai", "start": 120.139, "end": 120.379, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 120.379, "end": 120.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "dimenticato", "start": 120.379, "end": 121.019, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 121.019, "end": 121.219, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "questa", "start": 121.219, "end": 121.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 121.479, "end": 121.499, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "stupida", "start": 121.499, "end": 121.859, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 121.859, "end": 121.859, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "storia.", "start": 121.859, "end": 122.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 122.299, "end": 122.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Potrai", "start": 122.819, "end": 123.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 123.199, "end": 123.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 123.219, "end": 123.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 123.819, "end": 123.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "in", "start": 123.819, "end": 123.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 123.959, "end": 123.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "città,", "start": 123.979, "end": 124.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 124.459, "end": 124.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>,", "start": 124.619, "end": 125.519, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 125.519, "end": 125.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "cinematografi...", "start": 125.699, "end": 127.059, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 127.059, "end": 127.079, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>,", "start": 127.079, "end": 127.539, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 127.539, "end": 128.039, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "che", "start": 128.039, "end": 128.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 128.139, "end": 128.159, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "in", "start": 128.159, "end": 128.259, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 128.259, "end": 128.279, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "fondo", "start": 128.279, "end": 128.539, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 128.539, "end": 128.539, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ha", "start": 128.539, "end": 128.659, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 128.659, "end": 128.699, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "bisogno", "start": 128.699, "end": 129.039, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 129.039, "end": 129.039, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 129.039, "end": 129.179, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 129.179, "end": 129.199, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "cambiare,", "start": 129.199, "end": 129.739, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 129.739, "end": 130.079, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 130.079, "end": 130.179, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 130.179, "end": 130.179, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "conoscere", "start": 130.179, "end": 130.659, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 130.659, "end": 130.679, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "gente", "start": 130.679, "end": 131.019, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 131.019, "end": 131.019, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "come", "start": 131.019, "end": 131.219, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 131.219, "end": 131.239, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "si", "start": 131.239, "end": 131.319, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 131.319, "end": 131.339, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "deve.", "start": 131.339, "end": 131.699, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 131.699, "end": 132.259, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Come", "start": 132.259, "end": 132.499, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 132.499, "end": 132.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "quella", "start": 132.539, "end": 132.739, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 132.739, "end": 132.759, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 132.759, "end": 132.839, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 132.839, "end": 132.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "frequenta", "start": 132.879, "end": 133.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 133.319, "end": 133.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "questa", "start": 133.319, "end": 133.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 133.559, "end": 133.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "casa,", "start": 133.599, "end": 133.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 133.959, "end": 133.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "forse?", "start": 133.959, "end": 134.419, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 134.419, "end": 135.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Che", "start": 135.299, "end": 135.439, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 135.439, "end": 135.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "cosa", "start": 135.479, "end": 135.659, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 135.659, "end": 135.659, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "vuoi", "start": 135.659, "end": 135.819, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 135.819, "end": 135.859, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "dire?", "start": 135.859, "end": 136.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 136.139, "end": 136.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "È", "start": 136.739, "end": 136.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 136.899, "end": 136.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "per", "start": 136.959, "end": 137.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 137.139, "end": 137.199, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "me", "start": 137.199, "end": 137.359, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 137.359, "end": 137.379, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 137.379, "end": 137.519, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 137.519, "end": 137.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "lo", "start": 137.539, "end": 137.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 137.619, "end": 137.679, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "dice.", "start": 137.679, "end": 138.099, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 138.099, "end": 140.859, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 140.859, "end": 141.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 141.139, "end": 141.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "figlia", "start": 141.159, "end": 141.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 141.459, "end": 141.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "mi", "start": 141.479, "end": 141.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 141.579, "end": 141.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "guarda", "start": 141.619, "end": 141.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 141.919, "end": 141.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "da<PERSON><PERSON><PERSON>", "start": 141.959, "end": 142.359, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 142.359, "end": 142.419, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "con", "start": 142.419, "end": 142.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 142.559, "end": 142.639, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "molta", "start": 142.639, "end": 142.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 142.959, "end": 143.039, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "simpatia.", "start": 143.039, "end": 143.839, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 143.839, "end": 144.719, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>", "start": 144.719, "end": 144.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 144.799, "end": 144.859, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "meraviglio", "start": 144.859, "end": 145.359, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 145.359, "end": 145.379, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 145.379, "end": 145.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 145.479, "end": 145.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "con", "start": 145.559, "end": 145.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 145.659, "end": 145.679, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Margh<PERSON><PERSON>", "start": 145.679, "end": 146.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 146.199, "end": 146.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "non", "start": 146.219, "end": 146.339, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 146.339, "end": 146.379, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "si", "start": 146.379, "end": 146.499, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 146.499, "end": 146.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "siano", "start": 146.559, "end": 146.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 146.779, "end": 146.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "accordate", "start": 146.779, "end": 147.379, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 147.379, "end": 147.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "per", "start": 147.459, "end": 147.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 147.619, "end": 147.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.", "start": 147.619, "end": 148.499, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 148.499, "end": 148.899, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Non", "start": 148.899, "end": 149.039, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 149.039, "end": 149.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "è", "start": 149.059, "end": 149.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 149.119, "end": 149.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "vero,", "start": 149.159, "end": 149.419, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 149.419, "end": 149.439, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>?", "start": 149.439, "end": 149.879, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 149.879, "end": 154.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Non", "start": 154.599, "end": 154.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 154.759, "end": 154.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "preoccuparti,", "start": 154.819, "end": 155.739, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 155.739, "end": 155.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>.", "start": 155.739, "end": 156.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 156.219, "end": 156.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>a", "start": 156.619, "end": 156.839, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 156.839, "end": 156.859, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ragazza", "start": 156.859, "end": 157.259, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 157.259, "end": 157.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "far<PERSON>", "start": 157.299, "end": 157.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 157.559, "end": 157.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "bene,", "start": 157.599, "end": 157.939, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 157.939, "end": 157.939, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "vedrai.", "start": 157.939, "end": 158.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 158.479, "end": 158.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "E", "start": 158.819, "end": 158.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 158.919, "end": 159.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "poi", "start": 159.019, "end": 159.259, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 159.259, "end": 159.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "quando", "start": 159.299, "end": 159.599, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 159.599, "end": 159.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ritornerai", "start": 159.619, "end": 160.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 160.159, "end": 160.179, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "da", "start": 160.179, "end": 160.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 160.299, "end": 160.359, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Palermo,", "start": 160.359, "end": 160.999, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 160.999, "end": 161.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "saremo", "start": 161.599, "end": 161.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 161.979, "end": 162.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "più", "start": 162.019, "end": 162.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 162.299, "end": 162.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "soli.", "start": 162.319, "end": 162.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 162.819, "end": 164.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Così", "start": 164.599, "end": 164.879, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 164.879, "end": 164.939, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "voi", "start": 164.939, "end": 165.179, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 165.179, "end": 165.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "partiste", "start": 165.299, "end": 165.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 165.799, "end": 165.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "per", "start": 165.799, "end": 165.939, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 165.939, "end": 165.999, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Palermo", "start": 165.999, "end": 166.399, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 166.399, "end": 166.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "e", "start": 166.739, "end": 166.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 166.919, "end": 167.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "vostra", "start": 167.019, "end": 167.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 167.319, "end": 167.379, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "moglie", "start": 167.379, "end": 167.679, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 167.679, "end": 167.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "rimase", "start": 167.799, "end": 168.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 168.299, "end": 168.419, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sola.", "start": 168.419, "end": 168.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 168.819, "end": 169.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Per", "start": 169.559, "end": 169.719, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 169.719, "end": 169.759, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "pochi", "start": 169.759, "end": 170.019, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 170.019, "end": 170.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>ior<PERSON>", "start": 170.059, "end": 170.359, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 170.359, "end": 170.399, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "per<PERSON>.", "start": 170.399, "end": 170.739, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 170.739, "end": 171.359, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Vi", "start": 171.359, "end": 171.519, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 171.519, "end": 171.539, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sembre<PERSON><PERSON>", "start": 171.539, "end": 171.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 171.979, "end": 171.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "impossibile,", "start": 171.979, "end": 172.639, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 172.639, "end": 172.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ma", "start": 172.639, "end": 172.859, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 172.859, "end": 174.199, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ogni", "start": 174.199, "end": 174.439, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 174.439, "end": 174.499, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "minuto", "start": 174.499, "end": 174.819, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 174.819, "end": 174.839, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "lontano", "start": 174.839, "end": 175.199, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 175.199, "end": 175.219, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "da", "start": 175.219, "end": 175.339, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 175.339, "end": 175.339, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "lei", "start": 175.339, "end": 175.599, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 175.599, "end": 175.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "era", "start": 175.799, "end": 176.039, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 176.039, "end": 176.079, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "per", "start": 176.079, "end": 176.279, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 176.279, "end": 176.339, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "me", "start": 176.339, "end": 176.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 176.479, "end": 176.519, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "peggio", "start": 176.519, "end": 176.859, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 176.859, "end": 176.859, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 176.859, "end": 176.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 176.979, "end": 176.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "una", "start": 176.979, "end": 177.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 177.159, "end": 177.179, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "coltellata.", "start": 177.179, "end": 177.839, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 177.839, "end": 178.579, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "La", "start": 178.579, "end": 178.699, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 178.699, "end": 178.699, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "trovai", "start": 178.699, "end": 179.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 179.139, "end": 179.279, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "nervosa,", "start": 179.279, "end": 179.859, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 179.859, "end": 179.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mi", "start": 179.919, "end": 180.079, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 180.079, "end": 180.099, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "fece", "start": 180.099, "end": 180.319, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 180.319, "end": 180.359, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "uno", "start": 180.359, "end": 180.519, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 180.519, "end": 180.539, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "strano", "start": 180.539, "end": 180.839, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 180.839, "end": 180.839, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "effetto.", "start": 180.839, "end": 181.439, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 181.439, "end": 181.659, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "E", "start": 181.659, "end": 181.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 181.819, "end": 181.839, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "non", "start": 181.839, "end": 181.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 181.959, "end": 181.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "vi", "start": 181.979, "end": 182.079, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 182.079, "end": 182.079, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "domandaste", "start": 182.079, "end": 182.639, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 182.639, "end": 182.679, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "il", "start": 182.679, "end": 182.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 182.799, "end": 182.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "perché", "start": 182.879, "end": 183.339, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 183.339, "end": 183.379, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 183.379, "end": 183.539, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 183.539, "end": 183.579, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "questo", "start": 183.579, "end": 183.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 183.819, "end": 183.839, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "cambiamento?", "start": 183.839, "end": 184.499, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 184.499, "end": 185.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "No.", "start": 185.139, "end": 185.499, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 185.499, "end": 186.079, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Credevo", "start": 186.079, "end": 186.499, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 186.499, "end": 186.519, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "che", "start": 186.519, "end": 186.619, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 186.619, "end": 186.659, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "fosse", "start": 186.659, "end": 186.939, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 186.939, "end": 186.939, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "per", "start": 186.939, "end": 187.059, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 187.059, "end": 187.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "quanto", "start": 187.119, "end": 187.359, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 187.359, "end": 187.359, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "era", "start": 187.359, "end": 187.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 187.559, "end": 187.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "successo", "start": 187.559, "end": 187.999, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 187.999, "end": 188.019, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "nell'isola.", "start": 188.019, "end": 188.659, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 188.659, "end": 189.239, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Il", "start": 189.239, "end": 189.359, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 189.359, "end": 189.419, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "suicidio", "start": 189.419, "end": 189.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 189.979, "end": 190.079, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 190.079, "end": 190.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 190.159, "end": 190.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON>?", "start": 190.219, "end": 190.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 190.779, "end": 190.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Sì.", "start": 190.959, "end": 191.279, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 191.279, "end": 192.019, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "La", "start": 192.019, "end": 192.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 192.139, "end": 192.159, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sorella", "start": 192.159, "end": 192.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 192.479, "end": 192.499, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 192.499, "end": 192.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 192.559, "end": 192.599, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Rosario", "start": 192.599, "end": 192.959, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 192.959, "end": 192.959, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "era", "start": 192.959, "end": 193.119, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 193.119, "end": 193.139, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "stata", "start": 193.139, "end": 193.379, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 193.379, "end": 193.439, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "trovata", "start": 193.439, "end": 193.759, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 193.759, "end": 193.759, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "impiccata.", "start": 193.759, "end": 194.439, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 194.439, "end": 195.659, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "L'aveva", "start": 195.659, "end": 196.079, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 196.079, "end": 196.099, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "fatto", "start": 196.099, "end": 196.419, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 196.419, "end": 196.419, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "per", "start": 196.419, "end": 196.579, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 196.579, "end": 196.579, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "amore,", "start": 196.579, "end": 197.099, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 197.099, "end": 197.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "per", "start": 197.559, "end": 197.759, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 197.759, "end": 197.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "quel", "start": 197.799, "end": 198.039, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 198.039, "end": 198.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "disgraziato", "start": 198.059, "end": 198.819, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 198.819, "end": 198.839, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 198.839, "end": 198.939, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 198.939, "end": 198.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>.", "start": 198.979, "end": 199.679, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 199.679, "end": 201.039, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 201.039, "end": 201.419, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 201.419, "end": 201.439, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "vero", "start": 201.439, "end": 201.699, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 201.699, "end": 201.699, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "che", "start": 201.699, "end": 201.819, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 201.819, "end": 201.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "le", "start": 201.819, "end": 201.919, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 201.919, "end": 201.959, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "donne", "start": 201.959, "end": 202.279, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 202.279, "end": 202.519, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "si", "start": 202.519, "end": 202.639, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 202.639, "end": 202.719, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "perdono", "start": 202.719, "end": 203.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 203.159, "end": 203.199, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "per", "start": 203.199, "end": 203.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 203.299, "end": 203.319, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "i", "start": 203.319, "end": 203.399, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 203.399, "end": 203.399, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>.", "start": 203.399, "end": 204.059, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 204.059, "end": 205.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Di", "start": 205.379, "end": 205.499, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 205.499, "end": 205.519, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "quel", "start": 205.519, "end": 205.679, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 205.679, "end": 205.679, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "suicidio", "start": 205.679, "end": 206.319, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 206.319, "end": 206.339, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ne", "start": 206.339, "end": 206.459, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 206.459, "end": 206.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 206.479, "end": 206.959, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 206.959, "end": 206.999, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "tutti", "start": 206.999, "end": 207.339, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 207.339, "end": 207.419, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "nell'isola.", "start": 207.419, "end": 208.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 208.159, "end": 211.599, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Il", "start": 211.599, "end": 211.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 211.799, "end": 211.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "negro", "start": 211.819, "end": 212.099, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 212.099, "end": 212.099, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "l'ha", "start": 212.099, "end": 212.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 212.219, "end": 212.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "vista", "start": 212.219, "end": 212.439, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 212.439, "end": 212.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "co<PERSON>e", "start": 212.479, "end": 212.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 212.779, "end": 212.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "per", "start": 212.779, "end": 212.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 212.899, "end": 212.899, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "la", "start": 212.899, "end": 212.999, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 212.999, "end": 212.999, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sco<PERSON><PERSON>", "start": 212.999, "end": 213.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 213.479, "end": 213.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "e", "start": 213.559, "end": 213.639, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 213.639, "end": 213.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "poi", "start": 213.659, "end": 213.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 213.819, "end": 213.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "l'hanno", "start": 213.819, "end": 214.039, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 214.039, "end": 214.079, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "trovata", "start": 214.079, "end": 214.339, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 214.339, "end": 214.339, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "impiccata", "start": 214.339, "end": 214.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 214.779, "end": 214.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "nel", "start": 214.799, "end": 214.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 214.959, "end": 214.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "fondo", "start": 214.979, "end": 215.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 215.199, "end": 215.199, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 215.199, "end": 215.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 215.299, "end": 215.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Padron", "start": 215.299, "end": 215.599, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 215.599, "end": 215.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Grava.", "start": 215.619, "end": 215.999, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 215.999, "end": 216.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Povero", "start": 216.299, "end": 216.639, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 216.639, "end": 216.659, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Rosario.", "start": 216.659, "end": 217.279, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 217.279, "end": 217.399, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Chissà", "start": 217.399, "end": 217.699, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 217.699, "end": 217.739, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "che", "start": 217.739, "end": 217.879, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 217.879, "end": 217.879, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "colpo", "start": 217.879, "end": 218.119, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 218.119, "end": 218.139, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "terribile", "start": 218.139, "end": 218.599, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 218.599, "end": 218.599, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "sarà", "start": 218.599, "end": 218.819, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 218.819, "end": 218.839, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "per", "start": 218.839, "end": 218.979, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 218.979, "end": 218.999, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "lui", "start": 218.999, "end": 219.159, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 219.159, "end": 219.159, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "quando", "start": 219.159, "end": 219.399, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 219.399, "end": 219.439, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "tornerà", "start": 219.439, "end": 219.779, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 219.779, "end": 219.819, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "da", "start": 219.819, "end": 219.959, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 219.959, "end": 219.959, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Palermo.", "start": 219.959, "end": 220.459, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 220.459, "end": 220.579, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Certo", "start": 220.579, "end": 220.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 220.859, "end": 220.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "non", "start": 220.879, "end": 220.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 220.979, "end": 220.999, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 220.999, "end": 221.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 221.239, "end": 221.279, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "troppo", "start": 221.279, "end": 221.539, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 221.539, "end": 221.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "liscia.", "start": 221.559, "end": 221.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 221.959, "end": 222.639, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Salute,", "start": 222.639, "end": 222.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 222.979, "end": 222.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "don", "start": 222.979, "end": 223.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 223.119, "end": 223.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>.", "start": 223.139, "end": 223.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 223.579, "end": 225.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Salute.", "start": 225.059, "end": 225.459, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 225.459, "end": 225.459, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Salute.", "start": 225.459, "end": 225.599, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 225.599, "end": 227.899, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>", "start": 227.899, "end": 228.179, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 228.179, "end": 228.179, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "un", "start": 228.179, "end": 228.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 228.299, "end": 228.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "anice.", "start": 228.299, "end": 228.859, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 228.859, "end": 231.699, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Scusate,", "start": 231.699, "end": 232.259, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 232.259, "end": 232.259, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "don", "start": 232.259, "end": 232.379, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 232.379, "end": 232.419, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON>.", "start": 232.419, "end": 232.759, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 232.759, "end": 232.759, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Che", "start": 232.759, "end": 232.879, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 232.879, "end": 232.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "c'è?", "start": 232.979, "end": 233.259, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 233.259, "end": 233.259, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "È", "start": 233.259, "end": 233.279, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 233.279, "end": 233.999, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "per", "start": 233.999, "end": 234.239, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 234.239, "end": 234.239, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "il", "start": 234.239, "end": 234.359, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 234.359, "end": 234.419, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>,", "start": 234.419, "end": 234.919, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 234.919, "end": 234.939, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "sapete?", "start": 234.939, "end": 235.459, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 235.459, "end": 235.899, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Che", "start": 235.899, "end": 236.039, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 236.039, "end": 236.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "battel<PERSON>?", "start": 236.059, "end": 236.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 236.559, "end": 237.099, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Il", "start": 237.099, "end": 237.259, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 237.259, "end": 237.319, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Fortuna", "start": 237.319, "end": 237.659, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 237.659, "end": 237.679, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "II,", "start": 237.679, "end": 238.039, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 238.039, "end": 238.499, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "quello", "start": 238.499, "end": 238.739, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 238.739, "end": 238.759, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "che", "start": 238.759, "end": 238.839, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 238.839, "end": 238.879, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "mi", "start": 238.879, "end": 238.959, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 238.959, "end": 238.979, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "avete", "start": 238.979, "end": 239.159, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 239.159, "end": 239.159, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "affittato", "start": 239.159, "end": 239.579, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 239.579, "end": 239.639, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "voi.", "start": 239.639, "end": 240.119, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 240.119, "end": 240.359, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Beh?", "start": 240.359, "end": 240.679, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 240.679, "end": 241.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 241.299, "end": 241.619, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 241.619, "end": 241.619, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "do<PERSON><PERSON>", "start": 241.619, "end": 241.899, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 241.899, "end": 241.919, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "pagare.", "start": 241.919, "end": 242.399, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 242.399, "end": 242.539, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "E", "start": 242.539, "end": 242.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 242.559, "end": 242.659, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "tu", "start": 242.659, "end": 242.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 242.979, "end": 242.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "pagami.", "start": 242.979, "end": 243.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 243.479, "end": 243.519, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Ma", "start": 243.519, "end": 244.259, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 244.259, "end": 244.279, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "non", "start": 244.279, "end": 244.419, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 244.419, "end": 244.419, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "ho", "start": 244.419, "end": 244.519, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 244.519, "end": 244.519, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "i", "start": 244.519, "end": 244.579, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 244.579, "end": 244.639, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "soldi.", "start": 244.639, "end": 245.139, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 245.139, "end": 246.199, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Be'", "start": 246.199, "end": 246.459, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 246.459, "end": 246.519, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "adesso", "start": 246.519, "end": 246.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 246.979, "end": 247.659, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "non", "start": 247.659, "end": 247.819, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 247.819, "end": 247.839, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "è", "start": 247.839, "end": 247.859, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 247.859, "end": 247.859, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "il", "start": 247.859, "end": 247.959, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 247.959, "end": 247.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "caso", "start": 247.979, "end": 248.239, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 248.239, "end": 248.239, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 248.239, "end": 248.359, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 248.359, "end": 248.359, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "parlare", "start": 248.359, "end": 248.719, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 248.719, "end": 248.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 248.739, "end": 248.859, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 248.859, "end": 248.879, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "questo.", "start": 248.879, "end": 249.279, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 249.279, "end": 249.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Me", "start": 249.979, "end": 250.119, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 250.119, "end": 250.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "li", "start": 250.119, "end": 250.219, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 250.219, "end": 250.219, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "darai", "start": 250.219, "end": 250.759, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 250.759, "end": 250.839, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "quando", "start": 250.839, "end": 251.219, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 251.219, "end": 251.239, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "potrai.", "start": 251.239, "end": 251.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 251.799, "end": 252.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>raz<PERSON>", "start": 252.559, "end": 252.919, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 252.919, "end": 252.939, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "don", "start": 252.939, "end": 253.059, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 253.059, "end": 253.099, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON>,", "start": 253.099, "end": 253.499, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 253.499, "end": 253.539, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "grazie.", "start": 253.539, "end": 253.919, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 253.919, "end": 254.399, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Avete", "start": 254.399, "end": 254.679, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 254.679, "end": 254.699, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "sentito", "start": 254.699, "end": 255.019, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 255.019, "end": 255.039, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "don", "start": 255.039, "end": 255.179, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 255.179, "end": 255.199, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON>?", "start": 255.199, "end": 255.599, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 255.599, "end": 255.899, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Già,", "start": 255.899, "end": 256.399, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 256.399, "end": 257.359, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "povera", "start": 257.359, "end": 257.739, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 257.739, "end": 257.759, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ragazza.", "start": 257.759, "end": 258.259, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 258.259, "end": 258.939, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "E", "start": 258.939, "end": 259.119, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 259.119, "end": 259.139, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "non", "start": 259.139, "end": 259.259, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 259.259, "end": 259.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "si", "start": 259.299, "end": 259.399, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 259.399, "end": 259.399, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "è", "start": 259.399, "end": 259.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 259.479, "end": 259.499, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "saputo", "start": 259.499, "end": 259.899, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 259.899, "end": 259.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "perché", "start": 259.919, "end": 260.179, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 260.179, "end": 260.179, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "la", "start": 260.179, "end": 260.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 260.299, "end": 260.319, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "fa.", "start": 260.319, "end": 260.599, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 260.599, "end": 260.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Dicono", "start": 260.639, "end": 261.019, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 261.019, "end": 261.039, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "che", "start": 261.039, "end": 261.139, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 261.139, "end": 261.159, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "verranno", "start": 261.159, "end": 261.539, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 261.539, "end": 261.539, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "la", "start": 261.539, "end": 261.639, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 261.639, "end": 261.659, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "polizia", "start": 261.659, "end": 262.119, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 262.119, "end": 262.139, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "e", "start": 262.139, "end": 262.199, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 262.199, "end": 262.219, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "il", "start": 262.219, "end": 262.279, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 262.279, "end": 262.319, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "medico", "start": 262.319, "end": 262.619, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 262.619, "end": 262.639, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "legale.", "start": 262.639, "end": 263.099, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 263.099, "end": 263.859, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON>", "start": 263.859, "end": 263.999, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 263.999, "end": 264.019, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "saputo", "start": 264.019, "end": 264.299, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 264.299, "end": 264.299, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "che", "start": 264.299, "end": 264.399, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 264.399, "end": 264.419, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "hanno", "start": 264.419, "end": 264.559, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 264.559, "end": 264.579, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "interrogato", "start": 264.579, "end": 265.079, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 265.079, "end": 265.079, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "il", "start": 265.079, "end": 265.159, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 265.159, "end": 265.199, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "negro,", "start": 265.199, "end": 265.539, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 265.539, "end": 265.539, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "ma", "start": 265.539, "end": 265.659, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 265.659, "end": 265.679, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "da", "start": 265.679, "end": 265.759, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 265.759, "end": 265.779, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "lui", "start": 265.779, "end": 265.919, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 265.919, "end": 265.959, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "pu<PERSON><PERSON><PERSON>", "start": 265.959, "end": 266.419, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 266.419, "end": 266.419, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "sono", "start": 266.419, "end": 266.659, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 266.659, "end": 266.679, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 266.679, "end": 267.119, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 267.119, "end": 267.119, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "a", "start": 267.119, "end": 267.199, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 267.199, "end": 267.199, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "cavar", "start": 267.199, "end": 267.479, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 267.479, "end": 267.499, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "fuori", "start": 267.499, "end": 267.779, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 267.779, "end": 267.819, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "ben", "start": 267.819, "end": 268.059, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 268.059, "end": 268.059, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "poco.", "start": 268.059, "end": 268.439, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 268.439, "end": 268.439, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "È", "start": 268.439, "end": 269.339, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 269.339, "end": 269.679, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "una", "start": 269.679, "end": 269.899, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 269.899, "end": 269.939, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "brutta", "start": 269.939, "end": 270.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 270.299, "end": 270.339, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "storia.", "start": 270.339, "end": 270.919, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 270.919, "end": 273.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 273.739, "end": 274.099, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 274.099, "end": 274.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "stato", "start": 274.119, "end": 274.379, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 274.379, "end": 274.399, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "meglio", "start": 274.399, "end": 274.659, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 274.659, "end": 274.659, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "che", "start": 274.659, "end": 274.759, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 274.759, "end": 274.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "foste", "start": 274.799, "end": 275.039, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 275.039, "end": 275.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "venuto", "start": 275.059, "end": 275.339, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 275.339, "end": 275.339, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "allora.", "start": 275.339, "end": 275.759, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 275.759, "end": 276.079, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "E", "start": 276.079, "end": 276.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 276.239, "end": 276.259, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "siete", "start": 276.259, "end": 276.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 276.579, "end": 276.579, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "proprio", "start": 276.579, "end": 276.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 276.959, "end": 276.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sicuro?", "start": 276.959, "end": 277.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 277.459, "end": 277.679, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Da", "start": 277.679, "end": 277.839, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 277.839, "end": 277.859, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "quel", "start": 277.859, "end": 277.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 277.979, "end": 277.999, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "momento", "start": 277.999, "end": 278.319, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 278.319, "end": 278.339, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "le", "start": 278.339, "end": 278.459, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 278.459, "end": 278.459, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "cose", "start": 278.459, "end": 278.699, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 278.699, "end": 278.719, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "precipitarono.", "start": 278.719, "end": 279.519, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 279.519, "end": 280.579, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Voi", "start": 280.579, "end": 280.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 280.799, "end": 280.839, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "siete", "start": 280.839, "end": 281.019, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 281.019, "end": 281.039, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "un", "start": 281.039, "end": 281.119, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 281.119, "end": 281.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "poliziotto", "start": 281.119, "end": 281.599, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 281.599, "end": 281.599, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "e", "start": 281.599, "end": 281.639, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 281.639, "end": 281.659, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "vi", "start": 281.659, "end": 281.759, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 281.759, "end": 281.779, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "piace", "start": 281.779, "end": 282.019, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 282.019, "end": 282.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sapere", "start": 282.059, "end": 282.359, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 282.359, "end": 282.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "i", "start": 282.379, "end": 282.459, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 282.459, "end": 282.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "fatti", "start": 282.479, "end": 282.699, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 282.699, "end": 282.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "nell'or<PERSON>.", "start": 282.739, "end": 283.519, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 283.519, "end": 284.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "E", "start": 284.379, "end": 284.499, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 284.499, "end": 284.519, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "invece", "start": 284.519, "end": 284.859, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 284.859, "end": 284.899, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "tutto", "start": 284.899, "end": 285.119, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 285.119, "end": 285.159, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "fu", "start": 285.159, "end": 285.279, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 285.279, "end": 285.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "così", "start": 285.299, "end": 285.519, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 285.519, "end": 285.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "confuso", "start": 285.559, "end": 286.059, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 286.059, "end": 286.099, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "che...", "start": 286.099, "end": 286.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}]}}, "created_at": 1754319515.7684252}