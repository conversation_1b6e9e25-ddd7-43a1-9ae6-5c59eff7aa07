"""字幕生成服务异常类

定义字幕生成过程中可能出现的各种异常情况，
提供详细的错误信息和错误代码以便于调试和错误处理。
"""

import time
from typing import Dict, Any, Optional


class SubtitleError(Exception):
    """字幕生成基础异常类

    所有字幕生成相关异常的基类，提供统一的异常处理接口和错误信息格式。
    """

    def __init__(self, message: str, error_code: str = "SUBTITLE_ERROR", details: dict = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        self.timestamp = time.time()

    def __str__(self):
        if self.error_code:
            return f"[{self.error_code}] {self.message}"
        return self.message



class LLMServiceError(SubtitleError):
    """LLM服务异常

    当LLM API调用失败时抛出，包括：
    - 没有可用的LLM API配置
    - 所有LLM API调用都失败
    - API响应格式错误或解析失败
    - 网络连接超时或其他通信错误
    """

    def __init__(self, message: str, error_code: str = "LLM_SERVICE_ERROR", details: dict = None):
        super().__init__(message, error_code, details)
