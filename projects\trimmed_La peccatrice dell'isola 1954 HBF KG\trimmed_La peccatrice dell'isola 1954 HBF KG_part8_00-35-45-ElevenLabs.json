{"success": true, "service": "elevenlabs", "transcription_id": "elevenlabs_1754319606", "text": "<PERSON>sso and<PERSON>ene ora? O serve ancora qualcosa? <PERSON>att<PERSON>, è meglio. Sparecchierai domani. La tua faccia non mi è simpatica. Voglio il tuo bacio morbido di seta. <PERSON><PERSON><PERSON>otte a voi. <PERSON><PERSON><PERSON>otte amico, vai da lei? Devo farlo. È scritto: il serpente perse Eva, ma da allora sono le donne che perdono gli uomini, rico<PERSON><PERSON><PERSON>. Non accadrà a noi, voglio solo impedirle di fare altro male. <PERSON><PERSON><PERSON><PERSON>, e buona fortuna. Su avanti, non avere paura. Sono sola. Riconosco che sei stata molto abile. Sei riuscita a sbarazzarti facilmente di padre e figlia in un sol colpo. Ma tu sai bene perché l'ho fatto. So soltanto che sono qui per dirti quello che meriti. Non ti è bastato combinare guai, seminare l'odio fra i pescatori. Adesso hai voluto liberarti anche di Maria. Ma perché sei venuta in quest'isola? Probabilmente perché sentivo che ti avrei incontrato. Romantico, no? Cerca di capirmi bene. Non potrai continuare a farti gioco di noi troppo a lungo. Potrebbero accadere cose molto spiacevoli. In quanto a me, non ti permetterò di torturare ancora Maria. Ma non capisci che ho fatto tutto questo per rimanere sola con te? Perché ti amo. Stai zitta, mi fai schifo. Ho paura. Sì, tu hai paura di me, come tutti gli uomini dell'isola che mi guardano, mi desiderano sotto gli occhi delle loro donne gelose di me, di questo corpo che tu dici disprezzare. O forse preferisci Maria, con quell'aria da ingenua, il viso angelico e l'esperienza fatta in collegio.", "words": [{"text": "Po<PERSON>", "start": 16.739, "end": 17.019, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 17.019, "end": 17.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 17.059, "end": 17.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 17.659, "end": 17.68, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ora?", "start": 17.68, "end": 18.06, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 18.06, "end": 18.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "O", "start": 18.299, "end": 18.379, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 18.379, "end": 18.42, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "serve", "start": 18.42, "end": 18.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 18.699, "end": 18.719, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ancora", "start": 18.719, "end": 19.039, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 19.039, "end": 19.079, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "qualcosa?", "start": 19.079, "end": 19.86, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 19.86, "end": 20.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>,", "start": 20.139, "end": 20.739, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 20.739, "end": 20.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "è", "start": 20.739, "end": 20.76, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 20.76, "end": 20.76, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "meglio.", "start": 20.76, "end": 21.199, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 21.199, "end": 21.699, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Sparecchierai", "start": 21.699, "end": 22.239, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 22.239, "end": 22.26, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "domani.", "start": 22.26, "end": 22.7, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 22.7, "end": 23.219, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "La", "start": 23.219, "end": 23.34, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 23.34, "end": 23.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "tua", "start": 23.379, "end": 23.519, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 23.519, "end": 23.539, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "faccia", "start": 23.539, "end": 23.84, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 23.84, "end": 23.84, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "non", "start": 23.84, "end": 23.94, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 23.94, "end": 23.959, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mi", "start": 23.959, "end": 24.04, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 24.04, "end": 24.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "è", "start": 24.059, "end": 24.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 24.139, "end": 24.159, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "simpatica.", "start": 24.159, "end": 25.02, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 25.02, "end": 102.239, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 102.239, "end": 102.699, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 102.699, "end": 102.72, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "il", "start": 102.72, "end": 102.839, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 102.839, "end": 102.899, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "tuo", "start": 102.899, "end": 103.079, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 103.079, "end": 103.119, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "bacio", "start": 103.119, "end": 103.54, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 103.54, "end": 103.559, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "morbido", "start": 103.559, "end": 104.599, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 104.599, "end": 104.68, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "di", "start": 104.68, "end": 104.859, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 104.859, "end": 104.919, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "seta.", "start": 104.919, "end": 107.539, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 107.539, "end": 134.52, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 134.52, "end": 135.0, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 135.0, "end": 135.02, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "a", "start": 135.02, "end": 135.08, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 135.08, "end": 135.099, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "voi.", "start": 135.099, "end": 135.379, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 135.379, "end": 135.839, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 135.839, "end": 136.459, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 136.459, "end": 136.459, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "amico,", "start": 136.459, "end": 136.9, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 136.9, "end": 137.039, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "vai", "start": 137.039, "end": 137.199, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 137.199, "end": 137.22, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "da", "start": 137.22, "end": 137.319, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 137.319, "end": 137.339, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "lei?", "start": 137.339, "end": 137.659, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 137.659, "end": 137.919, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "Devo", "start": 137.919, "end": 138.24, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 138.24, "end": 138.279, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "farlo.", "start": 138.279, "end": 138.719, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 138.719, "end": 139.58, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "È", "start": 139.58, "end": 139.72, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 139.72, "end": 139.739, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "scritto:", "start": 139.739, "end": 140.18, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 140.18, "end": 140.199, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "il", "start": 140.199, "end": 140.259, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 140.259, "end": 140.319, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "serpente", "start": 140.319, "end": 140.859, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 140.859, "end": 140.86, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "perse", "start": 140.86, "end": 141.22, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 141.22, "end": 141.22, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "Eva,", "start": 141.22, "end": 141.559, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 141.559, "end": 141.839, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "ma", "start": 141.839, "end": 141.959, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 141.959, "end": 141.979, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "da", "start": 141.979, "end": 142.099, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 142.099, "end": 142.099, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "allora", "start": 142.099, "end": 142.339, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 142.339, "end": 142.36, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "sono", "start": 142.36, "end": 142.58, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 142.58, "end": 142.599, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "le", "start": 142.599, "end": 142.679, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 142.679, "end": 142.739, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "donne", "start": 142.739, "end": 142.979, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 142.979, "end": 142.979, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "che", "start": 142.979, "end": 143.119, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 143.119, "end": 143.139, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "perdono", "start": 143.139, "end": 143.439, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 143.439, "end": 143.479, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "gli", "start": 143.479, "end": 143.539, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 143.539, "end": 143.539, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>,", "start": 143.539, "end": 143.939, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 143.939, "end": 144.08, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "ricordatelo", "start": 144.08, "end": 144.759, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 144.759, "end": 144.759, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "Rosario.", "start": 144.759, "end": 145.26, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 145.26, "end": 145.94, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "Non", "start": 145.94, "end": 146.139, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 146.139, "end": 146.139, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "accadrà", "start": 146.139, "end": 146.52, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 146.52, "end": 146.52, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "a", "start": 146.52, "end": 146.54, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 146.54, "end": 146.559, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "noi,", "start": 146.559, "end": 146.839, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 146.839, "end": 147.139, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "voglio", "start": 147.139, "end": 147.379, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 147.379, "end": 147.379, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "solo", "start": 147.379, "end": 147.559, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 147.559, "end": 147.559, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "impedirle", "start": 147.559, "end": 147.979, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 147.979, "end": 147.979, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "di", "start": 147.979, "end": 148.059, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 148.059, "end": 148.08, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "fare", "start": 148.08, "end": 148.279, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 148.279, "end": 148.3, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "altro", "start": 148.3, "end": 148.559, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 148.559, "end": 148.599, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "male.", "start": 148.599, "end": 148.96, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 148.96, "end": 150.02, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>,", "start": 150.02, "end": 150.699, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 150.699, "end": 151.36, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "e", "start": 151.36, "end": 151.46, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 151.46, "end": 151.479, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "buona", "start": 151.479, "end": 151.659, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 151.659, "end": 151.699, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "fortuna.", "start": 151.699, "end": 152.26, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 152.26, "end": 159.959, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "Su", "start": 159.959, "end": 160.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 160.699, "end": 160.72, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "avanti,", "start": 160.72, "end": 161.259, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 161.259, "end": 161.259, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "non", "start": 161.259, "end": 161.379, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 161.379, "end": 161.419, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "avere", "start": 161.419, "end": 161.679, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 161.679, "end": 161.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "paura.", "start": 161.739, "end": 162.259, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 162.259, "end": 164.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Sono", "start": 164.059, "end": 164.339, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 164.339, "end": 164.44, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sola.", "start": 164.44, "end": 164.999, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 164.999, "end": 183.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Riconosco", "start": 183.659, "end": 184.179, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 184.179, "end": 184.199, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "che", "start": 184.199, "end": 184.279, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 184.279, "end": 184.3, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "sei", "start": 184.3, "end": 184.439, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 184.439, "end": 184.459, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "stata", "start": 184.459, "end": 184.719, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 184.719, "end": 184.779, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "molto", "start": 184.779, "end": 185.079, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 185.079, "end": 185.159, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "abile.", "start": 185.159, "end": 185.559, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 185.559, "end": 186.699, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 186.699, "end": 186.899, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 186.899, "end": 186.94, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "r<PERSON><PERSON><PERSON>", "start": 186.94, "end": 187.259, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 187.259, "end": 187.259, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "a", "start": 187.259, "end": 187.319, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 187.319, "end": 187.319, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "start": 187.319, "end": 187.899, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 187.899, "end": 187.899, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "facilmente", "start": 187.899, "end": 188.439, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 188.439, "end": 188.599, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "di", "start": 188.599, "end": 188.719, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 188.719, "end": 188.759, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "padre", "start": 188.759, "end": 189.079, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 189.079, "end": 189.099, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "e", "start": 189.099, "end": 189.139, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 189.139, "end": 189.22, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "figlia", "start": 189.22, "end": 189.539, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 189.539, "end": 189.899, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "in", "start": 189.899, "end": 190.019, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 190.019, "end": 190.039, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "un", "start": 190.039, "end": 190.139, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 190.139, "end": 190.22, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "sol", "start": 190.22, "end": 190.419, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 190.419, "end": 190.479, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "colpo.", "start": 190.479, "end": 190.919, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 190.919, "end": 190.919, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "Ma", "start": 190.919, "end": 191.72, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 191.72, "end": 191.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tu", "start": 191.779, "end": 191.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 191.919, "end": 191.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sai", "start": 191.959, "end": 192.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 192.139, "end": 192.179, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "bene", "start": 192.179, "end": 192.5, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 192.5, "end": 192.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "perché", "start": 192.539, "end": 192.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 192.799, "end": 192.8, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "l'ho", "start": 192.8, "end": 192.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 192.979, "end": 193.0, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "fatto.", "start": 193.0, "end": 193.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 193.479, "end": 193.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "So", "start": 193.739, "end": 193.919, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 193.919, "end": 193.919, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "soltanto", "start": 193.919, "end": 194.339, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 194.339, "end": 194.339, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "che", "start": 194.339, "end": 194.419, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 194.419, "end": 194.44, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "sono", "start": 194.44, "end": 194.599, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 194.599, "end": 194.619, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "qui", "start": 194.619, "end": 194.739, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 194.739, "end": 194.759, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "per", "start": 194.759, "end": 194.879, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 194.879, "end": 194.899, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "dirti", "start": 194.899, "end": 195.199, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 195.199, "end": 195.22, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "quello", "start": 195.22, "end": 195.419, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 195.419, "end": 195.459, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "che", "start": 195.459, "end": 195.539, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 195.539, "end": 195.58, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "meriti.", "start": 195.58, "end": 196.079, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 196.079, "end": 197.399, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "Non", "start": 197.399, "end": 197.539, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 197.539, "end": 197.559, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "ti", "start": 197.559, "end": 197.639, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 197.639, "end": 197.639, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "è", "start": 197.639, "end": 197.699, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 197.699, "end": 197.699, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "bastato", "start": 197.699, "end": 198.059, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 198.059, "end": 198.099, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "combinare", "start": 198.099, "end": 198.5, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 198.5, "end": 198.519, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "guai,", "start": 198.519, "end": 198.919, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 198.919, "end": 199.279, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "seminare", "start": 199.279, "end": 199.72, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 199.72, "end": 199.72, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "l'odio", "start": 199.72, "end": 200.0, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 200.0, "end": 200.019, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "fra", "start": 200.019, "end": 200.099, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 200.099, "end": 200.099, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "i", "start": 200.099, "end": 200.159, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 200.159, "end": 200.159, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "pescatori.", "start": 200.159, "end": 200.799, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 200.799, "end": 203.059, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 203.059, "end": 203.379, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 203.379, "end": 203.379, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "hai", "start": 203.379, "end": 203.479, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 203.479, "end": 203.5, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "voluto", "start": 203.5, "end": 203.759, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 203.759, "end": 203.759, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "liberarti", "start": 203.759, "end": 204.239, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 204.239, "end": 204.259, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "anche", "start": 204.259, "end": 204.499, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 204.499, "end": 204.519, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "di", "start": 204.519, "end": 204.619, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 204.619, "end": 204.639, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "<PERSON>.", "start": 204.639, "end": 205.04, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 205.04, "end": 205.36, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "Ma", "start": 205.36, "end": 205.459, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 205.459, "end": 205.479, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "perché", "start": 205.479, "end": 205.679, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 205.679, "end": 205.699, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "sei", "start": 205.699, "end": 205.819, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 205.819, "end": 205.839, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "venuta", "start": 205.839, "end": 206.099, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 206.099, "end": 206.119, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "in", "start": 206.119, "end": 206.179, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 206.179, "end": 206.199, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "quest'isola?", "start": 206.199, "end": 206.799, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 206.799, "end": 207.08, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "Probabilmente", "start": 207.08, "end": 207.879, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 207.879, "end": 207.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "perché", "start": 207.879, "end": 208.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 208.139, "end": 208.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sentivo", "start": 208.159, "end": 208.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 208.619, "end": 208.639, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 208.639, "end": 208.74, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 208.74, "end": 208.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ti", "start": 208.779, "end": 208.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 208.859, "end": 208.86, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "avrei", "start": 208.86, "end": 209.059, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 209.059, "end": 209.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "incontrato.", "start": 209.059, "end": 209.86, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 209.86, "end": 212.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON>,", "start": 212.139, "end": 212.939, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 212.939, "end": 212.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "no?", "start": 212.959, "end": 213.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 213.199, "end": 214.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Cerca", "start": 214.319, "end": 214.739, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 214.739, "end": 214.739, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "di", "start": 214.739, "end": 214.839, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 214.839, "end": 214.839, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 214.839, "end": 215.239, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 215.239, "end": 215.239, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "bene.", "start": 215.239, "end": 215.579, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 215.579, "end": 216.86, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "Non", "start": 216.86, "end": 217.019, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 217.019, "end": 217.059, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "potrai", "start": 217.059, "end": 217.319, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 217.319, "end": 217.36, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "continuare", "start": 217.36, "end": 217.799, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 217.799, "end": 217.799, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "a", "start": 217.799, "end": 217.859, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 217.859, "end": 217.879, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "<PERSON>ti", "start": 217.879, "end": 218.119, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 218.119, "end": 218.119, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "gioco", "start": 218.119, "end": 218.339, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 218.339, "end": 218.379, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "di", "start": 218.379, "end": 218.439, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 218.439, "end": 218.459, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "noi", "start": 218.459, "end": 218.579, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 218.579, "end": 218.619, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "troppo", "start": 218.619, "end": 218.859, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 218.859, "end": 218.879, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "a", "start": 218.879, "end": 218.939, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 218.939, "end": 218.94, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "lungo.", "start": 218.94, "end": 219.32, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 219.32, "end": 220.08, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 220.08, "end": 220.519, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 220.519, "end": 220.539, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "accadere", "start": 220.539, "end": 220.859, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 220.859, "end": 220.919, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "cose", "start": 220.919, "end": 221.179, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 221.179, "end": 221.179, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "molto", "start": 221.179, "end": 221.439, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 221.439, "end": 221.459, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "s<PERSON><PERSON><PERSON><PERSON>.", "start": 221.459, "end": 222.119, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 222.119, "end": 223.099, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "In", "start": 223.099, "end": 223.239, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 223.239, "end": 223.259, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "quanto", "start": 223.259, "end": 223.479, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 223.479, "end": 223.479, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "a", "start": 223.479, "end": 223.559, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 223.559, "end": 223.58, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "me,", "start": 223.58, "end": 223.82, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 223.82, "end": 223.899, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "non", "start": 223.899, "end": 224.019, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 224.019, "end": 224.039, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "ti", "start": 224.039, "end": 224.139, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 224.139, "end": 224.179, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "permetter<PERSON>", "start": 224.179, "end": 224.599, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 224.599, "end": 224.619, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "di", "start": 224.619, "end": 224.679, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 224.679, "end": 224.72, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "torturare", "start": 224.72, "end": 225.199, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 225.199, "end": 225.22, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "ancora", "start": 225.22, "end": 225.539, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 225.539, "end": 225.58, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "<PERSON>.", "start": 225.58, "end": 225.959, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 225.959, "end": 225.959, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "Ma", "start": 225.959, "end": 226.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 226.799, "end": 226.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "non", "start": 226.819, "end": 227.0, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 227.0, "end": 227.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "capisci", "start": 227.059, "end": 227.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 227.559, "end": 227.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 227.619, "end": 227.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 227.699, "end": 227.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ho", "start": 227.699, "end": 227.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 227.819, "end": 227.839, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "fatto", "start": 227.839, "end": 228.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 228.119, "end": 228.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tutto", "start": 228.119, "end": 228.379, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 228.379, "end": 228.419, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "questo", "start": 228.419, "end": 228.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 228.759, "end": 228.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "per", "start": 228.799, "end": 228.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 228.919, "end": 228.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "rimanere", "start": 228.919, "end": 229.339, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 229.339, "end": 229.44, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sola", "start": 229.44, "end": 229.679, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 229.679, "end": 229.72, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "con", "start": 229.72, "end": 229.879, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 229.879, "end": 230.0, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "te?", "start": 230.0, "end": 230.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 230.199, "end": 231.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 231.539, "end": 231.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 231.919, "end": 231.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ti", "start": 231.979, "end": 232.179, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 232.179, "end": 232.199, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "amo.", "start": 232.199, "end": 232.539, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 232.539, "end": 234.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Stai", "start": 234.119, "end": 234.379, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 234.379, "end": 234.399, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "zitta,", "start": 234.399, "end": 234.639, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 234.639, "end": 234.659, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "mi", "start": 234.659, "end": 234.759, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 234.759, "end": 234.759, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "fai", "start": 234.759, "end": 234.879, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 234.879, "end": 234.879, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "schifo.", "start": 234.879, "end": 235.339, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 235.339, "end": 236.639, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "<PERSON>", "start": 236.639, "end": 236.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 236.819, "end": 236.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "paura.", "start": 236.919, "end": 237.539, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 237.539, "end": 238.0, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Sì,", "start": 238.0, "end": 238.24, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 238.24, "end": 238.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tu", "start": 238.299, "end": 238.399, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 238.399, "end": 238.399, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "hai", "start": 238.399, "end": 238.539, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 238.539, "end": 238.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "paura", "start": 238.559, "end": 238.8, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 238.8, "end": 238.839, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 238.839, "end": 238.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 238.899, "end": 238.939, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "me,", "start": 238.939, "end": 239.18, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 239.18, "end": 239.339, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "come", "start": 239.339, "end": 239.539, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 239.539, "end": 239.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tutti", "start": 239.559, "end": 239.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 239.699, "end": 239.72, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "gli", "start": 239.72, "end": 239.8, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 239.8, "end": 239.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 239.819, "end": 240.079, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 240.079, "end": 240.099, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "dell'isola", "start": 240.099, "end": 240.66, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 240.66, "end": 240.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 240.819, "end": 240.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 240.919, "end": 240.939, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "mi", "start": 240.939, "end": 241.059, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 241.059, "end": 241.08, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>ano,", "start": 241.08, "end": 241.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 241.559, "end": 241.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "mi", "start": 241.559, "end": 241.679, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 241.679, "end": 241.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 241.699, "end": 242.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 242.159, "end": 242.199, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sotto", "start": 242.199, "end": 242.399, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 242.399, "end": 242.419, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "gli", "start": 242.419, "end": 242.5, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 242.5, "end": 242.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "occhi", "start": 242.539, "end": 242.719, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 242.719, "end": 242.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "delle", "start": 242.739, "end": 242.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 242.899, "end": 242.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "loro", "start": 242.919, "end": 243.079, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 243.079, "end": 243.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "donne", "start": 243.139, "end": 243.399, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 243.399, "end": 243.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "gelose", "start": 243.459, "end": 243.839, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 243.839, "end": 243.839, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 243.839, "end": 243.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 243.959, "end": 243.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "me,", "start": 243.959, "end": 244.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 244.159, "end": 244.839, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 244.839, "end": 244.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 244.979, "end": 244.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "questo", "start": 244.979, "end": 245.419, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 245.419, "end": 245.419, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "corpo", "start": 245.419, "end": 245.8, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 245.8, "end": 245.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 245.819, "end": 245.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 245.899, "end": 245.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tu", "start": 245.959, "end": 246.1, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 246.1, "end": 246.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "dici", "start": 246.119, "end": 246.339, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 246.339, "end": 246.379, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "disprez<PERSON>e.", "start": 246.379, "end": 247.259, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 247.259, "end": 248.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "O", "start": 248.059, "end": 248.259, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 248.259, "end": 248.279, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "forse", "start": 248.279, "end": 248.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 248.699, "end": 248.72, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 248.72, "end": 249.279, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 249.279, "end": 249.36, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>,", "start": 249.36, "end": 249.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 249.899, "end": 250.36, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "con", "start": 250.36, "end": 250.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 250.619, "end": 250.639, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "quell'aria", "start": 250.639, "end": 251.179, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 251.179, "end": 251.179, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "da", "start": 251.179, "end": 251.259, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 251.259, "end": 251.259, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ingenua,", "start": 251.259, "end": 251.999, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 251.999, "end": 252.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "il", "start": 252.139, "end": 252.359, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 252.359, "end": 252.379, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "viso", "start": 252.379, "end": 252.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 252.619, "end": 252.639, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "angelico", "start": 252.639, "end": 253.36, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 253.36, "end": 253.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "e", "start": 253.599, "end": 253.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 253.699, "end": 253.72, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "l'esperienza", "start": 253.72, "end": 254.519, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 254.519, "end": 254.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "fatta", "start": 254.559, "end": 254.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 254.799, "end": 254.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "in", "start": 254.799, "end": 254.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 254.919, "end": 254.939, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "collegio.", "start": 254.939, "end": 255.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}], "language_detected": null, "confidence": null, "processing_time": 13.233543872833252, "metadata": {"service": "elevenlabs", "mode": "free", "model": "scribe_v1", "raw_result": {"language_code": "ita", "language_probability": 0.9960910081863403, "text": "<PERSON>sso and<PERSON>ene ora? O serve ancora qualcosa? <PERSON>att<PERSON>, è meglio. Sparecchierai domani. La tua faccia non mi è simpatica. Voglio il tuo bacio morbido di seta. <PERSON><PERSON><PERSON>otte a voi. <PERSON><PERSON><PERSON>otte amico, vai da lei? Devo farlo. È scritto: il serpente perse Eva, ma da allora sono le donne che perdono gli uomini, rico<PERSON><PERSON><PERSON>. Non accadrà a noi, voglio solo impedirle di fare altro male. <PERSON><PERSON><PERSON><PERSON>, e buona fortuna. Su avanti, non avere paura. Sono sola. Riconosco che sei stata molto abile. Sei riuscita a sbarazzarti facilmente di padre e figlia in un sol colpo. Ma tu sai bene perché l'ho fatto. So soltanto che sono qui per dirti quello che meriti. Non ti è bastato combinare guai, seminare l'odio fra i pescatori. Adesso hai voluto liberarti anche di Maria. Ma perché sei venuta in quest'isola? Probabilmente perché sentivo che ti avrei incontrato. Romantico, no? Cerca di capirmi bene. Non potrai continuare a farti gioco di noi troppo a lungo. Potrebbero accadere cose molto spiacevoli. In quanto a me, non ti permetterò di torturare ancora Maria. Ma non capisci che ho fatto tutto questo per rimanere sola con te? Perché ti amo. Stai zitta, mi fai schifo. Ho paura. Sì, tu hai paura di me, come tutti gli uomini dell'isola che mi guardano, mi desiderano sotto gli occhi delle loro donne gelose di me, di questo corpo che tu dici disprezzare. O forse preferisci Maria, con quell'aria da ingenua, il viso angelico e l'esperienza fatta in collegio.", "words": [{"text": "Po<PERSON>", "start": 16.739, "end": 17.019, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 17.019, "end": 17.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 17.059, "end": 17.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 17.659, "end": 17.68, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ora?", "start": 17.68, "end": 18.06, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 18.06, "end": 18.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "O", "start": 18.299, "end": 18.379, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 18.379, "end": 18.42, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "serve", "start": 18.42, "end": 18.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 18.699, "end": 18.719, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ancora", "start": 18.719, "end": 19.039, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 19.039, "end": 19.079, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "qualcosa?", "start": 19.079, "end": 19.86, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 19.86, "end": 20.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>,", "start": 20.139, "end": 20.739, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 20.739, "end": 20.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "è", "start": 20.739, "end": 20.76, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 20.76, "end": 20.76, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "meglio.", "start": 20.76, "end": 21.199, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 21.199, "end": 21.699, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Sparecchierai", "start": 21.699, "end": 22.239, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 22.239, "end": 22.26, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "domani.", "start": 22.26, "end": 22.7, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 22.7, "end": 23.219, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "La", "start": 23.219, "end": 23.34, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 23.34, "end": 23.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "tua", "start": 23.379, "end": 23.519, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 23.519, "end": 23.539, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "faccia", "start": 23.539, "end": 23.84, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 23.84, "end": 23.84, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "non", "start": 23.84, "end": 23.94, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 23.94, "end": 23.959, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mi", "start": 23.959, "end": 24.04, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 24.04, "end": 24.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "è", "start": 24.059, "end": 24.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 24.139, "end": 24.159, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "simpatica.", "start": 24.159, "end": 25.02, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 25.02, "end": 102.239, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 102.239, "end": 102.699, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 102.699, "end": 102.72, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "il", "start": 102.72, "end": 102.839, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 102.839, "end": 102.899, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "tuo", "start": 102.899, "end": 103.079, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 103.079, "end": 103.119, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "bacio", "start": 103.119, "end": 103.54, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 103.54, "end": 103.559, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "morbido", "start": 103.559, "end": 104.599, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 104.599, "end": 104.68, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "di", "start": 104.68, "end": 104.859, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 104.859, "end": 104.919, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "seta.", "start": 104.919, "end": 107.539, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 107.539, "end": 134.52, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 134.52, "end": 135.0, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 135.0, "end": 135.02, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "a", "start": 135.02, "end": 135.08, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 135.08, "end": 135.099, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "voi.", "start": 135.099, "end": 135.379, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 135.379, "end": 135.839, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 135.839, "end": 136.459, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 136.459, "end": 136.459, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "amico,", "start": 136.459, "end": 136.9, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 136.9, "end": 137.039, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "vai", "start": 137.039, "end": 137.199, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 137.199, "end": 137.22, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "da", "start": 137.22, "end": 137.319, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 137.319, "end": 137.339, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "lei?", "start": 137.339, "end": 137.659, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 137.659, "end": 137.919, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "Devo", "start": 137.919, "end": 138.24, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 138.24, "end": 138.279, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "farlo.", "start": 138.279, "end": 138.719, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 138.719, "end": 139.58, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "È", "start": 139.58, "end": 139.72, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 139.72, "end": 139.739, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "scritto:", "start": 139.739, "end": 140.18, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 140.18, "end": 140.199, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "il", "start": 140.199, "end": 140.259, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 140.259, "end": 140.319, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "serpente", "start": 140.319, "end": 140.859, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 140.859, "end": 140.86, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "perse", "start": 140.86, "end": 141.22, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 141.22, "end": 141.22, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "Eva,", "start": 141.22, "end": 141.559, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 141.559, "end": 141.839, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "ma", "start": 141.839, "end": 141.959, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 141.959, "end": 141.979, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "da", "start": 141.979, "end": 142.099, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 142.099, "end": 142.099, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "allora", "start": 142.099, "end": 142.339, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 142.339, "end": 142.36, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "sono", "start": 142.36, "end": 142.58, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 142.58, "end": 142.599, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "le", "start": 142.599, "end": 142.679, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 142.679, "end": 142.739, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "donne", "start": 142.739, "end": 142.979, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 142.979, "end": 142.979, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "che", "start": 142.979, "end": 143.119, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 143.119, "end": 143.139, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "perdono", "start": 143.139, "end": 143.439, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 143.439, "end": 143.479, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "gli", "start": 143.479, "end": 143.539, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 143.539, "end": 143.539, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>,", "start": 143.539, "end": 143.939, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 143.939, "end": 144.08, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "ricordatelo", "start": 144.08, "end": 144.759, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 144.759, "end": 144.759, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "Rosario.", "start": 144.759, "end": 145.26, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 145.26, "end": 145.94, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "Non", "start": 145.94, "end": 146.139, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 146.139, "end": 146.139, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "accadrà", "start": 146.139, "end": 146.52, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 146.52, "end": 146.52, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "a", "start": 146.52, "end": 146.54, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 146.54, "end": 146.559, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "noi,", "start": 146.559, "end": 146.839, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 146.839, "end": 147.139, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "voglio", "start": 147.139, "end": 147.379, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 147.379, "end": 147.379, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "solo", "start": 147.379, "end": 147.559, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 147.559, "end": 147.559, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "impedirle", "start": 147.559, "end": 147.979, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 147.979, "end": 147.979, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "di", "start": 147.979, "end": 148.059, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 148.059, "end": 148.08, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "fare", "start": 148.08, "end": 148.279, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 148.279, "end": 148.3, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "altro", "start": 148.3, "end": 148.559, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 148.559, "end": 148.599, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "male.", "start": 148.599, "end": 148.96, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 148.96, "end": 150.02, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>,", "start": 150.02, "end": 150.699, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 150.699, "end": 151.36, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "e", "start": 151.36, "end": 151.46, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 151.46, "end": 151.479, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "buona", "start": 151.479, "end": 151.659, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 151.659, "end": 151.699, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "fortuna.", "start": 151.699, "end": 152.26, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 152.26, "end": 159.959, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "Su", "start": 159.959, "end": 160.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 160.699, "end": 160.72, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "avanti,", "start": 160.72, "end": 161.259, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 161.259, "end": 161.259, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "non", "start": 161.259, "end": 161.379, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 161.379, "end": 161.419, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "avere", "start": 161.419, "end": 161.679, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 161.679, "end": 161.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "paura.", "start": 161.739, "end": 162.259, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 162.259, "end": 164.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Sono", "start": 164.059, "end": 164.339, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 164.339, "end": 164.44, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sola.", "start": 164.44, "end": 164.999, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 164.999, "end": 183.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Riconosco", "start": 183.659, "end": 184.179, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 184.179, "end": 184.199, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "che", "start": 184.199, "end": 184.279, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 184.279, "end": 184.3, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "sei", "start": 184.3, "end": 184.439, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 184.439, "end": 184.459, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "stata", "start": 184.459, "end": 184.719, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 184.719, "end": 184.779, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "molto", "start": 184.779, "end": 185.079, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 185.079, "end": 185.159, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "abile.", "start": 185.159, "end": 185.559, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 185.559, "end": 186.699, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 186.699, "end": 186.899, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 186.899, "end": 186.94, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "r<PERSON><PERSON><PERSON>", "start": 186.94, "end": 187.259, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 187.259, "end": 187.259, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "a", "start": 187.259, "end": 187.319, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 187.319, "end": 187.319, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "start": 187.319, "end": 187.899, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 187.899, "end": 187.899, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "facilmente", "start": 187.899, "end": 188.439, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 188.439, "end": 188.599, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "di", "start": 188.599, "end": 188.719, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 188.719, "end": 188.759, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "padre", "start": 188.759, "end": 189.079, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 189.079, "end": 189.099, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "e", "start": 189.099, "end": 189.139, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 189.139, "end": 189.22, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "figlia", "start": 189.22, "end": 189.539, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 189.539, "end": 189.899, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "in", "start": 189.899, "end": 190.019, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 190.019, "end": 190.039, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "un", "start": 190.039, "end": 190.139, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 190.139, "end": 190.22, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "sol", "start": 190.22, "end": 190.419, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 190.419, "end": 190.479, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "colpo.", "start": 190.479, "end": 190.919, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 190.919, "end": 190.919, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "Ma", "start": 190.919, "end": 191.72, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 191.72, "end": 191.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tu", "start": 191.779, "end": 191.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 191.919, "end": 191.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sai", "start": 191.959, "end": 192.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 192.139, "end": 192.179, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "bene", "start": 192.179, "end": 192.5, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 192.5, "end": 192.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "perché", "start": 192.539, "end": 192.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 192.799, "end": 192.8, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "l'ho", "start": 192.8, "end": 192.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 192.979, "end": 193.0, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "fatto.", "start": 193.0, "end": 193.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 193.479, "end": 193.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "So", "start": 193.739, "end": 193.919, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 193.919, "end": 193.919, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "soltanto", "start": 193.919, "end": 194.339, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 194.339, "end": 194.339, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "che", "start": 194.339, "end": 194.419, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 194.419, "end": 194.44, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "sono", "start": 194.44, "end": 194.599, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 194.599, "end": 194.619, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "qui", "start": 194.619, "end": 194.739, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 194.739, "end": 194.759, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "per", "start": 194.759, "end": 194.879, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 194.879, "end": 194.899, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "dirti", "start": 194.899, "end": 195.199, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 195.199, "end": 195.22, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "quello", "start": 195.22, "end": 195.419, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 195.419, "end": 195.459, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "che", "start": 195.459, "end": 195.539, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 195.539, "end": 195.58, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "meriti.", "start": 195.58, "end": 196.079, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 196.079, "end": 197.399, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "Non", "start": 197.399, "end": 197.539, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 197.539, "end": 197.559, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "ti", "start": 197.559, "end": 197.639, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 197.639, "end": 197.639, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "è", "start": 197.639, "end": 197.699, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 197.699, "end": 197.699, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "bastato", "start": 197.699, "end": 198.059, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 198.059, "end": 198.099, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "combinare", "start": 198.099, "end": 198.5, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 198.5, "end": 198.519, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "guai,", "start": 198.519, "end": 198.919, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 198.919, "end": 199.279, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "seminare", "start": 199.279, "end": 199.72, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 199.72, "end": 199.72, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "l'odio", "start": 199.72, "end": 200.0, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 200.0, "end": 200.019, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "fra", "start": 200.019, "end": 200.099, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 200.099, "end": 200.099, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "i", "start": 200.099, "end": 200.159, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 200.159, "end": 200.159, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "pescatori.", "start": 200.159, "end": 200.799, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 200.799, "end": 203.059, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 203.059, "end": 203.379, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 203.379, "end": 203.379, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "hai", "start": 203.379, "end": 203.479, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 203.479, "end": 203.5, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "voluto", "start": 203.5, "end": 203.759, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 203.759, "end": 203.759, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "liberarti", "start": 203.759, "end": 204.239, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 204.239, "end": 204.259, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "anche", "start": 204.259, "end": 204.499, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 204.499, "end": 204.519, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "di", "start": 204.519, "end": 204.619, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 204.619, "end": 204.639, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "<PERSON>.", "start": 204.639, "end": 205.04, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 205.04, "end": 205.36, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "Ma", "start": 205.36, "end": 205.459, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 205.459, "end": 205.479, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "perché", "start": 205.479, "end": 205.679, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 205.679, "end": 205.699, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "sei", "start": 205.699, "end": 205.819, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 205.819, "end": 205.839, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "venuta", "start": 205.839, "end": 206.099, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 206.099, "end": 206.119, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "in", "start": 206.119, "end": 206.179, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 206.179, "end": 206.199, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "quest'isola?", "start": 206.199, "end": 206.799, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 206.799, "end": 207.08, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "Probabilmente", "start": 207.08, "end": 207.879, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 207.879, "end": 207.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "perché", "start": 207.879, "end": 208.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 208.139, "end": 208.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sentivo", "start": 208.159, "end": 208.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 208.619, "end": 208.639, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 208.639, "end": 208.74, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 208.74, "end": 208.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ti", "start": 208.779, "end": 208.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 208.859, "end": 208.86, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "avrei", "start": 208.86, "end": 209.059, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 209.059, "end": 209.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "incontrato.", "start": 209.059, "end": 209.86, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 209.86, "end": 212.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON>,", "start": 212.139, "end": 212.939, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 212.939, "end": 212.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "no?", "start": 212.959, "end": 213.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 213.199, "end": 214.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Cerca", "start": 214.319, "end": 214.739, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 214.739, "end": 214.739, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "di", "start": 214.739, "end": 214.839, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 214.839, "end": 214.839, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 214.839, "end": 215.239, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 215.239, "end": 215.239, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "bene.", "start": 215.239, "end": 215.579, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 215.579, "end": 216.86, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "Non", "start": 216.86, "end": 217.019, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 217.019, "end": 217.059, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "potrai", "start": 217.059, "end": 217.319, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 217.319, "end": 217.36, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "continuare", "start": 217.36, "end": 217.799, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 217.799, "end": 217.799, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "a", "start": 217.799, "end": 217.859, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 217.859, "end": 217.879, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "<PERSON>ti", "start": 217.879, "end": 218.119, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 218.119, "end": 218.119, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "gioco", "start": 218.119, "end": 218.339, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 218.339, "end": 218.379, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "di", "start": 218.379, "end": 218.439, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 218.439, "end": 218.459, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "noi", "start": 218.459, "end": 218.579, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 218.579, "end": 218.619, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "troppo", "start": 218.619, "end": 218.859, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 218.859, "end": 218.879, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "a", "start": 218.879, "end": 218.939, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 218.939, "end": 218.94, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "lungo.", "start": 218.94, "end": 219.32, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 219.32, "end": 220.08, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 220.08, "end": 220.519, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 220.519, "end": 220.539, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "accadere", "start": 220.539, "end": 220.859, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 220.859, "end": 220.919, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "cose", "start": 220.919, "end": 221.179, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 221.179, "end": 221.179, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "molto", "start": 221.179, "end": 221.439, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 221.439, "end": 221.459, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "s<PERSON><PERSON><PERSON><PERSON>.", "start": 221.459, "end": 222.119, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 222.119, "end": 223.099, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "In", "start": 223.099, "end": 223.239, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 223.239, "end": 223.259, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "quanto", "start": 223.259, "end": 223.479, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 223.479, "end": 223.479, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "a", "start": 223.479, "end": 223.559, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 223.559, "end": 223.58, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "me,", "start": 223.58, "end": 223.82, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 223.82, "end": 223.899, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "non", "start": 223.899, "end": 224.019, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 224.019, "end": 224.039, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "ti", "start": 224.039, "end": 224.139, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 224.139, "end": 224.179, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "permetter<PERSON>", "start": 224.179, "end": 224.599, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 224.599, "end": 224.619, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "di", "start": 224.619, "end": 224.679, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 224.679, "end": 224.72, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "torturare", "start": 224.72, "end": 225.199, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 225.199, "end": 225.22, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "ancora", "start": 225.22, "end": 225.539, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 225.539, "end": 225.58, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "<PERSON>.", "start": 225.58, "end": 225.959, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 225.959, "end": 225.959, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "Ma", "start": 225.959, "end": 226.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 226.799, "end": 226.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "non", "start": 226.819, "end": 227.0, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 227.0, "end": 227.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "capisci", "start": 227.059, "end": 227.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 227.559, "end": 227.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 227.619, "end": 227.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 227.699, "end": 227.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ho", "start": 227.699, "end": 227.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 227.819, "end": 227.839, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "fatto", "start": 227.839, "end": 228.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 228.119, "end": 228.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tutto", "start": 228.119, "end": 228.379, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 228.379, "end": 228.419, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "questo", "start": 228.419, "end": 228.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 228.759, "end": 228.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "per", "start": 228.799, "end": 228.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 228.919, "end": 228.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "rimanere", "start": 228.919, "end": 229.339, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 229.339, "end": 229.44, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sola", "start": 229.44, "end": 229.679, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 229.679, "end": 229.72, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "con", "start": 229.72, "end": 229.879, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 229.879, "end": 230.0, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "te?", "start": 230.0, "end": 230.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 230.199, "end": 231.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 231.539, "end": 231.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 231.919, "end": 231.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ti", "start": 231.979, "end": 232.179, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 232.179, "end": 232.199, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "amo.", "start": 232.199, "end": 232.539, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 232.539, "end": 234.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Stai", "start": 234.119, "end": 234.379, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 234.379, "end": 234.399, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "zitta,", "start": 234.399, "end": 234.639, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 234.639, "end": 234.659, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "mi", "start": 234.659, "end": 234.759, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 234.759, "end": 234.759, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "fai", "start": 234.759, "end": 234.879, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 234.879, "end": 234.879, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "schifo.", "start": 234.879, "end": 235.339, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 235.339, "end": 236.639, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "<PERSON>", "start": 236.639, "end": 236.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 236.819, "end": 236.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "paura.", "start": 236.919, "end": 237.539, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 237.539, "end": 238.0, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Sì,", "start": 238.0, "end": 238.24, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 238.24, "end": 238.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tu", "start": 238.299, "end": 238.399, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 238.399, "end": 238.399, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "hai", "start": 238.399, "end": 238.539, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 238.539, "end": 238.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "paura", "start": 238.559, "end": 238.8, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 238.8, "end": 238.839, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 238.839, "end": 238.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 238.899, "end": 238.939, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "me,", "start": 238.939, "end": 239.18, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 239.18, "end": 239.339, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "come", "start": 239.339, "end": 239.539, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 239.539, "end": 239.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tutti", "start": 239.559, "end": 239.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 239.699, "end": 239.72, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "gli", "start": 239.72, "end": 239.8, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 239.8, "end": 239.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 239.819, "end": 240.079, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 240.079, "end": 240.099, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "dell'isola", "start": 240.099, "end": 240.66, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 240.66, "end": 240.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 240.819, "end": 240.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 240.919, "end": 240.939, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "mi", "start": 240.939, "end": 241.059, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 241.059, "end": 241.08, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>ano,", "start": 241.08, "end": 241.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 241.559, "end": 241.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "mi", "start": 241.559, "end": 241.679, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 241.679, "end": 241.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 241.699, "end": 242.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 242.159, "end": 242.199, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sotto", "start": 242.199, "end": 242.399, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 242.399, "end": 242.419, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "gli", "start": 242.419, "end": 242.5, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 242.5, "end": 242.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "occhi", "start": 242.539, "end": 242.719, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 242.719, "end": 242.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "delle", "start": 242.739, "end": 242.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 242.899, "end": 242.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "loro", "start": 242.919, "end": 243.079, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 243.079, "end": 243.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "donne", "start": 243.139, "end": 243.399, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 243.399, "end": 243.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "gelose", "start": 243.459, "end": 243.839, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 243.839, "end": 243.839, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 243.839, "end": 243.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 243.959, "end": 243.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "me,", "start": 243.959, "end": 244.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 244.159, "end": 244.839, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 244.839, "end": 244.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 244.979, "end": 244.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "questo", "start": 244.979, "end": 245.419, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 245.419, "end": 245.419, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "corpo", "start": 245.419, "end": 245.8, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 245.8, "end": 245.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 245.819, "end": 245.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 245.899, "end": 245.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tu", "start": 245.959, "end": 246.1, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 246.1, "end": 246.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "dici", "start": 246.119, "end": 246.339, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 246.339, "end": 246.379, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "disprez<PERSON>e.", "start": 246.379, "end": 247.259, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 247.259, "end": 248.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "O", "start": 248.059, "end": 248.259, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 248.259, "end": 248.279, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "forse", "start": 248.279, "end": 248.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 248.699, "end": 248.72, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 248.72, "end": 249.279, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 249.279, "end": 249.36, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>,", "start": 249.36, "end": 249.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 249.899, "end": 250.36, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "con", "start": 250.36, "end": 250.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 250.619, "end": 250.639, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "quell'aria", "start": 250.639, "end": 251.179, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 251.179, "end": 251.179, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "da", "start": 251.179, "end": 251.259, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 251.259, "end": 251.259, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ingenua,", "start": 251.259, "end": 251.999, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 251.999, "end": 252.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "il", "start": 252.139, "end": 252.359, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 252.359, "end": 252.379, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "viso", "start": 252.379, "end": 252.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 252.619, "end": 252.639, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "angelico", "start": 252.639, "end": 253.36, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 253.36, "end": 253.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "e", "start": 253.599, "end": 253.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 253.699, "end": 253.72, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "l'esperienza", "start": 253.72, "end": 254.519, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 254.519, "end": 254.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "fatta", "start": 254.559, "end": 254.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 254.799, "end": 254.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "in", "start": 254.799, "end": 254.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 254.919, "end": 254.939, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "collegio.", "start": 254.939, "end": 255.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}]}}, "created_at": 1754319619.9424167}