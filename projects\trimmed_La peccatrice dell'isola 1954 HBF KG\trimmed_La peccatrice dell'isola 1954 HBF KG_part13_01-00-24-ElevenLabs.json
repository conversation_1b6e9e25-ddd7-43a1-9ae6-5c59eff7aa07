{"success": true, "service": "elevenlabs", "transcription_id": "elevenlabs_1754319739", "text": "<PERSON>h? Me la dai la cicca? Non ti vergogni a fumare così, picco<PERSON>? E tu non gli dici niente, giu<PERSON><PERSON>? Io non fumo. La birra è diventata calda, ormai. Sono peggio delle mosche, qui, i ragazzini. È un'ora che gli dico d'andar via e non se ne vanno. Ma non ce l'hanno, padre e madre. Beh, come è andata? Ho controllato la risposta degli interrogati. Fosse per me, li arresterei tutti. <PERSON><PERSON>, per rimetterli fuori subito per mancanza di prove. L'affare è molto più ingarbugliato di quanto non sembrava. Quella donna era riuscita a farsi odiare da tutti. Il negro Abul, strano tipo di provenienza molto incerta, muore nell'incendio di una baracca mentre probabilmente sta cercando di assassinare una donna. Una ragazza di sedici anni viene trovata impiccata. Dicono che si uccise per amore, ma nulla lo dimostra. <PERSON>, una bellissima donna. <PERSON><PERSON> intelligente, per<PERSON>. Ma che parlate a fare di loro? Mica posso mettere le manette ai morti. I vivi, allora. Rosario Albanese, <PERSON> Ingarcia e suo padre, sono tre anche loro. Ne dimenticate uno. Chi? Francesco, il fidanzato di Carmela Albanese. Ordina un'altra birra e aspettami qui. Francesco! Tu sei Francesco Greco, vero? E con questo? Sai chi sono io? Sì, lo so, ma non mi interessa. E allora perché stai scappando? Io? Ma voi sognate. Meglio così. Avremo il tempo di fare due chiacchiere insieme, eh? Posso accompagnarti? Se ci tenete, ma io non vado in un posto allegro. Oh, neanche io ho voglia di divertirmi. Ci sono due morti là sotto. Carmela e il figlio che mi doveva nascere. Ah sì, avete ragione voi. È bene che anch'io vi dica quello che so, perché dovrei tacere... Non sono mai stato un tipo fortunato, io. Padre e madre, si può dire che non li ho neanche conosciuti. Mi lasciarono una bottega e se la presero i creditori. Allora mi misi a fare il pescatore, ma è troppo faticoso, e oggi non si guadagna più niente. Così preferivo arrangiarmi in qualsiasi altro modo. Mi misi a portare le valigie a quelli che venivano nell'isola. Vivevo aspettando qualche straniera ricca, perché ne vengono qui, sapete. Pescano i pesci col fucile. Carmela era l'unica che mi volesse bene. Ma voleva che mi mettessi a lavorare con Rosario. E le sue insistenze mi annoiavano. Suo fratello poi mi era antipatico con le sue arie da protettore dei poveri. E intanto filava con la figlia del padrone. Tutta gente schifosa in quest'isola. Magari non ci fossi mai nato. Non ti fare illusioni. Tipi come te stanno male anche in città. Lo vedrò presto se in città si sta male come dite. Me ne voglio andare a Milano. Lassù ne girano di soldi. Chi ti ha messo in testa quest'idea? Beh, Carla ci si è trovata bene, lei. Non tanto da rimanersi se è finita in quest'isola. Eh già. Avete ragione. Forse non era vero niente. Fin dal primo incontro mi trattò come un ragazzino. E io lo sapevo, ma facevo finta di nulla, mi piaceva molto. Passava le giornate chiuse in casa, a lisciarsi come una gatta. Sono l'unico qui ad avere del tempo libero. Nessuno mi comanda. E così andavo sotto le sue finestre a suonare la chitarra. E aspettavo. Sapevo che lei mi guardava.", "words": [{"text": "Beh?", "start": 12.739, "end": 13.06, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 13.06, "end": 13.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Me", "start": 13.739, "end": 13.84, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 13.84, "end": 13.859, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "la", "start": 13.859, "end": 13.96, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 13.96, "end": 13.979, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "dai", "start": 13.979, "end": 14.139, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 14.139, "end": 14.139, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "la", "start": 14.139, "end": 14.239, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 14.239, "end": 14.299, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "cicca?", "start": 14.299, "end": 14.719, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 14.719, "end": 14.759, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Non", "start": 14.759, "end": 14.859, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 14.859, "end": 14.88, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ti", "start": 14.88, "end": 14.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 14.979, "end": 15.0, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "vergo<PERSON>", "start": 15.0, "end": 15.359, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 15.359, "end": 15.42, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "a", "start": 15.42, "end": 15.44, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 15.44, "end": 15.44, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "fumare", "start": 15.44, "end": 15.739, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 15.739, "end": 15.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "così,", "start": 15.739, "end": 16.02, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 16.02, "end": 16.079, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "piccolo?", "start": 16.079, "end": 16.52, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 16.52, "end": 16.76, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "E", "start": 16.76, "end": 16.859, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 16.859, "end": 16.859, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "tu", "start": 16.859, "end": 16.959, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 16.959, "end": 16.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "non", "start": 16.979, "end": 17.079, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 17.079, "end": 17.079, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "gli", "start": 17.079, "end": 17.179, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 17.179, "end": 17.199, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "dici", "start": 17.199, "end": 17.399, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 17.399, "end": 17.42, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "niente,", "start": 17.42, "end": 17.7, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 17.7, "end": 17.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "giuggiolone?", "start": 17.799, "end": 18.279, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 18.279, "end": 18.279, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Io", "start": 18.279, "end": 18.299, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 18.299, "end": 18.299, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "non", "start": 18.299, "end": 18.399, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 18.399, "end": 18.459, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "fumo.", "start": 18.459, "end": 18.799, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 18.799, "end": 22.719, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "La", "start": 22.719, "end": 22.839, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 22.839, "end": 22.859, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "birra", "start": 22.859, "end": 23.059, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 23.059, "end": 23.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "è", "start": 23.119, "end": 23.179, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 23.179, "end": 23.26, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "diventata", "start": 23.26, "end": 23.68, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 23.68, "end": 23.68, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "calda,", "start": 23.68, "end": 23.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 23.979, "end": 24.0, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ormai.", "start": 24.0, "end": 24.459, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 24.459, "end": 24.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Sono", "start": 24.979, "end": 25.18, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 25.18, "end": 25.219, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "peggio", "start": 25.219, "end": 25.459, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 25.459, "end": 25.459, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "delle", "start": 25.459, "end": 25.619, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 25.619, "end": 25.659, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mosche,", "start": 25.659, "end": 25.939, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 25.939, "end": 25.939, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "qui,", "start": 25.939, "end": 26.079, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 26.079, "end": 26.079, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "i", "start": 26.079, "end": 26.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 26.159, "end": 26.159, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ragazzini.", "start": 26.159, "end": 26.699, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 26.699, "end": 26.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "È", "start": 26.819, "end": 27.239, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 27.239, "end": 27.239, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "un'ora", "start": 27.239, "end": 27.499, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 27.499, "end": 27.5, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "che", "start": 27.5, "end": 27.579, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 27.579, "end": 27.579, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "gli", "start": 27.579, "end": 27.68, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 27.68, "end": 27.699, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "dico", "start": 27.699, "end": 27.859, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 27.859, "end": 27.859, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "d'andar", "start": 27.859, "end": 28.119, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 28.119, "end": 28.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "via", "start": 28.119, "end": 28.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 28.299, "end": 28.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "e", "start": 28.299, "end": 28.359, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 28.359, "end": 28.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "non", "start": 28.479, "end": 28.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 28.559, "end": 28.599, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "se", "start": 28.599, "end": 28.659, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 28.659, "end": 28.68, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ne", "start": 28.68, "end": 28.779, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 28.779, "end": 28.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "vanno.", "start": 28.799, "end": 29.119, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 29.119, "end": 29.399, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Ma", "start": 29.399, "end": 29.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 29.479, "end": 29.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "non", "start": 29.479, "end": 29.599, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 29.599, "end": 29.599, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ce", "start": 29.599, "end": 29.679, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 29.679, "end": 29.679, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "l'hanno,", "start": 29.679, "end": 29.919, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 29.919, "end": 29.92, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "padre", "start": 29.92, "end": 30.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 30.139, "end": 30.159, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "e", "start": 30.159, "end": 30.18, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 30.18, "end": 30.239, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "madre.", "start": 30.239, "end": 30.579, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 30.579, "end": 30.899, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON>,", "start": 30.899, "end": 31.259, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 31.259, "end": 31.26, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "come", "start": 31.26, "end": 31.42, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 31.42, "end": 31.459, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "è", "start": 31.459, "end": 31.5, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 31.5, "end": 31.5, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "andata?", "start": 31.5, "end": 32.04, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 32.04, "end": 32.04, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "<PERSON>", "start": 32.04, "end": 32.88, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 32.88, "end": 32.899, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "controllato", "start": 32.899, "end": 33.459, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 33.459, "end": 33.479, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "la", "start": 33.479, "end": 33.579, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 33.579, "end": 33.579, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "risposta", "start": 33.579, "end": 34.0, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 34.0, "end": 34.02, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "<PERSON>gli", "start": 34.02, "end": 34.18, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 34.18, "end": 34.2, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "interrogati.", "start": 34.2, "end": 34.94, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 34.94, "end": 35.139, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "Fosse", "start": 35.139, "end": 35.42, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 35.42, "end": 35.459, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "per", "start": 35.459, "end": 35.6, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 35.6, "end": 35.639, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "me,", "start": 35.639, "end": 35.899, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 35.899, "end": 36.239, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "li", "start": 36.239, "end": 36.34, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 36.34, "end": 36.36, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "arresterei", "start": 36.36, "end": 36.819, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 36.819, "end": 36.86, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "tutti.", "start": 36.86, "end": 37.259, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 37.259, "end": 37.68, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "Sì,", "start": 37.68, "end": 38.1, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 38.1, "end": 38.36, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "per", "start": 38.36, "end": 38.479, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 38.479, "end": 38.479, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 38.479, "end": 38.919, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 38.919, "end": 38.959, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "fuori", "start": 38.959, "end": 39.24, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 39.24, "end": 39.34, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "subito", "start": 39.34, "end": 39.68, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 39.68, "end": 39.7, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "per", "start": 39.7, "end": 39.84, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 39.84, "end": 39.86, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "mancanza", "start": 39.86, "end": 40.279, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 40.279, "end": 40.299, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "di", "start": 40.299, "end": 40.38, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 40.38, "end": 40.419, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "prove.", "start": 40.419, "end": 40.88, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 40.88, "end": 42.299, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "L'affare", "start": 42.299, "end": 42.779, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 42.779, "end": 42.779, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "è", "start": 42.779, "end": 42.879, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 42.879, "end": 42.879, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "molto", "start": 42.879, "end": 43.159, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 43.159, "end": 43.239, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "più", "start": 43.239, "end": 43.36, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 43.36, "end": 43.36, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "ingarbugliato", "start": 43.36, "end": 43.979, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 43.979, "end": 44.0, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "di", "start": 44.0, "end": 44.08, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 44.08, "end": 44.119, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "quanto", "start": 44.119, "end": 44.36, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 44.36, "end": 44.379, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "non", "start": 44.379, "end": 44.479, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 44.479, "end": 44.52, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "sembrava.", "start": 44.52, "end": 45.079, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 45.079, "end": 45.719, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 45.719, "end": 45.979, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 45.979, "end": 46.039, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "donna", "start": 46.039, "end": 46.399, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 46.399, "end": 46.919, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "era", "start": 46.919, "end": 47.199, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 47.199, "end": 47.219, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "r<PERSON><PERSON><PERSON>", "start": 47.219, "end": 47.639, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 47.639, "end": 47.639, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "a", "start": 47.639, "end": 47.739, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 47.739, "end": 47.759, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "farsi", "start": 47.759, "end": 48.119, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 48.119, "end": 48.139, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "odiare", "start": 48.139, "end": 48.5, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 48.5, "end": 48.52, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "da", "start": 48.52, "end": 48.619, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 48.619, "end": 48.68, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "tutti.", "start": 48.68, "end": 49.1, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 49.1, "end": 53.219, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "Il", "start": 53.219, "end": 53.7, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 53.7, "end": 53.819, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "negro", "start": 53.819, "end": 54.119, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 54.119, "end": 54.239, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "Abul,", "start": 54.239, "end": 54.939, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 54.939, "end": 55.0, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "strano", "start": 55.0, "end": 55.459, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 55.459, "end": 55.5, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "tipo", "start": 55.5, "end": 55.74, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 55.74, "end": 55.759, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "di", "start": 55.759, "end": 55.84, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 55.84, "end": 55.899, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "proven<PERSON>za", "start": 55.899, "end": 56.459, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 56.459, "end": 56.5, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "molto", "start": 56.5, "end": 56.799, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 56.799, "end": 56.799, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "incerta,", "start": 56.799, "end": 57.46, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 57.46, "end": 57.599, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "muore", "start": 57.599, "end": 57.86, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 57.86, "end": 57.879, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "nell'incendio", "start": 57.879, "end": 58.459, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 58.459, "end": 58.459, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "di", "start": 58.459, "end": 58.54, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 58.54, "end": 58.559, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "una", "start": 58.559, "end": 58.68, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 58.68, "end": 58.739, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "baracca", "start": 58.739, "end": 59.119, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 59.119, "end": 59.519, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "mentre", "start": 59.519, "end": 59.819, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 59.819, "end": 59.839, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "probabilmente", "start": 59.839, "end": 60.419, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 60.419, "end": 60.419, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "sta", "start": 60.419, "end": 60.659, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 60.659, "end": 60.659, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "cercando", "start": 60.659, "end": 61.019, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 61.019, "end": 61.039, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "di", "start": 61.039, "end": 61.119, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 61.119, "end": 61.119, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "assassinare", "start": 61.119, "end": 61.779, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 61.779, "end": 61.799, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "una", "start": 61.799, "end": 62.019, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 62.019, "end": 62.059, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "donna.", "start": 62.059, "end": 62.439, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 62.439, "end": 63.879, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "Una", "start": 63.879, "end": 64.019, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 64.019, "end": 64.059, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "ragazza", "start": 64.059, "end": 64.399, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 64.399, "end": 64.419, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "di", "start": 64.419, "end": 64.499, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 64.499, "end": 64.519, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "sedici", "start": 64.519, "end": 64.839, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 64.839, "end": 64.879, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "anni", "start": 64.879, "end": 65.119, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 65.119, "end": 65.639, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "viene", "start": 65.639, "end": 65.919, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 65.919, "end": 65.959, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "trovata", "start": 65.959, "end": 66.379, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 66.379, "end": 66.399, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "impiccata.", "start": 66.399, "end": 67.259, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 67.259, "end": 67.799, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "Dicono", "start": 67.799, "end": 68.099, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 68.099, "end": 68.099, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "che", "start": 68.099, "end": 68.219, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 68.219, "end": 68.259, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "si", "start": 68.259, "end": 68.339, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 68.339, "end": 68.379, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "uccise", "start": 68.379, "end": 68.739, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 68.739, "end": 68.739, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "per", "start": 68.739, "end": 68.919, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 68.919, "end": 68.919, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "amore,", "start": 68.919, "end": 69.379, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 69.379, "end": 70.399, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "ma", "start": 70.399, "end": 70.539, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 70.539, "end": 70.579, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "nulla", "start": 70.579, "end": 70.779, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 70.779, "end": 70.799, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "lo", "start": 70.799, "end": 70.899, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 70.899, "end": 70.919, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "dimostra.", "start": 70.919, "end": 71.559, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 71.559, "end": 75.719, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "<PERSON>", "start": 75.719, "end": 76.119, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 76.119, "end": 76.139, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "Ingarcia,", "start": 76.139, "end": 76.979, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 76.979, "end": 77.599, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "una", "start": 77.599, "end": 77.719, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 77.719, "end": 77.799, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "bellissima", "start": 77.799, "end": 78.419, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 78.419, "end": 78.439, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "donna.", "start": 78.439, "end": 78.859, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 78.859, "end": 79.799, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "Poco", "start": 79.799, "end": 80.039, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 80.039, "end": 80.059, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "intelligente,", "start": 80.059, "end": 80.819, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 80.819, "end": 80.819, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "per<PERSON>.", "start": 80.819, "end": 81.179, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 81.179, "end": 81.699, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "Ma", "start": 81.699, "end": 81.779, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 81.779, "end": 81.779, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "che", "start": 81.779, "end": 81.879, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 81.879, "end": 81.939, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "parlate", "start": 81.939, "end": 82.319, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 82.319, "end": 82.339, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "a", "start": 82.339, "end": 82.399, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 82.399, "end": 82.439, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "fare", "start": 82.439, "end": 82.739, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 82.739, "end": 82.759, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "di", "start": 82.759, "end": 82.839, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 82.839, "end": 82.859, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "loro?", "start": 82.859, "end": 83.199, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 83.199, "end": 83.919, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "Mica", "start": 83.919, "end": 84.179, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 84.179, "end": 84.179, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "posso", "start": 84.179, "end": 84.379, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 84.379, "end": 84.419, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "mettere", "start": 84.419, "end": 84.679, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 84.679, "end": 84.679, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "le", "start": 84.679, "end": 84.759, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 84.759, "end": 84.799, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "manette", "start": 84.799, "end": 85.059, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 85.059, "end": 85.059, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "ai", "start": 85.059, "end": 85.139, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 85.139, "end": 85.159, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "morti.", "start": 85.159, "end": 85.559, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 85.559, "end": 85.759, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "I", "start": 85.759, "end": 85.899, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 85.899, "end": 85.899, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "vivi,", "start": 85.899, "end": 86.259, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 86.259, "end": 86.279, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "allora.", "start": 86.279, "end": 86.659, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 86.659, "end": 87.779, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "Rosario", "start": 87.779, "end": 88.099, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 88.099, "end": 88.099, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "Albanese,", "start": 88.099, "end": 88.699, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 88.699, "end": 89.019, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "<PERSON>", "start": 89.019, "end": 89.279, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 89.279, "end": 89.299, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "Ingarcia", "start": 89.299, "end": 89.859, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 89.859, "end": 90.259, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "e", "start": 90.259, "end": 90.379, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 90.379, "end": 90.379, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "suo", "start": 90.379, "end": 90.559, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 90.559, "end": 90.579, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "padre,", "start": 90.579, "end": 90.979, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 90.979, "end": 91.539, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "sono", "start": 91.539, "end": 91.739, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 91.739, "end": 91.759, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "tre", "start": 91.759, "end": 91.939, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 91.939, "end": 92.059, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "anche", "start": 92.059, "end": 92.279, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 92.279, "end": 92.279, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "loro.", "start": 92.279, "end": 92.639, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 92.639, "end": 93.199, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "Ne", "start": 93.199, "end": 93.299, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 93.299, "end": 93.379, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "dimenticate", "start": 93.379, "end": 94.019, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 94.019, "end": 94.039, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "uno.", "start": 94.039, "end": 94.379, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 94.379, "end": 94.499, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "Chi?", "start": 94.499, "end": 94.859, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 94.859, "end": 98.679, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "<PERSON>,", "start": 98.679, "end": 99.279, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 99.279, "end": 99.759, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "il", "start": 99.759, "end": 99.899, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 99.899, "end": 99.899, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "fi<PERSON><PERSON><PERSON>", "start": 99.899, "end": 100.359, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 100.359, "end": 100.359, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "di", "start": 100.359, "end": 100.439, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 100.439, "end": 100.479, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "Carmel<PERSON>", "start": 100.479, "end": 100.759, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 100.759, "end": 100.779, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "Albanese.", "start": 100.779, "end": 101.359, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 101.359, "end": 101.539, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "Ordina", "start": 101.539, "end": 101.859, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 101.859, "end": 101.859, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "un'altra", "start": 101.859, "end": 102.099, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 102.099, "end": 102.099, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "birra", "start": 102.099, "end": 102.279, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 102.279, "end": 102.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "e", "start": 102.299, "end": 102.359, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 102.359, "end": 102.359, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "as<PERSON><PERSON><PERSON>", "start": 102.359, "end": 102.719, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 102.719, "end": 102.759, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "qui.", "start": 102.759, "end": 102.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 102.979, "end": 114.139, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Francesco!", "start": 114.139, "end": 115.019, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 115.019, "end": 122.679, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Tu", "start": 122.679, "end": 122.799, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 122.799, "end": 122.839, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "sei", "start": 122.839, "end": 123.019, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 123.019, "end": 123.059, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "<PERSON>", "start": 123.059, "end": 123.499, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 123.499, "end": 123.559, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "<PERSON>,", "start": 123.559, "end": 123.879, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 123.879, "end": 123.879, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "vero?", "start": 123.879, "end": 124.179, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 124.179, "end": 124.439, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "E", "start": 124.439, "end": 124.539, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 124.539, "end": 124.539, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "con", "start": 124.539, "end": 124.699, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 124.699, "end": 124.699, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "questo?", "start": 124.699, "end": 125.119, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 125.119, "end": 125.599, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>", "start": 125.599, "end": 125.839, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 125.839, "end": 125.919, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "chi", "start": 125.919, "end": 126.039, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 126.039, "end": 126.079, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "sono", "start": 126.079, "end": 126.259, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 126.259, "end": 126.299, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "io?", "start": 126.299, "end": 126.499, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 126.499, "end": 126.939, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "Sì,", "start": 126.939, "end": 127.099, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 127.099, "end": 127.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "lo", "start": 127.119, "end": 127.199, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 127.199, "end": 127.219, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "so,", "start": 127.219, "end": 127.459, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 127.459, "end": 127.699, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ma", "start": 127.699, "end": 127.779, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 127.779, "end": 127.779, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "non", "start": 127.779, "end": 127.859, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 127.859, "end": 127.859, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mi", "start": 127.859, "end": 127.959, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 127.959, "end": 127.959, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "interessa.", "start": 127.959, "end": 128.459, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 128.459, "end": 128.659, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "E", "start": 128.659, "end": 128.719, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 128.719, "end": 128.719, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "allora", "start": 128.719, "end": 128.939, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 128.939, "end": 128.959, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "perché", "start": 128.959, "end": 129.159, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 129.159, "end": 129.179, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "stai", "start": 129.179, "end": 129.379, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 129.379, "end": 129.379, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "scappando?", "start": 129.379, "end": 129.979, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 129.979, "end": 129.999, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "Io?", "start": 129.999, "end": 130.279, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 130.279, "end": 130.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Ma", "start": 130.639, "end": 130.719, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 130.719, "end": 130.719, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "voi", "start": 130.719, "end": 130.859, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 130.859, "end": 130.879, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sognate.", "start": 130.879, "end": 131.419, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 131.419, "end": 131.879, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 131.879, "end": 132.179, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 132.179, "end": 132.179, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "così.", "start": 132.179, "end": 132.539, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 132.539, "end": 133.219, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 133.219, "end": 133.459, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 133.459, "end": 133.459, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "il", "start": 133.459, "end": 133.559, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 133.559, "end": 133.579, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "tempo", "start": 133.579, "end": 133.779, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 133.779, "end": 133.799, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "di", "start": 133.799, "end": 133.899, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 133.899, "end": 133.919, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "fare", "start": 133.919, "end": 134.099, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 134.099, "end": 134.119, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "due", "start": 134.119, "end": 134.259, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 134.259, "end": 134.319, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "chiac<PERSON>ere", "start": 134.319, "end": 134.719, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 134.719, "end": 134.739, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "insieme,", "start": 134.739, "end": 135.179, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 135.179, "end": 135.179, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "eh?", "start": 135.179, "end": 135.359, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 135.359, "end": 135.839, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "Po<PERSON>", "start": 135.839, "end": 136.039, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 136.039, "end": 136.059, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "accomp<PERSON><PERSON><PERSON>?", "start": 136.059, "end": 136.719, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 136.719, "end": 136.939, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "Se", "start": 136.939, "end": 137.059, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 137.059, "end": 137.099, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ci", "start": 137.099, "end": 137.199, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 137.199, "end": 137.219, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "tenete,", "start": 137.219, "end": 137.679, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 137.679, "end": 138.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ma", "start": 138.119, "end": 138.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 138.299, "end": 138.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "io", "start": 138.299, "end": 138.419, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 138.419, "end": 138.439, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "non", "start": 138.439, "end": 138.519, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 138.519, "end": 138.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "vado", "start": 138.559, "end": 138.739, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 138.739, "end": 138.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "in", "start": 138.739, "end": 138.819, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 138.819, "end": 138.839, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "un", "start": 138.839, "end": 138.879, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 138.879, "end": 138.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "posto", "start": 138.919, "end": 139.119, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 139.119, "end": 139.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "allegro.", "start": 139.119, "end": 139.539, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 139.539, "end": 139.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Oh,", "start": 139.639, "end": 139.819, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 139.819, "end": 139.839, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "neanche", "start": 139.839, "end": 140.139, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 140.139, "end": 140.159, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "io", "start": 140.159, "end": 140.279, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 140.279, "end": 140.299, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "ho", "start": 140.299, "end": 140.319, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 140.319, "end": 140.319, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "voglia", "start": 140.319, "end": 140.519, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 140.519, "end": 140.539, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "di", "start": 140.539, "end": 140.639, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 140.639, "end": 140.639, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "divertirmi.", "start": 140.639, "end": 141.299, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 141.299, "end": 161.019, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "Ci", "start": 161.019, "end": 161.159, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 161.159, "end": 161.199, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "sono", "start": 161.199, "end": 161.399, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 161.399, "end": 161.439, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "due", "start": 161.439, "end": 161.579, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 161.579, "end": 161.619, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "morti", "start": 161.619, "end": 161.939, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 161.939, "end": 161.939, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "là", "start": 161.939, "end": 162.079, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 162.079, "end": 162.099, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "sotto.", "start": 162.099, "end": 162.559, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 162.559, "end": 163.519, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "Carmel<PERSON>", "start": 163.519, "end": 164.019, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 164.019, "end": 164.039, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "e", "start": 164.039, "end": 164.099, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 164.099, "end": 164.119, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "il", "start": 164.119, "end": 164.199, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 164.199, "end": 164.259, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "figlio", "start": 164.259, "end": 164.539, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 164.539, "end": 164.559, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "che", "start": 164.559, "end": 164.639, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 164.639, "end": 164.659, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "mi", "start": 164.659, "end": 164.759, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 164.759, "end": 164.779, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 164.779, "end": 165.079, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 165.079, "end": 165.099, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "nascere.", "start": 165.099, "end": 165.659, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 165.659, "end": 171.419, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "Ah", "start": 171.419, "end": 171.519, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 171.519, "end": 171.579, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "sì,", "start": 171.579, "end": 171.799, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 171.799, "end": 171.819, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "avete", "start": 171.819, "end": 172.039, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 172.039, "end": 172.079, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "ragione", "start": 172.079, "end": 172.339, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 172.339, "end": 172.399, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "voi.", "start": 172.399, "end": 172.819, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 172.819, "end": 173.439, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "È", "start": 173.439, "end": 173.559, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 173.559, "end": 173.579, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "bene", "start": 173.579, "end": 173.799, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 173.799, "end": 173.819, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "che", "start": 173.819, "end": 173.899, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 173.899, "end": 173.899, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "anch'io", "start": 173.899, "end": 174.239, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 174.239, "end": 174.239, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "vi", "start": 174.239, "end": 174.339, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 174.339, "end": 174.359, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "dica", "start": 174.359, "end": 174.579, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 174.579, "end": 174.599, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "quello", "start": 174.599, "end": 174.799, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 174.799, "end": 174.819, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "che", "start": 174.819, "end": 174.919, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 174.919, "end": 174.999, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "so,", "start": 174.999, "end": 175.299, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 175.299, "end": 175.459, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "perché", "start": 175.459, "end": 175.739, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 175.739, "end": 175.739, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "do<PERSON><PERSON>", "start": 175.739, "end": 176.019, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 176.019, "end": 176.019, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "tacere...", "start": 176.019, "end": 176.699, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 176.699, "end": 178.559, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "Non", "start": 178.559, "end": 178.679, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 178.679, "end": 178.719, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "sono", "start": 178.719, "end": 178.859, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 178.859, "end": 178.899, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "mai", "start": 178.899, "end": 179.019, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 179.019, "end": 179.039, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "stato", "start": 179.039, "end": 179.259, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 179.259, "end": 179.259, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "un", "start": 179.259, "end": 179.339, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 179.339, "end": 179.339, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "tipo", "start": 179.339, "end": 179.559, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 179.559, "end": 179.559, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "fortunato,", "start": 179.559, "end": 180.079, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 180.079, "end": 180.099, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "io.", "start": 180.099, "end": 180.319, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 180.319, "end": 180.379, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 180.379, "end": 180.639, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 180.639, "end": 180.639, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "e", "start": 180.639, "end": 180.719, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 180.719, "end": 180.719, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "madre,", "start": 180.719, "end": 181.119, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 181.119, "end": 181.179, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "si", "start": 181.179, "end": 181.279, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 181.279, "end": 181.319, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 181.319, "end": 181.439, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 181.439, "end": 181.459, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "dire", "start": 181.459, "end": 181.619, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 181.619, "end": 181.639, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "che", "start": 181.639, "end": 181.699, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 181.699, "end": 181.719, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "non", "start": 181.719, "end": 181.819, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 181.819, "end": 181.819, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "li", "start": 181.819, "end": 181.919, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 181.919, "end": 181.919, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "ho", "start": 181.919, "end": 181.979, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 181.979, "end": 181.979, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "neanche", "start": 181.979, "end": 182.239, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 182.239, "end": 182.279, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "con<PERSON><PERSON><PERSON>.", "start": 182.279, "end": 182.879, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 182.879, "end": 183.499, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "<PERSON>", "start": 183.499, "end": 183.579, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 183.579, "end": 183.579, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 183.579, "end": 183.999, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 183.999, "end": 184.019, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "una", "start": 184.019, "end": 184.119, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 184.119, "end": 184.159, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "bottega", "start": 184.159, "end": 184.519, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 184.519, "end": 184.539, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "e", "start": 184.539, "end": 184.559, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 184.559, "end": 184.559, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "se", "start": 184.559, "end": 184.639, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 184.639, "end": 184.659, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "la", "start": 184.659, "end": 184.739, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 184.739, "end": 184.739, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "presero", "start": 184.739, "end": 184.979, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 184.979, "end": 184.979, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "i", "start": 184.979, "end": 185.039, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 185.039, "end": 185.039, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "creditori.", "start": 185.039, "end": 185.599, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 185.599, "end": 186.379, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 186.379, "end": 186.619, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 186.619, "end": 186.619, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "mi", "start": 186.619, "end": 186.679, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 186.679, "end": 186.719, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "misi", "start": 186.719, "end": 186.879, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 186.879, "end": 186.879, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "a", "start": 186.879, "end": 186.959, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 186.959, "end": 186.959, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "fare", "start": 186.959, "end": 187.099, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 187.099, "end": 187.099, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "il", "start": 187.099, "end": 187.179, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 187.179, "end": 187.199, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "pescatore,", "start": 187.199, "end": 187.679, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 187.679, "end": 187.699, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "ma", "start": 187.699, "end": 187.839, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 187.839, "end": 188.039, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "è", "start": 188.039, "end": 188.179, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 188.179, "end": 188.179, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "troppo", "start": 188.179, "end": 188.439, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 188.439, "end": 188.439, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "faticoso,", "start": 188.439, "end": 188.979, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 188.979, "end": 189.419, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "e", "start": 189.419, "end": 189.519, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 189.519, "end": 189.539, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "oggi", "start": 189.539, "end": 189.739, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 189.739, "end": 189.759, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "non", "start": 189.759, "end": 189.839, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 189.839, "end": 189.879, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "si", "start": 189.879, "end": 189.959, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 189.959, "end": 189.979, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "guadagna", "start": 189.979, "end": 190.279, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 190.279, "end": 190.299, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "più", "start": 190.299, "end": 190.419, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 190.419, "end": 190.439, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "niente.", "start": 190.439, "end": 190.879, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 190.879, "end": 192.239, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "Così", "start": 192.239, "end": 192.479, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 192.479, "end": 192.479, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "preferivo", "start": 192.479, "end": 192.819, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 192.819, "end": 192.839, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "start": 192.839, "end": 193.239, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 193.239, "end": 193.239, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "in", "start": 193.239, "end": 193.319, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 193.319, "end": 193.339, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "qualsia<PERSON>", "start": 193.339, "end": 193.679, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 193.679, "end": 193.679, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "altro", "start": 193.679, "end": 193.939, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 193.939, "end": 193.959, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "modo.", "start": 193.959, "end": 194.259, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 194.259, "end": 194.879, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "<PERSON>", "start": 194.879, "end": 194.959, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 194.959, "end": 194.979, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "misi", "start": 194.979, "end": 195.139, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 195.139, "end": 195.139, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "a", "start": 195.139, "end": 195.219, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 195.219, "end": 195.219, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "portare", "start": 195.219, "end": 195.499, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 195.499, "end": 195.499, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "le", "start": 195.499, "end": 195.599, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 195.599, "end": 195.619, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "valigie", "start": 195.619, "end": 195.859, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 195.859, "end": 195.859, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "a", "start": 195.859, "end": 195.939, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 195.939, "end": 195.939, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "quelli", "start": 195.939, "end": 196.099, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 196.099, "end": 196.099, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "che", "start": 196.099, "end": 196.199, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 196.199, "end": 196.239, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "venivano", "start": 196.239, "end": 196.579, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 196.579, "end": 196.579, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "nell'isola.", "start": 196.579, "end": 197.139, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 197.139, "end": 198.159, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 198.159, "end": 198.699, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 198.699, "end": 198.739, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 198.739, "end": 199.359, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 199.359, "end": 199.379, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "qualche", "start": 199.379, "end": 199.639, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 199.639, "end": 199.659, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "straniera", "start": 199.659, "end": 200.219, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 200.219, "end": 200.779, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "ric<PERSON>,", "start": 200.779, "end": 201.399, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 201.399, "end": 201.619, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "perché", "start": 201.619, "end": 201.779, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 201.779, "end": 201.799, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "ne", "start": 201.799, "end": 201.879, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 201.879, "end": 201.899, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "vengono", "start": 201.899, "end": 202.259, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 202.259, "end": 202.259, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "qui,", "start": 202.259, "end": 202.459, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 202.459, "end": 202.459, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "sapete.", "start": 202.459, "end": 202.899, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 202.899, "end": 203.439, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "Pescano", "start": 203.439, "end": 203.759, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 203.759, "end": 203.759, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "i", "start": 203.759, "end": 203.779, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 203.779, "end": 203.839, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "pesci", "start": 203.839, "end": 204.059, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 204.059, "end": 204.079, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "col", "start": 204.079, "end": 204.219, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 204.219, "end": 204.239, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "fucile.", "start": 204.239, "end": 204.699, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 204.699, "end": 206.499, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "Carmel<PERSON>", "start": 206.499, "end": 206.919, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 206.919, "end": 206.919, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "era", "start": 206.919, "end": 207.059, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 207.059, "end": 207.079, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "l'unica", "start": 207.079, "end": 207.379, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 207.379, "end": 207.399, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "che", "start": 207.399, "end": 207.479, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 207.479, "end": 207.499, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "mi", "start": 207.499, "end": 207.599, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 207.599, "end": 207.599, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "volesse", "start": 207.599, "end": 207.899, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 207.899, "end": 207.939, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "bene.", "start": 207.939, "end": 208.259, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 208.259, "end": 208.999, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "Ma", "start": 208.999, "end": 209.099, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 209.099, "end": 209.099, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "<PERSON>eva", "start": 209.099, "end": 209.319, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 209.319, "end": 209.319, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "che", "start": 209.319, "end": 209.419, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 209.419, "end": 209.439, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "mi", "start": 209.439, "end": 209.519, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 209.519, "end": 209.519, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 209.519, "end": 209.799, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 209.799, "end": 209.799, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "a", "start": 209.799, "end": 209.859, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 209.859, "end": 209.879, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "lavorare", "start": 209.879, "end": 210.199, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 210.199, "end": 210.199, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "con", "start": 210.199, "end": 210.319, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 210.319, "end": 210.339, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "Rosario.", "start": 210.339, "end": 210.879, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 210.879, "end": 212.259, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "E", "start": 212.259, "end": 212.399, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 212.399, "end": 212.399, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "le", "start": 212.399, "end": 212.499, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 212.499, "end": 212.519, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "sue", "start": 212.519, "end": 212.659, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 212.659, "end": 212.659, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "insistenze", "start": 212.659, "end": 213.219, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 213.219, "end": 213.219, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "mi", "start": 213.219, "end": 213.319, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 213.319, "end": 213.339, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "annoiavano.", "start": 213.339, "end": 213.959, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 213.959, "end": 214.279, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 214.279, "end": 214.419, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 214.419, "end": 214.459, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "fratello", "start": 214.459, "end": 214.739, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 214.739, "end": 214.739, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "poi", "start": 214.739, "end": 214.879, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 214.879, "end": 214.879, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "mi", "start": 214.879, "end": 214.979, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 214.979, "end": 214.979, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "era", "start": 214.979, "end": 215.059, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 215.059, "end": 215.059, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "antipatico", "start": 215.059, "end": 215.619, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 215.619, "end": 215.639, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "con", "start": 215.639, "end": 215.739, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 215.739, "end": 215.739, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "le", "start": 215.739, "end": 215.839, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 215.839, "end": 215.859, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "sue", "start": 215.859, "end": 215.979, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 215.979, "end": 215.979, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "arie", "start": 215.979, "end": 216.139, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 216.139, "end": 216.139, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "da", "start": 216.139, "end": 216.259, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 216.259, "end": 216.259, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "protettore", "start": 216.259, "end": 216.739, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 216.739, "end": 216.739, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "dei", "start": 216.739, "end": 216.839, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 216.839, "end": 216.879, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "poveri.", "start": 216.879, "end": 217.279, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 217.279, "end": 218.319, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "E", "start": 218.319, "end": 218.399, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 218.399, "end": 218.419, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "intanto", "start": 218.419, "end": 218.739, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 218.739, "end": 218.759, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "filava", "start": 218.759, "end": 219.059, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 219.059, "end": 219.059, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "con", "start": 219.059, "end": 219.179, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 219.179, "end": 219.179, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "la", "start": 219.179, "end": 219.259, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 219.259, "end": 219.299, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "figlia", "start": 219.299, "end": 219.479, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 219.479, "end": 219.499, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "del", "start": 219.499, "end": 219.619, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 219.619, "end": 219.639, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "padrone.", "start": 219.639, "end": 220.059, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 220.059, "end": 220.239, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "Tu<PERSON>", "start": 220.239, "end": 220.639, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 220.639, "end": 220.659, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "gente", "start": 220.659, "end": 220.979, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 220.979, "end": 221.019, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "schi<PERSON><PERSON>", "start": 221.019, "end": 221.519, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 221.519, "end": 221.519, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "in", "start": 221.519, "end": 221.599, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 221.599, "end": 221.599, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "quest'isola.", "start": 221.599, "end": 222.159, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 222.159, "end": 222.439, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 222.439, "end": 222.799, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 222.799, "end": 222.799, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "non", "start": 222.799, "end": 222.919, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 222.919, "end": 222.919, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "ci", "start": 222.919, "end": 223.019, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 223.019, "end": 223.039, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "fossi", "start": 223.039, "end": 223.259, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 223.259, "end": 223.259, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "mai", "start": 223.259, "end": 223.379, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 223.379, "end": 223.379, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "nato.", "start": 223.379, "end": 223.839, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 223.839, "end": 223.859, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "Non", "start": 223.859, "end": 225.239, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 225.239, "end": 225.259, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "ti", "start": 225.259, "end": 225.399, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 225.399, "end": 225.399, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "fare", "start": 225.399, "end": 225.659, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 225.659, "end": 225.659, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "illusioni.", "start": 225.659, "end": 226.419, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 226.419, "end": 227.099, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "T<PERSON><PERSON>", "start": 227.099, "end": 227.359, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 227.359, "end": 227.359, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "come", "start": 227.359, "end": 227.579, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 227.579, "end": 227.619, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "te", "start": 227.619, "end": 227.759, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 227.759, "end": 227.759, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "stanno", "start": 227.759, "end": 228.079, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 228.079, "end": 228.079, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "male", "start": 228.079, "end": 228.399, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 228.399, "end": 228.399, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "anche", "start": 228.399, "end": 228.639, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 228.639, "end": 228.639, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "in", "start": 228.639, "end": 228.759, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 228.759, "end": 228.779, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "città.", "start": 228.779, "end": 230.459, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 230.459, "end": 230.459, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "Lo", "start": 230.459, "end": 230.639, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 230.639, "end": 230.639, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "vedr<PERSON>", "start": 230.639, "end": 230.899, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 230.899, "end": 230.899, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "presto", "start": 230.899, "end": 231.279, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 231.279, "end": 231.279, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "se", "start": 231.279, "end": 231.399, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 231.399, "end": 231.399, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "in", "start": 231.399, "end": 231.479, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 231.479, "end": 231.479, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "città", "start": 231.479, "end": 231.739, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 231.739, "end": 231.739, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "si", "start": 231.739, "end": 231.879, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 231.879, "end": 231.879, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "sta", "start": 231.879, "end": 232.039, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 232.039, "end": 232.039, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "male", "start": 232.039, "end": 232.239, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 232.239, "end": 232.239, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "come", "start": 232.239, "end": 232.419, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 232.419, "end": 232.439, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "dite.", "start": 232.439, "end": 232.819, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 232.819, "end": 234.739, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "Me", "start": 234.739, "end": 234.819, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 234.819, "end": 234.819, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "ne", "start": 234.819, "end": 234.899, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 234.899, "end": 234.899, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "voglio", "start": 234.899, "end": 235.059, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 235.059, "end": 235.059, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "andare", "start": 235.059, "end": 235.259, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 235.259, "end": 235.259, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "a", "start": 235.259, "end": 235.319, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 235.319, "end": 235.339, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "Milano.", "start": 235.339, "end": 235.779, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 235.779, "end": 236.239, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "Las<PERSON>ù", "start": 236.239, "end": 236.479, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 236.479, "end": 236.499, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "ne", "start": 236.499, "end": 236.619, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 236.619, "end": 236.639, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "girano", "start": 236.639, "end": 236.939, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 236.939, "end": 236.939, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "di", "start": 236.939, "end": 237.039, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 237.039, "end": 237.059, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "soldi.", "start": 237.059, "end": 238.179, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 238.179, "end": 238.179, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "<PERSON>", "start": 238.179, "end": 238.339, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 238.339, "end": 238.379, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "ti", "start": 238.379, "end": 238.479, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 238.479, "end": 238.499, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "ha", "start": 238.499, "end": 238.579, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 238.579, "end": 238.599, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "messo", "start": 238.599, "end": 238.919, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 238.919, "end": 238.919, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "in", "start": 238.919, "end": 239.059, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 239.059, "end": 239.059, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "testa", "start": 239.059, "end": 239.399, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 239.399, "end": 239.419, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "quest'idea?", "start": 239.419, "end": 240.579, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 240.579, "end": 240.579, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "<PERSON><PERSON>,", "start": 240.579, "end": 240.899, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 240.899, "end": 241.299, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "<PERSON>", "start": 241.299, "end": 241.679, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 241.679, "end": 241.719, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "ci", "start": 241.719, "end": 241.839, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 241.839, "end": 241.839, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "si", "start": 241.839, "end": 241.919, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 241.919, "end": 241.919, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "è", "start": 241.919, "end": 241.979, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 241.979, "end": 241.979, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "trovata", "start": 241.979, "end": 242.299, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 242.299, "end": 242.299, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "bene,", "start": 242.299, "end": 242.519, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 242.519, "end": 242.539, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "lei.", "start": 242.539, "end": 244.179, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 244.179, "end": 244.179, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "Non", "start": 244.179, "end": 244.459, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 244.459, "end": 244.539, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "tanto", "start": 244.539, "end": 244.859, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 244.859, "end": 244.879, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "da", "start": 244.879, "end": 244.979, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 244.979, "end": 244.999, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 244.999, "end": 245.599, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 245.599, "end": 245.599, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "se", "start": 245.599, "end": 245.739, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 245.739, "end": 245.739, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "è", "start": 245.739, "end": 245.839, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 245.839, "end": 245.859, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "finita", "start": 245.859, "end": 246.199, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 246.199, "end": 246.199, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "in", "start": 246.199, "end": 246.299, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 246.299, "end": 246.319, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "quest'isola.", "start": 246.319, "end": 247.099, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 247.099, "end": 247.659, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "Eh", "start": 247.659, "end": 247.819, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 247.819, "end": 247.819, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "già.", "start": 247.819, "end": 248.179, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 248.179, "end": 249.959, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "Avete", "start": 249.959, "end": 250.239, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 250.239, "end": 250.259, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "ragione.", "start": 250.259, "end": 250.759, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 250.759, "end": 251.119, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 251.119, "end": 251.499, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 251.499, "end": 251.519, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "non", "start": 251.519, "end": 251.639, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 251.639, "end": 251.659, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "era", "start": 251.659, "end": 251.799, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 251.799, "end": 251.799, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "vero", "start": 251.799, "end": 251.979, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 251.979, "end": 251.999, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "niente.", "start": 251.999, "end": 252.479, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 252.479, "end": 253.959, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "Fin", "start": 253.959, "end": 254.159, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 254.159, "end": 254.159, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "dal", "start": 254.159, "end": 254.299, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 254.299, "end": 254.319, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "primo", "start": 254.319, "end": 254.479, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 254.479, "end": 254.499, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "incontro", "start": 254.499, "end": 254.879, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 254.879, "end": 254.879, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "mi", "start": 254.879, "end": 254.979, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 254.979, "end": 254.979, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "trat<PERSON><PERSON>", "start": 254.979, "end": 255.299, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 255.299, "end": 255.299, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "come", "start": 255.299, "end": 255.459, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 255.459, "end": 255.459, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "un", "start": 255.459, "end": 255.539, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 255.539, "end": 255.539, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "ragazzino.", "start": 255.539, "end": 256.139, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 256.139, "end": 257.019, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "E", "start": 257.019, "end": 257.159, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 257.159, "end": 257.159, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "io", "start": 257.159, "end": 257.259, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 257.259, "end": 257.279, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "lo", "start": 257.279, "end": 257.359, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 257.359, "end": 257.379, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "sapevo,", "start": 257.379, "end": 257.759, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 257.759, "end": 257.759, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "ma", "start": 257.759, "end": 257.879, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 257.879, "end": 257.899, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "facevo", "start": 257.899, "end": 258.199, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 258.199, "end": 258.219, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "finta", "start": 258.219, "end": 258.479, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 258.479, "end": 258.479, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "di", "start": 258.479, "end": 258.559, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 258.559, "end": 258.579, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "nulla,", "start": 258.579, "end": 258.919, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 258.919, "end": 258.939, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "mi", "start": 258.939, "end": 259.079, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 259.079, "end": 259.099, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 259.099, "end": 259.479, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 259.479, "end": 259.499, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "molto.", "start": 259.499, "end": 259.979, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 259.979, "end": 260.679, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "Passava", "start": 260.679, "end": 261.039, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 261.039, "end": 261.039, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "le", "start": 261.039, "end": 261.159, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 261.159, "end": 261.179, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "giornate", "start": 261.179, "end": 261.539, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 261.539, "end": 261.539, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "chiuse", "start": 261.539, "end": 261.779, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 261.779, "end": 261.799, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "in", "start": 261.799, "end": 261.879, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 261.879, "end": 261.919, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "casa,", "start": 261.919, "end": 262.239, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 262.239, "end": 262.499, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "a", "start": 262.499, "end": 262.599, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 262.599, "end": 262.599, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "l<PERSON><PERSON><PERSON><PERSON>", "start": 262.599, "end": 263.119, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 263.119, "end": 263.159, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "come", "start": 263.159, "end": 263.279, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 263.279, "end": 263.279, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "una", "start": 263.279, "end": 263.419, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 263.419, "end": 263.439, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "gatta.", "start": 263.439, "end": 263.819, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 263.819, "end": 264.959, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "Sono", "start": 264.959, "end": 265.159, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 265.159, "end": 265.199, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "l'unico", "start": 265.199, "end": 265.479, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 265.479, "end": 265.519, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "qui", "start": 265.519, "end": 265.659, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 265.659, "end": 265.679, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "ad", "start": 265.679, "end": 265.759, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 265.759, "end": 265.759, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "avere", "start": 265.759, "end": 265.979, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 265.979, "end": 265.999, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "del", "start": 265.999, "end": 266.119, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 266.119, "end": 266.119, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "tempo", "start": 266.119, "end": 266.379, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 266.379, "end": 266.399, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "libero.", "start": 266.399, "end": 266.719, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 266.719, "end": 267.279, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 267.279, "end": 267.639, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 267.639, "end": 267.659, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "mi", "start": 267.659, "end": 267.739, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 267.739, "end": 267.739, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "comanda.", "start": 267.739, "end": 268.259, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 268.259, "end": 269.019, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "E", "start": 269.019, "end": 269.119, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 269.119, "end": 269.159, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "così", "start": 269.159, "end": 269.419, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 269.419, "end": 269.459, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "andavo", "start": 269.459, "end": 269.799, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 269.799, "end": 269.819, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "sotto", "start": 269.819, "end": 270.019, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 270.019, "end": 270.039, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "le", "start": 270.039, "end": 270.119, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 270.119, "end": 270.139, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "sue", "start": 270.139, "end": 270.279, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 270.279, "end": 270.299, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "finestre", "start": 270.299, "end": 270.699, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 270.699, "end": 270.759, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "a", "start": 270.759, "end": 270.859, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 270.859, "end": 270.859, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "su<PERSON><PERSON>", "start": 270.859, "end": 271.179, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 271.179, "end": 271.199, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "la", "start": 271.199, "end": 271.259, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 271.259, "end": 271.299, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "chitarra.", "start": 271.299, "end": 271.839, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 271.839, "end": 272.539, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "E", "start": 272.539, "end": 272.639, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 272.639, "end": 272.679, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "aspettavo.", "start": 272.679, "end": 273.419, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 273.419, "end": 274.699, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "Sapevo", "start": 274.699, "end": 275.079, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 275.079, "end": 275.119, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "che", "start": 275.119, "end": 275.199, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 275.199, "end": 275.219, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "lei", "start": 275.219, "end": 275.339, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 275.339, "end": 275.359, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "mi", "start": 275.359, "end": 275.459, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 275.459, "end": 275.459, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "guardava.", "start": 275.459, "end": 275.979, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}], "language_detected": null, "confidence": null, "processing_time": 25.032063484191895, "metadata": {"service": "elevenlabs", "mode": "free", "model": "scribe_v1", "raw_result": {"language_code": "ita", "language_probability": 0.9756278395652771, "text": "<PERSON>h? Me la dai la cicca? Non ti vergogni a fumare così, picco<PERSON>? E tu non gli dici niente, giu<PERSON><PERSON>? Io non fumo. La birra è diventata calda, ormai. Sono peggio delle mosche, qui, i ragazzini. È un'ora che gli dico d'andar via e non se ne vanno. Ma non ce l'hanno, padre e madre. Beh, come è andata? Ho controllato la risposta degli interrogati. Fosse per me, li arresterei tutti. <PERSON><PERSON>, per rimetterli fuori subito per mancanza di prove. L'affare è molto più ingarbugliato di quanto non sembrava. Quella donna era riuscita a farsi odiare da tutti. Il negro Abul, strano tipo di provenienza molto incerta, muore nell'incendio di una baracca mentre probabilmente sta cercando di assassinare una donna. Una ragazza di sedici anni viene trovata impiccata. Dicono che si uccise per amore, ma nulla lo dimostra. <PERSON>, una bellissima donna. <PERSON><PERSON> intelligente, per<PERSON>. Ma che parlate a fare di loro? Mica posso mettere le manette ai morti. I vivi, allora. Rosario Albanese, <PERSON> Ingarcia e suo padre, sono tre anche loro. Ne dimenticate uno. Chi? Francesco, il fidanzato di Carmela Albanese. Ordina un'altra birra e aspettami qui. Francesco! Tu sei Francesco Greco, vero? E con questo? Sai chi sono io? Sì, lo so, ma non mi interessa. E allora perché stai scappando? Io? Ma voi sognate. Meglio così. Avremo il tempo di fare due chiacchiere insieme, eh? Posso accompagnarti? Se ci tenete, ma io non vado in un posto allegro. Oh, neanche io ho voglia di divertirmi. Ci sono due morti là sotto. Carmela e il figlio che mi doveva nascere. Ah sì, avete ragione voi. È bene che anch'io vi dica quello che so, perché dovrei tacere... Non sono mai stato un tipo fortunato, io. Padre e madre, si può dire che non li ho neanche conosciuti. Mi lasciarono una bottega e se la presero i creditori. Allora mi misi a fare il pescatore, ma è troppo faticoso, e oggi non si guadagna più niente. Così preferivo arrangiarmi in qualsiasi altro modo. Mi misi a portare le valigie a quelli che venivano nell'isola. Vivevo aspettando qualche straniera ricca, perché ne vengono qui, sapete. Pescano i pesci col fucile. Carmela era l'unica che mi volesse bene. Ma voleva che mi mettessi a lavorare con Rosario. E le sue insistenze mi annoiavano. Suo fratello poi mi era antipatico con le sue arie da protettore dei poveri. E intanto filava con la figlia del padrone. Tutta gente schifosa in quest'isola. Magari non ci fossi mai nato. Non ti fare illusioni. Tipi come te stanno male anche in città. Lo vedrò presto se in città si sta male come dite. Me ne voglio andare a Milano. Lassù ne girano di soldi. Chi ti ha messo in testa quest'idea? Beh, Carla ci si è trovata bene, lei. Non tanto da rimanersi se è finita in quest'isola. Eh già. Avete ragione. Forse non era vero niente. Fin dal primo incontro mi trattò come un ragazzino. E io lo sapevo, ma facevo finta di nulla, mi piaceva molto. Passava le giornate chiuse in casa, a lisciarsi come una gatta. Sono l'unico qui ad avere del tempo libero. Nessuno mi comanda. E così andavo sotto le sue finestre a suonare la chitarra. E aspettavo. Sapevo che lei mi guardava.", "words": [{"text": "Beh?", "start": 12.739, "end": 13.06, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 13.06, "end": 13.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Me", "start": 13.739, "end": 13.84, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 13.84, "end": 13.859, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "la", "start": 13.859, "end": 13.96, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 13.96, "end": 13.979, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "dai", "start": 13.979, "end": 14.139, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 14.139, "end": 14.139, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "la", "start": 14.139, "end": 14.239, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 14.239, "end": 14.299, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "cicca?", "start": 14.299, "end": 14.719, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 14.719, "end": 14.759, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Non", "start": 14.759, "end": 14.859, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 14.859, "end": 14.88, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ti", "start": 14.88, "end": 14.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 14.979, "end": 15.0, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "vergo<PERSON>", "start": 15.0, "end": 15.359, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 15.359, "end": 15.42, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "a", "start": 15.42, "end": 15.44, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 15.44, "end": 15.44, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "fumare", "start": 15.44, "end": 15.739, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 15.739, "end": 15.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "così,", "start": 15.739, "end": 16.02, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 16.02, "end": 16.079, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "piccolo?", "start": 16.079, "end": 16.52, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 16.52, "end": 16.76, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "E", "start": 16.76, "end": 16.859, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 16.859, "end": 16.859, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "tu", "start": 16.859, "end": 16.959, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 16.959, "end": 16.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "non", "start": 16.979, "end": 17.079, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 17.079, "end": 17.079, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "gli", "start": 17.079, "end": 17.179, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 17.179, "end": 17.199, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "dici", "start": 17.199, "end": 17.399, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 17.399, "end": 17.42, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "niente,", "start": 17.42, "end": 17.7, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 17.7, "end": 17.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "giuggiolone?", "start": 17.799, "end": 18.279, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 18.279, "end": 18.279, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Io", "start": 18.279, "end": 18.299, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 18.299, "end": 18.299, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "non", "start": 18.299, "end": 18.399, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 18.399, "end": 18.459, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "fumo.", "start": 18.459, "end": 18.799, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 18.799, "end": 22.719, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "La", "start": 22.719, "end": 22.839, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 22.839, "end": 22.859, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "birra", "start": 22.859, "end": 23.059, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 23.059, "end": 23.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "è", "start": 23.119, "end": 23.179, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 23.179, "end": 23.26, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "diventata", "start": 23.26, "end": 23.68, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 23.68, "end": 23.68, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "calda,", "start": 23.68, "end": 23.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 23.979, "end": 24.0, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ormai.", "start": 24.0, "end": 24.459, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 24.459, "end": 24.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Sono", "start": 24.979, "end": 25.18, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 25.18, "end": 25.219, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "peggio", "start": 25.219, "end": 25.459, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 25.459, "end": 25.459, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "delle", "start": 25.459, "end": 25.619, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 25.619, "end": 25.659, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mosche,", "start": 25.659, "end": 25.939, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 25.939, "end": 25.939, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "qui,", "start": 25.939, "end": 26.079, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 26.079, "end": 26.079, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "i", "start": 26.079, "end": 26.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 26.159, "end": 26.159, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ragazzini.", "start": 26.159, "end": 26.699, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 26.699, "end": 26.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "È", "start": 26.819, "end": 27.239, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 27.239, "end": 27.239, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "un'ora", "start": 27.239, "end": 27.499, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 27.499, "end": 27.5, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "che", "start": 27.5, "end": 27.579, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 27.579, "end": 27.579, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "gli", "start": 27.579, "end": 27.68, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 27.68, "end": 27.699, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "dico", "start": 27.699, "end": 27.859, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 27.859, "end": 27.859, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "d'andar", "start": 27.859, "end": 28.119, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 28.119, "end": 28.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "via", "start": 28.119, "end": 28.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 28.299, "end": 28.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "e", "start": 28.299, "end": 28.359, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 28.359, "end": 28.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "non", "start": 28.479, "end": 28.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 28.559, "end": 28.599, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "se", "start": 28.599, "end": 28.659, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 28.659, "end": 28.68, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ne", "start": 28.68, "end": 28.779, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 28.779, "end": 28.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "vanno.", "start": 28.799, "end": 29.119, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 29.119, "end": 29.399, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Ma", "start": 29.399, "end": 29.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 29.479, "end": 29.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "non", "start": 29.479, "end": 29.599, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 29.599, "end": 29.599, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ce", "start": 29.599, "end": 29.679, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 29.679, "end": 29.679, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "l'hanno,", "start": 29.679, "end": 29.919, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 29.919, "end": 29.92, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "padre", "start": 29.92, "end": 30.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 30.139, "end": 30.159, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "e", "start": 30.159, "end": 30.18, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 30.18, "end": 30.239, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "madre.", "start": 30.239, "end": 30.579, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 30.579, "end": 30.899, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON>,", "start": 30.899, "end": 31.259, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 31.259, "end": 31.26, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "come", "start": 31.26, "end": 31.42, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 31.42, "end": 31.459, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "è", "start": 31.459, "end": 31.5, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 31.5, "end": 31.5, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "andata?", "start": 31.5, "end": 32.04, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 32.04, "end": 32.04, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "<PERSON>", "start": 32.04, "end": 32.88, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 32.88, "end": 32.899, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "controllato", "start": 32.899, "end": 33.459, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 33.459, "end": 33.479, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "la", "start": 33.479, "end": 33.579, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 33.579, "end": 33.579, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "risposta", "start": 33.579, "end": 34.0, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 34.0, "end": 34.02, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "<PERSON>gli", "start": 34.02, "end": 34.18, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 34.18, "end": 34.2, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "interrogati.", "start": 34.2, "end": 34.94, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 34.94, "end": 35.139, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "Fosse", "start": 35.139, "end": 35.42, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 35.42, "end": 35.459, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "per", "start": 35.459, "end": 35.6, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 35.6, "end": 35.639, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "me,", "start": 35.639, "end": 35.899, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 35.899, "end": 36.239, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "li", "start": 36.239, "end": 36.34, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 36.34, "end": 36.36, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "arresterei", "start": 36.36, "end": 36.819, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 36.819, "end": 36.86, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "tutti.", "start": 36.86, "end": 37.259, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 37.259, "end": 37.68, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "Sì,", "start": 37.68, "end": 38.1, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 38.1, "end": 38.36, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "per", "start": 38.36, "end": 38.479, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 38.479, "end": 38.479, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 38.479, "end": 38.919, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 38.919, "end": 38.959, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "fuori", "start": 38.959, "end": 39.24, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 39.24, "end": 39.34, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "subito", "start": 39.34, "end": 39.68, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 39.68, "end": 39.7, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "per", "start": 39.7, "end": 39.84, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 39.84, "end": 39.86, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "mancanza", "start": 39.86, "end": 40.279, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 40.279, "end": 40.299, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "di", "start": 40.299, "end": 40.38, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 40.38, "end": 40.419, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "prove.", "start": 40.419, "end": 40.88, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 40.88, "end": 42.299, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "L'affare", "start": 42.299, "end": 42.779, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 42.779, "end": 42.779, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "è", "start": 42.779, "end": 42.879, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 42.879, "end": 42.879, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "molto", "start": 42.879, "end": 43.159, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 43.159, "end": 43.239, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "più", "start": 43.239, "end": 43.36, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 43.36, "end": 43.36, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "ingarbugliato", "start": 43.36, "end": 43.979, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 43.979, "end": 44.0, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "di", "start": 44.0, "end": 44.08, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 44.08, "end": 44.119, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "quanto", "start": 44.119, "end": 44.36, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 44.36, "end": 44.379, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "non", "start": 44.379, "end": 44.479, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 44.479, "end": 44.52, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "sembrava.", "start": 44.52, "end": 45.079, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 45.079, "end": 45.719, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 45.719, "end": 45.979, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 45.979, "end": 46.039, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "donna", "start": 46.039, "end": 46.399, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 46.399, "end": 46.919, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "era", "start": 46.919, "end": 47.199, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 47.199, "end": 47.219, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "r<PERSON><PERSON><PERSON>", "start": 47.219, "end": 47.639, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 47.639, "end": 47.639, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "a", "start": 47.639, "end": 47.739, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 47.739, "end": 47.759, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "farsi", "start": 47.759, "end": 48.119, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 48.119, "end": 48.139, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "odiare", "start": 48.139, "end": 48.5, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 48.5, "end": 48.52, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "da", "start": 48.52, "end": 48.619, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 48.619, "end": 48.68, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "tutti.", "start": 48.68, "end": 49.1, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 49.1, "end": 53.219, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "Il", "start": 53.219, "end": 53.7, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 53.7, "end": 53.819, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "negro", "start": 53.819, "end": 54.119, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 54.119, "end": 54.239, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "Abul,", "start": 54.239, "end": 54.939, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 54.939, "end": 55.0, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "strano", "start": 55.0, "end": 55.459, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 55.459, "end": 55.5, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "tipo", "start": 55.5, "end": 55.74, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 55.74, "end": 55.759, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "di", "start": 55.759, "end": 55.84, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 55.84, "end": 55.899, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "proven<PERSON>za", "start": 55.899, "end": 56.459, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 56.459, "end": 56.5, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "molto", "start": 56.5, "end": 56.799, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 56.799, "end": 56.799, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "incerta,", "start": 56.799, "end": 57.46, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 57.46, "end": 57.599, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "muore", "start": 57.599, "end": 57.86, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 57.86, "end": 57.879, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "nell'incendio", "start": 57.879, "end": 58.459, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 58.459, "end": 58.459, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "di", "start": 58.459, "end": 58.54, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 58.54, "end": 58.559, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "una", "start": 58.559, "end": 58.68, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 58.68, "end": 58.739, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "baracca", "start": 58.739, "end": 59.119, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 59.119, "end": 59.519, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "mentre", "start": 59.519, "end": 59.819, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 59.819, "end": 59.839, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "probabilmente", "start": 59.839, "end": 60.419, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 60.419, "end": 60.419, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "sta", "start": 60.419, "end": 60.659, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 60.659, "end": 60.659, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "cercando", "start": 60.659, "end": 61.019, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 61.019, "end": 61.039, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "di", "start": 61.039, "end": 61.119, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 61.119, "end": 61.119, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "assassinare", "start": 61.119, "end": 61.779, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 61.779, "end": 61.799, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "una", "start": 61.799, "end": 62.019, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 62.019, "end": 62.059, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "donna.", "start": 62.059, "end": 62.439, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 62.439, "end": 63.879, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "Una", "start": 63.879, "end": 64.019, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 64.019, "end": 64.059, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "ragazza", "start": 64.059, "end": 64.399, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 64.399, "end": 64.419, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "di", "start": 64.419, "end": 64.499, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 64.499, "end": 64.519, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "sedici", "start": 64.519, "end": 64.839, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 64.839, "end": 64.879, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "anni", "start": 64.879, "end": 65.119, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 65.119, "end": 65.639, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "viene", "start": 65.639, "end": 65.919, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 65.919, "end": 65.959, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "trovata", "start": 65.959, "end": 66.379, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 66.379, "end": 66.399, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "impiccata.", "start": 66.399, "end": 67.259, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 67.259, "end": 67.799, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "Dicono", "start": 67.799, "end": 68.099, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 68.099, "end": 68.099, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "che", "start": 68.099, "end": 68.219, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 68.219, "end": 68.259, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "si", "start": 68.259, "end": 68.339, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 68.339, "end": 68.379, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "uccise", "start": 68.379, "end": 68.739, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 68.739, "end": 68.739, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "per", "start": 68.739, "end": 68.919, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 68.919, "end": 68.919, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "amore,", "start": 68.919, "end": 69.379, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 69.379, "end": 70.399, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "ma", "start": 70.399, "end": 70.539, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 70.539, "end": 70.579, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "nulla", "start": 70.579, "end": 70.779, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 70.779, "end": 70.799, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "lo", "start": 70.799, "end": 70.899, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 70.899, "end": 70.919, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "dimostra.", "start": 70.919, "end": 71.559, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 71.559, "end": 75.719, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "<PERSON>", "start": 75.719, "end": 76.119, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 76.119, "end": 76.139, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "Ingarcia,", "start": 76.139, "end": 76.979, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 76.979, "end": 77.599, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "una", "start": 77.599, "end": 77.719, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 77.719, "end": 77.799, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "bellissima", "start": 77.799, "end": 78.419, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 78.419, "end": 78.439, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "donna.", "start": 78.439, "end": 78.859, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 78.859, "end": 79.799, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "Poco", "start": 79.799, "end": 80.039, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 80.039, "end": 80.059, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "intelligente,", "start": 80.059, "end": 80.819, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 80.819, "end": 80.819, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "per<PERSON>.", "start": 80.819, "end": 81.179, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 81.179, "end": 81.699, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "Ma", "start": 81.699, "end": 81.779, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 81.779, "end": 81.779, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "che", "start": 81.779, "end": 81.879, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 81.879, "end": 81.939, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "parlate", "start": 81.939, "end": 82.319, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 82.319, "end": 82.339, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "a", "start": 82.339, "end": 82.399, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 82.399, "end": 82.439, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "fare", "start": 82.439, "end": 82.739, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 82.739, "end": 82.759, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "di", "start": 82.759, "end": 82.839, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 82.839, "end": 82.859, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "loro?", "start": 82.859, "end": 83.199, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 83.199, "end": 83.919, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "Mica", "start": 83.919, "end": 84.179, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 84.179, "end": 84.179, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "posso", "start": 84.179, "end": 84.379, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 84.379, "end": 84.419, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "mettere", "start": 84.419, "end": 84.679, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 84.679, "end": 84.679, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "le", "start": 84.679, "end": 84.759, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 84.759, "end": 84.799, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "manette", "start": 84.799, "end": 85.059, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 85.059, "end": 85.059, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "ai", "start": 85.059, "end": 85.139, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 85.139, "end": 85.159, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "morti.", "start": 85.159, "end": 85.559, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 85.559, "end": 85.759, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "I", "start": 85.759, "end": 85.899, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 85.899, "end": 85.899, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "vivi,", "start": 85.899, "end": 86.259, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 86.259, "end": 86.279, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "allora.", "start": 86.279, "end": 86.659, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 86.659, "end": 87.779, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "Rosario", "start": 87.779, "end": 88.099, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 88.099, "end": 88.099, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "Albanese,", "start": 88.099, "end": 88.699, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 88.699, "end": 89.019, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "<PERSON>", "start": 89.019, "end": 89.279, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 89.279, "end": 89.299, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "Ingarcia", "start": 89.299, "end": 89.859, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 89.859, "end": 90.259, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "e", "start": 90.259, "end": 90.379, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 90.379, "end": 90.379, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "suo", "start": 90.379, "end": 90.559, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 90.559, "end": 90.579, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "padre,", "start": 90.579, "end": 90.979, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 90.979, "end": 91.539, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "sono", "start": 91.539, "end": 91.739, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 91.739, "end": 91.759, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "tre", "start": 91.759, "end": 91.939, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 91.939, "end": 92.059, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "anche", "start": 92.059, "end": 92.279, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 92.279, "end": 92.279, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "loro.", "start": 92.279, "end": 92.639, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 92.639, "end": 93.199, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "Ne", "start": 93.199, "end": 93.299, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 93.299, "end": 93.379, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "dimenticate", "start": 93.379, "end": 94.019, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 94.019, "end": 94.039, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "uno.", "start": 94.039, "end": 94.379, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 94.379, "end": 94.499, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "Chi?", "start": 94.499, "end": 94.859, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 94.859, "end": 98.679, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "<PERSON>,", "start": 98.679, "end": 99.279, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 99.279, "end": 99.759, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "il", "start": 99.759, "end": 99.899, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 99.899, "end": 99.899, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "fi<PERSON><PERSON><PERSON>", "start": 99.899, "end": 100.359, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 100.359, "end": 100.359, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "di", "start": 100.359, "end": 100.439, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 100.439, "end": 100.479, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "Carmel<PERSON>", "start": 100.479, "end": 100.759, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 100.759, "end": 100.779, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "Albanese.", "start": 100.779, "end": 101.359, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 101.359, "end": 101.539, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "Ordina", "start": 101.539, "end": 101.859, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 101.859, "end": 101.859, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "un'altra", "start": 101.859, "end": 102.099, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 102.099, "end": 102.099, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "birra", "start": 102.099, "end": 102.279, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 102.279, "end": 102.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "e", "start": 102.299, "end": 102.359, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 102.359, "end": 102.359, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "as<PERSON><PERSON><PERSON>", "start": 102.359, "end": 102.719, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 102.719, "end": 102.759, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "qui.", "start": 102.759, "end": 102.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 102.979, "end": 114.139, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Francesco!", "start": 114.139, "end": 115.019, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 115.019, "end": 122.679, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Tu", "start": 122.679, "end": 122.799, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 122.799, "end": 122.839, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "sei", "start": 122.839, "end": 123.019, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 123.019, "end": 123.059, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "<PERSON>", "start": 123.059, "end": 123.499, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 123.499, "end": 123.559, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "<PERSON>,", "start": 123.559, "end": 123.879, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 123.879, "end": 123.879, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "vero?", "start": 123.879, "end": 124.179, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 124.179, "end": 124.439, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "E", "start": 124.439, "end": 124.539, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 124.539, "end": 124.539, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "con", "start": 124.539, "end": 124.699, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 124.699, "end": 124.699, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "questo?", "start": 124.699, "end": 125.119, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 125.119, "end": 125.599, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>", "start": 125.599, "end": 125.839, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 125.839, "end": 125.919, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "chi", "start": 125.919, "end": 126.039, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 126.039, "end": 126.079, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "sono", "start": 126.079, "end": 126.259, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 126.259, "end": 126.299, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "io?", "start": 126.299, "end": 126.499, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 126.499, "end": 126.939, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "Sì,", "start": 126.939, "end": 127.099, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 127.099, "end": 127.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "lo", "start": 127.119, "end": 127.199, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 127.199, "end": 127.219, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "so,", "start": 127.219, "end": 127.459, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 127.459, "end": 127.699, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ma", "start": 127.699, "end": 127.779, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 127.779, "end": 127.779, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "non", "start": 127.779, "end": 127.859, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 127.859, "end": 127.859, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mi", "start": 127.859, "end": 127.959, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 127.959, "end": 127.959, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "interessa.", "start": 127.959, "end": 128.459, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 128.459, "end": 128.659, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "E", "start": 128.659, "end": 128.719, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 128.719, "end": 128.719, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "allora", "start": 128.719, "end": 128.939, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 128.939, "end": 128.959, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "perché", "start": 128.959, "end": 129.159, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 129.159, "end": 129.179, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "stai", "start": 129.179, "end": 129.379, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 129.379, "end": 129.379, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "scappando?", "start": 129.379, "end": 129.979, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 129.979, "end": 129.999, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "Io?", "start": 129.999, "end": 130.279, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 130.279, "end": 130.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Ma", "start": 130.639, "end": 130.719, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 130.719, "end": 130.719, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "voi", "start": 130.719, "end": 130.859, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 130.859, "end": 130.879, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sognate.", "start": 130.879, "end": 131.419, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 131.419, "end": 131.879, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 131.879, "end": 132.179, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 132.179, "end": 132.179, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "così.", "start": 132.179, "end": 132.539, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 132.539, "end": 133.219, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 133.219, "end": 133.459, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 133.459, "end": 133.459, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "il", "start": 133.459, "end": 133.559, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 133.559, "end": 133.579, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "tempo", "start": 133.579, "end": 133.779, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 133.779, "end": 133.799, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "di", "start": 133.799, "end": 133.899, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 133.899, "end": 133.919, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "fare", "start": 133.919, "end": 134.099, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 134.099, "end": 134.119, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "due", "start": 134.119, "end": 134.259, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 134.259, "end": 134.319, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "chiac<PERSON>ere", "start": 134.319, "end": 134.719, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 134.719, "end": 134.739, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "insieme,", "start": 134.739, "end": 135.179, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 135.179, "end": 135.179, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "eh?", "start": 135.179, "end": 135.359, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 135.359, "end": 135.839, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "Po<PERSON>", "start": 135.839, "end": 136.039, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 136.039, "end": 136.059, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "accomp<PERSON><PERSON><PERSON>?", "start": 136.059, "end": 136.719, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 136.719, "end": 136.939, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "Se", "start": 136.939, "end": 137.059, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 137.059, "end": 137.099, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ci", "start": 137.099, "end": 137.199, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 137.199, "end": 137.219, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "tenete,", "start": 137.219, "end": 137.679, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 137.679, "end": 138.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ma", "start": 138.119, "end": 138.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 138.299, "end": 138.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "io", "start": 138.299, "end": 138.419, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 138.419, "end": 138.439, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "non", "start": 138.439, "end": 138.519, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 138.519, "end": 138.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "vado", "start": 138.559, "end": 138.739, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 138.739, "end": 138.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "in", "start": 138.739, "end": 138.819, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 138.819, "end": 138.839, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "un", "start": 138.839, "end": 138.879, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 138.879, "end": 138.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "posto", "start": 138.919, "end": 139.119, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 139.119, "end": 139.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "allegro.", "start": 139.119, "end": 139.539, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 139.539, "end": 139.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Oh,", "start": 139.639, "end": 139.819, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 139.819, "end": 139.839, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "neanche", "start": 139.839, "end": 140.139, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 140.139, "end": 140.159, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "io", "start": 140.159, "end": 140.279, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 140.279, "end": 140.299, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "ho", "start": 140.299, "end": 140.319, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 140.319, "end": 140.319, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "voglia", "start": 140.319, "end": 140.519, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 140.519, "end": 140.539, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "di", "start": 140.539, "end": 140.639, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 140.639, "end": 140.639, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "divertirmi.", "start": 140.639, "end": 141.299, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 141.299, "end": 161.019, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "Ci", "start": 161.019, "end": 161.159, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 161.159, "end": 161.199, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "sono", "start": 161.199, "end": 161.399, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 161.399, "end": 161.439, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "due", "start": 161.439, "end": 161.579, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 161.579, "end": 161.619, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "morti", "start": 161.619, "end": 161.939, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 161.939, "end": 161.939, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "là", "start": 161.939, "end": 162.079, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 162.079, "end": 162.099, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "sotto.", "start": 162.099, "end": 162.559, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 162.559, "end": 163.519, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "Carmel<PERSON>", "start": 163.519, "end": 164.019, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 164.019, "end": 164.039, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "e", "start": 164.039, "end": 164.099, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 164.099, "end": 164.119, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "il", "start": 164.119, "end": 164.199, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 164.199, "end": 164.259, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "figlio", "start": 164.259, "end": 164.539, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 164.539, "end": 164.559, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "che", "start": 164.559, "end": 164.639, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 164.639, "end": 164.659, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "mi", "start": 164.659, "end": 164.759, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 164.759, "end": 164.779, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 164.779, "end": 165.079, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 165.079, "end": 165.099, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "nascere.", "start": 165.099, "end": 165.659, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 165.659, "end": 171.419, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "Ah", "start": 171.419, "end": 171.519, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 171.519, "end": 171.579, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "sì,", "start": 171.579, "end": 171.799, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 171.799, "end": 171.819, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "avete", "start": 171.819, "end": 172.039, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 172.039, "end": 172.079, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "ragione", "start": 172.079, "end": 172.339, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 172.339, "end": 172.399, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "voi.", "start": 172.399, "end": 172.819, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 172.819, "end": 173.439, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "È", "start": 173.439, "end": 173.559, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 173.559, "end": 173.579, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "bene", "start": 173.579, "end": 173.799, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 173.799, "end": 173.819, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "che", "start": 173.819, "end": 173.899, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 173.899, "end": 173.899, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "anch'io", "start": 173.899, "end": 174.239, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 174.239, "end": 174.239, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "vi", "start": 174.239, "end": 174.339, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 174.339, "end": 174.359, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "dica", "start": 174.359, "end": 174.579, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 174.579, "end": 174.599, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "quello", "start": 174.599, "end": 174.799, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 174.799, "end": 174.819, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "che", "start": 174.819, "end": 174.919, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 174.919, "end": 174.999, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "so,", "start": 174.999, "end": 175.299, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 175.299, "end": 175.459, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "perché", "start": 175.459, "end": 175.739, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 175.739, "end": 175.739, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "do<PERSON><PERSON>", "start": 175.739, "end": 176.019, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 176.019, "end": 176.019, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "tacere...", "start": 176.019, "end": 176.699, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 176.699, "end": 178.559, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "Non", "start": 178.559, "end": 178.679, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 178.679, "end": 178.719, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "sono", "start": 178.719, "end": 178.859, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 178.859, "end": 178.899, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "mai", "start": 178.899, "end": 179.019, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 179.019, "end": 179.039, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "stato", "start": 179.039, "end": 179.259, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 179.259, "end": 179.259, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "un", "start": 179.259, "end": 179.339, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 179.339, "end": 179.339, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "tipo", "start": 179.339, "end": 179.559, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 179.559, "end": 179.559, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "fortunato,", "start": 179.559, "end": 180.079, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 180.079, "end": 180.099, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "io.", "start": 180.099, "end": 180.319, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 180.319, "end": 180.379, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 180.379, "end": 180.639, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 180.639, "end": 180.639, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "e", "start": 180.639, "end": 180.719, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 180.719, "end": 180.719, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "madre,", "start": 180.719, "end": 181.119, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 181.119, "end": 181.179, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "si", "start": 181.179, "end": 181.279, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 181.279, "end": 181.319, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 181.319, "end": 181.439, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 181.439, "end": 181.459, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "dire", "start": 181.459, "end": 181.619, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 181.619, "end": 181.639, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "che", "start": 181.639, "end": 181.699, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 181.699, "end": 181.719, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "non", "start": 181.719, "end": 181.819, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 181.819, "end": 181.819, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "li", "start": 181.819, "end": 181.919, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 181.919, "end": 181.919, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "ho", "start": 181.919, "end": 181.979, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 181.979, "end": 181.979, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "neanche", "start": 181.979, "end": 182.239, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 182.239, "end": 182.279, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "con<PERSON><PERSON><PERSON>.", "start": 182.279, "end": 182.879, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 182.879, "end": 183.499, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "<PERSON>", "start": 183.499, "end": 183.579, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 183.579, "end": 183.579, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 183.579, "end": 183.999, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 183.999, "end": 184.019, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "una", "start": 184.019, "end": 184.119, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 184.119, "end": 184.159, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "bottega", "start": 184.159, "end": 184.519, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 184.519, "end": 184.539, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "e", "start": 184.539, "end": 184.559, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 184.559, "end": 184.559, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "se", "start": 184.559, "end": 184.639, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 184.639, "end": 184.659, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "la", "start": 184.659, "end": 184.739, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 184.739, "end": 184.739, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "presero", "start": 184.739, "end": 184.979, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 184.979, "end": 184.979, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "i", "start": 184.979, "end": 185.039, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 185.039, "end": 185.039, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "creditori.", "start": 185.039, "end": 185.599, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 185.599, "end": 186.379, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 186.379, "end": 186.619, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 186.619, "end": 186.619, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "mi", "start": 186.619, "end": 186.679, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 186.679, "end": 186.719, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "misi", "start": 186.719, "end": 186.879, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 186.879, "end": 186.879, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "a", "start": 186.879, "end": 186.959, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 186.959, "end": 186.959, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "fare", "start": 186.959, "end": 187.099, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 187.099, "end": 187.099, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "il", "start": 187.099, "end": 187.179, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 187.179, "end": 187.199, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "pescatore,", "start": 187.199, "end": 187.679, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 187.679, "end": 187.699, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "ma", "start": 187.699, "end": 187.839, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 187.839, "end": 188.039, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "è", "start": 188.039, "end": 188.179, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 188.179, "end": 188.179, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "troppo", "start": 188.179, "end": 188.439, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 188.439, "end": 188.439, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "faticoso,", "start": 188.439, "end": 188.979, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 188.979, "end": 189.419, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "e", "start": 189.419, "end": 189.519, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 189.519, "end": 189.539, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "oggi", "start": 189.539, "end": 189.739, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 189.739, "end": 189.759, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "non", "start": 189.759, "end": 189.839, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 189.839, "end": 189.879, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "si", "start": 189.879, "end": 189.959, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 189.959, "end": 189.979, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "guadagna", "start": 189.979, "end": 190.279, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 190.279, "end": 190.299, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "più", "start": 190.299, "end": 190.419, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 190.419, "end": 190.439, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "niente.", "start": 190.439, "end": 190.879, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 190.879, "end": 192.239, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "Così", "start": 192.239, "end": 192.479, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 192.479, "end": 192.479, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "preferivo", "start": 192.479, "end": 192.819, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 192.819, "end": 192.839, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "start": 192.839, "end": 193.239, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 193.239, "end": 193.239, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "in", "start": 193.239, "end": 193.319, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 193.319, "end": 193.339, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "qualsia<PERSON>", "start": 193.339, "end": 193.679, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 193.679, "end": 193.679, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "altro", "start": 193.679, "end": 193.939, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 193.939, "end": 193.959, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "modo.", "start": 193.959, "end": 194.259, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 194.259, "end": 194.879, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "<PERSON>", "start": 194.879, "end": 194.959, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 194.959, "end": 194.979, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "misi", "start": 194.979, "end": 195.139, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 195.139, "end": 195.139, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "a", "start": 195.139, "end": 195.219, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 195.219, "end": 195.219, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "portare", "start": 195.219, "end": 195.499, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 195.499, "end": 195.499, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "le", "start": 195.499, "end": 195.599, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 195.599, "end": 195.619, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "valigie", "start": 195.619, "end": 195.859, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 195.859, "end": 195.859, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "a", "start": 195.859, "end": 195.939, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 195.939, "end": 195.939, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "quelli", "start": 195.939, "end": 196.099, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 196.099, "end": 196.099, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "che", "start": 196.099, "end": 196.199, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 196.199, "end": 196.239, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "venivano", "start": 196.239, "end": 196.579, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 196.579, "end": 196.579, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "nell'isola.", "start": 196.579, "end": 197.139, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 197.139, "end": 198.159, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 198.159, "end": 198.699, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 198.699, "end": 198.739, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 198.739, "end": 199.359, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 199.359, "end": 199.379, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "qualche", "start": 199.379, "end": 199.639, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 199.639, "end": 199.659, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "straniera", "start": 199.659, "end": 200.219, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 200.219, "end": 200.779, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "ric<PERSON>,", "start": 200.779, "end": 201.399, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 201.399, "end": 201.619, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "perché", "start": 201.619, "end": 201.779, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 201.779, "end": 201.799, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "ne", "start": 201.799, "end": 201.879, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 201.879, "end": 201.899, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "vengono", "start": 201.899, "end": 202.259, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 202.259, "end": 202.259, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "qui,", "start": 202.259, "end": 202.459, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 202.459, "end": 202.459, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "sapete.", "start": 202.459, "end": 202.899, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 202.899, "end": 203.439, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "Pescano", "start": 203.439, "end": 203.759, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 203.759, "end": 203.759, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "i", "start": 203.759, "end": 203.779, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 203.779, "end": 203.839, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "pesci", "start": 203.839, "end": 204.059, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 204.059, "end": 204.079, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "col", "start": 204.079, "end": 204.219, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 204.219, "end": 204.239, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "fucile.", "start": 204.239, "end": 204.699, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 204.699, "end": 206.499, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "Carmel<PERSON>", "start": 206.499, "end": 206.919, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 206.919, "end": 206.919, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "era", "start": 206.919, "end": 207.059, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 207.059, "end": 207.079, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "l'unica", "start": 207.079, "end": 207.379, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 207.379, "end": 207.399, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "che", "start": 207.399, "end": 207.479, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 207.479, "end": 207.499, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "mi", "start": 207.499, "end": 207.599, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 207.599, "end": 207.599, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "volesse", "start": 207.599, "end": 207.899, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 207.899, "end": 207.939, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "bene.", "start": 207.939, "end": 208.259, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 208.259, "end": 208.999, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "Ma", "start": 208.999, "end": 209.099, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 209.099, "end": 209.099, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "<PERSON>eva", "start": 209.099, "end": 209.319, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 209.319, "end": 209.319, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "che", "start": 209.319, "end": 209.419, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 209.419, "end": 209.439, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "mi", "start": 209.439, "end": 209.519, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 209.519, "end": 209.519, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 209.519, "end": 209.799, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 209.799, "end": 209.799, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "a", "start": 209.799, "end": 209.859, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 209.859, "end": 209.879, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "lavorare", "start": 209.879, "end": 210.199, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 210.199, "end": 210.199, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "con", "start": 210.199, "end": 210.319, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 210.319, "end": 210.339, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "Rosario.", "start": 210.339, "end": 210.879, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 210.879, "end": 212.259, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "E", "start": 212.259, "end": 212.399, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 212.399, "end": 212.399, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "le", "start": 212.399, "end": 212.499, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 212.499, "end": 212.519, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "sue", "start": 212.519, "end": 212.659, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 212.659, "end": 212.659, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "insistenze", "start": 212.659, "end": 213.219, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 213.219, "end": 213.219, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "mi", "start": 213.219, "end": 213.319, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 213.319, "end": 213.339, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "annoiavano.", "start": 213.339, "end": 213.959, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 213.959, "end": 214.279, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 214.279, "end": 214.419, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 214.419, "end": 214.459, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "fratello", "start": 214.459, "end": 214.739, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 214.739, "end": 214.739, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "poi", "start": 214.739, "end": 214.879, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 214.879, "end": 214.879, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "mi", "start": 214.879, "end": 214.979, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 214.979, "end": 214.979, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "era", "start": 214.979, "end": 215.059, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 215.059, "end": 215.059, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "antipatico", "start": 215.059, "end": 215.619, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 215.619, "end": 215.639, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "con", "start": 215.639, "end": 215.739, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 215.739, "end": 215.739, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "le", "start": 215.739, "end": 215.839, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 215.839, "end": 215.859, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "sue", "start": 215.859, "end": 215.979, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 215.979, "end": 215.979, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "arie", "start": 215.979, "end": 216.139, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 216.139, "end": 216.139, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "da", "start": 216.139, "end": 216.259, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 216.259, "end": 216.259, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "protettore", "start": 216.259, "end": 216.739, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 216.739, "end": 216.739, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "dei", "start": 216.739, "end": 216.839, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 216.839, "end": 216.879, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "poveri.", "start": 216.879, "end": 217.279, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 217.279, "end": 218.319, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "E", "start": 218.319, "end": 218.399, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 218.399, "end": 218.419, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "intanto", "start": 218.419, "end": 218.739, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 218.739, "end": 218.759, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "filava", "start": 218.759, "end": 219.059, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 219.059, "end": 219.059, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "con", "start": 219.059, "end": 219.179, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 219.179, "end": 219.179, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "la", "start": 219.179, "end": 219.259, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 219.259, "end": 219.299, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "figlia", "start": 219.299, "end": 219.479, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 219.479, "end": 219.499, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "del", "start": 219.499, "end": 219.619, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 219.619, "end": 219.639, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "padrone.", "start": 219.639, "end": 220.059, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 220.059, "end": 220.239, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "Tu<PERSON>", "start": 220.239, "end": 220.639, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 220.639, "end": 220.659, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "gente", "start": 220.659, "end": 220.979, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 220.979, "end": 221.019, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "schi<PERSON><PERSON>", "start": 221.019, "end": 221.519, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 221.519, "end": 221.519, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "in", "start": 221.519, "end": 221.599, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 221.599, "end": 221.599, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "quest'isola.", "start": 221.599, "end": 222.159, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 222.159, "end": 222.439, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 222.439, "end": 222.799, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 222.799, "end": 222.799, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "non", "start": 222.799, "end": 222.919, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 222.919, "end": 222.919, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "ci", "start": 222.919, "end": 223.019, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 223.019, "end": 223.039, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "fossi", "start": 223.039, "end": 223.259, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 223.259, "end": 223.259, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "mai", "start": 223.259, "end": 223.379, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 223.379, "end": 223.379, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "nato.", "start": 223.379, "end": 223.839, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 223.839, "end": 223.859, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "Non", "start": 223.859, "end": 225.239, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 225.239, "end": 225.259, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "ti", "start": 225.259, "end": 225.399, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 225.399, "end": 225.399, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "fare", "start": 225.399, "end": 225.659, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 225.659, "end": 225.659, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "illusioni.", "start": 225.659, "end": 226.419, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 226.419, "end": 227.099, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "T<PERSON><PERSON>", "start": 227.099, "end": 227.359, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 227.359, "end": 227.359, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "come", "start": 227.359, "end": 227.579, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 227.579, "end": 227.619, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "te", "start": 227.619, "end": 227.759, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 227.759, "end": 227.759, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "stanno", "start": 227.759, "end": 228.079, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 228.079, "end": 228.079, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "male", "start": 228.079, "end": 228.399, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 228.399, "end": 228.399, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "anche", "start": 228.399, "end": 228.639, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 228.639, "end": 228.639, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "in", "start": 228.639, "end": 228.759, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 228.759, "end": 228.779, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "città.", "start": 228.779, "end": 230.459, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 230.459, "end": 230.459, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "Lo", "start": 230.459, "end": 230.639, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 230.639, "end": 230.639, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "vedr<PERSON>", "start": 230.639, "end": 230.899, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 230.899, "end": 230.899, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "presto", "start": 230.899, "end": 231.279, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 231.279, "end": 231.279, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "se", "start": 231.279, "end": 231.399, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 231.399, "end": 231.399, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "in", "start": 231.399, "end": 231.479, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 231.479, "end": 231.479, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "città", "start": 231.479, "end": 231.739, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 231.739, "end": 231.739, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "si", "start": 231.739, "end": 231.879, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 231.879, "end": 231.879, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "sta", "start": 231.879, "end": 232.039, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 232.039, "end": 232.039, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "male", "start": 232.039, "end": 232.239, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 232.239, "end": 232.239, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "come", "start": 232.239, "end": 232.419, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 232.419, "end": 232.439, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "dite.", "start": 232.439, "end": 232.819, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 232.819, "end": 234.739, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "Me", "start": 234.739, "end": 234.819, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 234.819, "end": 234.819, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "ne", "start": 234.819, "end": 234.899, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 234.899, "end": 234.899, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "voglio", "start": 234.899, "end": 235.059, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 235.059, "end": 235.059, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "andare", "start": 235.059, "end": 235.259, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 235.259, "end": 235.259, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "a", "start": 235.259, "end": 235.319, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 235.319, "end": 235.339, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "Milano.", "start": 235.339, "end": 235.779, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 235.779, "end": 236.239, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "Las<PERSON>ù", "start": 236.239, "end": 236.479, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 236.479, "end": 236.499, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "ne", "start": 236.499, "end": 236.619, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 236.619, "end": 236.639, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "girano", "start": 236.639, "end": 236.939, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 236.939, "end": 236.939, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "di", "start": 236.939, "end": 237.039, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 237.039, "end": 237.059, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "soldi.", "start": 237.059, "end": 238.179, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 238.179, "end": 238.179, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "<PERSON>", "start": 238.179, "end": 238.339, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 238.339, "end": 238.379, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "ti", "start": 238.379, "end": 238.479, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 238.479, "end": 238.499, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "ha", "start": 238.499, "end": 238.579, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 238.579, "end": 238.599, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "messo", "start": 238.599, "end": 238.919, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 238.919, "end": 238.919, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "in", "start": 238.919, "end": 239.059, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 239.059, "end": 239.059, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "testa", "start": 239.059, "end": 239.399, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 239.399, "end": 239.419, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "quest'idea?", "start": 239.419, "end": 240.579, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 240.579, "end": 240.579, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "<PERSON><PERSON>,", "start": 240.579, "end": 240.899, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 240.899, "end": 241.299, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "<PERSON>", "start": 241.299, "end": 241.679, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 241.679, "end": 241.719, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "ci", "start": 241.719, "end": 241.839, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 241.839, "end": 241.839, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "si", "start": 241.839, "end": 241.919, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 241.919, "end": 241.919, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "è", "start": 241.919, "end": 241.979, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 241.979, "end": 241.979, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "trovata", "start": 241.979, "end": 242.299, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 242.299, "end": 242.299, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "bene,", "start": 242.299, "end": 242.519, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 242.519, "end": 242.539, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "lei.", "start": 242.539, "end": 244.179, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 244.179, "end": 244.179, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "Non", "start": 244.179, "end": 244.459, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 244.459, "end": 244.539, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "tanto", "start": 244.539, "end": 244.859, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 244.859, "end": 244.879, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "da", "start": 244.879, "end": 244.979, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 244.979, "end": 244.999, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 244.999, "end": 245.599, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 245.599, "end": 245.599, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "se", "start": 245.599, "end": 245.739, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 245.739, "end": 245.739, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "è", "start": 245.739, "end": 245.839, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 245.839, "end": 245.859, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "finita", "start": 245.859, "end": 246.199, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 246.199, "end": 246.199, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "in", "start": 246.199, "end": 246.299, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 246.299, "end": 246.319, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "quest'isola.", "start": 246.319, "end": 247.099, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 247.099, "end": 247.659, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "Eh", "start": 247.659, "end": 247.819, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 247.819, "end": 247.819, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "già.", "start": 247.819, "end": 248.179, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 248.179, "end": 249.959, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "Avete", "start": 249.959, "end": 250.239, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 250.239, "end": 250.259, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "ragione.", "start": 250.259, "end": 250.759, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 250.759, "end": 251.119, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 251.119, "end": 251.499, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 251.499, "end": 251.519, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "non", "start": 251.519, "end": 251.639, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 251.639, "end": 251.659, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "era", "start": 251.659, "end": 251.799, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 251.799, "end": 251.799, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "vero", "start": 251.799, "end": 251.979, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 251.979, "end": 251.999, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "niente.", "start": 251.999, "end": 252.479, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 252.479, "end": 253.959, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "Fin", "start": 253.959, "end": 254.159, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 254.159, "end": 254.159, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "dal", "start": 254.159, "end": 254.299, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 254.299, "end": 254.319, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "primo", "start": 254.319, "end": 254.479, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 254.479, "end": 254.499, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "incontro", "start": 254.499, "end": 254.879, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 254.879, "end": 254.879, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "mi", "start": 254.879, "end": 254.979, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 254.979, "end": 254.979, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "trat<PERSON><PERSON>", "start": 254.979, "end": 255.299, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 255.299, "end": 255.299, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "come", "start": 255.299, "end": 255.459, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 255.459, "end": 255.459, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "un", "start": 255.459, "end": 255.539, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 255.539, "end": 255.539, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "ragazzino.", "start": 255.539, "end": 256.139, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 256.139, "end": 257.019, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "E", "start": 257.019, "end": 257.159, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 257.159, "end": 257.159, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "io", "start": 257.159, "end": 257.259, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 257.259, "end": 257.279, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "lo", "start": 257.279, "end": 257.359, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 257.359, "end": 257.379, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "sapevo,", "start": 257.379, "end": 257.759, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 257.759, "end": 257.759, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "ma", "start": 257.759, "end": 257.879, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 257.879, "end": 257.899, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "facevo", "start": 257.899, "end": 258.199, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 258.199, "end": 258.219, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "finta", "start": 258.219, "end": 258.479, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 258.479, "end": 258.479, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "di", "start": 258.479, "end": 258.559, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 258.559, "end": 258.579, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "nulla,", "start": 258.579, "end": 258.919, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 258.919, "end": 258.939, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "mi", "start": 258.939, "end": 259.079, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 259.079, "end": 259.099, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 259.099, "end": 259.479, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 259.479, "end": 259.499, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "molto.", "start": 259.499, "end": 259.979, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 259.979, "end": 260.679, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "Passava", "start": 260.679, "end": 261.039, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 261.039, "end": 261.039, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "le", "start": 261.039, "end": 261.159, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 261.159, "end": 261.179, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "giornate", "start": 261.179, "end": 261.539, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 261.539, "end": 261.539, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "chiuse", "start": 261.539, "end": 261.779, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 261.779, "end": 261.799, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "in", "start": 261.799, "end": 261.879, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 261.879, "end": 261.919, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "casa,", "start": 261.919, "end": 262.239, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 262.239, "end": 262.499, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "a", "start": 262.499, "end": 262.599, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 262.599, "end": 262.599, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "l<PERSON><PERSON><PERSON><PERSON>", "start": 262.599, "end": 263.119, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 263.119, "end": 263.159, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "come", "start": 263.159, "end": 263.279, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 263.279, "end": 263.279, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "una", "start": 263.279, "end": 263.419, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 263.419, "end": 263.439, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "gatta.", "start": 263.439, "end": 263.819, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 263.819, "end": 264.959, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "Sono", "start": 264.959, "end": 265.159, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 265.159, "end": 265.199, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "l'unico", "start": 265.199, "end": 265.479, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 265.479, "end": 265.519, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "qui", "start": 265.519, "end": 265.659, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 265.659, "end": 265.679, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "ad", "start": 265.679, "end": 265.759, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 265.759, "end": 265.759, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "avere", "start": 265.759, "end": 265.979, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 265.979, "end": 265.999, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "del", "start": 265.999, "end": 266.119, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 266.119, "end": 266.119, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "tempo", "start": 266.119, "end": 266.379, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 266.379, "end": 266.399, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "libero.", "start": 266.399, "end": 266.719, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 266.719, "end": 267.279, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 267.279, "end": 267.639, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 267.639, "end": 267.659, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "mi", "start": 267.659, "end": 267.739, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 267.739, "end": 267.739, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "comanda.", "start": 267.739, "end": 268.259, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 268.259, "end": 269.019, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "E", "start": 269.019, "end": 269.119, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 269.119, "end": 269.159, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "così", "start": 269.159, "end": 269.419, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 269.419, "end": 269.459, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "andavo", "start": 269.459, "end": 269.799, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 269.799, "end": 269.819, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "sotto", "start": 269.819, "end": 270.019, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 270.019, "end": 270.039, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "le", "start": 270.039, "end": 270.119, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 270.119, "end": 270.139, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "sue", "start": 270.139, "end": 270.279, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 270.279, "end": 270.299, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "finestre", "start": 270.299, "end": 270.699, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 270.699, "end": 270.759, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "a", "start": 270.759, "end": 270.859, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 270.859, "end": 270.859, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "su<PERSON><PERSON>", "start": 270.859, "end": 271.179, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 271.179, "end": 271.199, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "la", "start": 271.199, "end": 271.259, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 271.259, "end": 271.299, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "chitarra.", "start": 271.299, "end": 271.839, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 271.839, "end": 272.539, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "E", "start": 272.539, "end": 272.639, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 272.639, "end": 272.679, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "aspettavo.", "start": 272.679, "end": 273.419, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 273.419, "end": 274.699, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "Sapevo", "start": 274.699, "end": 275.079, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 275.079, "end": 275.119, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "che", "start": 275.119, "end": 275.199, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 275.199, "end": 275.219, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "lei", "start": 275.219, "end": 275.339, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 275.339, "end": 275.359, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "mi", "start": 275.359, "end": 275.459, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 275.459, "end": 275.459, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "guardava.", "start": 275.459, "end": 275.979, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}]}}, "created_at": 1754319764.6783297}