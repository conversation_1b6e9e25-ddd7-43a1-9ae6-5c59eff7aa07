{"success": true, "service": "elevenlabs", "transcription_id": "elevenlabs_1754319649", "text": "Credo che bisognerà assumere una serva più giovane. Quella è un po' malandata, mi pare. Noi non consideriamo Margherita una serva, ci ha visto nascere tutti. Molte cose dovranno cambiare. E se dovremmo vivere insieme, sarà bene spiegarci una volta per sempre. Non sono venuta a seppellirmi qui per sopportare le prediche di nessuno. Smettiamola adesso. Tutto deve continuare come sempre in questa casa. E tu non prendere quell'aria da vittima. Se ci sarà qualche cosa da cambiare, cambieremo senza troppe storie. Ho già noie sufficienti nell'isola per dovermi preoccupare anche dei tuoi capricci. Noie, <PERSON>? Di che genere? Denaro? No, non si tratta di denaro. Sono i pescatori che alzano la cresta. I pescatori vogliono soltanto vivere. Sono amici tuoi, lo so. E tu li difendi contro tuo padre. <PERSON><PERSON>, dì quello che vuoi dirmi in fretta. Vuoi rimproveri qualcosa? Qualcosa che non va, alla signorina? O hai visto ancora quella canaglia prima che i battelli partissero? Eri con lui al molo, vero? Rosario non è una canaglia e tu lo sai bene. Ma è lui che organizza i pescatori contro di me e tu gli tieni mano. È innamorata, ecco tutto. E una donna innamorata è sempre pericolosa, anche nei confronti del padre. No, nessun pericolo fino a che sarò io il padrone qui. So bene io come sistemare le cose, ma non parliamo di questo. Vieni Carla. Io credo che addirittura si divertisse ad Are scander, ma mio padre non si accorgeva di niente. Cosa intendi dire per escander? Gli dico la mattina. Sì, quella mattina, mentre tornavo a casa dall'aver fatto la spesa, la incontrai. C'è qualcosa che non va? Dove è andata? A un ricevimento. Che razza di domanda va da fare il bagno? A Punta Piccola c'è una spiaggia isolata da quella parte. Troverò quello che cerco da sola. Sono abbastanza grande per cavarmi. Passava delle ore a bruciarsi al sole sugli scogli, poi si tuffava in acqua e nuotava. Le nostre donne in quest'isola non lo fanno. Il mare ci serve per darci da mangiare, non per divertimento. Ma questo Carla, non lo capii. Quel giorno la seguì e non vista assistetti al suo brusco incontro con Abu. Seppe più tardi che l'aveva offeso mortalmente chiamandolo sporco negro. Signorina, quella è uscita in pantaloni corti, al mare è andata. Sì, lo so, l'ho incontrata. Papà l'ha vista? No, Don Pedro è uscito presto stamattina. Fa che non lo sappia, Margherita, e meglio non irritarlo. A quando lo saprà, non avrà il coraggio di dirle niente. In paese sono cominciate già le chiacchere. Dicono che prima o poi quella finirà per rovinare questa casa e Don Pedro. Purtroppo qualche tempo dopo i miei dubbi ebbero una conferma. Mio padre mi aveva pregato di andare ad aiutarla. Sperava che saremmo diventate amiche. Maria, sei tu? Maria? Sì, sono io. Cosa ti serve? Il mio accappatoio, quello bianco che sta sulla poltrona. Prendimi anche le mollezze, devono essere accanto allo specchio. Non ci so. Guarda nella mia valigia allora, quella piccola. ", "words": [{"text": "<PERSON><PERSON>", "start": 20.699, "end": 21.019, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 21.019, "end": 21.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 21.059, "end": 21.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 21.139, "end": 21.18, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "bisognerà", "start": 21.18, "end": 21.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 21.579, "end": 21.6, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "assumere", "start": 21.6, "end": 21.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 21.959, "end": 21.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "una", "start": 21.959, "end": 22.079, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 22.079, "end": 22.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "serva", "start": 22.119, "end": 22.399, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 22.399, "end": 22.439, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "più", "start": 22.439, "end": 22.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 22.559, "end": 22.6, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "giovane.", "start": 22.6, "end": 23.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 23.159, "end": 24.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 24.459, "end": 24.719, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 24.719, "end": 24.719, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "è", "start": 24.719, "end": 24.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 24.779, "end": 24.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "un", "start": 24.779, "end": 24.92, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 24.92, "end": 24.939, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "po'", "start": 24.939, "end": 25.04, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 25.04, "end": 25.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "maland<PERSON>,", "start": 25.059, "end": 25.639, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 25.639, "end": 25.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "mi", "start": 25.659, "end": 25.78, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 25.78, "end": 25.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "pare.", "start": 25.819, "end": 26.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 26.199, "end": 26.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>i", "start": 26.559, "end": 26.739, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 26.739, "end": 26.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "non", "start": 26.779, "end": 26.879, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 26.879, "end": 26.939, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON>o", "start": 26.939, "end": 27.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 27.619, "end": 27.639, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Margh<PERSON><PERSON>", "start": 27.639, "end": 28.18, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 28.18, "end": 28.18, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "una", "start": 28.18, "end": 28.399, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 28.399, "end": 28.42, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "serva,", "start": 28.42, "end": 28.879, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 28.879, "end": 28.92, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ci", "start": 28.92, "end": 29.0, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 29.0, "end": 29.0, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ha", "start": 29.0, "end": 29.079, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 29.079, "end": 29.079, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "visto", "start": 29.079, "end": 29.34, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 29.34, "end": 29.359, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "nascere", "start": 29.359, "end": 29.76, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 29.76, "end": 29.76, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tutti.", "start": 29.76, "end": 30.179, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 30.179, "end": 30.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 30.479, "end": 30.84, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 30.84, "end": 30.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "cose", "start": 30.879, "end": 31.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 31.159, "end": 31.179, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "dovranno", "start": 31.179, "end": 31.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 31.559, "end": 31.579, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "cambiare.", "start": 31.579, "end": 32.18, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 32.18, "end": 32.579, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "E", "start": 32.579, "end": 32.679, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 32.679, "end": 32.719, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "se", "start": 32.719, "end": 32.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 32.819, "end": 32.84, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 32.84, "end": 33.18, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 33.18, "end": 33.18, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "vivere", "start": 33.18, "end": 33.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 33.479, "end": 33.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "insieme,", "start": 33.479, "end": 34.04, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 34.04, "end": 34.04, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sarà", "start": 34.04, "end": 34.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 34.299, "end": 34.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "bene", "start": 34.299, "end": 34.56, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 34.56, "end": 34.579, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 34.579, "end": 35.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 35.319, "end": 35.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "una", "start": 35.319, "end": 35.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 35.479, "end": 35.5, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "volta", "start": 35.5, "end": 35.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 35.779, "end": 35.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "per", "start": 35.819, "end": 35.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 35.959, "end": 36.079, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sempre.", "start": 36.079, "end": 36.599, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 36.599, "end": 37.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Non", "start": 37.299, "end": 37.439, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 37.439, "end": 37.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sono", "start": 37.479, "end": 37.68, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 37.68, "end": 37.719, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "venuta", "start": 37.719, "end": 37.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 37.959, "end": 37.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 37.959, "end": 38.04, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 38.04, "end": 38.04, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "start": 38.04, "end": 38.54, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 38.54, "end": 38.54, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "qui", "start": 38.54, "end": 38.68, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 38.68, "end": 38.719, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "per", "start": 38.719, "end": 38.84, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 38.84, "end": 38.84, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sopportare", "start": 38.84, "end": 39.28, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 39.28, "end": 39.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "le", "start": 39.299, "end": 39.399, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 39.399, "end": 39.419, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "prediche", "start": 39.419, "end": 39.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 39.759, "end": 39.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 39.799, "end": 39.88, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 39.88, "end": 39.899, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "nessuno.", "start": 39.899, "end": 40.379, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 40.379, "end": 40.379, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 40.379, "end": 41.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 41.559, "end": 41.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "adesso.", "start": 41.559, "end": 41.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 41.979, "end": 42.5, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 42.5, "end": 42.72, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 42.72, "end": 42.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "deve", "start": 42.739, "end": 42.959, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 42.959, "end": 42.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "continuare", "start": 42.979, "end": 43.5, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 43.5, "end": 43.52, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "come", "start": 43.52, "end": 43.719, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 43.719, "end": 43.719, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sempre", "start": 43.719, "end": 44.02, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 44.02, "end": 44.04, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "in", "start": 44.04, "end": 44.099, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 44.099, "end": 44.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "questa", "start": 44.119, "end": 44.359, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 44.359, "end": 44.36, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "casa.", "start": 44.36, "end": 44.719, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 44.719, "end": 44.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "E", "start": 44.819, "end": 44.959, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 44.959, "end": 44.959, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "tu", "start": 44.959, "end": 45.08, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 45.08, "end": 45.099, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "non", "start": 45.099, "end": 45.22, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 45.22, "end": 45.239, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "prendere", "start": 45.239, "end": 45.52, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 45.52, "end": 45.539, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "quell'aria", "start": 45.539, "end": 45.899, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 45.899, "end": 45.899, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "da", "start": 45.899, "end": 46.0, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 46.0, "end": 46.0, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "vittima.", "start": 46.0, "end": 46.499, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 46.499, "end": 47.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Se", "start": 47.379, "end": 47.5, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 47.5, "end": 47.52, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ci", "start": 47.52, "end": 47.619, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 47.619, "end": 47.659, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sarà", "start": 47.659, "end": 47.84, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 47.84, "end": 47.899, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "qualche", "start": 47.899, "end": 48.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 48.139, "end": 48.159, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "cosa", "start": 48.159, "end": 48.34, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 48.34, "end": 48.36, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "da", "start": 48.36, "end": 48.44, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 48.44, "end": 48.459, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "cambiare,", "start": 48.459, "end": 48.879, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 48.879, "end": 48.879, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "cambieremo", "start": 48.879, "end": 49.379, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 49.379, "end": 49.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "senza", "start": 49.379, "end": 49.619, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 49.619, "end": 49.619, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "troppe", "start": 49.619, "end": 49.84, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 49.84, "end": 49.84, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "storie.", "start": 49.84, "end": 50.259, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 50.259, "end": 54.279, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>", "start": 54.279, "end": 54.439, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 54.439, "end": 54.439, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "già", "start": 54.439, "end": 54.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 54.559, "end": 54.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "noie", "start": 54.559, "end": 54.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 54.799, "end": 54.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sufficienti", "start": 54.819, "end": 55.34, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 55.34, "end": 55.34, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "nell'isola", "start": 55.34, "end": 55.759, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 55.759, "end": 55.779, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "per", "start": 55.779, "end": 55.899, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 55.899, "end": 55.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 55.919, "end": 56.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 56.299, "end": 56.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "preoccupare", "start": 56.299, "end": 56.86, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 56.86, "end": 56.86, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "anche", "start": 56.86, "end": 57.079, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 57.079, "end": 57.099, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "dei", "start": 57.099, "end": 57.199, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 57.199, "end": 57.199, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "tuoi", "start": 57.199, "end": 57.34, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 57.34, "end": 57.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>.", "start": 57.379, "end": 57.839, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 57.839, "end": 58.259, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON>,", "start": 58.259, "end": 58.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 58.619, "end": 58.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>?", "start": 58.619, "end": 59.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 59.119, "end": 59.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Di", "start": 59.879, "end": 59.999, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 59.999, "end": 59.999, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 59.999, "end": 60.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 60.159, "end": 60.199, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "genere?", "start": 60.199, "end": 60.719, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 60.719, "end": 62.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Denaro?", "start": 62.219, "end": 62.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 62.859, "end": 64.179, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "No,", "start": 64.179, "end": 64.539, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 64.539, "end": 64.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "non", "start": 64.559, "end": 64.659, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 64.659, "end": 64.699, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "si", "start": 64.699, "end": 64.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 64.799, "end": 64.839, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "tratta", "start": 64.839, "end": 65.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 65.139, "end": 65.159, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 65.159, "end": 65.219, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 65.219, "end": 65.219, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "denaro.", "start": 65.219, "end": 65.679, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 65.679, "end": 65.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Sono", "start": 65.919, "end": 66.119, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 66.119, "end": 66.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "i", "start": 66.119, "end": 66.179, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 66.179, "end": 66.179, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "pescatori", "start": 66.179, "end": 66.639, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 66.639, "end": 66.659, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "che", "start": 66.659, "end": 66.759, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 66.759, "end": 66.759, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "alzano", "start": 66.759, "end": 66.999, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 66.999, "end": 67.019, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "la", "start": 67.019, "end": 67.099, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 67.099, "end": 67.099, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "cresta.", "start": 67.099, "end": 67.539, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 67.539, "end": 67.779, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "I", "start": 67.779, "end": 67.879, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 67.879, "end": 67.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "pescatori", "start": 67.919, "end": 68.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 68.459, "end": 68.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "vog<PERSON><PERSON>", "start": 68.479, "end": 68.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 68.819, "end": 68.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "soltanto", "start": 68.879, "end": 69.379, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 69.379, "end": 69.399, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "vivere.", "start": 69.399, "end": 69.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 69.859, "end": 70.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Sono", "start": 70.699, "end": 70.919, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 70.919, "end": 70.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "amici", "start": 70.919, "end": 71.199, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 71.199, "end": 71.219, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "tuoi,", "start": 71.219, "end": 71.439, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 71.439, "end": 71.439, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "lo", "start": 71.439, "end": 71.539, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 71.539, "end": 71.579, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "so.", "start": 71.579, "end": 71.919, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 71.919, "end": 72.539, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "E", "start": 72.539, "end": 72.639, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 72.639, "end": 72.679, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "tu", "start": 72.679, "end": 72.759, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 72.759, "end": 72.759, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "li", "start": 72.759, "end": 72.839, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 72.839, "end": 72.859, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "difendi", "start": 72.859, "end": 73.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 73.159, "end": 73.159, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "contro", "start": 73.159, "end": 73.379, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 73.379, "end": 73.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "tuo", "start": 73.379, "end": 73.519, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 73.519, "end": 73.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "padre.", "start": 73.559, "end": 73.899, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 73.899, "end": 74.099, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON>,", "start": 74.099, "end": 74.639, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 74.639, "end": 75.159, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "dì", "start": 75.159, "end": 75.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 75.299, "end": 75.339, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "quello", "start": 75.339, "end": 75.499, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 75.499, "end": 75.499, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "che", "start": 75.499, "end": 75.599, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 75.599, "end": 75.619, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "vuoi", "start": 75.619, "end": 75.739, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 75.739, "end": 75.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "dir<PERSON>", "start": 75.739, "end": 75.939, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 75.939, "end": 75.959, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "in", "start": 75.959, "end": 76.059, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 76.059, "end": 76.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "fretta.", "start": 76.059, "end": 76.419, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 76.419, "end": 77.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 77.379, "end": 77.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 77.479, "end": 77.579, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 77.579, "end": 77.899, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 77.899, "end": 77.899, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "qualcosa?", "start": 77.899, "end": 78.379, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 78.379, "end": 78.499, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Qualcosa", "start": 78.499, "end": 78.919, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 78.919, "end": 78.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "che", "start": 78.919, "end": 79.019, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 79.019, "end": 79.039, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "non", "start": 79.039, "end": 79.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 79.139, "end": 79.179, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "va,", "start": 79.179, "end": 79.379, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 79.379, "end": 79.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "alla", "start": 79.379, "end": 79.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 79.479, "end": 79.519, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "signorina?", "start": 79.519, "end": 80.059, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 80.059, "end": 80.519, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "O", "start": 80.519, "end": 80.619, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 80.619, "end": 80.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "hai", "start": 80.639, "end": 80.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 80.799, "end": 80.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "visto", "start": 80.819, "end": 81.019, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 81.019, "end": 81.019, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ancora", "start": 81.019, "end": 81.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 81.299, "end": 81.339, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "quella", "start": 81.339, "end": 81.519, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 81.519, "end": 81.579, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "canaglia", "start": 81.579, "end": 81.919, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 81.919, "end": 81.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "prima", "start": 81.919, "end": 82.119, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 82.119, "end": 82.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "che", "start": 82.119, "end": 82.219, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 82.219, "end": 82.219, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "i", "start": 82.219, "end": 82.279, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 82.279, "end": 82.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "battelli", "start": 82.299, "end": 82.659, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 82.659, "end": 82.679, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "partissero?", "start": 82.679, "end": 83.239, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 83.239, "end": 83.599, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 83.599, "end": 83.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 83.799, "end": 83.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "con", "start": 83.799, "end": 83.939, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 83.939, "end": 83.939, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "lui", "start": 83.939, "end": 84.079, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 84.079, "end": 84.079, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "al", "start": 84.079, "end": 84.199, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 84.199, "end": 84.199, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "molo,", "start": 84.199, "end": 84.439, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 84.439, "end": 84.439, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "vero?", "start": 84.439, "end": 84.739, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 84.739, "end": 85.199, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Rosario", "start": 85.199, "end": 85.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 85.659, "end": 85.679, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "non", "start": 85.679, "end": 85.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 85.819, "end": 85.839, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "è", "start": 85.839, "end": 85.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 85.919, "end": 85.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "una", "start": 85.919, "end": 86.079, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 86.079, "end": 86.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "canaglia", "start": 86.139, "end": 86.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 86.559, "end": 86.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "e", "start": 86.879, "end": 86.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 86.979, "end": 87.039, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tu", "start": 87.039, "end": 87.179, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 87.179, "end": 87.199, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "lo", "start": 87.199, "end": 87.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 87.299, "end": 87.359, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sai", "start": 87.359, "end": 87.499, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 87.499, "end": 87.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "bene.", "start": 87.539, "end": 87.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 87.859, "end": 87.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Ma", "start": 87.979, "end": 88.099, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 88.099, "end": 88.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "è", "start": 88.119, "end": 88.199, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 88.199, "end": 88.199, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "lui", "start": 88.199, "end": 88.419, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 88.419, "end": 88.419, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "che", "start": 88.419, "end": 88.499, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 88.499, "end": 88.499, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "organizza", "start": 88.499, "end": 88.859, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 88.859, "end": 88.859, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "i", "start": 88.859, "end": 88.939, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 88.939, "end": 88.939, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "pescatori", "start": 88.939, "end": 89.359, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 89.359, "end": 89.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "contro", "start": 89.379, "end": 89.599, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 89.599, "end": 89.619, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 89.619, "end": 89.679, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 89.679, "end": 89.719, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "me", "start": 89.719, "end": 89.899, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 89.899, "end": 90.599, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "e", "start": 90.599, "end": 90.739, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 90.739, "end": 90.779, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "tu", "start": 90.779, "end": 90.899, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 90.899, "end": 90.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "gli", "start": 90.919, "end": 91.039, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 91.039, "end": 91.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "tieni", "start": 91.059, "end": 91.279, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 91.279, "end": 91.279, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mano.", "start": 91.279, "end": 91.659, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 91.659, "end": 94.599, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "È", "start": 94.599, "end": 94.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 94.799, "end": 94.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "innamorata,", "start": 94.799, "end": 95.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 95.759, "end": 95.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ecco", "start": 95.819, "end": 96.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 96.159, "end": 96.179, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tutto.", "start": 96.179, "end": 96.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 96.619, "end": 96.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "E", "start": 96.919, "end": 97.019, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 97.019, "end": 97.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "una", "start": 97.019, "end": 97.179, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 97.179, "end": 97.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "donna", "start": 97.239, "end": 97.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 97.479, "end": 97.499, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "innamorata", "start": 97.499, "end": 98.019, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 98.019, "end": 98.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "è", "start": 98.019, "end": 98.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 98.139, "end": 98.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sempre", "start": 98.139, "end": 98.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 98.479, "end": 98.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "perico<PERSON>a,", "start": 98.539, "end": 99.259, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 99.259, "end": 99.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "anche", "start": 99.539, "end": 99.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 99.759, "end": 99.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "nei", "start": 99.779, "end": 99.939, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 99.939, "end": 99.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>i", "start": 99.959, "end": 100.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 100.479, "end": 100.499, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "del", "start": 100.499, "end": 100.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 100.619, "end": 100.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "padre.", "start": 100.739, "end": 101.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 101.199, "end": 101.279, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "No,", "start": 101.279, "end": 101.679, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 101.679, "end": 103.279, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "nessun", "start": 103.279, "end": 103.639, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 103.639, "end": 103.679, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "pericolo", "start": 103.679, "end": 104.079, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 104.079, "end": 104.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "fino", "start": 104.119, "end": 104.279, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 104.279, "end": 104.279, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "a", "start": 104.279, "end": 104.319, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 104.319, "end": 104.319, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "che", "start": 104.319, "end": 104.459, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 104.459, "end": 104.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sar<PERSON>", "start": 104.479, "end": 104.739, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 104.739, "end": 104.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "io", "start": 104.739, "end": 104.839, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 104.839, "end": 104.859, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "il", "start": 104.859, "end": 104.919, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 104.919, "end": 104.939, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "padrone", "start": 104.939, "end": 105.319, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 105.319, "end": 105.319, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "qui.", "start": 105.319, "end": 105.599, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 105.599, "end": 106.039, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "So", "start": 106.039, "end": 106.179, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 106.179, "end": 106.179, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "bene", "start": 106.179, "end": 106.339, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 106.339, "end": 106.359, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "io", "start": 106.359, "end": 106.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 106.479, "end": 106.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "come", "start": 106.479, "end": 106.679, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 106.679, "end": 106.679, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 106.679, "end": 107.079, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 107.079, "end": 107.079, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "le", "start": 107.079, "end": 107.179, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 107.179, "end": 107.219, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "cose,", "start": 107.219, "end": 107.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 107.559, "end": 107.619, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ma", "start": 107.619, "end": 107.859, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 107.859, "end": 108.219, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "non", "start": 108.219, "end": 108.359, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 108.359, "end": 108.399, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "parlia<PERSON>", "start": 108.399, "end": 108.759, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 108.759, "end": 108.759, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 108.759, "end": 108.879, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 108.879, "end": 108.899, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "questo.", "start": 108.899, "end": 109.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 109.299, "end": 113.719, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 113.719, "end": 114.019, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 114.019, "end": 114.039, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>.", "start": 114.039, "end": 116.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 116.799, "end": 121.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Io", "start": 121.559, "end": 122.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 122.919, "end": 122.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "credo", "start": 122.979, "end": 123.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 123.199, "end": 123.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 123.239, "end": 123.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 123.319, "end": 123.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 123.319, "end": 123.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 123.799, "end": 123.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "si", "start": 123.799, "end": 123.939, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 123.939, "end": 123.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "divertisse", "start": 123.959, "end": 124.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 124.579, "end": 124.579, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ad", "start": 124.579, "end": 124.739, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 124.739, "end": 124.759, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Are", "start": 124.759, "end": 124.879, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 124.879, "end": 124.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "scander,", "start": 124.919, "end": 125.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 125.479, "end": 125.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ma", "start": 125.919, "end": 125.999, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 125.999, "end": 126.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "mio", "start": 126.059, "end": 126.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 126.199, "end": 126.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "padre", "start": 126.239, "end": 126.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 126.559, "end": 126.579, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "non", "start": 126.579, "end": 126.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 126.659, "end": 126.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "si", "start": 126.699, "end": 126.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 126.779, "end": 126.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "accorgeva", "start": 126.799, "end": 127.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 127.239, "end": 127.259, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 127.259, "end": 127.339, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 127.339, "end": 127.379, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "niente.", "start": 127.379, "end": 127.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 127.799, "end": 127.859, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Cosa", "start": 127.859, "end": 128.199, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 128.199, "end": 128.199, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "intendi", "start": 128.199, "end": 128.619, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 128.619, "end": 128.619, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "dire", "start": 128.619, "end": 128.859, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 128.859, "end": 128.899, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "per", "start": 128.899, "end": 129.059, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 129.059, "end": 129.079, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "escander?", "start": 129.079, "end": 129.459, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 129.459, "end": 129.459, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 129.459, "end": 129.539, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 129.539, "end": 129.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "dico", "start": 129.539, "end": 129.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 129.779, "end": 129.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "la", "start": 129.779, "end": 129.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 129.899, "end": 129.899, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "mattina.", "start": 129.899, "end": 130.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 130.299, "end": 132.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Sì,", "start": 132.559, "end": 132.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 132.759, "end": 132.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "quella", "start": 132.799, "end": 133.059, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 133.059, "end": 133.079, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "mat<PERSON>,", "start": 133.079, "end": 133.539, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 133.539, "end": 133.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "mentre", "start": 133.539, "end": 133.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 133.799, "end": 133.839, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tornavo", "start": 133.839, "end": 134.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 134.199, "end": 134.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 134.219, "end": 134.279, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 134.279, "end": 134.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "casa", "start": 134.319, "end": 134.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 134.559, "end": 134.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "dall'aver", "start": 134.599, "end": 134.939, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 134.939, "end": 134.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "fatto", "start": 134.979, "end": 135.259, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 135.259, "end": 135.279, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "la", "start": 135.279, "end": 135.359, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 135.359, "end": 135.399, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "spesa,", "start": 135.399, "end": 135.839, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 135.839, "end": 135.839, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "la", "start": 135.839, "end": 135.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 135.959, "end": 135.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "incontrai.", "start": 135.959, "end": 136.739, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 136.739, "end": 141.579, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "C'è", "start": 141.579, "end": 141.739, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 141.739, "end": 141.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "qualcosa", "start": 141.739, "end": 142.079, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 142.079, "end": 142.099, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 142.099, "end": 142.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 142.199, "end": 142.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "non", "start": 142.219, "end": 142.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 142.319, "end": 142.339, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "va?", "start": 142.339, "end": 142.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 142.619, "end": 143.519, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Dove", "start": 143.519, "end": 143.759, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 143.759, "end": 143.799, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "è", "start": 143.799, "end": 143.819, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 143.819, "end": 143.839, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "andata?", "start": 143.839, "end": 144.299, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 144.299, "end": 144.479, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "A", "start": 144.479, "end": 144.599, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 144.599, "end": 144.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "un", "start": 144.619, "end": 144.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 144.699, "end": 144.719, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ricevimento.", "start": 144.719, "end": 145.519, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 145.519, "end": 145.519, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Che", "start": 145.519, "end": 145.599, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 145.599, "end": 145.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "razza", "start": 145.599, "end": 145.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 145.819, "end": 145.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 145.819, "end": 145.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 145.899, "end": 145.939, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 145.939, "end": 146.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 146.239, "end": 146.259, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "va", "start": 146.259, "end": 146.359, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 146.359, "end": 146.379, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "da", "start": 146.379, "end": 146.399, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 146.399, "end": 146.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "fare", "start": 146.459, "end": 146.599, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 146.599, "end": 146.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "il", "start": 146.619, "end": 146.679, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 146.679, "end": 146.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "bagno?", "start": 146.699, "end": 147.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 147.119, "end": 147.439, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "A", "start": 147.439, "end": 147.599, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 147.599, "end": 147.639, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 147.639, "end": 147.939, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 147.939, "end": 147.979, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 147.979, "end": 148.439, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 148.439, "end": 148.519, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "c'è", "start": 148.519, "end": 148.699, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 148.699, "end": 148.699, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "una", "start": 148.699, "end": 148.819, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 148.819, "end": 148.859, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "spiaggia", "start": 148.859, "end": 149.199, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 149.199, "end": 149.219, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "isolata", "start": 149.219, "end": 149.599, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 149.599, "end": 149.619, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "da", "start": 149.619, "end": 149.699, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 149.699, "end": 149.739, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "quella", "start": 149.739, "end": 149.919, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 149.919, "end": 149.959, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "parte.", "start": 149.959, "end": 150.299, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 150.299, "end": 150.299, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Trove<PERSON><PERSON>", "start": 150.299, "end": 150.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 150.659, "end": 150.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "quello", "start": 150.699, "end": 150.879, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 150.879, "end": 150.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 150.919, "end": 150.999, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 150.999, "end": 151.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "cerco", "start": 151.019, "end": 151.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 151.319, "end": 151.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "da", "start": 151.319, "end": 151.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 151.459, "end": 151.499, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sola.", "start": 151.499, "end": 151.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 151.819, "end": 151.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Sono", "start": 151.819, "end": 151.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 151.979, "end": 151.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 151.979, "end": 152.359, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 152.359, "end": 152.399, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "grande", "start": 152.399, "end": 152.739, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 152.739, "end": 152.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "per", "start": 152.739, "end": 152.939, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 152.939, "end": 152.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "cavar<PERSON>.", "start": 152.959, "end": 153.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 153.559, "end": 157.079, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Passava", "start": 157.079, "end": 157.599, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 157.599, "end": 157.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "delle", "start": 157.619, "end": 157.879, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 157.879, "end": 157.899, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ore", "start": 157.899, "end": 158.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 158.159, "end": 158.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 158.159, "end": 158.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 158.239, "end": 158.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 158.239, "end": 158.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 158.799, "end": 158.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "al", "start": 158.819, "end": 158.879, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 158.879, "end": 158.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sole", "start": 158.979, "end": 159.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 159.199, "end": 159.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sugli", "start": 159.219, "end": 159.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 159.459, "end": 159.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "scogli,", "start": 159.479, "end": 160.099, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 160.099, "end": 160.639, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "poi", "start": 160.639, "end": 160.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 160.819, "end": 160.839, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "si", "start": 160.839, "end": 160.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 160.959, "end": 160.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tuffava", "start": 160.979, "end": 161.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 161.299, "end": 161.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "in", "start": 161.299, "end": 161.419, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 161.419, "end": 161.419, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "acqua", "start": 161.419, "end": 161.639, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 161.639, "end": 161.639, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "e", "start": 161.639, "end": 161.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 161.699, "end": 161.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "nuotava.", "start": 161.699, "end": 162.279, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 162.279, "end": 163.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Le", "start": 163.479, "end": 163.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 163.619, "end": 163.639, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "nostre", "start": 163.639, "end": 163.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 163.899, "end": 163.939, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "donne", "start": 163.939, "end": 164.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 164.199, "end": 164.199, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "in", "start": 164.199, "end": 164.279, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 164.279, "end": 164.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "quest'isola", "start": 164.299, "end": 164.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 164.859, "end": 164.859, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "non", "start": 164.859, "end": 164.999, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 164.999, "end": 165.039, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "lo", "start": 165.039, "end": 165.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 165.119, "end": 165.179, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "fanno.", "start": 165.179, "end": 165.639, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 165.639, "end": 165.899, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Il", "start": 165.899, "end": 166.019, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 166.019, "end": 166.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "mare", "start": 166.059, "end": 166.279, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 166.279, "end": 166.279, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ci", "start": 166.279, "end": 166.399, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 166.399, "end": 166.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "serve", "start": 166.459, "end": 166.719, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 166.719, "end": 166.719, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "per", "start": 166.719, "end": 166.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 166.859, "end": 166.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "darci", "start": 166.879, "end": 167.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 167.119, "end": 167.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "da", "start": 167.139, "end": 167.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 167.219, "end": 167.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "mangiare,", "start": 167.239, "end": 167.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 167.759, "end": 167.759, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "non", "start": 167.759, "end": 167.939, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 167.939, "end": 167.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "per", "start": 167.979, "end": 168.099, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 168.099, "end": 168.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "divertimento.", "start": 168.119, "end": 168.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 168.899, "end": 169.939, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Ma", "start": 169.939, "end": 170.059, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 170.059, "end": 170.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "questo", "start": 170.159, "end": 170.519, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 170.519, "end": 170.579, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>,", "start": 170.579, "end": 171.039, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 171.039, "end": 171.419, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "non", "start": 171.419, "end": 171.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 171.579, "end": 171.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "lo", "start": 171.599, "end": 171.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 171.699, "end": 171.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "capii.", "start": 171.739, "end": 172.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 172.319, "end": 192.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Quel", "start": 192.239, "end": 192.419, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 192.419, "end": 192.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>ior<PERSON>", "start": 192.479, "end": 192.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 192.779, "end": 192.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "la", "start": 192.779, "end": 192.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 192.919, "end": 192.939, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 192.939, "end": 193.359, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 193.359, "end": 193.899, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "e", "start": 193.899, "end": 193.999, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 193.999, "end": 194.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "non", "start": 194.019, "end": 194.179, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 194.179, "end": 194.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "vista", "start": 194.219, "end": 194.499, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 194.499, "end": 194.499, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "assist<PERSON>", "start": 194.499, "end": 195.039, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 195.039, "end": 195.039, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "al", "start": 195.039, "end": 195.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 195.139, "end": 195.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "suo", "start": 195.159, "end": 195.279, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 195.279, "end": 195.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "brusco", "start": 195.299, "end": 195.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 195.619, "end": 195.639, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "incontro", "start": 195.639, "end": 196.059, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 196.059, "end": 196.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "con", "start": 196.059, "end": 196.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 196.239, "end": 196.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>.", "start": 196.239, "end": 196.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 196.619, "end": 197.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Sep<PERSON>", "start": 197.879, "end": 198.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 198.159, "end": 198.199, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "più", "start": 198.199, "end": 198.399, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 198.399, "end": 198.399, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tardi", "start": 198.399, "end": 198.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 198.759, "end": 198.759, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 198.759, "end": 198.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 198.859, "end": 198.859, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "l'aveva", "start": 198.859, "end": 199.179, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 199.179, "end": 199.179, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "offeso", "start": 199.179, "end": 199.599, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 199.599, "end": 199.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "mortalmente", "start": 199.619, "end": 200.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 200.319, "end": 200.359, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "chiam<PERSON><PERSON>", "start": 200.359, "end": 200.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 200.979, "end": 201.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sporco", "start": 201.059, "end": 201.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 201.579, "end": 201.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "negro.", "start": 201.619, "end": 202.079, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 202.079, "end": 227.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Signorina,", "start": 227.979, "end": 228.539, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 228.539, "end": 229.479, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "quella", "start": 229.479, "end": 229.799, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 229.799, "end": 229.859, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "è", "start": 229.859, "end": 229.879, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 229.879, "end": 229.879, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "uscita", "start": 229.879, "end": 230.239, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 230.239, "end": 230.259, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "in", "start": 230.259, "end": 230.339, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 230.339, "end": 230.379, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 230.379, "end": 230.919, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 230.919, "end": 230.919, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "corti,", "start": 230.919, "end": 231.359, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 231.359, "end": 231.359, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "al", "start": 231.359, "end": 231.499, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 231.499, "end": 231.559, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "mare", "start": 231.559, "end": 231.919, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 231.919, "end": 231.919, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "è", "start": 231.919, "end": 231.939, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 231.939, "end": 231.939, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "andata.", "start": 231.939, "end": 232.539, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 232.539, "end": 232.719, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Sì,", "start": 232.719, "end": 232.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 232.899, "end": 232.899, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "lo", "start": 232.899, "end": 233.039, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 233.039, "end": 233.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "so,", "start": 233.059, "end": 233.179, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 233.179, "end": 233.199, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "l'ho", "start": 233.199, "end": 233.279, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 233.279, "end": 233.279, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "incontrata.", "start": 233.279, "end": 233.879, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 233.879, "end": 233.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Papà", "start": 233.879, "end": 234.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 234.119, "end": 234.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "l'ha", "start": 234.139, "end": 234.279, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 234.279, "end": 234.279, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "vista?", "start": 234.279, "end": 234.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 234.659, "end": 234.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "No,", "start": 234.699, "end": 234.879, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 234.879, "end": 235.079, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Don", "start": 235.079, "end": 235.279, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 235.279, "end": 235.359, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON>", "start": 235.359, "end": 235.619, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 235.619, "end": 235.679, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "è", "start": 235.679, "end": 235.699, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 235.699, "end": 235.699, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "uscito", "start": 235.699, "end": 235.999, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 235.999, "end": 235.999, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "presto", "start": 235.999, "end": 236.359, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 236.359, "end": 236.359, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "stamattina.", "start": 236.359, "end": 236.999, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 236.999, "end": 237.059, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Fa", "start": 237.059, "end": 237.259, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 237.259, "end": 237.259, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 237.259, "end": 237.379, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 237.379, "end": 237.399, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "non", "start": 237.399, "end": 237.519, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 237.519, "end": 237.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "lo", "start": 237.539, "end": 237.639, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 237.639, "end": 237.679, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sappia,", "start": 237.679, "end": 237.999, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 237.999, "end": 238.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Mar<PERSON><PERSON>ta,", "start": 238.019, "end": 238.539, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 238.539, "end": 238.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "e", "start": 238.559, "end": 238.599, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 238.599, "end": 238.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "meglio", "start": 238.699, "end": 239.019, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 239.019, "end": 239.039, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "non", "start": 239.039, "end": 239.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 239.219, "end": 239.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "irritarlo.", "start": 239.219, "end": 239.939, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 239.939, "end": 240.259, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "A", "start": 240.259, "end": 240.399, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 240.399, "end": 240.599, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "quando", "start": 240.599, "end": 240.919, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 240.919, "end": 240.919, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "lo", "start": 240.919, "end": 241.059, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 241.059, "end": 241.079, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "saprà,", "start": 241.079, "end": 241.439, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 241.439, "end": 241.459, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "non", "start": 241.459, "end": 241.599, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 241.599, "end": 241.599, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "avrà", "start": 241.599, "end": 241.759, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 241.759, "end": 241.759, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "il", "start": 241.759, "end": 241.859, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 241.859, "end": 241.859, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "coraggio", "start": 241.859, "end": 242.319, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 242.319, "end": 242.319, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "di", "start": 242.319, "end": 242.439, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 242.439, "end": 242.459, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "dirle", "start": 242.459, "end": 242.719, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 242.719, "end": 242.739, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "niente.", "start": 242.739, "end": 243.239, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 243.239, "end": 243.779, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "In", "start": 243.779, "end": 244.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 244.019, "end": 244.039, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "paese", "start": 244.039, "end": 244.479, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 244.479, "end": 244.479, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "sono", "start": 244.479, "end": 244.659, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 244.659, "end": 244.659, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "cominciate", "start": 244.659, "end": 245.139, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 245.139, "end": 245.139, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "già", "start": 245.139, "end": 245.279, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 245.279, "end": 245.279, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "le", "start": 245.279, "end": 245.439, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 245.439, "end": 245.439, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "chiac<PERSON><PERSON>.", "start": 245.439, "end": 246.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 246.019, "end": 246.419, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Dicono", "start": 246.419, "end": 246.859, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 246.859, "end": 246.899, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "che", "start": 246.899, "end": 246.999, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 246.999, "end": 247.099, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "prima", "start": 247.099, "end": 247.399, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 247.399, "end": 247.399, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "o", "start": 247.399, "end": 247.479, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 247.479, "end": 247.479, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "poi", "start": 247.479, "end": 247.659, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 247.659, "end": 247.659, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "quella", "start": 247.659, "end": 247.999, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 247.999, "end": 248.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "finirà", "start": 248.019, "end": 248.359, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 248.359, "end": 248.379, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "per", "start": 248.379, "end": 248.479, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 248.479, "end": 248.479, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "rovinare", "start": 248.479, "end": 248.939, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 248.939, "end": 248.939, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "questa", "start": 248.939, "end": 249.219, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 249.219, "end": 249.279, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "casa", "start": 249.279, "end": 249.539, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 249.539, "end": 249.539, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "e", "start": 249.539, "end": 249.599, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 249.599, "end": 249.639, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Don", "start": 249.639, "end": 249.799, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 249.799, "end": 249.859, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON>.", "start": 249.859, "end": 250.419, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 250.419, "end": 251.359, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 251.359, "end": 251.939, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 251.939, "end": 251.939, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "qualche", "start": 251.939, "end": 252.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 252.239, "end": 252.259, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tempo", "start": 252.259, "end": 252.539, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 252.539, "end": 252.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "dopo", "start": 252.539, "end": 252.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 252.779, "end": 252.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "i", "start": 252.799, "end": 252.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 252.859, "end": 252.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "miei", "start": 252.879, "end": 253.039, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 253.039, "end": 253.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "dubbi", "start": 253.059, "end": 253.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 253.319, "end": 253.339, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ebbe<PERSON>", "start": 253.339, "end": 253.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 253.619, "end": 253.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "una", "start": 253.619, "end": 253.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 253.759, "end": 253.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "conferma.", "start": 253.819, "end": 254.499, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 254.499, "end": 255.279, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 255.279, "end": 255.439, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 255.439, "end": 255.499, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "padre", "start": 255.499, "end": 255.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 255.819, "end": 255.839, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "mi", "start": 255.839, "end": 255.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 255.919, "end": 255.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "aveva", "start": 255.919, "end": 256.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 256.219, "end": 256.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "pregato", "start": 256.219, "end": 256.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 256.619, "end": 256.639, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 256.639, "end": 256.719, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 256.719, "end": 256.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "andare", "start": 256.739, "end": 257.019, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 257.019, "end": 257.039, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ad", "start": 257.039, "end": 257.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 257.159, "end": 257.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "aiutarla.", "start": 257.159, "end": 257.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 257.859, "end": 258.679, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Sperava", "start": 258.679, "end": 259.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 259.119, "end": 259.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 259.159, "end": 259.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 259.239, "end": 259.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sa<PERSON><PERSON>", "start": 259.299, "end": 259.599, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 259.599, "end": 259.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "diventate", "start": 259.619, "end": 260.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 260.159, "end": 260.179, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "amiche.", "start": 260.179, "end": 260.739, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 260.739, "end": 264.039, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>,", "start": 264.039, "end": 265.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 265.139, "end": 265.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sei", "start": 265.159, "end": 265.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 265.299, "end": 265.379, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tu?", "start": 265.379, "end": 265.639, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 265.639, "end": 266.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>?", "start": 266.919, "end": 267.419, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 267.419, "end": 267.939, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Sì,", "start": 267.939, "end": 268.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 268.139, "end": 268.179, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sono", "start": 268.179, "end": 268.419, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 268.419, "end": 268.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "io.", "start": 268.459, "end": 268.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 268.659, "end": 268.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Cosa", "start": 268.699, "end": 268.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 268.899, "end": 268.939, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ti", "start": 268.939, "end": 269.059, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 269.059, "end": 269.079, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "serve?", "start": 269.079, "end": 269.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 269.479, "end": 269.679, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Il", "start": 269.679, "end": 269.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 269.759, "end": 269.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "mio", "start": 269.779, "end": 269.879, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 269.879, "end": 269.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON>,", "start": 269.879, "end": 270.599, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 270.599, "end": 270.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "quello", "start": 270.799, "end": 271.019, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 271.019, "end": 271.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "bianco", "start": 271.019, "end": 271.339, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 271.339, "end": 271.339, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 271.339, "end": 271.439, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 271.439, "end": 271.439, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sta", "start": 271.439, "end": 271.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 271.559, "end": 271.579, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sulla", "start": 271.579, "end": 271.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 271.759, "end": 271.759, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "poltrona.", "start": 271.759, "end": 272.339, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 272.339, "end": 272.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 272.919, "end": 273.379, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 273.379, "end": 273.379, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "anche", "start": 273.379, "end": 273.539, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 273.539, "end": 273.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "le", "start": 273.559, "end": 273.639, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 273.639, "end": 273.639, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "mollez<PERSON>,", "start": 273.639, "end": 274.039, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 274.039, "end": 274.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 274.059, "end": 274.279, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 274.279, "end": 274.279, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "essere", "start": 274.279, "end": 274.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 274.479, "end": 274.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "accanto", "start": 274.479, "end": 274.719, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 274.719, "end": 274.719, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "allo", "start": 274.719, "end": 274.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 274.859, "end": 274.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "specchio.", "start": 274.879, "end": 275.519, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 275.519, "end": 281.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Non", "start": 281.559, "end": 284.019, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 284.019, "end": 284.039, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "ci", "start": 284.039, "end": 284.159, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 284.159, "end": 284.199, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "so.", "start": 284.199, "end": 284.499, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 284.499, "end": 284.599, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Guarda", "start": 284.599, "end": 284.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 284.959, "end": 285.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "nella", "start": 285.019, "end": 285.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 285.119, "end": 285.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "mia", "start": 285.139, "end": 285.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 285.239, "end": 285.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "valigia", "start": 285.239, "end": 285.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 285.559, "end": 285.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "allora,", "start": 285.599, "end": 285.839, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 285.839, "end": 285.839, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "quella", "start": 285.839, "end": 286.039, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 286.039, "end": 286.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "piccola.", "start": 286.059, "end": 286.539, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 286.539, "end": 293.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}], "language_detected": null, "confidence": null, "processing_time": 28.33228087425232, "metadata": {"service": "elevenlabs", "mode": "free", "model": "scribe_v1", "raw_result": {"language_code": "ita", "language_probability": 0.9976788759231567, "text": "Credo che bisognerà assumere una serva più giovane. Quella è un po' malandata, mi pare. Noi non consideriamo Margherita una serva, ci ha visto nascere tutti. Molte cose dovranno cambiare. E se dovremmo vivere insieme, sarà bene spiegarci una volta per sempre. Non sono venuta a seppellirmi qui per sopportare le prediche di nessuno. Smettiamola adesso. Tutto deve continuare come sempre in questa casa. E tu non prendere quell'aria da vittima. Se ci sarà qualche cosa da cambiare, cambieremo senza troppe storie. Ho già noie sufficienti nell'isola per dovermi preoccupare anche dei tuoi capricci. Noie, <PERSON>? Di che genere? Denaro? No, non si tratta di denaro. Sono i pescatori che alzano la cresta. I pescatori vogliono soltanto vivere. Sono amici tuoi, lo so. E tu li difendi contro tuo padre. <PERSON><PERSON>, dì quello che vuoi dirmi in fretta. Vuoi rimproveri qualcosa? Qualcosa che non va, alla signorina? O hai visto ancora quella canaglia prima che i battelli partissero? Eri con lui al molo, vero? Rosario non è una canaglia e tu lo sai bene. Ma è lui che organizza i pescatori contro di me e tu gli tieni mano. È innamorata, ecco tutto. E una donna innamorata è sempre pericolosa, anche nei confronti del padre. No, nessun pericolo fino a che sarò io il padrone qui. So bene io come sistemare le cose, ma non parliamo di questo. Vieni Carla. Io credo che addirittura si divertisse ad Are scander, ma mio padre non si accorgeva di niente. Cosa intendi dire per escander? Gli dico la mattina. Sì, quella mattina, mentre tornavo a casa dall'aver fatto la spesa, la incontrai. C'è qualcosa che non va? Dove è andata? A un ricevimento. Che razza di domanda va da fare il bagno? A Punta Piccola c'è una spiaggia isolata da quella parte. Troverò quello che cerco da sola. Sono abbastanza grande per cavarmi. Passava delle ore a bruciarsi al sole sugli scogli, poi si tuffava in acqua e nuotava. Le nostre donne in quest'isola non lo fanno. Il mare ci serve per darci da mangiare, non per divertimento. Ma questo Carla, non lo capii. Quel giorno la seguì e non vista assistetti al suo brusco incontro con Abu. Seppe più tardi che l'aveva offeso mortalmente chiamandolo sporco negro. Signorina, quella è uscita in pantaloni corti, al mare è andata. Sì, lo so, l'ho incontrata. Papà l'ha vista? No, Don Pedro è uscito presto stamattina. Fa che non lo sappia, Margherita, e meglio non irritarlo. A quando lo saprà, non avrà il coraggio di dirle niente. In paese sono cominciate già le chiacchere. Dicono che prima o poi quella finirà per rovinare questa casa e Don Pedro. Purtroppo qualche tempo dopo i miei dubbi ebbero una conferma. Mio padre mi aveva pregato di andare ad aiutarla. Sperava che saremmo diventate amiche. Maria, sei tu? Maria? Sì, sono io. Cosa ti serve? Il mio accappatoio, quello bianco che sta sulla poltrona. Prendimi anche le mollezze, devono essere accanto allo specchio. Non ci so. Guarda nella mia valigia allora, quella piccola. ", "words": [{"text": "<PERSON><PERSON>", "start": 20.699, "end": 21.019, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 21.019, "end": 21.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 21.059, "end": 21.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 21.139, "end": 21.18, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "bisognerà", "start": 21.18, "end": 21.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 21.579, "end": 21.6, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "assumere", "start": 21.6, "end": 21.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 21.959, "end": 21.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "una", "start": 21.959, "end": 22.079, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 22.079, "end": 22.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "serva", "start": 22.119, "end": 22.399, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 22.399, "end": 22.439, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "più", "start": 22.439, "end": 22.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 22.559, "end": 22.6, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "giovane.", "start": 22.6, "end": 23.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 23.159, "end": 24.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 24.459, "end": 24.719, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 24.719, "end": 24.719, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "è", "start": 24.719, "end": 24.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 24.779, "end": 24.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "un", "start": 24.779, "end": 24.92, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 24.92, "end": 24.939, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "po'", "start": 24.939, "end": 25.04, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 25.04, "end": 25.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "maland<PERSON>,", "start": 25.059, "end": 25.639, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 25.639, "end": 25.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "mi", "start": 25.659, "end": 25.78, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 25.78, "end": 25.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "pare.", "start": 25.819, "end": 26.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 26.199, "end": 26.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>i", "start": 26.559, "end": 26.739, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 26.739, "end": 26.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "non", "start": 26.779, "end": 26.879, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 26.879, "end": 26.939, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON>o", "start": 26.939, "end": 27.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 27.619, "end": 27.639, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Margh<PERSON><PERSON>", "start": 27.639, "end": 28.18, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 28.18, "end": 28.18, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "una", "start": 28.18, "end": 28.399, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 28.399, "end": 28.42, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "serva,", "start": 28.42, "end": 28.879, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 28.879, "end": 28.92, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ci", "start": 28.92, "end": 29.0, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 29.0, "end": 29.0, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ha", "start": 29.0, "end": 29.079, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 29.079, "end": 29.079, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "visto", "start": 29.079, "end": 29.34, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 29.34, "end": 29.359, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "nascere", "start": 29.359, "end": 29.76, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 29.76, "end": 29.76, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tutti.", "start": 29.76, "end": 30.179, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 30.179, "end": 30.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 30.479, "end": 30.84, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 30.84, "end": 30.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "cose", "start": 30.879, "end": 31.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 31.159, "end": 31.179, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "dovranno", "start": 31.179, "end": 31.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 31.559, "end": 31.579, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "cambiare.", "start": 31.579, "end": 32.18, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 32.18, "end": 32.579, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "E", "start": 32.579, "end": 32.679, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 32.679, "end": 32.719, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "se", "start": 32.719, "end": 32.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 32.819, "end": 32.84, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 32.84, "end": 33.18, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 33.18, "end": 33.18, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "vivere", "start": 33.18, "end": 33.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 33.479, "end": 33.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "insieme,", "start": 33.479, "end": 34.04, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 34.04, "end": 34.04, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sarà", "start": 34.04, "end": 34.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 34.299, "end": 34.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "bene", "start": 34.299, "end": 34.56, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 34.56, "end": 34.579, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 34.579, "end": 35.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 35.319, "end": 35.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "una", "start": 35.319, "end": 35.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 35.479, "end": 35.5, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "volta", "start": 35.5, "end": 35.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 35.779, "end": 35.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "per", "start": 35.819, "end": 35.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 35.959, "end": 36.079, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sempre.", "start": 36.079, "end": 36.599, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 36.599, "end": 37.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Non", "start": 37.299, "end": 37.439, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 37.439, "end": 37.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sono", "start": 37.479, "end": 37.68, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 37.68, "end": 37.719, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "venuta", "start": 37.719, "end": 37.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 37.959, "end": 37.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 37.959, "end": 38.04, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 38.04, "end": 38.04, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "start": 38.04, "end": 38.54, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 38.54, "end": 38.54, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "qui", "start": 38.54, "end": 38.68, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 38.68, "end": 38.719, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "per", "start": 38.719, "end": 38.84, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 38.84, "end": 38.84, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sopportare", "start": 38.84, "end": 39.28, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 39.28, "end": 39.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "le", "start": 39.299, "end": 39.399, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 39.399, "end": 39.419, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "prediche", "start": 39.419, "end": 39.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 39.759, "end": 39.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 39.799, "end": 39.88, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 39.88, "end": 39.899, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "nessuno.", "start": 39.899, "end": 40.379, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 40.379, "end": 40.379, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 40.379, "end": 41.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 41.559, "end": 41.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "adesso.", "start": 41.559, "end": 41.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 41.979, "end": 42.5, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 42.5, "end": 42.72, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 42.72, "end": 42.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "deve", "start": 42.739, "end": 42.959, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 42.959, "end": 42.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "continuare", "start": 42.979, "end": 43.5, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 43.5, "end": 43.52, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "come", "start": 43.52, "end": 43.719, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 43.719, "end": 43.719, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sempre", "start": 43.719, "end": 44.02, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 44.02, "end": 44.04, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "in", "start": 44.04, "end": 44.099, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 44.099, "end": 44.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "questa", "start": 44.119, "end": 44.359, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 44.359, "end": 44.36, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "casa.", "start": 44.36, "end": 44.719, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 44.719, "end": 44.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "E", "start": 44.819, "end": 44.959, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 44.959, "end": 44.959, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "tu", "start": 44.959, "end": 45.08, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 45.08, "end": 45.099, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "non", "start": 45.099, "end": 45.22, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 45.22, "end": 45.239, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "prendere", "start": 45.239, "end": 45.52, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 45.52, "end": 45.539, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "quell'aria", "start": 45.539, "end": 45.899, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 45.899, "end": 45.899, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "da", "start": 45.899, "end": 46.0, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 46.0, "end": 46.0, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "vittima.", "start": 46.0, "end": 46.499, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 46.499, "end": 47.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Se", "start": 47.379, "end": 47.5, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 47.5, "end": 47.52, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ci", "start": 47.52, "end": 47.619, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 47.619, "end": 47.659, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sarà", "start": 47.659, "end": 47.84, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 47.84, "end": 47.899, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "qualche", "start": 47.899, "end": 48.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 48.139, "end": 48.159, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "cosa", "start": 48.159, "end": 48.34, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 48.34, "end": 48.36, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "da", "start": 48.36, "end": 48.44, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 48.44, "end": 48.459, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "cambiare,", "start": 48.459, "end": 48.879, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 48.879, "end": 48.879, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "cambieremo", "start": 48.879, "end": 49.379, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 49.379, "end": 49.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "senza", "start": 49.379, "end": 49.619, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 49.619, "end": 49.619, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "troppe", "start": 49.619, "end": 49.84, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 49.84, "end": 49.84, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "storie.", "start": 49.84, "end": 50.259, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 50.259, "end": 54.279, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>", "start": 54.279, "end": 54.439, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 54.439, "end": 54.439, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "già", "start": 54.439, "end": 54.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 54.559, "end": 54.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "noie", "start": 54.559, "end": 54.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 54.799, "end": 54.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sufficienti", "start": 54.819, "end": 55.34, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 55.34, "end": 55.34, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "nell'isola", "start": 55.34, "end": 55.759, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 55.759, "end": 55.779, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "per", "start": 55.779, "end": 55.899, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 55.899, "end": 55.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 55.919, "end": 56.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 56.299, "end": 56.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "preoccupare", "start": 56.299, "end": 56.86, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 56.86, "end": 56.86, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "anche", "start": 56.86, "end": 57.079, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 57.079, "end": 57.099, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "dei", "start": 57.099, "end": 57.199, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 57.199, "end": 57.199, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "tuoi", "start": 57.199, "end": 57.34, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 57.34, "end": 57.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>.", "start": 57.379, "end": 57.839, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 57.839, "end": 58.259, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON>,", "start": 58.259, "end": 58.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 58.619, "end": 58.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>?", "start": 58.619, "end": 59.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 59.119, "end": 59.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Di", "start": 59.879, "end": 59.999, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 59.999, "end": 59.999, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 59.999, "end": 60.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 60.159, "end": 60.199, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "genere?", "start": 60.199, "end": 60.719, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 60.719, "end": 62.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Denaro?", "start": 62.219, "end": 62.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 62.859, "end": 64.179, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "No,", "start": 64.179, "end": 64.539, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 64.539, "end": 64.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "non", "start": 64.559, "end": 64.659, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 64.659, "end": 64.699, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "si", "start": 64.699, "end": 64.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 64.799, "end": 64.839, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "tratta", "start": 64.839, "end": 65.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 65.139, "end": 65.159, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 65.159, "end": 65.219, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 65.219, "end": 65.219, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "denaro.", "start": 65.219, "end": 65.679, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 65.679, "end": 65.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Sono", "start": 65.919, "end": 66.119, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 66.119, "end": 66.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "i", "start": 66.119, "end": 66.179, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 66.179, "end": 66.179, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "pescatori", "start": 66.179, "end": 66.639, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 66.639, "end": 66.659, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "che", "start": 66.659, "end": 66.759, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 66.759, "end": 66.759, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "alzano", "start": 66.759, "end": 66.999, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 66.999, "end": 67.019, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "la", "start": 67.019, "end": 67.099, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 67.099, "end": 67.099, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "cresta.", "start": 67.099, "end": 67.539, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 67.539, "end": 67.779, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "I", "start": 67.779, "end": 67.879, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 67.879, "end": 67.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "pescatori", "start": 67.919, "end": 68.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 68.459, "end": 68.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "vog<PERSON><PERSON>", "start": 68.479, "end": 68.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 68.819, "end": 68.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "soltanto", "start": 68.879, "end": 69.379, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 69.379, "end": 69.399, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "vivere.", "start": 69.399, "end": 69.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 69.859, "end": 70.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Sono", "start": 70.699, "end": 70.919, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 70.919, "end": 70.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "amici", "start": 70.919, "end": 71.199, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 71.199, "end": 71.219, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "tuoi,", "start": 71.219, "end": 71.439, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 71.439, "end": 71.439, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "lo", "start": 71.439, "end": 71.539, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 71.539, "end": 71.579, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "so.", "start": 71.579, "end": 71.919, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 71.919, "end": 72.539, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "E", "start": 72.539, "end": 72.639, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 72.639, "end": 72.679, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "tu", "start": 72.679, "end": 72.759, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 72.759, "end": 72.759, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "li", "start": 72.759, "end": 72.839, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 72.839, "end": 72.859, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "difendi", "start": 72.859, "end": 73.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 73.159, "end": 73.159, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "contro", "start": 73.159, "end": 73.379, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 73.379, "end": 73.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "tuo", "start": 73.379, "end": 73.519, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 73.519, "end": 73.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "padre.", "start": 73.559, "end": 73.899, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 73.899, "end": 74.099, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON>,", "start": 74.099, "end": 74.639, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 74.639, "end": 75.159, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "dì", "start": 75.159, "end": 75.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 75.299, "end": 75.339, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "quello", "start": 75.339, "end": 75.499, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 75.499, "end": 75.499, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "che", "start": 75.499, "end": 75.599, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 75.599, "end": 75.619, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "vuoi", "start": 75.619, "end": 75.739, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 75.739, "end": 75.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "dir<PERSON>", "start": 75.739, "end": 75.939, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 75.939, "end": 75.959, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "in", "start": 75.959, "end": 76.059, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 76.059, "end": 76.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "fretta.", "start": 76.059, "end": 76.419, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 76.419, "end": 77.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 77.379, "end": 77.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 77.479, "end": 77.579, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 77.579, "end": 77.899, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 77.899, "end": 77.899, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "qualcosa?", "start": 77.899, "end": 78.379, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 78.379, "end": 78.499, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Qualcosa", "start": 78.499, "end": 78.919, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 78.919, "end": 78.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "che", "start": 78.919, "end": 79.019, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 79.019, "end": 79.039, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "non", "start": 79.039, "end": 79.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 79.139, "end": 79.179, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "va,", "start": 79.179, "end": 79.379, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 79.379, "end": 79.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "alla", "start": 79.379, "end": 79.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 79.479, "end": 79.519, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "signorina?", "start": 79.519, "end": 80.059, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 80.059, "end": 80.519, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "O", "start": 80.519, "end": 80.619, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 80.619, "end": 80.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "hai", "start": 80.639, "end": 80.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 80.799, "end": 80.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "visto", "start": 80.819, "end": 81.019, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 81.019, "end": 81.019, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ancora", "start": 81.019, "end": 81.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 81.299, "end": 81.339, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "quella", "start": 81.339, "end": 81.519, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 81.519, "end": 81.579, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "canaglia", "start": 81.579, "end": 81.919, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 81.919, "end": 81.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "prima", "start": 81.919, "end": 82.119, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 82.119, "end": 82.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "che", "start": 82.119, "end": 82.219, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 82.219, "end": 82.219, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "i", "start": 82.219, "end": 82.279, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 82.279, "end": 82.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "battelli", "start": 82.299, "end": 82.659, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 82.659, "end": 82.679, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "partissero?", "start": 82.679, "end": 83.239, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 83.239, "end": 83.599, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 83.599, "end": 83.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 83.799, "end": 83.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "con", "start": 83.799, "end": 83.939, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 83.939, "end": 83.939, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "lui", "start": 83.939, "end": 84.079, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 84.079, "end": 84.079, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "al", "start": 84.079, "end": 84.199, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 84.199, "end": 84.199, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "molo,", "start": 84.199, "end": 84.439, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 84.439, "end": 84.439, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "vero?", "start": 84.439, "end": 84.739, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 84.739, "end": 85.199, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Rosario", "start": 85.199, "end": 85.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 85.659, "end": 85.679, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "non", "start": 85.679, "end": 85.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 85.819, "end": 85.839, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "è", "start": 85.839, "end": 85.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 85.919, "end": 85.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "una", "start": 85.919, "end": 86.079, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 86.079, "end": 86.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "canaglia", "start": 86.139, "end": 86.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 86.559, "end": 86.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "e", "start": 86.879, "end": 86.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 86.979, "end": 87.039, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tu", "start": 87.039, "end": 87.179, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 87.179, "end": 87.199, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "lo", "start": 87.199, "end": 87.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 87.299, "end": 87.359, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sai", "start": 87.359, "end": 87.499, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 87.499, "end": 87.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "bene.", "start": 87.539, "end": 87.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 87.859, "end": 87.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Ma", "start": 87.979, "end": 88.099, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 88.099, "end": 88.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "è", "start": 88.119, "end": 88.199, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 88.199, "end": 88.199, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "lui", "start": 88.199, "end": 88.419, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 88.419, "end": 88.419, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "che", "start": 88.419, "end": 88.499, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 88.499, "end": 88.499, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "organizza", "start": 88.499, "end": 88.859, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 88.859, "end": 88.859, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "i", "start": 88.859, "end": 88.939, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 88.939, "end": 88.939, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "pescatori", "start": 88.939, "end": 89.359, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 89.359, "end": 89.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "contro", "start": 89.379, "end": 89.599, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 89.599, "end": 89.619, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 89.619, "end": 89.679, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 89.679, "end": 89.719, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "me", "start": 89.719, "end": 89.899, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 89.899, "end": 90.599, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "e", "start": 90.599, "end": 90.739, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 90.739, "end": 90.779, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "tu", "start": 90.779, "end": 90.899, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 90.899, "end": 90.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "gli", "start": 90.919, "end": 91.039, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 91.039, "end": 91.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "tieni", "start": 91.059, "end": 91.279, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 91.279, "end": 91.279, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mano.", "start": 91.279, "end": 91.659, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 91.659, "end": 94.599, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "È", "start": 94.599, "end": 94.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 94.799, "end": 94.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "innamorata,", "start": 94.799, "end": 95.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 95.759, "end": 95.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ecco", "start": 95.819, "end": 96.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 96.159, "end": 96.179, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tutto.", "start": 96.179, "end": 96.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 96.619, "end": 96.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "E", "start": 96.919, "end": 97.019, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 97.019, "end": 97.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "una", "start": 97.019, "end": 97.179, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 97.179, "end": 97.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "donna", "start": 97.239, "end": 97.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 97.479, "end": 97.499, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "innamorata", "start": 97.499, "end": 98.019, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 98.019, "end": 98.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "è", "start": 98.019, "end": 98.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 98.139, "end": 98.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sempre", "start": 98.139, "end": 98.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 98.479, "end": 98.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "perico<PERSON>a,", "start": 98.539, "end": 99.259, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 99.259, "end": 99.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "anche", "start": 99.539, "end": 99.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 99.759, "end": 99.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "nei", "start": 99.779, "end": 99.939, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 99.939, "end": 99.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>i", "start": 99.959, "end": 100.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 100.479, "end": 100.499, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "del", "start": 100.499, "end": 100.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 100.619, "end": 100.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "padre.", "start": 100.739, "end": 101.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 101.199, "end": 101.279, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "No,", "start": 101.279, "end": 101.679, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 101.679, "end": 103.279, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "nessun", "start": 103.279, "end": 103.639, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 103.639, "end": 103.679, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "pericolo", "start": 103.679, "end": 104.079, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 104.079, "end": 104.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "fino", "start": 104.119, "end": 104.279, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 104.279, "end": 104.279, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "a", "start": 104.279, "end": 104.319, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 104.319, "end": 104.319, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "che", "start": 104.319, "end": 104.459, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 104.459, "end": 104.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sar<PERSON>", "start": 104.479, "end": 104.739, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 104.739, "end": 104.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "io", "start": 104.739, "end": 104.839, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 104.839, "end": 104.859, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "il", "start": 104.859, "end": 104.919, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 104.919, "end": 104.939, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "padrone", "start": 104.939, "end": 105.319, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 105.319, "end": 105.319, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "qui.", "start": 105.319, "end": 105.599, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 105.599, "end": 106.039, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "So", "start": 106.039, "end": 106.179, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 106.179, "end": 106.179, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "bene", "start": 106.179, "end": 106.339, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 106.339, "end": 106.359, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "io", "start": 106.359, "end": 106.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 106.479, "end": 106.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "come", "start": 106.479, "end": 106.679, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 106.679, "end": 106.679, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 106.679, "end": 107.079, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 107.079, "end": 107.079, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "le", "start": 107.079, "end": 107.179, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 107.179, "end": 107.219, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "cose,", "start": 107.219, "end": 107.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 107.559, "end": 107.619, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ma", "start": 107.619, "end": 107.859, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 107.859, "end": 108.219, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "non", "start": 108.219, "end": 108.359, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 108.359, "end": 108.399, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "parlia<PERSON>", "start": 108.399, "end": 108.759, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 108.759, "end": 108.759, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 108.759, "end": 108.879, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 108.879, "end": 108.899, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "questo.", "start": 108.899, "end": 109.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 109.299, "end": 113.719, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 113.719, "end": 114.019, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 114.019, "end": 114.039, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>.", "start": 114.039, "end": 116.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 116.799, "end": 121.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Io", "start": 121.559, "end": 122.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 122.919, "end": 122.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "credo", "start": 122.979, "end": 123.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 123.199, "end": 123.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 123.239, "end": 123.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 123.319, "end": 123.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 123.319, "end": 123.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 123.799, "end": 123.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "si", "start": 123.799, "end": 123.939, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 123.939, "end": 123.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "divertisse", "start": 123.959, "end": 124.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 124.579, "end": 124.579, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ad", "start": 124.579, "end": 124.739, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 124.739, "end": 124.759, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Are", "start": 124.759, "end": 124.879, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 124.879, "end": 124.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "scander,", "start": 124.919, "end": 125.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 125.479, "end": 125.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ma", "start": 125.919, "end": 125.999, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 125.999, "end": 126.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "mio", "start": 126.059, "end": 126.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 126.199, "end": 126.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "padre", "start": 126.239, "end": 126.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 126.559, "end": 126.579, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "non", "start": 126.579, "end": 126.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 126.659, "end": 126.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "si", "start": 126.699, "end": 126.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 126.779, "end": 126.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "accorgeva", "start": 126.799, "end": 127.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 127.239, "end": 127.259, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 127.259, "end": 127.339, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 127.339, "end": 127.379, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "niente.", "start": 127.379, "end": 127.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 127.799, "end": 127.859, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Cosa", "start": 127.859, "end": 128.199, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 128.199, "end": 128.199, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "intendi", "start": 128.199, "end": 128.619, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 128.619, "end": 128.619, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "dire", "start": 128.619, "end": 128.859, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 128.859, "end": 128.899, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "per", "start": 128.899, "end": 129.059, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 129.059, "end": 129.079, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "escander?", "start": 129.079, "end": 129.459, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 129.459, "end": 129.459, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 129.459, "end": 129.539, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 129.539, "end": 129.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "dico", "start": 129.539, "end": 129.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 129.779, "end": 129.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "la", "start": 129.779, "end": 129.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 129.899, "end": 129.899, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "mattina.", "start": 129.899, "end": 130.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 130.299, "end": 132.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Sì,", "start": 132.559, "end": 132.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 132.759, "end": 132.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "quella", "start": 132.799, "end": 133.059, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 133.059, "end": 133.079, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "mat<PERSON>,", "start": 133.079, "end": 133.539, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 133.539, "end": 133.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "mentre", "start": 133.539, "end": 133.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 133.799, "end": 133.839, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tornavo", "start": 133.839, "end": 134.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 134.199, "end": 134.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 134.219, "end": 134.279, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 134.279, "end": 134.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "casa", "start": 134.319, "end": 134.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 134.559, "end": 134.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "dall'aver", "start": 134.599, "end": 134.939, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 134.939, "end": 134.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "fatto", "start": 134.979, "end": 135.259, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 135.259, "end": 135.279, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "la", "start": 135.279, "end": 135.359, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 135.359, "end": 135.399, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "spesa,", "start": 135.399, "end": 135.839, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 135.839, "end": 135.839, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "la", "start": 135.839, "end": 135.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 135.959, "end": 135.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "incontrai.", "start": 135.959, "end": 136.739, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 136.739, "end": 141.579, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "C'è", "start": 141.579, "end": 141.739, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 141.739, "end": 141.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "qualcosa", "start": 141.739, "end": 142.079, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 142.079, "end": 142.099, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 142.099, "end": 142.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 142.199, "end": 142.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "non", "start": 142.219, "end": 142.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 142.319, "end": 142.339, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "va?", "start": 142.339, "end": 142.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 142.619, "end": 143.519, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Dove", "start": 143.519, "end": 143.759, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 143.759, "end": 143.799, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "è", "start": 143.799, "end": 143.819, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 143.819, "end": 143.839, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "andata?", "start": 143.839, "end": 144.299, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 144.299, "end": 144.479, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "A", "start": 144.479, "end": 144.599, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 144.599, "end": 144.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "un", "start": 144.619, "end": 144.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 144.699, "end": 144.719, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ricevimento.", "start": 144.719, "end": 145.519, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 145.519, "end": 145.519, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Che", "start": 145.519, "end": 145.599, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 145.599, "end": 145.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "razza", "start": 145.599, "end": 145.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 145.819, "end": 145.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 145.819, "end": 145.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 145.899, "end": 145.939, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 145.939, "end": 146.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 146.239, "end": 146.259, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "va", "start": 146.259, "end": 146.359, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 146.359, "end": 146.379, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "da", "start": 146.379, "end": 146.399, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 146.399, "end": 146.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "fare", "start": 146.459, "end": 146.599, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 146.599, "end": 146.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "il", "start": 146.619, "end": 146.679, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 146.679, "end": 146.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "bagno?", "start": 146.699, "end": 147.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 147.119, "end": 147.439, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "A", "start": 147.439, "end": 147.599, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 147.599, "end": 147.639, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 147.639, "end": 147.939, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 147.939, "end": 147.979, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 147.979, "end": 148.439, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 148.439, "end": 148.519, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "c'è", "start": 148.519, "end": 148.699, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 148.699, "end": 148.699, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "una", "start": 148.699, "end": 148.819, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 148.819, "end": 148.859, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "spiaggia", "start": 148.859, "end": 149.199, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 149.199, "end": 149.219, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "isolata", "start": 149.219, "end": 149.599, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 149.599, "end": 149.619, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "da", "start": 149.619, "end": 149.699, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 149.699, "end": 149.739, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "quella", "start": 149.739, "end": 149.919, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 149.919, "end": 149.959, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "parte.", "start": 149.959, "end": 150.299, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 150.299, "end": 150.299, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Trove<PERSON><PERSON>", "start": 150.299, "end": 150.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 150.659, "end": 150.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "quello", "start": 150.699, "end": 150.879, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 150.879, "end": 150.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 150.919, "end": 150.999, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 150.999, "end": 151.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "cerco", "start": 151.019, "end": 151.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 151.319, "end": 151.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "da", "start": 151.319, "end": 151.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 151.459, "end": 151.499, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sola.", "start": 151.499, "end": 151.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 151.819, "end": 151.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Sono", "start": 151.819, "end": 151.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 151.979, "end": 151.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 151.979, "end": 152.359, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 152.359, "end": 152.399, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "grande", "start": 152.399, "end": 152.739, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 152.739, "end": 152.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "per", "start": 152.739, "end": 152.939, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 152.939, "end": 152.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "cavar<PERSON>.", "start": 152.959, "end": 153.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 153.559, "end": 157.079, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Passava", "start": 157.079, "end": 157.599, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 157.599, "end": 157.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "delle", "start": 157.619, "end": 157.879, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 157.879, "end": 157.899, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ore", "start": 157.899, "end": 158.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 158.159, "end": 158.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 158.159, "end": 158.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 158.239, "end": 158.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 158.239, "end": 158.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 158.799, "end": 158.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "al", "start": 158.819, "end": 158.879, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 158.879, "end": 158.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sole", "start": 158.979, "end": 159.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 159.199, "end": 159.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sugli", "start": 159.219, "end": 159.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 159.459, "end": 159.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "scogli,", "start": 159.479, "end": 160.099, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 160.099, "end": 160.639, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "poi", "start": 160.639, "end": 160.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 160.819, "end": 160.839, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "si", "start": 160.839, "end": 160.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 160.959, "end": 160.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tuffava", "start": 160.979, "end": 161.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 161.299, "end": 161.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "in", "start": 161.299, "end": 161.419, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 161.419, "end": 161.419, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "acqua", "start": 161.419, "end": 161.639, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 161.639, "end": 161.639, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "e", "start": 161.639, "end": 161.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 161.699, "end": 161.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "nuotava.", "start": 161.699, "end": 162.279, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 162.279, "end": 163.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Le", "start": 163.479, "end": 163.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 163.619, "end": 163.639, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "nostre", "start": 163.639, "end": 163.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 163.899, "end": 163.939, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "donne", "start": 163.939, "end": 164.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 164.199, "end": 164.199, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "in", "start": 164.199, "end": 164.279, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 164.279, "end": 164.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "quest'isola", "start": 164.299, "end": 164.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 164.859, "end": 164.859, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "non", "start": 164.859, "end": 164.999, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 164.999, "end": 165.039, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "lo", "start": 165.039, "end": 165.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 165.119, "end": 165.179, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "fanno.", "start": 165.179, "end": 165.639, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 165.639, "end": 165.899, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Il", "start": 165.899, "end": 166.019, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 166.019, "end": 166.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "mare", "start": 166.059, "end": 166.279, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 166.279, "end": 166.279, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ci", "start": 166.279, "end": 166.399, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 166.399, "end": 166.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "serve", "start": 166.459, "end": 166.719, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 166.719, "end": 166.719, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "per", "start": 166.719, "end": 166.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 166.859, "end": 166.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "darci", "start": 166.879, "end": 167.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 167.119, "end": 167.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "da", "start": 167.139, "end": 167.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 167.219, "end": 167.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "mangiare,", "start": 167.239, "end": 167.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 167.759, "end": 167.759, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "non", "start": 167.759, "end": 167.939, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 167.939, "end": 167.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "per", "start": 167.979, "end": 168.099, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 168.099, "end": 168.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "divertimento.", "start": 168.119, "end": 168.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 168.899, "end": 169.939, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Ma", "start": 169.939, "end": 170.059, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 170.059, "end": 170.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "questo", "start": 170.159, "end": 170.519, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 170.519, "end": 170.579, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>,", "start": 170.579, "end": 171.039, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 171.039, "end": 171.419, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "non", "start": 171.419, "end": 171.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 171.579, "end": 171.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "lo", "start": 171.599, "end": 171.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 171.699, "end": 171.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "capii.", "start": 171.739, "end": 172.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 172.319, "end": 192.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Quel", "start": 192.239, "end": 192.419, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 192.419, "end": 192.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>ior<PERSON>", "start": 192.479, "end": 192.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 192.779, "end": 192.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "la", "start": 192.779, "end": 192.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 192.919, "end": 192.939, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 192.939, "end": 193.359, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 193.359, "end": 193.899, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "e", "start": 193.899, "end": 193.999, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 193.999, "end": 194.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "non", "start": 194.019, "end": 194.179, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 194.179, "end": 194.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "vista", "start": 194.219, "end": 194.499, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 194.499, "end": 194.499, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "assist<PERSON>", "start": 194.499, "end": 195.039, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 195.039, "end": 195.039, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "al", "start": 195.039, "end": 195.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 195.139, "end": 195.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "suo", "start": 195.159, "end": 195.279, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 195.279, "end": 195.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "brusco", "start": 195.299, "end": 195.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 195.619, "end": 195.639, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "incontro", "start": 195.639, "end": 196.059, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 196.059, "end": 196.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "con", "start": 196.059, "end": 196.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 196.239, "end": 196.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>.", "start": 196.239, "end": 196.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 196.619, "end": 197.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Sep<PERSON>", "start": 197.879, "end": 198.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 198.159, "end": 198.199, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "più", "start": 198.199, "end": 198.399, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 198.399, "end": 198.399, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tardi", "start": 198.399, "end": 198.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 198.759, "end": 198.759, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 198.759, "end": 198.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 198.859, "end": 198.859, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "l'aveva", "start": 198.859, "end": 199.179, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 199.179, "end": 199.179, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "offeso", "start": 199.179, "end": 199.599, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 199.599, "end": 199.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "mortalmente", "start": 199.619, "end": 200.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 200.319, "end": 200.359, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "chiam<PERSON><PERSON>", "start": 200.359, "end": 200.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 200.979, "end": 201.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sporco", "start": 201.059, "end": 201.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 201.579, "end": 201.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "negro.", "start": 201.619, "end": 202.079, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 202.079, "end": 227.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Signorina,", "start": 227.979, "end": 228.539, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 228.539, "end": 229.479, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "quella", "start": 229.479, "end": 229.799, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 229.799, "end": 229.859, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "è", "start": 229.859, "end": 229.879, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 229.879, "end": 229.879, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "uscita", "start": 229.879, "end": 230.239, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 230.239, "end": 230.259, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "in", "start": 230.259, "end": 230.339, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 230.339, "end": 230.379, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 230.379, "end": 230.919, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 230.919, "end": 230.919, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "corti,", "start": 230.919, "end": 231.359, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 231.359, "end": 231.359, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "al", "start": 231.359, "end": 231.499, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 231.499, "end": 231.559, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "mare", "start": 231.559, "end": 231.919, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 231.919, "end": 231.919, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "è", "start": 231.919, "end": 231.939, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 231.939, "end": 231.939, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "andata.", "start": 231.939, "end": 232.539, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 232.539, "end": 232.719, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Sì,", "start": 232.719, "end": 232.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 232.899, "end": 232.899, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "lo", "start": 232.899, "end": 233.039, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 233.039, "end": 233.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "so,", "start": 233.059, "end": 233.179, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 233.179, "end": 233.199, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "l'ho", "start": 233.199, "end": 233.279, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 233.279, "end": 233.279, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "incontrata.", "start": 233.279, "end": 233.879, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 233.879, "end": 233.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Papà", "start": 233.879, "end": 234.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 234.119, "end": 234.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "l'ha", "start": 234.139, "end": 234.279, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 234.279, "end": 234.279, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "vista?", "start": 234.279, "end": 234.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 234.659, "end": 234.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "No,", "start": 234.699, "end": 234.879, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 234.879, "end": 235.079, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Don", "start": 235.079, "end": 235.279, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 235.279, "end": 235.359, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON>", "start": 235.359, "end": 235.619, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 235.619, "end": 235.679, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "è", "start": 235.679, "end": 235.699, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 235.699, "end": 235.699, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "uscito", "start": 235.699, "end": 235.999, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 235.999, "end": 235.999, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "presto", "start": 235.999, "end": 236.359, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 236.359, "end": 236.359, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "stamattina.", "start": 236.359, "end": 236.999, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 236.999, "end": 237.059, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Fa", "start": 237.059, "end": 237.259, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 237.259, "end": 237.259, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 237.259, "end": 237.379, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 237.379, "end": 237.399, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "non", "start": 237.399, "end": 237.519, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 237.519, "end": 237.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "lo", "start": 237.539, "end": 237.639, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 237.639, "end": 237.679, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sappia,", "start": 237.679, "end": 237.999, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 237.999, "end": 238.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Mar<PERSON><PERSON>ta,", "start": 238.019, "end": 238.539, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 238.539, "end": 238.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "e", "start": 238.559, "end": 238.599, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 238.599, "end": 238.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "meglio", "start": 238.699, "end": 239.019, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 239.019, "end": 239.039, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "non", "start": 239.039, "end": 239.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 239.219, "end": 239.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "irritarlo.", "start": 239.219, "end": 239.939, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 239.939, "end": 240.259, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "A", "start": 240.259, "end": 240.399, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 240.399, "end": 240.599, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "quando", "start": 240.599, "end": 240.919, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 240.919, "end": 240.919, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "lo", "start": 240.919, "end": 241.059, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 241.059, "end": 241.079, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "saprà,", "start": 241.079, "end": 241.439, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 241.439, "end": 241.459, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "non", "start": 241.459, "end": 241.599, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 241.599, "end": 241.599, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "avrà", "start": 241.599, "end": 241.759, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 241.759, "end": 241.759, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "il", "start": 241.759, "end": 241.859, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 241.859, "end": 241.859, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "coraggio", "start": 241.859, "end": 242.319, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 242.319, "end": 242.319, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "di", "start": 242.319, "end": 242.439, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 242.439, "end": 242.459, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "dirle", "start": 242.459, "end": 242.719, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 242.719, "end": 242.739, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "niente.", "start": 242.739, "end": 243.239, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 243.239, "end": 243.779, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "In", "start": 243.779, "end": 244.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 244.019, "end": 244.039, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "paese", "start": 244.039, "end": 244.479, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 244.479, "end": 244.479, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "sono", "start": 244.479, "end": 244.659, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 244.659, "end": 244.659, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "cominciate", "start": 244.659, "end": 245.139, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 245.139, "end": 245.139, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "già", "start": 245.139, "end": 245.279, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 245.279, "end": 245.279, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "le", "start": 245.279, "end": 245.439, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 245.439, "end": 245.439, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "chiac<PERSON><PERSON>.", "start": 245.439, "end": 246.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 246.019, "end": 246.419, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Dicono", "start": 246.419, "end": 246.859, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 246.859, "end": 246.899, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "che", "start": 246.899, "end": 246.999, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 246.999, "end": 247.099, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "prima", "start": 247.099, "end": 247.399, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 247.399, "end": 247.399, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "o", "start": 247.399, "end": 247.479, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 247.479, "end": 247.479, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "poi", "start": 247.479, "end": 247.659, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 247.659, "end": 247.659, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "quella", "start": 247.659, "end": 247.999, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 247.999, "end": 248.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "finirà", "start": 248.019, "end": 248.359, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 248.359, "end": 248.379, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "per", "start": 248.379, "end": 248.479, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 248.479, "end": 248.479, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "rovinare", "start": 248.479, "end": 248.939, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 248.939, "end": 248.939, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "questa", "start": 248.939, "end": 249.219, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 249.219, "end": 249.279, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "casa", "start": 249.279, "end": 249.539, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 249.539, "end": 249.539, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "e", "start": 249.539, "end": 249.599, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 249.599, "end": 249.639, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Don", "start": 249.639, "end": 249.799, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 249.799, "end": 249.859, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON>.", "start": 249.859, "end": 250.419, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 250.419, "end": 251.359, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 251.359, "end": 251.939, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 251.939, "end": 251.939, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "qualche", "start": 251.939, "end": 252.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 252.239, "end": 252.259, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tempo", "start": 252.259, "end": 252.539, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 252.539, "end": 252.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "dopo", "start": 252.539, "end": 252.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 252.779, "end": 252.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "i", "start": 252.799, "end": 252.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 252.859, "end": 252.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "miei", "start": 252.879, "end": 253.039, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 253.039, "end": 253.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "dubbi", "start": 253.059, "end": 253.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 253.319, "end": 253.339, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ebbe<PERSON>", "start": 253.339, "end": 253.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 253.619, "end": 253.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "una", "start": 253.619, "end": 253.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 253.759, "end": 253.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "conferma.", "start": 253.819, "end": 254.499, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 254.499, "end": 255.279, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 255.279, "end": 255.439, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 255.439, "end": 255.499, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "padre", "start": 255.499, "end": 255.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 255.819, "end": 255.839, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "mi", "start": 255.839, "end": 255.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 255.919, "end": 255.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "aveva", "start": 255.919, "end": 256.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 256.219, "end": 256.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "pregato", "start": 256.219, "end": 256.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 256.619, "end": 256.639, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 256.639, "end": 256.719, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 256.719, "end": 256.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "andare", "start": 256.739, "end": 257.019, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 257.019, "end": 257.039, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ad", "start": 257.039, "end": 257.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 257.159, "end": 257.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "aiutarla.", "start": 257.159, "end": 257.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 257.859, "end": 258.679, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Sperava", "start": 258.679, "end": 259.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 259.119, "end": 259.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 259.159, "end": 259.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 259.239, "end": 259.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sa<PERSON><PERSON>", "start": 259.299, "end": 259.599, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 259.599, "end": 259.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "diventate", "start": 259.619, "end": 260.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 260.159, "end": 260.179, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "amiche.", "start": 260.179, "end": 260.739, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 260.739, "end": 264.039, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>,", "start": 264.039, "end": 265.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 265.139, "end": 265.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sei", "start": 265.159, "end": 265.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 265.299, "end": 265.379, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tu?", "start": 265.379, "end": 265.639, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 265.639, "end": 266.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>?", "start": 266.919, "end": 267.419, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 267.419, "end": 267.939, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Sì,", "start": 267.939, "end": 268.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 268.139, "end": 268.179, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sono", "start": 268.179, "end": 268.419, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 268.419, "end": 268.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "io.", "start": 268.459, "end": 268.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 268.659, "end": 268.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Cosa", "start": 268.699, "end": 268.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 268.899, "end": 268.939, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ti", "start": 268.939, "end": 269.059, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 269.059, "end": 269.079, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "serve?", "start": 269.079, "end": 269.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 269.479, "end": 269.679, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Il", "start": 269.679, "end": 269.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 269.759, "end": 269.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "mio", "start": 269.779, "end": 269.879, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 269.879, "end": 269.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON>,", "start": 269.879, "end": 270.599, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 270.599, "end": 270.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "quello", "start": 270.799, "end": 271.019, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 271.019, "end": 271.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "bianco", "start": 271.019, "end": 271.339, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 271.339, "end": 271.339, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 271.339, "end": 271.439, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 271.439, "end": 271.439, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sta", "start": 271.439, "end": 271.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 271.559, "end": 271.579, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sulla", "start": 271.579, "end": 271.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 271.759, "end": 271.759, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "poltrona.", "start": 271.759, "end": 272.339, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 272.339, "end": 272.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 272.919, "end": 273.379, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 273.379, "end": 273.379, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "anche", "start": 273.379, "end": 273.539, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 273.539, "end": 273.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "le", "start": 273.559, "end": 273.639, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 273.639, "end": 273.639, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "mollez<PERSON>,", "start": 273.639, "end": 274.039, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 274.039, "end": 274.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 274.059, "end": 274.279, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 274.279, "end": 274.279, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "essere", "start": 274.279, "end": 274.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 274.479, "end": 274.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "accanto", "start": 274.479, "end": 274.719, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 274.719, "end": 274.719, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "allo", "start": 274.719, "end": 274.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 274.859, "end": 274.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "specchio.", "start": 274.879, "end": 275.519, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 275.519, "end": 281.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Non", "start": 281.559, "end": 284.019, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 284.019, "end": 284.039, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "ci", "start": 284.039, "end": 284.159, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 284.159, "end": 284.199, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "so.", "start": 284.199, "end": 284.499, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 284.499, "end": 284.599, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Guarda", "start": 284.599, "end": 284.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 284.959, "end": 285.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "nella", "start": 285.019, "end": 285.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 285.119, "end": 285.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "mia", "start": 285.139, "end": 285.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 285.239, "end": 285.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "valigia", "start": 285.239, "end": 285.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 285.559, "end": 285.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "allora,", "start": 285.599, "end": 285.839, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 285.839, "end": 285.839, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "quella", "start": 285.839, "end": 286.039, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 286.039, "end": 286.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "piccola.", "start": 286.059, "end": 286.539, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 286.539, "end": 293.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}]}}, "created_at": 1754319677.9173863}