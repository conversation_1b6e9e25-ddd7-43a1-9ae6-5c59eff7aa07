{"success": true, "service": "elevenlabs", "transcription_id": "elevenlabs_1754319816", "text": "Che? Carmela si è uccisa. Parl<PERSON>. Alza le braccia. Ti spiego. <PERSON><PERSON><PERSON>, non sento più tutto. Chi ha ucciso Carmela morirà, chi? Parla, parla, parla! Qualcuno qualcosa mi deve dire. Non lo so. Rosario mi avrebbe ucciso se avesse potuto quella sera. La morte di Carmela gli aveva dato il pretesto di sfogare il suo odio contro di me, perché Carla mi aveva preferito a lui. Allora è stato Rosario ad uccidere la moglie di Ingarcia? Già, sì, sì, potrebbe essere stato lui. È lui l'assassino, si è vendicato e ora vuol far credere che sono stato io. Così mi ha detto. Ma voi lo sapete che non è vero, perché quella sera- Adesso te lo dico io cos'è successo quella sera. Tornasti a casa dopo la lite con Rosario ancora stortito e pieno d'odio. Sapevi che tutto il paese ti disprezzava. Carmela era morta e nessuno ti avrebbe difeso più. Eri rimasto solo. E non sapevi con chi sfogare il tuo dolore e la tua collera. In casa avevi una vecchia pistola. In certe occasioni si desidera avere un'arma in mano. La prendesti meccanicamente senza nemmeno sapere perché. Forse per sentirti più forte degli altri. Poi uscisti per la strada, ma ad un tratto la vedesti. Vogliono farmi del male, solo perché una pazza ha perduto la testa. Ma tu puoi nascondermi, Abul. Puoi salvarmi. Tu sei a giustizia. Il serbo, la tua spa- Senti, Abul. Tu sei marinaio, non è vero? Sapresti portarmi fuori di qui, via dall'isola? Ecco, vedi? Puoi prenderli. Anche tutti se puoi, purché tu mi conduca via di qui, via di qui, dove vorrai ma via, via. Niente denaro. Anche tu come noi. È me che vuoi. Demonio. Maligno con volto di donna. Potrai farlo. Te lo prometto. Sarò tua, tua. Ma fammi uscire di qui. Hai tuo corpo. Lei, Carla in Garcia, la causa di tutto. Capisti che era lei e non Rosario che volevi uccidere. Lei aveva soltanto giocato con te, e tu in quel gioco avevi perduto Carmela, il bambino che ti doveva nascere. Hai aspettato a sparare perché credevi che Carla trovasse nelle fiamme la giusta punizione. Ma quando ti accorgesti che stava per salvarsi, non hai sparato. Poi hai nascosto la pistola e ti sei confuso con gli altri che sopravvenivano per spegnere l'incendio. Credevi che le fiamme avrebbero distrutto tutto, che saresti stato salvo. E lo sarò. Non ce la farete ad arrestarmi. Non voglio finire la mia vita in prigione. Metti giù quell'arma, Francesco. Anche se tu mi uccidessi, non riusciresti a fuggire. Io non voglio uccidervi. Voglio andare via, lontano da qui. Voglio dimenticare tutto, voglio vivere. Stai fermo Francesco, non muoverti. Ce la faremo, ragazzo. ", "words": [{"text": "Che?", "start": 44.04, "end": 44.099, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 44.099, "end": 44.099, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Carmel<PERSON>", "start": 44.099, "end": 44.939, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 44.939, "end": 44.939, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "si", "start": 44.939, "end": 45.039, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 45.039, "end": 45.039, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "è", "start": 45.039, "end": 45.059, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 45.059, "end": 45.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "uccisa.", "start": 45.059, "end": 45.48, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 45.48, "end": 46.02, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Parla.", "start": 46.02, "end": 46.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 46.559, "end": 46.7, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Alza", "start": 46.7, "end": 47.0, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 47.0, "end": 47.119, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "le", "start": 47.119, "end": 47.139, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 47.139, "end": 47.18, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "braccia.", "start": 47.18, "end": 47.5, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 47.5, "end": 47.5, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Ti", "start": 47.5, "end": 47.599, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 47.599, "end": 47.599, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "spiego.", "start": 47.599, "end": 47.899, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 47.899, "end": 47.899, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Pa<PERSON>a,", "start": 47.899, "end": 48.2, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 48.2, "end": 48.259, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "non", "start": 48.259, "end": 48.399, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 48.399, "end": 48.399, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sento", "start": 48.399, "end": 48.68, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 48.68, "end": 48.68, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "più", "start": 48.68, "end": 48.959, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 48.959, "end": 48.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "tutto.", "start": 48.979, "end": 49.22, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 49.22, "end": 50.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>", "start": 50.059, "end": 50.22, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 50.22, "end": 50.239, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ha", "start": 50.239, "end": 50.34, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 50.34, "end": 50.36, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ucciso", "start": 50.36, "end": 50.659, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 50.659, "end": 50.68, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Carmel<PERSON>", "start": 50.68, "end": 51.039, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 51.039, "end": 51.039, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>,", "start": 51.039, "end": 51.4, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 51.4, "end": 51.459, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "chi?", "start": 51.459, "end": 51.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 51.799, "end": 52.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Pa<PERSON>a,", "start": 52.299, "end": 52.899, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 52.899, "end": 52.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "parla,", "start": 52.919, "end": 53.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 53.479, "end": 53.68, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "parla!", "start": 53.68, "end": 54.239, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 54.239, "end": 54.279, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Qualcu<PERSON>", "start": 54.279, "end": 54.7, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 54.7, "end": 54.7, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "qualcosa", "start": 54.7, "end": 54.919, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 54.919, "end": 54.939, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "mi", "start": 54.939, "end": 55.0, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 55.0, "end": 55.039, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "deve", "start": 55.039, "end": 55.2, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 55.2, "end": 55.259, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "dire.", "start": 55.259, "end": 55.559, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 55.559, "end": 56.84, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Non", "start": 56.84, "end": 57.119, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 57.119, "end": 57.119, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "lo", "start": 57.119, "end": 57.219, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 57.219, "end": 57.219, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "so.", "start": 57.219, "end": 57.439, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 57.439, "end": 79.099, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Rosario", "start": 79.099, "end": 80.199, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 80.199, "end": 80.199, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "mi", "start": 80.199, "end": 80.259, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 80.259, "end": 80.279, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 80.279, "end": 80.499, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 80.499, "end": 80.499, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "ucciso", "start": 80.499, "end": 80.859, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 80.859, "end": 80.859, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "se", "start": 80.859, "end": 80.939, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 80.939, "end": 80.939, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "avesse", "start": 80.939, "end": 81.179, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 81.179, "end": 81.179, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "potuto", "start": 81.179, "end": 81.479, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 81.479, "end": 81.479, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "quella", "start": 81.479, "end": 81.679, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 81.679, "end": 81.699, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "sera.", "start": 81.699, "end": 82.039, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 82.039, "end": 83.379, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "La", "start": 83.379, "end": 83.499, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 83.499, "end": 83.519, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "morte", "start": 83.519, "end": 83.759, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 83.759, "end": 83.779, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "di", "start": 83.779, "end": 83.839, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 83.839, "end": 83.859, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Carmel<PERSON>", "start": 83.859, "end": 84.159, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 84.159, "end": 84.159, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "gli", "start": 84.159, "end": 84.239, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 84.239, "end": 84.259, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "aveva", "start": 84.259, "end": 84.459, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 84.459, "end": 84.479, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "dato", "start": 84.479, "end": 84.639, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 84.639, "end": 84.639, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "il", "start": 84.639, "end": 84.719, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 84.719, "end": 84.739, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "pretesto", "start": 84.739, "end": 85.079, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 85.079, "end": 85.079, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "di", "start": 85.079, "end": 85.179, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 85.179, "end": 85.179, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "sfogare", "start": 85.179, "end": 85.499, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 85.499, "end": 85.499, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "il", "start": 85.499, "end": 85.559, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 85.559, "end": 85.599, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "suo", "start": 85.599, "end": 85.739, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 85.739, "end": 85.779, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "odio", "start": 85.779, "end": 86.039, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 86.039, "end": 86.319, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "contro", "start": 86.319, "end": 86.619, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 86.619, "end": 86.619, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "di", "start": 86.619, "end": 86.719, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 86.719, "end": 86.719, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "me,", "start": 86.719, "end": 87.019, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 87.019, "end": 87.879, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "perché", "start": 87.879, "end": 88.119, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 88.119, "end": 88.159, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON>", "start": 88.159, "end": 88.439, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 88.439, "end": 88.459, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "mi", "start": 88.459, "end": 88.519, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 88.519, "end": 88.539, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "aveva", "start": 88.539, "end": 88.739, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 88.739, "end": 88.799, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "preferito", "start": 88.799, "end": 89.139, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 89.139, "end": 89.139, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "a", "start": 89.139, "end": 89.219, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 89.219, "end": 89.239, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "lui.", "start": 89.239, "end": 89.659, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 89.659, "end": 89.959, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 89.959, "end": 90.279, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 90.279, "end": 90.319, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "è", "start": 90.319, "end": 90.379, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 90.379, "end": 90.399, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "stato", "start": 90.399, "end": 90.719, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 90.719, "end": 90.739, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Rosario", "start": 90.739, "end": 91.339, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 91.339, "end": 91.339, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "ad", "start": 91.339, "end": 91.479, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 91.479, "end": 91.499, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "uccidere", "start": 91.499, "end": 91.939, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 91.939, "end": 91.939, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "la", "start": 91.939, "end": 92.059, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 92.059, "end": 92.119, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "moglie", "start": 92.119, "end": 92.379, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 92.379, "end": 92.399, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "di", "start": 92.399, "end": 92.459, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 92.459, "end": 92.459, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Ingarcia?", "start": 92.459, "end": 93.259, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 93.259, "end": 93.899, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Già,", "start": 93.899, "end": 94.159, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 94.159, "end": 94.239, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "sì,", "start": 94.239, "end": 94.459, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 94.459, "end": 95.119, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "sì,", "start": 95.119, "end": 95.279, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 95.279, "end": 95.279, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 95.279, "end": 95.619, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 95.619, "end": 95.619, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "essere", "start": 95.619, "end": 95.859, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 95.859, "end": 95.859, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "stato", "start": 95.859, "end": 96.099, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 96.099, "end": 96.119, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "lui.", "start": 96.119, "end": 96.399, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 96.399, "end": 97.279, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "È", "start": 97.279, "end": 97.399, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 97.399, "end": 97.399, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "lui", "start": 97.399, "end": 97.559, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 97.559, "end": 97.579, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "l'assassino,", "start": 97.579, "end": 98.219, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 98.219, "end": 98.239, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "si", "start": 98.239, "end": 98.339, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 98.339, "end": 98.339, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "è", "start": 98.339, "end": 98.379, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 98.379, "end": 98.399, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "vendicato", "start": 98.399, "end": 98.819, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 98.819, "end": 98.819, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "e", "start": 98.819, "end": 98.879, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 98.879, "end": 98.879, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "ora", "start": 98.879, "end": 98.999, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 98.999, "end": 98.999, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "vuol", "start": 98.999, "end": 99.159, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 99.159, "end": 99.159, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "far", "start": 99.159, "end": 99.279, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 99.279, "end": 99.339, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "credere", "start": 99.339, "end": 99.619, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 99.619, "end": 99.639, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "che", "start": 99.639, "end": 99.719, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 99.719, "end": 99.719, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "sono", "start": 99.719, "end": 99.899, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 99.899, "end": 99.939, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "stato", "start": 99.939, "end": 100.179, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 100.179, "end": 100.239, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "io.", "start": 100.239, "end": 100.499, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 100.499, "end": 100.639, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Così", "start": 100.639, "end": 100.959, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 100.959, "end": 100.999, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "mi", "start": 100.999, "end": 101.119, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 101.119, "end": 101.119, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "ha", "start": 101.119, "end": 101.199, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 101.199, "end": 101.259, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "detto.", "start": 101.259, "end": 101.759, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 101.759, "end": 102.159, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Ma", "start": 102.159, "end": 102.239, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 102.239, "end": 102.239, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "voi", "start": 102.239, "end": 102.339, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 102.339, "end": 102.359, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "lo", "start": 102.359, "end": 102.459, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 102.459, "end": 102.479, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "sapete", "start": 102.479, "end": 102.759, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 102.759, "end": 102.759, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "che", "start": 102.759, "end": 102.839, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 102.839, "end": 102.839, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "non", "start": 102.839, "end": 102.959, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 102.959, "end": 102.979, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "è", "start": 102.979, "end": 103.059, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 103.059, "end": 103.059, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "vero,", "start": 103.059, "end": 103.399, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 103.399, "end": 103.899, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "perché", "start": 103.899, "end": 104.139, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 104.139, "end": 104.139, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "quella", "start": 104.139, "end": 104.299, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 104.299, "end": 104.359, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "sera-", "start": 104.359, "end": 104.619, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 104.619, "end": 104.699, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 104.699, "end": 105.059, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 105.059, "end": 105.079, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "te", "start": 105.079, "end": 105.179, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 105.179, "end": 105.199, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "lo", "start": 105.199, "end": 105.279, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 105.279, "end": 105.319, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "dico", "start": 105.319, "end": 105.559, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 105.559, "end": 105.619, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "io", "start": 105.619, "end": 105.819, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 105.819, "end": 105.859, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "cos'è", "start": 105.859, "end": 106.099, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 106.099, "end": 106.099, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "successo", "start": 106.099, "end": 106.459, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 106.459, "end": 106.479, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "quella", "start": 106.479, "end": 106.699, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 106.699, "end": 106.739, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "sera.", "start": 106.739, "end": 107.119, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 107.119, "end": 108.539, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 108.539, "end": 109.039, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 109.039, "end": 109.059, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "a", "start": 109.059, "end": 109.139, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 109.139, "end": 109.159, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "casa", "start": 109.159, "end": 109.439, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 109.439, "end": 109.459, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "dopo", "start": 109.459, "end": 109.639, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 109.639, "end": 109.659, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "la", "start": 109.659, "end": 109.759, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 109.759, "end": 109.759, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "lite", "start": 109.759, "end": 109.999, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 109.999, "end": 109.999, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "con", "start": 109.999, "end": 110.159, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 110.159, "end": 110.179, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Rosario", "start": 110.179, "end": 110.619, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 110.619, "end": 110.639, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "ancora", "start": 110.639, "end": 110.959, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 110.959, "end": 110.979, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "stortito", "start": 110.979, "end": 111.539, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 111.539, "end": 111.539, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "e", "start": 111.539, "end": 111.659, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 111.659, "end": 111.659, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "pieno", "start": 111.659, "end": 111.899, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 111.899, "end": 111.919, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "d'odio.", "start": 111.919, "end": 112.459, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 112.459, "end": 113.979, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 113.979, "end": 114.339, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 114.339, "end": 114.339, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "che", "start": 114.339, "end": 114.439, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 114.439, "end": 114.439, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "tutto", "start": 114.439, "end": 114.619, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 114.619, "end": 114.639, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "il", "start": 114.639, "end": 114.679, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 114.679, "end": 114.759, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "paese", "start": 114.759, "end": 115.059, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 115.059, "end": 115.059, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "ti", "start": 115.059, "end": 115.159, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 115.159, "end": 115.179, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "disprez<PERSON><PERSON>.", "start": 115.179, "end": 115.939, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 115.939, "end": 117.159, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Carmel<PERSON>", "start": 117.159, "end": 117.619, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 117.619, "end": 117.619, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "era", "start": 117.619, "end": 117.799, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 117.799, "end": 117.819, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "morta", "start": 117.819, "end": 118.299, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 118.299, "end": 118.759, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "e", "start": 118.759, "end": 118.859, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 118.859, "end": 118.879, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "nessuno", "start": 118.879, "end": 119.319, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 119.319, "end": 119.339, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "ti", "start": 119.339, "end": 119.439, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 119.439, "end": 119.439, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 119.439, "end": 119.719, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 119.719, "end": 119.719, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "difeso", "start": 119.719, "end": 120.079, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 120.079, "end": 120.119, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "più.", "start": 120.119, "end": 120.419, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 120.419, "end": 123.259, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 123.259, "end": 123.499, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 123.499, "end": 123.519, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "rimasto", "start": 123.519, "end": 124.059, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 124.059, "end": 124.119, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "solo.", "start": 124.119, "end": 124.619, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 124.619, "end": 128.339, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "E", "start": 128.339, "end": 128.439, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 128.439, "end": 128.479, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "non", "start": 128.479, "end": 128.559, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 128.559, "end": 128.619, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "sapevi", "start": 128.619, "end": 128.979, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 128.979, "end": 128.979, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "con", "start": 128.979, "end": 129.119, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 129.119, "end": 129.119, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "chi", "start": 129.119, "end": 129.239, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 129.239, "end": 129.259, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "sfogare", "start": 129.259, "end": 129.699, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 129.699, "end": 129.719, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "il", "start": 129.719, "end": 129.799, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 129.799, "end": 129.879, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "tuo", "start": 129.879, "end": 130.039, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 130.039, "end": 130.079, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "dolore", "start": 130.079, "end": 130.339, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 130.339, "end": 130.379, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "e", "start": 130.379, "end": 130.399, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 130.399, "end": 130.399, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "la", "start": 130.399, "end": 130.479, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 130.479, "end": 130.519, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "tua", "start": 130.519, "end": 130.639, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 130.639, "end": 130.699, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "collera.", "start": 130.699, "end": 131.159, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 131.159, "end": 139.539, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "In", "start": 139.539, "end": 139.699, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 139.699, "end": 139.759, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "casa", "start": 139.759, "end": 139.999, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 139.999, "end": 139.999, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "avevi", "start": 139.999, "end": 140.319, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 140.319, "end": 140.339, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "una", "start": 140.339, "end": 140.479, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 140.479, "end": 140.519, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "vecchia", "start": 140.519, "end": 140.779, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 140.779, "end": 140.819, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "pistola.", "start": 140.819, "end": 141.439, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 141.439, "end": 142.539, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "In", "start": 142.539, "end": 142.639, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 142.639, "end": 142.659, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "certe", "start": 142.659, "end": 142.939, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 142.939, "end": 142.939, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "occasioni", "start": 142.939, "end": 143.359, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 143.359, "end": 143.379, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "si", "start": 143.379, "end": 143.499, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 143.499, "end": 143.539, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "desidera", "start": 143.539, "end": 143.959, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 143.959, "end": 143.999, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "avere", "start": 143.999, "end": 144.239, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 144.239, "end": 144.259, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "un'arma", "start": 144.259, "end": 144.619, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 144.619, "end": 144.659, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "in", "start": 144.659, "end": 144.739, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 144.739, "end": 144.799, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "mano.", "start": 144.799, "end": 145.199, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 145.199, "end": 145.759, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "La", "start": 145.759, "end": 145.879, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 145.879, "end": 145.879, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 145.879, "end": 146.279, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 146.279, "end": 146.279, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "meccanicamente", "start": 146.279, "end": 146.899, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 146.899, "end": 146.899, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "senza", "start": 146.899, "end": 147.119, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 147.119, "end": 147.139, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 147.139, "end": 147.379, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 147.379, "end": 147.379, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "sapere", "start": 147.379, "end": 147.679, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 147.679, "end": 147.679, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "perché.", "start": 147.679, "end": 148.079, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 148.079, "end": 148.879, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 148.879, "end": 149.199, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 149.199, "end": 149.199, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "per", "start": 149.199, "end": 149.319, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 149.319, "end": 149.359, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "sentirti", "start": 149.359, "end": 149.799, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 149.799, "end": 149.799, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "più", "start": 149.799, "end": 149.979, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 149.979, "end": 149.999, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "forte", "start": 149.999, "end": 150.259, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 150.259, "end": 150.259, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON>gli", "start": 150.259, "end": 150.479, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 150.479, "end": 150.539, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "altri.", "start": 150.539, "end": 150.979, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 150.979, "end": 152.899, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 152.899, "end": 153.139, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 153.139, "end": 153.139, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 153.139, "end": 153.419, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 153.419, "end": 153.419, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "per", "start": 153.419, "end": 153.539, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 153.539, "end": 153.539, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "la", "start": 153.539, "end": 153.639, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 153.639, "end": 153.659, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "strada,", "start": 153.659, "end": 154.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 154.019, "end": 154.959, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "ma", "start": 154.959, "end": 155.059, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 155.059, "end": 155.059, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "ad", "start": 155.059, "end": 155.119, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 155.119, "end": 155.159, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "un", "start": 155.159, "end": 155.239, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 155.239, "end": 155.259, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "tratto", "start": 155.259, "end": 155.619, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 155.619, "end": 155.779, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "la", "start": 155.779, "end": 155.879, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 155.879, "end": 155.879, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "vedesti.", "start": 155.879, "end": 156.439, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 156.439, "end": 169.919, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 169.919, "end": 170.259, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 170.259, "end": 170.259, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "farmi", "start": 170.259, "end": 170.499, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 170.499, "end": 170.519, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "del", "start": 170.519, "end": 170.659, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 170.659, "end": 170.679, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "male,", "start": 170.679, "end": 171.079, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 171.079, "end": 171.759, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "solo", "start": 171.759, "end": 172.019, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 172.019, "end": 172.039, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "perché", "start": 172.039, "end": 172.219, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 172.219, "end": 172.239, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "una", "start": 172.239, "end": 172.379, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 172.379, "end": 172.379, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "pazza", "start": 172.379, "end": 172.599, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 172.599, "end": 172.599, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "ha", "start": 172.599, "end": 172.659, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 172.659, "end": 172.659, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "perduto", "start": 172.659, "end": 172.979, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 172.979, "end": 173.019, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "la", "start": 173.019, "end": 173.079, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 173.079, "end": 173.119, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "testa.", "start": 173.119, "end": 173.579, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 173.579, "end": 174.779, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Ma", "start": 174.779, "end": 174.919, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 174.919, "end": 174.999, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "tu", "start": 174.999, "end": 175.139, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 175.139, "end": 175.179, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "puoi", "start": 175.179, "end": 175.339, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 175.339, "end": 175.339, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>,", "start": 175.339, "end": 176.019, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 176.019, "end": 176.019, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON>.", "start": 176.019, "end": 176.479, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 176.479, "end": 177.879, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Puo<PERSON>", "start": 177.879, "end": 178.059, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 178.059, "end": 178.099, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "sal<PERSON><PERSON>.", "start": 178.099, "end": 178.939, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 178.939, "end": 188.859, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Tu", "start": 188.859, "end": 189.319, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 189.319, "end": 189.359, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "sei", "start": 189.359, "end": 189.599, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 189.599, "end": 189.619, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "a", "start": 189.619, "end": 189.699, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 189.699, "end": 189.759, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "giustizia.", "start": 189.759, "end": 191.299, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 191.299, "end": 193.839, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "Il", "start": 193.839, "end": 194.079, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 194.079, "end": 194.179, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "serbo,", "start": 194.179, "end": 194.939, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 194.939, "end": 195.619, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "la", "start": 195.619, "end": 195.759, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 195.759, "end": 195.799, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "tua", "start": 195.799, "end": 195.979, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 195.979, "end": 195.999, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "spa-", "start": 195.999, "end": 196.419, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 196.419, "end": 202.799, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "<PERSON><PERSON>,", "start": 202.799, "end": 203.419, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 203.419, "end": 203.439, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON>.", "start": 203.439, "end": 203.799, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 203.799, "end": 205.199, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Tu", "start": 205.199, "end": 205.319, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 205.319, "end": 205.359, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "sei", "start": 205.359, "end": 205.519, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 205.519, "end": 205.519, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "marinaio,", "start": 205.519, "end": 206.039, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 206.039, "end": 206.059, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "non", "start": 206.059, "end": 206.159, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 206.159, "end": 206.179, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "è", "start": 206.179, "end": 206.239, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 206.239, "end": 206.259, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "vero?", "start": 206.259, "end": 206.639, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 206.639, "end": 207.739, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 207.739, "end": 208.219, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 208.219, "end": 208.239, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "portarmi", "start": 208.239, "end": 208.639, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 208.639, "end": 208.679, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "fuori", "start": 208.679, "end": 208.899, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 208.899, "end": 208.899, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "di", "start": 208.899, "end": 208.979, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 208.979, "end": 209.039, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "qui,", "start": 209.039, "end": 209.299, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 209.299, "end": 209.999, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "via", "start": 209.999, "end": 210.219, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 210.219, "end": 210.219, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "dall'isola?", "start": 210.219, "end": 210.939, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 210.939, "end": 213.479, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON>,", "start": 213.479, "end": 213.799, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 213.799, "end": 213.799, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "vedi?", "start": 213.799, "end": 214.179, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 214.179, "end": 214.659, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Puo<PERSON>", "start": 214.659, "end": 214.859, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 214.859, "end": 214.859, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "pre<PERSON><PERSON>.", "start": 214.859, "end": 215.499, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 215.499, "end": 216.019, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 216.019, "end": 216.259, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 216.259, "end": 216.259, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "tutti", "start": 216.259, "end": 216.499, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 216.499, "end": 216.519, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "se", "start": 216.519, "end": 216.659, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 216.659, "end": 216.659, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "puoi,", "start": 216.659, "end": 217.039, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 217.039, "end": 217.379, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "purché", "start": 217.379, "end": 217.619, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 217.619, "end": 217.619, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "tu", "start": 217.619, "end": 217.739, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 217.739, "end": 217.739, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "mi", "start": 217.739, "end": 217.839, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 217.839, "end": 217.859, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "conduca", "start": 217.859, "end": 218.199, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 218.199, "end": 218.199, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "via", "start": 218.199, "end": 218.379, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 218.379, "end": 218.379, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "di", "start": 218.379, "end": 218.459, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 218.459, "end": 218.519, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "qui,", "start": 218.519, "end": 218.759, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 218.759, "end": 219.039, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "via", "start": 219.039, "end": 219.299, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 219.299, "end": 219.299, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "di", "start": 219.299, "end": 219.439, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 219.439, "end": 219.459, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "qui,", "start": 219.459, "end": 219.719, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 219.719, "end": 220.139, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "dove", "start": 220.139, "end": 220.339, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 220.339, "end": 220.339, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "vorrai", "start": 220.339, "end": 220.659, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 220.659, "end": 220.659, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "ma", "start": 220.659, "end": 220.759, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 220.759, "end": 220.839, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "via,", "start": 220.839, "end": 221.179, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 221.179, "end": 221.759, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "via.", "start": 221.759, "end": 222.339, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 222.339, "end": 222.439, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 222.439, "end": 222.919, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 222.919, "end": 222.979, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "denaro.", "start": 222.979, "end": 223.619, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 223.619, "end": 234.979, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 234.979, "end": 237.859, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 237.859, "end": 237.879, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "tu", "start": 237.879, "end": 238.059, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 238.059, "end": 238.079, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "come", "start": 238.079, "end": 238.279, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 238.279, "end": 238.359, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "noi.", "start": 238.359, "end": 238.839, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 238.839, "end": 239.559, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "È", "start": 239.559, "end": 239.739, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 239.739, "end": 239.799, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "me", "start": 239.799, "end": 239.959, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 239.959, "end": 240.079, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "che", "start": 240.079, "end": 240.239, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 240.239, "end": 240.279, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "vuoi.", "start": 240.279, "end": 240.859, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 240.859, "end": 242.899, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Demonio.", "start": 242.899, "end": 243.719, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 243.719, "end": 244.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Maligno", "start": 244.799, "end": 245.359, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 245.359, "end": 245.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "con", "start": 245.379, "end": 245.579, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 245.579, "end": 245.619, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "volto", "start": 245.619, "end": 245.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 245.979, "end": 245.999, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 245.999, "end": 246.099, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 246.099, "end": 246.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "donna.", "start": 246.119, "end": 246.459, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 246.459, "end": 246.459, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Potrai", "start": 246.459, "end": 246.719, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 246.719, "end": 246.739, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "farlo.", "start": 246.739, "end": 247.219, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 247.219, "end": 247.739, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Te", "start": 247.739, "end": 247.839, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 247.839, "end": 247.859, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "lo", "start": 247.859, "end": 247.939, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 247.939, "end": 247.939, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "prometto.", "start": 247.939, "end": 248.459, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 248.459, "end": 248.699, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 248.699, "end": 248.939, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 248.939, "end": 248.999, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "tua,", "start": 248.999, "end": 249.399, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 249.399, "end": 249.899, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "tua.", "start": 249.899, "end": 250.379, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 250.379, "end": 251.039, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Ma", "start": 251.039, "end": 251.179, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 251.179, "end": 251.259, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "fammi", "start": 251.259, "end": 251.619, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 251.619, "end": 251.619, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "uscire", "start": 251.619, "end": 252.019, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 252.019, "end": 252.019, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "di", "start": 252.019, "end": 252.159, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 252.159, "end": 252.179, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "qui.", "start": 252.179, "end": 252.499, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 252.499, "end": 253.719, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON>", "start": 253.719, "end": 253.999, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 253.999, "end": 254.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "tuo", "start": 254.379, "end": 254.579, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 254.579, "end": 254.759, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "corpo.", "start": 254.759, "end": 255.359, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 255.359, "end": 256.159, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON>,", "start": 256.159, "end": 256.739, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 256.739, "end": 256.759, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON>", "start": 256.759, "end": 257.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 257.019, "end": 257.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "in", "start": 257.019, "end": 257.099, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 257.099, "end": 257.099, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON>,", "start": 257.099, "end": 257.619, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 257.619, "end": 257.699, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "la", "start": 257.699, "end": 257.799, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 257.799, "end": 257.839, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "causa", "start": 257.839, "end": 258.039, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 258.039, "end": 258.059, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "di", "start": 258.059, "end": 258.179, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 258.179, "end": 258.179, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "tutto.", "start": 258.179, "end": 258.539, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 258.539, "end": 258.879, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 258.879, "end": 259.239, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 259.239, "end": 259.259, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "che", "start": 259.259, "end": 259.339, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 259.339, "end": 259.359, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "era", "start": 259.359, "end": 259.479, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 259.479, "end": 259.519, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "lei", "start": 259.519, "end": 259.699, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 259.699, "end": 259.719, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "e", "start": 259.719, "end": 259.819, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 259.819, "end": 259.819, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "non", "start": 259.819, "end": 259.919, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 259.919, "end": 259.939, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Rosario", "start": 259.939, "end": 260.279, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 260.279, "end": 260.279, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "che", "start": 260.279, "end": 260.379, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 260.379, "end": 260.399, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON>i", "start": 260.399, "end": 260.639, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 260.639, "end": 260.639, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "uccidere.", "start": 260.639, "end": 261.099, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 261.099, "end": 261.599, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 261.599, "end": 261.759, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 261.759, "end": 261.779, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "aveva", "start": 261.779, "end": 261.979, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 261.979, "end": 261.979, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "soltanto", "start": 261.979, "end": 262.339, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 262.339, "end": 262.359, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "giocato", "start": 262.359, "end": 262.699, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 262.699, "end": 262.719, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "con", "start": 262.719, "end": 262.859, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 262.859, "end": 262.899, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "te,", "start": 262.899, "end": 263.059, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 263.059, "end": 263.219, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "e", "start": 263.219, "end": 263.239, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 263.239, "end": 263.239, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "tu", "start": 263.239, "end": 263.339, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 263.339, "end": 263.339, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "in", "start": 263.339, "end": 263.439, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 263.439, "end": 263.439, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "quel", "start": 263.439, "end": 263.579, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 263.579, "end": 263.599, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "gioco", "start": 263.599, "end": 263.759, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 263.759, "end": 263.759, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "avevi", "start": 263.759, "end": 263.979, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 263.979, "end": 263.979, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "perduto", "start": 263.979, "end": 264.339, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 264.339, "end": 264.359, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Carmela,", "start": 264.359, "end": 264.779, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 264.779, "end": 264.779, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "il", "start": 264.779, "end": 264.879, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 264.879, "end": 264.899, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "bambino", "start": 264.899, "end": 265.159, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 265.159, "end": 265.159, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "che", "start": 265.159, "end": 265.239, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 265.239, "end": 265.259, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "ti", "start": 265.259, "end": 265.339, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 265.339, "end": 265.359, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 265.359, "end": 265.619, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 265.619, "end": 265.619, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "nascere.", "start": 265.619, "end": 266.079, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 266.079, "end": 266.739, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON>", "start": 266.739, "end": 266.919, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 266.919, "end": 266.919, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "aspettato", "start": 266.919, "end": 267.379, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 267.379, "end": 267.379, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "a", "start": 267.379, "end": 267.459, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 267.459, "end": 267.459, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "sparare", "start": 267.459, "end": 267.819, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 267.819, "end": 267.819, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "perché", "start": 267.819, "end": 268.119, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 268.119, "end": 268.299, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "credevi", "start": 268.299, "end": 268.659, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 268.659, "end": 268.659, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "che", "start": 268.659, "end": 268.779, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 268.779, "end": 268.799, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON>", "start": 268.799, "end": 269.039, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 269.039, "end": 269.039, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "trovasse", "start": 269.039, "end": 269.399, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 269.399, "end": 269.399, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "nelle", "start": 269.399, "end": 269.559, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 269.559, "end": 269.559, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "fiamme", "start": 269.559, "end": 269.799, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 269.799, "end": 269.799, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "la", "start": 269.799, "end": 269.919, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 269.919, "end": 269.919, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "giusta", "start": 269.919, "end": 270.139, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 270.139, "end": 270.159, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "punizione.", "start": 270.159, "end": 270.659, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 270.659, "end": 271.119, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Ma", "start": 271.119, "end": 271.239, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 271.239, "end": 271.239, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "quando", "start": 271.239, "end": 271.459, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 271.459, "end": 271.459, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "ti", "start": 271.459, "end": 271.579, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 271.579, "end": 271.579, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "acco<PERSON><PERSON>", "start": 271.579, "end": 271.999, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 271.999, "end": 271.999, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "che", "start": 271.999, "end": 272.099, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 272.099, "end": 272.099, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "stava", "start": 272.099, "end": 272.339, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 272.339, "end": 272.339, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "per", "start": 272.339, "end": 272.459, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 272.459, "end": 272.499, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>,", "start": 272.499, "end": 272.959, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 272.959, "end": 273.119, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "non", "start": 273.119, "end": 273.339, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 273.339, "end": 273.579, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "hai", "start": 273.579, "end": 273.779, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 273.779, "end": 273.799, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "sparato.", "start": 273.799, "end": 274.379, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 274.379, "end": 274.659, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 274.659, "end": 274.819, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 274.819, "end": 274.819, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "hai", "start": 274.819, "end": 274.939, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 274.939, "end": 274.939, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "nascosto", "start": 274.939, "end": 275.279, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 275.279, "end": 275.299, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "la", "start": 275.299, "end": 275.399, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 275.399, "end": 275.399, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "pistola", "start": 275.399, "end": 275.699, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 275.699, "end": 275.719, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "e", "start": 275.719, "end": 275.759, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 275.759, "end": 275.759, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "ti", "start": 275.759, "end": 275.839, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 275.839, "end": 275.859, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "sei", "start": 275.859, "end": 275.999, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 275.999, "end": 275.999, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "confuso", "start": 275.999, "end": 276.339, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 276.339, "end": 276.339, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "con", "start": 276.339, "end": 276.479, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 276.479, "end": 276.479, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "gli", "start": 276.479, "end": 276.539, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 276.539, "end": 276.559, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "altri", "start": 276.559, "end": 276.739, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 276.739, "end": 276.739, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "che", "start": 276.739, "end": 276.839, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 276.839, "end": 276.859, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "sopravvenivano", "start": 276.859, "end": 277.419, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 277.419, "end": 277.439, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "per", "start": 277.439, "end": 277.539, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 277.539, "end": 277.559, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "spe<PERSON><PERSON>", "start": 277.559, "end": 277.879, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 277.879, "end": 277.879, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "l'incendio.", "start": 277.879, "end": 278.479, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 278.479, "end": 279.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 279.019, "end": 279.419, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 279.419, "end": 279.439, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "che", "start": 279.439, "end": 279.519, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 279.519, "end": 279.539, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "le", "start": 279.539, "end": 279.639, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 279.639, "end": 279.699, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "fiamme", "start": 279.699, "end": 280.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 280.019, "end": 280.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 280.019, "end": 280.419, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 280.419, "end": 280.419, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "distrutto", "start": 280.419, "end": 280.859, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 280.859, "end": 280.899, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "tutto,", "start": 280.899, "end": 281.279, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 281.279, "end": 281.819, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "che", "start": 281.819, "end": 281.939, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 281.939, "end": 281.939, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "saresti", "start": 281.939, "end": 282.339, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 282.339, "end": 282.379, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "stato", "start": 282.379, "end": 282.599, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 282.599, "end": 282.659, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "salvo.", "start": 282.659, "end": 283.079, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 283.079, "end": 283.079, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "E", "start": 283.079, "end": 283.219, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 283.219, "end": 283.219, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "lo", "start": 283.219, "end": 283.319, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 283.319, "end": 283.339, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "sarò.", "start": 283.339, "end": 283.739, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 283.739, "end": 284.639, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Non", "start": 284.639, "end": 284.799, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 284.799, "end": 284.819, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "ce", "start": 284.819, "end": 284.899, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 284.899, "end": 284.899, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "la", "start": 284.899, "end": 284.999, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 284.999, "end": 284.999, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "farete", "start": 284.999, "end": 285.239, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 285.239, "end": 285.239, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "ad", "start": 285.239, "end": 285.359, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 285.359, "end": 285.359, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "arrest<PERSON>i.", "start": 285.359, "end": 285.979, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 285.979, "end": 286.699, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Non", "start": 286.699, "end": 286.819, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 286.819, "end": 286.879, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "voglio", "start": 286.879, "end": 287.119, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 287.119, "end": 287.159, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "finire", "start": 287.159, "end": 287.419, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 287.419, "end": 287.419, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "la", "start": 287.419, "end": 287.519, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 287.519, "end": 287.539, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "mia", "start": 287.539, "end": 287.639, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 287.639, "end": 287.659, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "vita", "start": 287.659, "end": 287.799, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 287.799, "end": 287.799, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "in", "start": 287.799, "end": 287.879, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 287.879, "end": 287.879, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "prigione.", "start": 287.879, "end": 288.359, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 288.359, "end": 288.359, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 288.359, "end": 288.819, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 288.819, "end": 288.839, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "giù", "start": 288.839, "end": 288.959, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 288.959, "end": 288.959, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "quell'arma,", "start": 288.959, "end": 289.399, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 289.399, "end": 289.399, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON>.", "start": 289.399, "end": 289.959, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 289.959, "end": 290.399, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 290.399, "end": 290.659, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 290.659, "end": 290.679, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "se", "start": 290.679, "end": 290.779, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 290.779, "end": 290.779, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "tu", "start": 290.779, "end": 290.899, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 290.899, "end": 290.899, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "mi", "start": 290.899, "end": 290.999, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 290.999, "end": 290.999, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>,", "start": 290.999, "end": 291.419, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 291.419, "end": 291.439, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "non", "start": 291.439, "end": 291.539, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 291.539, "end": 291.539, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "start": 291.539, "end": 292.099, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 292.099, "end": 292.099, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "a", "start": 292.099, "end": 292.179, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 292.179, "end": 292.179, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "fuggire.", "start": 292.179, "end": 292.659, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 292.659, "end": 292.659, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Io", "start": 292.659, "end": 293.099, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 293.099, "end": 293.119, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "non", "start": 293.119, "end": 293.199, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 293.199, "end": 293.239, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "voglio", "start": 293.239, "end": 293.439, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 293.439, "end": 293.439, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "ucci<PERSON><PERSON>.", "start": 293.439, "end": 294.039, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 294.039, "end": 294.519, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 294.519, "end": 294.699, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 294.699, "end": 294.699, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "andare", "start": 294.699, "end": 294.919, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 294.919, "end": 294.939, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "via,", "start": 294.939, "end": 295.119, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 295.119, "end": 295.119, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "lontano", "start": 295.119, "end": 295.459, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 295.459, "end": 295.459, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "da", "start": 295.459, "end": 295.579, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 295.579, "end": 295.599, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "qui.", "start": 295.599, "end": 295.859, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 295.859, "end": 296.079, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 296.079, "end": 296.259, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 296.259, "end": 296.279, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "dimenticare", "start": 296.279, "end": 296.799, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 296.799, "end": 296.839, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "tutto,", "start": 296.839, "end": 297.159, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 297.159, "end": 297.159, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "voglio", "start": 297.159, "end": 297.399, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 297.399, "end": 297.419, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "vivere.", "start": 297.419, "end": 297.979, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 297.979, "end": 305.079, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Stai", "start": 305.079, "end": 307.179, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 307.179, "end": 307.199, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "fermo", "start": 307.199, "end": 307.459, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 307.459, "end": 307.459, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON>,", "start": 307.459, "end": 308.039, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 308.039, "end": 308.259, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "non", "start": 308.259, "end": 308.419, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 308.419, "end": 308.419, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "muoverti.", "start": 308.419, "end": 308.899, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 308.899, "end": 309.499, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Ce", "start": 309.499, "end": 309.619, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 309.619, "end": 309.619, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "la", "start": 309.619, "end": 309.719, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 309.719, "end": 309.739, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "faremo,", "start": 309.739, "end": 310.059, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 310.059, "end": 310.059, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "ragazzo.", "start": 310.059, "end": 310.639, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 310.639, "end": 310.639, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}], "language_detected": null, "confidence": null, "processing_time": 24.410916566848755, "metadata": {"service": "elevenlabs", "mode": "free", "model": "scribe_v1", "raw_result": {"language_code": "ita", "language_probability": 0.9949238896369934, "text": "Che? Carmela si è uccisa. Parl<PERSON>. Alza le braccia. Ti spiego. <PERSON><PERSON><PERSON>, non sento più tutto. Chi ha ucciso Carmela morirà, chi? Parla, parla, parla! Qualcuno qualcosa mi deve dire. Non lo so. Rosario mi avrebbe ucciso se avesse potuto quella sera. La morte di Carmela gli aveva dato il pretesto di sfogare il suo odio contro di me, perché Carla mi aveva preferito a lui. Allora è stato Rosario ad uccidere la moglie di Ingarcia? Già, sì, sì, potrebbe essere stato lui. È lui l'assassino, si è vendicato e ora vuol far credere che sono stato io. Così mi ha detto. Ma voi lo sapete che non è vero, perché quella sera- Adesso te lo dico io cos'è successo quella sera. Tornasti a casa dopo la lite con Rosario ancora stortito e pieno d'odio. Sapevi che tutto il paese ti disprezzava. Carmela era morta e nessuno ti avrebbe difeso più. Eri rimasto solo. E non sapevi con chi sfogare il tuo dolore e la tua collera. In casa avevi una vecchia pistola. In certe occasioni si desidera avere un'arma in mano. La prendesti meccanicamente senza nemmeno sapere perché. Forse per sentirti più forte degli altri. Poi uscisti per la strada, ma ad un tratto la vedesti. Vogliono farmi del male, solo perché una pazza ha perduto la testa. Ma tu puoi nascondermi, Abul. Puoi salvarmi. Tu sei a giustizia. Il serbo, la tua spa- Senti, Abul. Tu sei marinaio, non è vero? Sapresti portarmi fuori di qui, via dall'isola? Ecco, vedi? Puoi prenderli. Anche tutti se puoi, purché tu mi conduca via di qui, via di qui, dove vorrai ma via, via. Niente denaro. Anche tu come noi. È me che vuoi. Demonio. Maligno con volto di donna. Potrai farlo. Te lo prometto. Sarò tua, tua. Ma fammi uscire di qui. Hai tuo corpo. Lei, Carla in Garcia, la causa di tutto. Capisti che era lei e non Rosario che volevi uccidere. Lei aveva soltanto giocato con te, e tu in quel gioco avevi perduto Carmela, il bambino che ti doveva nascere. Hai aspettato a sparare perché credevi che Carla trovasse nelle fiamme la giusta punizione. Ma quando ti accorgesti che stava per salvarsi, non hai sparato. Poi hai nascosto la pistola e ti sei confuso con gli altri che sopravvenivano per spegnere l'incendio. Credevi che le fiamme avrebbero distrutto tutto, che saresti stato salvo. E lo sarò. Non ce la farete ad arrestarmi. Non voglio finire la mia vita in prigione. Metti giù quell'arma, Francesco. Anche se tu mi uccidessi, non riusciresti a fuggire. Io non voglio uccidervi. Voglio andare via, lontano da qui. Voglio dimenticare tutto, voglio vivere. Stai fermo Francesco, non muoverti. Ce la faremo, ragazzo. ", "words": [{"text": "Che?", "start": 44.04, "end": 44.099, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 44.099, "end": 44.099, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Carmel<PERSON>", "start": 44.099, "end": 44.939, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 44.939, "end": 44.939, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "si", "start": 44.939, "end": 45.039, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 45.039, "end": 45.039, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "è", "start": 45.039, "end": 45.059, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 45.059, "end": 45.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "uccisa.", "start": 45.059, "end": 45.48, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 45.48, "end": 46.02, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Parla.", "start": 46.02, "end": 46.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 46.559, "end": 46.7, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Alza", "start": 46.7, "end": 47.0, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 47.0, "end": 47.119, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "le", "start": 47.119, "end": 47.139, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 47.139, "end": 47.18, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "braccia.", "start": 47.18, "end": 47.5, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 47.5, "end": 47.5, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Ti", "start": 47.5, "end": 47.599, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 47.599, "end": 47.599, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "spiego.", "start": 47.599, "end": 47.899, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 47.899, "end": 47.899, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Pa<PERSON>a,", "start": 47.899, "end": 48.2, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 48.2, "end": 48.259, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "non", "start": 48.259, "end": 48.399, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 48.399, "end": 48.399, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sento", "start": 48.399, "end": 48.68, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 48.68, "end": 48.68, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "più", "start": 48.68, "end": 48.959, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 48.959, "end": 48.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "tutto.", "start": 48.979, "end": 49.22, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 49.22, "end": 50.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>", "start": 50.059, "end": 50.22, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 50.22, "end": 50.239, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ha", "start": 50.239, "end": 50.34, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 50.34, "end": 50.36, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ucciso", "start": 50.36, "end": 50.659, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 50.659, "end": 50.68, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Carmel<PERSON>", "start": 50.68, "end": 51.039, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 51.039, "end": 51.039, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>,", "start": 51.039, "end": 51.4, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 51.4, "end": 51.459, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "chi?", "start": 51.459, "end": 51.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 51.799, "end": 52.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Pa<PERSON>a,", "start": 52.299, "end": 52.899, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 52.899, "end": 52.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "parla,", "start": 52.919, "end": 53.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 53.479, "end": 53.68, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "parla!", "start": 53.68, "end": 54.239, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 54.239, "end": 54.279, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Qualcu<PERSON>", "start": 54.279, "end": 54.7, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 54.7, "end": 54.7, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "qualcosa", "start": 54.7, "end": 54.919, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 54.919, "end": 54.939, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "mi", "start": 54.939, "end": 55.0, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 55.0, "end": 55.039, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "deve", "start": 55.039, "end": 55.2, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 55.2, "end": 55.259, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "dire.", "start": 55.259, "end": 55.559, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 55.559, "end": 56.84, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Non", "start": 56.84, "end": 57.119, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 57.119, "end": 57.119, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "lo", "start": 57.119, "end": 57.219, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 57.219, "end": 57.219, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "so.", "start": 57.219, "end": 57.439, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 57.439, "end": 79.099, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Rosario", "start": 79.099, "end": 80.199, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 80.199, "end": 80.199, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "mi", "start": 80.199, "end": 80.259, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 80.259, "end": 80.279, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 80.279, "end": 80.499, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 80.499, "end": 80.499, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "ucciso", "start": 80.499, "end": 80.859, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 80.859, "end": 80.859, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "se", "start": 80.859, "end": 80.939, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 80.939, "end": 80.939, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "avesse", "start": 80.939, "end": 81.179, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 81.179, "end": 81.179, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "potuto", "start": 81.179, "end": 81.479, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 81.479, "end": 81.479, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "quella", "start": 81.479, "end": 81.679, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 81.679, "end": 81.699, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "sera.", "start": 81.699, "end": 82.039, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 82.039, "end": 83.379, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "La", "start": 83.379, "end": 83.499, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 83.499, "end": 83.519, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "morte", "start": 83.519, "end": 83.759, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 83.759, "end": 83.779, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "di", "start": 83.779, "end": 83.839, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 83.839, "end": 83.859, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Carmel<PERSON>", "start": 83.859, "end": 84.159, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 84.159, "end": 84.159, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "gli", "start": 84.159, "end": 84.239, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 84.239, "end": 84.259, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "aveva", "start": 84.259, "end": 84.459, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 84.459, "end": 84.479, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "dato", "start": 84.479, "end": 84.639, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 84.639, "end": 84.639, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "il", "start": 84.639, "end": 84.719, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 84.719, "end": 84.739, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "pretesto", "start": 84.739, "end": 85.079, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 85.079, "end": 85.079, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "di", "start": 85.079, "end": 85.179, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 85.179, "end": 85.179, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "sfogare", "start": 85.179, "end": 85.499, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 85.499, "end": 85.499, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "il", "start": 85.499, "end": 85.559, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 85.559, "end": 85.599, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "suo", "start": 85.599, "end": 85.739, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 85.739, "end": 85.779, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "odio", "start": 85.779, "end": 86.039, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 86.039, "end": 86.319, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "contro", "start": 86.319, "end": 86.619, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 86.619, "end": 86.619, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "di", "start": 86.619, "end": 86.719, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 86.719, "end": 86.719, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "me,", "start": 86.719, "end": 87.019, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 87.019, "end": 87.879, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "perché", "start": 87.879, "end": 88.119, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 88.119, "end": 88.159, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON>", "start": 88.159, "end": 88.439, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 88.439, "end": 88.459, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "mi", "start": 88.459, "end": 88.519, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 88.519, "end": 88.539, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "aveva", "start": 88.539, "end": 88.739, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 88.739, "end": 88.799, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "preferito", "start": 88.799, "end": 89.139, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 89.139, "end": 89.139, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "a", "start": 89.139, "end": 89.219, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 89.219, "end": 89.239, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "lui.", "start": 89.239, "end": 89.659, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 89.659, "end": 89.959, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 89.959, "end": 90.279, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 90.279, "end": 90.319, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "è", "start": 90.319, "end": 90.379, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 90.379, "end": 90.399, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "stato", "start": 90.399, "end": 90.719, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 90.719, "end": 90.739, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Rosario", "start": 90.739, "end": 91.339, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 91.339, "end": 91.339, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "ad", "start": 91.339, "end": 91.479, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 91.479, "end": 91.499, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "uccidere", "start": 91.499, "end": 91.939, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 91.939, "end": 91.939, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "la", "start": 91.939, "end": 92.059, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 92.059, "end": 92.119, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "moglie", "start": 92.119, "end": 92.379, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 92.379, "end": 92.399, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "di", "start": 92.399, "end": 92.459, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 92.459, "end": 92.459, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Ingarcia?", "start": 92.459, "end": 93.259, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 93.259, "end": 93.899, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Già,", "start": 93.899, "end": 94.159, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 94.159, "end": 94.239, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "sì,", "start": 94.239, "end": 94.459, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 94.459, "end": 95.119, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "sì,", "start": 95.119, "end": 95.279, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 95.279, "end": 95.279, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 95.279, "end": 95.619, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 95.619, "end": 95.619, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "essere", "start": 95.619, "end": 95.859, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 95.859, "end": 95.859, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "stato", "start": 95.859, "end": 96.099, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 96.099, "end": 96.119, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "lui.", "start": 96.119, "end": 96.399, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 96.399, "end": 97.279, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "È", "start": 97.279, "end": 97.399, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 97.399, "end": 97.399, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "lui", "start": 97.399, "end": 97.559, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 97.559, "end": 97.579, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "l'assassino,", "start": 97.579, "end": 98.219, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 98.219, "end": 98.239, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "si", "start": 98.239, "end": 98.339, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 98.339, "end": 98.339, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "è", "start": 98.339, "end": 98.379, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 98.379, "end": 98.399, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "vendicato", "start": 98.399, "end": 98.819, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 98.819, "end": 98.819, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "e", "start": 98.819, "end": 98.879, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 98.879, "end": 98.879, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "ora", "start": 98.879, "end": 98.999, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 98.999, "end": 98.999, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "vuol", "start": 98.999, "end": 99.159, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 99.159, "end": 99.159, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "far", "start": 99.159, "end": 99.279, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 99.279, "end": 99.339, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "credere", "start": 99.339, "end": 99.619, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 99.619, "end": 99.639, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "che", "start": 99.639, "end": 99.719, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 99.719, "end": 99.719, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "sono", "start": 99.719, "end": 99.899, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 99.899, "end": 99.939, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "stato", "start": 99.939, "end": 100.179, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 100.179, "end": 100.239, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "io.", "start": 100.239, "end": 100.499, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 100.499, "end": 100.639, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Così", "start": 100.639, "end": 100.959, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 100.959, "end": 100.999, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "mi", "start": 100.999, "end": 101.119, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 101.119, "end": 101.119, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "ha", "start": 101.119, "end": 101.199, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 101.199, "end": 101.259, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "detto.", "start": 101.259, "end": 101.759, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 101.759, "end": 102.159, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Ma", "start": 102.159, "end": 102.239, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 102.239, "end": 102.239, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "voi", "start": 102.239, "end": 102.339, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 102.339, "end": 102.359, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "lo", "start": 102.359, "end": 102.459, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 102.459, "end": 102.479, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "sapete", "start": 102.479, "end": 102.759, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 102.759, "end": 102.759, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "che", "start": 102.759, "end": 102.839, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 102.839, "end": 102.839, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "non", "start": 102.839, "end": 102.959, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 102.959, "end": 102.979, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "è", "start": 102.979, "end": 103.059, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 103.059, "end": 103.059, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "vero,", "start": 103.059, "end": 103.399, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 103.399, "end": 103.899, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "perché", "start": 103.899, "end": 104.139, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 104.139, "end": 104.139, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "quella", "start": 104.139, "end": 104.299, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 104.299, "end": 104.359, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "sera-", "start": 104.359, "end": 104.619, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 104.619, "end": 104.699, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 104.699, "end": 105.059, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 105.059, "end": 105.079, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "te", "start": 105.079, "end": 105.179, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 105.179, "end": 105.199, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "lo", "start": 105.199, "end": 105.279, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 105.279, "end": 105.319, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "dico", "start": 105.319, "end": 105.559, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 105.559, "end": 105.619, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "io", "start": 105.619, "end": 105.819, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 105.819, "end": 105.859, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "cos'è", "start": 105.859, "end": 106.099, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 106.099, "end": 106.099, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "successo", "start": 106.099, "end": 106.459, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 106.459, "end": 106.479, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "quella", "start": 106.479, "end": 106.699, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 106.699, "end": 106.739, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "sera.", "start": 106.739, "end": 107.119, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 107.119, "end": 108.539, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 108.539, "end": 109.039, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 109.039, "end": 109.059, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "a", "start": 109.059, "end": 109.139, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 109.139, "end": 109.159, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "casa", "start": 109.159, "end": 109.439, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 109.439, "end": 109.459, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "dopo", "start": 109.459, "end": 109.639, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 109.639, "end": 109.659, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "la", "start": 109.659, "end": 109.759, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 109.759, "end": 109.759, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "lite", "start": 109.759, "end": 109.999, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 109.999, "end": 109.999, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "con", "start": 109.999, "end": 110.159, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 110.159, "end": 110.179, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Rosario", "start": 110.179, "end": 110.619, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 110.619, "end": 110.639, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "ancora", "start": 110.639, "end": 110.959, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 110.959, "end": 110.979, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "stortito", "start": 110.979, "end": 111.539, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 111.539, "end": 111.539, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "e", "start": 111.539, "end": 111.659, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 111.659, "end": 111.659, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "pieno", "start": 111.659, "end": 111.899, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 111.899, "end": 111.919, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "d'odio.", "start": 111.919, "end": 112.459, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 112.459, "end": 113.979, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 113.979, "end": 114.339, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 114.339, "end": 114.339, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "che", "start": 114.339, "end": 114.439, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 114.439, "end": 114.439, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "tutto", "start": 114.439, "end": 114.619, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 114.619, "end": 114.639, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "il", "start": 114.639, "end": 114.679, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 114.679, "end": 114.759, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "paese", "start": 114.759, "end": 115.059, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 115.059, "end": 115.059, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "ti", "start": 115.059, "end": 115.159, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 115.159, "end": 115.179, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "disprez<PERSON><PERSON>.", "start": 115.179, "end": 115.939, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 115.939, "end": 117.159, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Carmel<PERSON>", "start": 117.159, "end": 117.619, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 117.619, "end": 117.619, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "era", "start": 117.619, "end": 117.799, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 117.799, "end": 117.819, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "morta", "start": 117.819, "end": 118.299, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 118.299, "end": 118.759, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "e", "start": 118.759, "end": 118.859, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 118.859, "end": 118.879, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "nessuno", "start": 118.879, "end": 119.319, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 119.319, "end": 119.339, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "ti", "start": 119.339, "end": 119.439, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 119.439, "end": 119.439, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 119.439, "end": 119.719, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 119.719, "end": 119.719, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "difeso", "start": 119.719, "end": 120.079, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 120.079, "end": 120.119, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "più.", "start": 120.119, "end": 120.419, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 120.419, "end": 123.259, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 123.259, "end": 123.499, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 123.499, "end": 123.519, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "rimasto", "start": 123.519, "end": 124.059, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 124.059, "end": 124.119, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "solo.", "start": 124.119, "end": 124.619, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 124.619, "end": 128.339, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "E", "start": 128.339, "end": 128.439, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 128.439, "end": 128.479, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "non", "start": 128.479, "end": 128.559, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 128.559, "end": 128.619, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "sapevi", "start": 128.619, "end": 128.979, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 128.979, "end": 128.979, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "con", "start": 128.979, "end": 129.119, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 129.119, "end": 129.119, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "chi", "start": 129.119, "end": 129.239, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 129.239, "end": 129.259, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "sfogare", "start": 129.259, "end": 129.699, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 129.699, "end": 129.719, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "il", "start": 129.719, "end": 129.799, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 129.799, "end": 129.879, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "tuo", "start": 129.879, "end": 130.039, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 130.039, "end": 130.079, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "dolore", "start": 130.079, "end": 130.339, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 130.339, "end": 130.379, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "e", "start": 130.379, "end": 130.399, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 130.399, "end": 130.399, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "la", "start": 130.399, "end": 130.479, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 130.479, "end": 130.519, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "tua", "start": 130.519, "end": 130.639, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 130.639, "end": 130.699, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "collera.", "start": 130.699, "end": 131.159, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 131.159, "end": 139.539, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "In", "start": 139.539, "end": 139.699, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 139.699, "end": 139.759, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "casa", "start": 139.759, "end": 139.999, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 139.999, "end": 139.999, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "avevi", "start": 139.999, "end": 140.319, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 140.319, "end": 140.339, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "una", "start": 140.339, "end": 140.479, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 140.479, "end": 140.519, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "vecchia", "start": 140.519, "end": 140.779, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 140.779, "end": 140.819, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "pistola.", "start": 140.819, "end": 141.439, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 141.439, "end": 142.539, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "In", "start": 142.539, "end": 142.639, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 142.639, "end": 142.659, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "certe", "start": 142.659, "end": 142.939, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 142.939, "end": 142.939, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "occasioni", "start": 142.939, "end": 143.359, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 143.359, "end": 143.379, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "si", "start": 143.379, "end": 143.499, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 143.499, "end": 143.539, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "desidera", "start": 143.539, "end": 143.959, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 143.959, "end": 143.999, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "avere", "start": 143.999, "end": 144.239, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 144.239, "end": 144.259, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "un'arma", "start": 144.259, "end": 144.619, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 144.619, "end": 144.659, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "in", "start": 144.659, "end": 144.739, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 144.739, "end": 144.799, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "mano.", "start": 144.799, "end": 145.199, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 145.199, "end": 145.759, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "La", "start": 145.759, "end": 145.879, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 145.879, "end": 145.879, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 145.879, "end": 146.279, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 146.279, "end": 146.279, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "meccanicamente", "start": 146.279, "end": 146.899, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 146.899, "end": 146.899, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "senza", "start": 146.899, "end": 147.119, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 147.119, "end": 147.139, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 147.139, "end": 147.379, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 147.379, "end": 147.379, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "sapere", "start": 147.379, "end": 147.679, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 147.679, "end": 147.679, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "perché.", "start": 147.679, "end": 148.079, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 148.079, "end": 148.879, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 148.879, "end": 149.199, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 149.199, "end": 149.199, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "per", "start": 149.199, "end": 149.319, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 149.319, "end": 149.359, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "sentirti", "start": 149.359, "end": 149.799, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 149.799, "end": 149.799, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "più", "start": 149.799, "end": 149.979, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 149.979, "end": 149.999, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "forte", "start": 149.999, "end": 150.259, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 150.259, "end": 150.259, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON>gli", "start": 150.259, "end": 150.479, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 150.479, "end": 150.539, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "altri.", "start": 150.539, "end": 150.979, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 150.979, "end": 152.899, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 152.899, "end": 153.139, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 153.139, "end": 153.139, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 153.139, "end": 153.419, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 153.419, "end": 153.419, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "per", "start": 153.419, "end": 153.539, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 153.539, "end": 153.539, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "la", "start": 153.539, "end": 153.639, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 153.639, "end": 153.659, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "strada,", "start": 153.659, "end": 154.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 154.019, "end": 154.959, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "ma", "start": 154.959, "end": 155.059, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 155.059, "end": 155.059, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "ad", "start": 155.059, "end": 155.119, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 155.119, "end": 155.159, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "un", "start": 155.159, "end": 155.239, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 155.239, "end": 155.259, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "tratto", "start": 155.259, "end": 155.619, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 155.619, "end": 155.779, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "la", "start": 155.779, "end": 155.879, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 155.879, "end": 155.879, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "vedesti.", "start": 155.879, "end": 156.439, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 156.439, "end": 169.919, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 169.919, "end": 170.259, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 170.259, "end": 170.259, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "farmi", "start": 170.259, "end": 170.499, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 170.499, "end": 170.519, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "del", "start": 170.519, "end": 170.659, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 170.659, "end": 170.679, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "male,", "start": 170.679, "end": 171.079, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 171.079, "end": 171.759, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "solo", "start": 171.759, "end": 172.019, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 172.019, "end": 172.039, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "perché", "start": 172.039, "end": 172.219, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 172.219, "end": 172.239, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "una", "start": 172.239, "end": 172.379, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 172.379, "end": 172.379, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "pazza", "start": 172.379, "end": 172.599, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 172.599, "end": 172.599, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "ha", "start": 172.599, "end": 172.659, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 172.659, "end": 172.659, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "perduto", "start": 172.659, "end": 172.979, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 172.979, "end": 173.019, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "la", "start": 173.019, "end": 173.079, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 173.079, "end": 173.119, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "testa.", "start": 173.119, "end": 173.579, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 173.579, "end": 174.779, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Ma", "start": 174.779, "end": 174.919, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 174.919, "end": 174.999, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "tu", "start": 174.999, "end": 175.139, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 175.139, "end": 175.179, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "puoi", "start": 175.179, "end": 175.339, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 175.339, "end": 175.339, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>,", "start": 175.339, "end": 176.019, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 176.019, "end": 176.019, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON>.", "start": 176.019, "end": 176.479, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 176.479, "end": 177.879, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Puo<PERSON>", "start": 177.879, "end": 178.059, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 178.059, "end": 178.099, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "sal<PERSON><PERSON>.", "start": 178.099, "end": 178.939, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 178.939, "end": 188.859, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Tu", "start": 188.859, "end": 189.319, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 189.319, "end": 189.359, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "sei", "start": 189.359, "end": 189.599, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 189.599, "end": 189.619, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "a", "start": 189.619, "end": 189.699, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 189.699, "end": 189.759, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "giustizia.", "start": 189.759, "end": 191.299, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 191.299, "end": 193.839, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "Il", "start": 193.839, "end": 194.079, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 194.079, "end": 194.179, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "serbo,", "start": 194.179, "end": 194.939, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 194.939, "end": 195.619, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "la", "start": 195.619, "end": 195.759, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 195.759, "end": 195.799, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "tua", "start": 195.799, "end": 195.979, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 195.979, "end": 195.999, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "spa-", "start": 195.999, "end": 196.419, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 196.419, "end": 202.799, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "<PERSON><PERSON>,", "start": 202.799, "end": 203.419, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 203.419, "end": 203.439, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON>.", "start": 203.439, "end": 203.799, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 203.799, "end": 205.199, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Tu", "start": 205.199, "end": 205.319, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 205.319, "end": 205.359, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "sei", "start": 205.359, "end": 205.519, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 205.519, "end": 205.519, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "marinaio,", "start": 205.519, "end": 206.039, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 206.039, "end": 206.059, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "non", "start": 206.059, "end": 206.159, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 206.159, "end": 206.179, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "è", "start": 206.179, "end": 206.239, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 206.239, "end": 206.259, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "vero?", "start": 206.259, "end": 206.639, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 206.639, "end": 207.739, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 207.739, "end": 208.219, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 208.219, "end": 208.239, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "portarmi", "start": 208.239, "end": 208.639, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 208.639, "end": 208.679, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "fuori", "start": 208.679, "end": 208.899, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 208.899, "end": 208.899, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "di", "start": 208.899, "end": 208.979, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 208.979, "end": 209.039, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "qui,", "start": 209.039, "end": 209.299, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 209.299, "end": 209.999, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "via", "start": 209.999, "end": 210.219, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 210.219, "end": 210.219, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "dall'isola?", "start": 210.219, "end": 210.939, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 210.939, "end": 213.479, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON>,", "start": 213.479, "end": 213.799, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 213.799, "end": 213.799, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "vedi?", "start": 213.799, "end": 214.179, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 214.179, "end": 214.659, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Puo<PERSON>", "start": 214.659, "end": 214.859, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 214.859, "end": 214.859, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "pre<PERSON><PERSON>.", "start": 214.859, "end": 215.499, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 215.499, "end": 216.019, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 216.019, "end": 216.259, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 216.259, "end": 216.259, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "tutti", "start": 216.259, "end": 216.499, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 216.499, "end": 216.519, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "se", "start": 216.519, "end": 216.659, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 216.659, "end": 216.659, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "puoi,", "start": 216.659, "end": 217.039, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 217.039, "end": 217.379, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "purché", "start": 217.379, "end": 217.619, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 217.619, "end": 217.619, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "tu", "start": 217.619, "end": 217.739, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 217.739, "end": 217.739, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "mi", "start": 217.739, "end": 217.839, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 217.839, "end": 217.859, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "conduca", "start": 217.859, "end": 218.199, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 218.199, "end": 218.199, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "via", "start": 218.199, "end": 218.379, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 218.379, "end": 218.379, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "di", "start": 218.379, "end": 218.459, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 218.459, "end": 218.519, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "qui,", "start": 218.519, "end": 218.759, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 218.759, "end": 219.039, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "via", "start": 219.039, "end": 219.299, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 219.299, "end": 219.299, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "di", "start": 219.299, "end": 219.439, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 219.439, "end": 219.459, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "qui,", "start": 219.459, "end": 219.719, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 219.719, "end": 220.139, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "dove", "start": 220.139, "end": 220.339, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 220.339, "end": 220.339, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "vorrai", "start": 220.339, "end": 220.659, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 220.659, "end": 220.659, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "ma", "start": 220.659, "end": 220.759, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 220.759, "end": 220.839, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "via,", "start": 220.839, "end": 221.179, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 221.179, "end": 221.759, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "via.", "start": 221.759, "end": 222.339, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 222.339, "end": 222.439, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 222.439, "end": 222.919, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 222.919, "end": 222.979, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "denaro.", "start": 222.979, "end": 223.619, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 223.619, "end": 234.979, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 234.979, "end": 237.859, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 237.859, "end": 237.879, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "tu", "start": 237.879, "end": 238.059, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 238.059, "end": 238.079, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "come", "start": 238.079, "end": 238.279, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 238.279, "end": 238.359, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "noi.", "start": 238.359, "end": 238.839, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 238.839, "end": 239.559, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "È", "start": 239.559, "end": 239.739, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 239.739, "end": 239.799, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "me", "start": 239.799, "end": 239.959, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 239.959, "end": 240.079, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "che", "start": 240.079, "end": 240.239, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 240.239, "end": 240.279, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "vuoi.", "start": 240.279, "end": 240.859, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 240.859, "end": 242.899, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Demonio.", "start": 242.899, "end": 243.719, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 243.719, "end": 244.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Maligno", "start": 244.799, "end": 245.359, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 245.359, "end": 245.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "con", "start": 245.379, "end": 245.579, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 245.579, "end": 245.619, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "volto", "start": 245.619, "end": 245.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 245.979, "end": 245.999, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 245.999, "end": 246.099, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 246.099, "end": 246.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "donna.", "start": 246.119, "end": 246.459, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 246.459, "end": 246.459, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Potrai", "start": 246.459, "end": 246.719, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 246.719, "end": 246.739, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "farlo.", "start": 246.739, "end": 247.219, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 247.219, "end": 247.739, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Te", "start": 247.739, "end": 247.839, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 247.839, "end": 247.859, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "lo", "start": 247.859, "end": 247.939, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 247.939, "end": 247.939, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "prometto.", "start": 247.939, "end": 248.459, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 248.459, "end": 248.699, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 248.699, "end": 248.939, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 248.939, "end": 248.999, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "tua,", "start": 248.999, "end": 249.399, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 249.399, "end": 249.899, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "tua.", "start": 249.899, "end": 250.379, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 250.379, "end": 251.039, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Ma", "start": 251.039, "end": 251.179, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 251.179, "end": 251.259, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "fammi", "start": 251.259, "end": 251.619, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 251.619, "end": 251.619, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "uscire", "start": 251.619, "end": 252.019, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 252.019, "end": 252.019, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "di", "start": 252.019, "end": 252.159, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 252.159, "end": 252.179, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "qui.", "start": 252.179, "end": 252.499, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 252.499, "end": 253.719, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON>", "start": 253.719, "end": 253.999, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 253.999, "end": 254.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "tuo", "start": 254.379, "end": 254.579, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 254.579, "end": 254.759, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "corpo.", "start": 254.759, "end": 255.359, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 255.359, "end": 256.159, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON>,", "start": 256.159, "end": 256.739, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 256.739, "end": 256.759, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON>", "start": 256.759, "end": 257.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 257.019, "end": 257.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "in", "start": 257.019, "end": 257.099, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 257.099, "end": 257.099, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON>,", "start": 257.099, "end": 257.619, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 257.619, "end": 257.699, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "la", "start": 257.699, "end": 257.799, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 257.799, "end": 257.839, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "causa", "start": 257.839, "end": 258.039, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 258.039, "end": 258.059, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "di", "start": 258.059, "end": 258.179, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 258.179, "end": 258.179, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "tutto.", "start": 258.179, "end": 258.539, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 258.539, "end": 258.879, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 258.879, "end": 259.239, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 259.239, "end": 259.259, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "che", "start": 259.259, "end": 259.339, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 259.339, "end": 259.359, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "era", "start": 259.359, "end": 259.479, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 259.479, "end": 259.519, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "lei", "start": 259.519, "end": 259.699, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 259.699, "end": 259.719, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "e", "start": 259.719, "end": 259.819, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 259.819, "end": 259.819, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "non", "start": 259.819, "end": 259.919, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 259.919, "end": 259.939, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Rosario", "start": 259.939, "end": 260.279, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 260.279, "end": 260.279, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "che", "start": 260.279, "end": 260.379, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 260.379, "end": 260.399, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON>i", "start": 260.399, "end": 260.639, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 260.639, "end": 260.639, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "uccidere.", "start": 260.639, "end": 261.099, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 261.099, "end": 261.599, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 261.599, "end": 261.759, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 261.759, "end": 261.779, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "aveva", "start": 261.779, "end": 261.979, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 261.979, "end": 261.979, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "soltanto", "start": 261.979, "end": 262.339, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 262.339, "end": 262.359, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "giocato", "start": 262.359, "end": 262.699, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 262.699, "end": 262.719, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "con", "start": 262.719, "end": 262.859, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 262.859, "end": 262.899, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "te,", "start": 262.899, "end": 263.059, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 263.059, "end": 263.219, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "e", "start": 263.219, "end": 263.239, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 263.239, "end": 263.239, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "tu", "start": 263.239, "end": 263.339, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 263.339, "end": 263.339, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "in", "start": 263.339, "end": 263.439, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 263.439, "end": 263.439, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "quel", "start": 263.439, "end": 263.579, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 263.579, "end": 263.599, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "gioco", "start": 263.599, "end": 263.759, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 263.759, "end": 263.759, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "avevi", "start": 263.759, "end": 263.979, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 263.979, "end": 263.979, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "perduto", "start": 263.979, "end": 264.339, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 264.339, "end": 264.359, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Carmela,", "start": 264.359, "end": 264.779, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 264.779, "end": 264.779, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "il", "start": 264.779, "end": 264.879, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 264.879, "end": 264.899, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "bambino", "start": 264.899, "end": 265.159, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 265.159, "end": 265.159, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "che", "start": 265.159, "end": 265.239, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 265.239, "end": 265.259, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "ti", "start": 265.259, "end": 265.339, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 265.339, "end": 265.359, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 265.359, "end": 265.619, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 265.619, "end": 265.619, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "nascere.", "start": 265.619, "end": 266.079, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 266.079, "end": 266.739, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON>", "start": 266.739, "end": 266.919, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 266.919, "end": 266.919, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "aspettato", "start": 266.919, "end": 267.379, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 267.379, "end": 267.379, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "a", "start": 267.379, "end": 267.459, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 267.459, "end": 267.459, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "sparare", "start": 267.459, "end": 267.819, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 267.819, "end": 267.819, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "perché", "start": 267.819, "end": 268.119, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 268.119, "end": 268.299, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "credevi", "start": 268.299, "end": 268.659, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 268.659, "end": 268.659, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "che", "start": 268.659, "end": 268.779, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 268.779, "end": 268.799, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON>", "start": 268.799, "end": 269.039, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 269.039, "end": 269.039, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "trovasse", "start": 269.039, "end": 269.399, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 269.399, "end": 269.399, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "nelle", "start": 269.399, "end": 269.559, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 269.559, "end": 269.559, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "fiamme", "start": 269.559, "end": 269.799, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 269.799, "end": 269.799, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "la", "start": 269.799, "end": 269.919, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 269.919, "end": 269.919, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "giusta", "start": 269.919, "end": 270.139, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 270.139, "end": 270.159, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "punizione.", "start": 270.159, "end": 270.659, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 270.659, "end": 271.119, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Ma", "start": 271.119, "end": 271.239, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 271.239, "end": 271.239, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "quando", "start": 271.239, "end": 271.459, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 271.459, "end": 271.459, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "ti", "start": 271.459, "end": 271.579, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 271.579, "end": 271.579, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "acco<PERSON><PERSON>", "start": 271.579, "end": 271.999, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 271.999, "end": 271.999, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "che", "start": 271.999, "end": 272.099, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 272.099, "end": 272.099, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "stava", "start": 272.099, "end": 272.339, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 272.339, "end": 272.339, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "per", "start": 272.339, "end": 272.459, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 272.459, "end": 272.499, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>,", "start": 272.499, "end": 272.959, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 272.959, "end": 273.119, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "non", "start": 273.119, "end": 273.339, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 273.339, "end": 273.579, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "hai", "start": 273.579, "end": 273.779, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 273.779, "end": 273.799, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "sparato.", "start": 273.799, "end": 274.379, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 274.379, "end": 274.659, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 274.659, "end": 274.819, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 274.819, "end": 274.819, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "hai", "start": 274.819, "end": 274.939, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 274.939, "end": 274.939, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "nascosto", "start": 274.939, "end": 275.279, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 275.279, "end": 275.299, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "la", "start": 275.299, "end": 275.399, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 275.399, "end": 275.399, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "pistola", "start": 275.399, "end": 275.699, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 275.699, "end": 275.719, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "e", "start": 275.719, "end": 275.759, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 275.759, "end": 275.759, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "ti", "start": 275.759, "end": 275.839, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 275.839, "end": 275.859, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "sei", "start": 275.859, "end": 275.999, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 275.999, "end": 275.999, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "confuso", "start": 275.999, "end": 276.339, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 276.339, "end": 276.339, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "con", "start": 276.339, "end": 276.479, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 276.479, "end": 276.479, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "gli", "start": 276.479, "end": 276.539, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 276.539, "end": 276.559, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "altri", "start": 276.559, "end": 276.739, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 276.739, "end": 276.739, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "che", "start": 276.739, "end": 276.839, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 276.839, "end": 276.859, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "sopravvenivano", "start": 276.859, "end": 277.419, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 277.419, "end": 277.439, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "per", "start": 277.439, "end": 277.539, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 277.539, "end": 277.559, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "spe<PERSON><PERSON>", "start": 277.559, "end": 277.879, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 277.879, "end": 277.879, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "l'incendio.", "start": 277.879, "end": 278.479, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 278.479, "end": 279.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 279.019, "end": 279.419, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 279.419, "end": 279.439, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "che", "start": 279.439, "end": 279.519, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 279.519, "end": 279.539, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "le", "start": 279.539, "end": 279.639, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 279.639, "end": 279.699, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "fiamme", "start": 279.699, "end": 280.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 280.019, "end": 280.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 280.019, "end": 280.419, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 280.419, "end": 280.419, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "distrutto", "start": 280.419, "end": 280.859, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 280.859, "end": 280.899, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "tutto,", "start": 280.899, "end": 281.279, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 281.279, "end": 281.819, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "che", "start": 281.819, "end": 281.939, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 281.939, "end": 281.939, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "saresti", "start": 281.939, "end": 282.339, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 282.339, "end": 282.379, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "stato", "start": 282.379, "end": 282.599, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 282.599, "end": 282.659, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "salvo.", "start": 282.659, "end": 283.079, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 283.079, "end": 283.079, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "E", "start": 283.079, "end": 283.219, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 283.219, "end": 283.219, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "lo", "start": 283.219, "end": 283.319, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 283.319, "end": 283.339, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "sarò.", "start": 283.339, "end": 283.739, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 283.739, "end": 284.639, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Non", "start": 284.639, "end": 284.799, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 284.799, "end": 284.819, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "ce", "start": 284.819, "end": 284.899, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 284.899, "end": 284.899, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "la", "start": 284.899, "end": 284.999, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 284.999, "end": 284.999, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "farete", "start": 284.999, "end": 285.239, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 285.239, "end": 285.239, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "ad", "start": 285.239, "end": 285.359, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 285.359, "end": 285.359, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "arrest<PERSON>i.", "start": 285.359, "end": 285.979, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 285.979, "end": 286.699, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Non", "start": 286.699, "end": 286.819, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 286.819, "end": 286.879, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "voglio", "start": 286.879, "end": 287.119, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 287.119, "end": 287.159, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "finire", "start": 287.159, "end": 287.419, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 287.419, "end": 287.419, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "la", "start": 287.419, "end": 287.519, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 287.519, "end": 287.539, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "mia", "start": 287.539, "end": 287.639, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 287.639, "end": 287.659, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "vita", "start": 287.659, "end": 287.799, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 287.799, "end": 287.799, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "in", "start": 287.799, "end": 287.879, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 287.879, "end": 287.879, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "prigione.", "start": 287.879, "end": 288.359, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 288.359, "end": 288.359, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 288.359, "end": 288.819, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 288.819, "end": 288.839, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "giù", "start": 288.839, "end": 288.959, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 288.959, "end": 288.959, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "quell'arma,", "start": 288.959, "end": 289.399, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 289.399, "end": 289.399, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON>.", "start": 289.399, "end": 289.959, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 289.959, "end": 290.399, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 290.399, "end": 290.659, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 290.659, "end": 290.679, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "se", "start": 290.679, "end": 290.779, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 290.779, "end": 290.779, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "tu", "start": 290.779, "end": 290.899, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 290.899, "end": 290.899, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "mi", "start": 290.899, "end": 290.999, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 290.999, "end": 290.999, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>,", "start": 290.999, "end": 291.419, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 291.419, "end": 291.439, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "non", "start": 291.439, "end": 291.539, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 291.539, "end": 291.539, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "start": 291.539, "end": 292.099, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 292.099, "end": 292.099, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "a", "start": 292.099, "end": 292.179, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 292.179, "end": 292.179, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "fuggire.", "start": 292.179, "end": 292.659, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 292.659, "end": 292.659, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Io", "start": 292.659, "end": 293.099, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 293.099, "end": 293.119, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "non", "start": 293.119, "end": 293.199, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 293.199, "end": 293.239, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "voglio", "start": 293.239, "end": 293.439, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 293.439, "end": 293.439, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "ucci<PERSON><PERSON>.", "start": 293.439, "end": 294.039, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 294.039, "end": 294.519, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 294.519, "end": 294.699, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 294.699, "end": 294.699, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "andare", "start": 294.699, "end": 294.919, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 294.919, "end": 294.939, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "via,", "start": 294.939, "end": 295.119, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 295.119, "end": 295.119, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "lontano", "start": 295.119, "end": 295.459, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 295.459, "end": 295.459, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "da", "start": 295.459, "end": 295.579, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 295.579, "end": 295.599, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "qui.", "start": 295.599, "end": 295.859, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 295.859, "end": 296.079, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 296.079, "end": 296.259, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 296.259, "end": 296.279, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "dimenticare", "start": 296.279, "end": 296.799, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 296.799, "end": 296.839, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "tutto,", "start": 296.839, "end": 297.159, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 297.159, "end": 297.159, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "voglio", "start": 297.159, "end": 297.399, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 297.399, "end": 297.419, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "vivere.", "start": 297.419, "end": 297.979, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 297.979, "end": 305.079, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Stai", "start": 305.079, "end": 307.179, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 307.179, "end": 307.199, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "fermo", "start": 307.199, "end": 307.459, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 307.459, "end": 307.459, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON>,", "start": 307.459, "end": 308.039, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 308.039, "end": 308.259, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "non", "start": 308.259, "end": 308.419, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 308.419, "end": 308.419, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "muoverti.", "start": 308.419, "end": 308.899, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 308.899, "end": 309.499, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Ce", "start": 309.499, "end": 309.619, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 309.619, "end": 309.619, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "la", "start": 309.619, "end": 309.719, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 309.719, "end": 309.739, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "faremo,", "start": 309.739, "end": 310.059, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 310.059, "end": 310.059, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "ragazzo.", "start": 310.059, "end": 310.639, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 310.639, "end": 310.639, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}]}}, "created_at": 1754319840.4239511}