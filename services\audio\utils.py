"""
音频处理工具函数（简化版）

专门为WAV统一输出设计的简化工具函数。

保留功能：
- 路径管理：输出路径生成和目录创建
- 数据格式化：文件大小和时长格式化
- 基础验证：文件路径验证

移除功能：
- 格式检测函数（不再需要复杂格式识别）
- 格式支持检查（统一支持所有FFmpeg可读格式）
"""

import os
from typing import Optional


def generate_output_path(input_path: str, output_dir: Optional[str] = None,
                        suffix: str = "_converted") -> str:
    """生成输出文件路径（固定.wav扩展名）

    Args:
        input_path: 输入文件路径
        output_dir: 输出目录（可选）
        suffix: 文件名后缀（可选）

    Returns:
        str: 输出文件路径（.wav格式）
    """
    # 获取输入文件的基本信息
    input_dir = os.path.dirname(input_path)
    input_name = os.path.splitext(os.path.basename(input_path))[0]

    # 确定输出目录
    target_dir = output_dir if output_dir else input_dir

    # 生成输出文件名（固定.wav扩展名）
    output_filename = f"{input_name}{suffix}.wav"

    return os.path.join(target_dir, output_filename)


def ensure_directory_exists(directory_path: str) -> bool:
    """确保目录存在
    
    Args:
        directory_path: 目录路径
        
    Returns:
        bool: 目录是否存在或创建成功
    """
    if not directory_path:
        return False
    
    try:
        os.makedirs(directory_path, exist_ok=True)
        return True
    except OSError:
        return False


def validate_file_path(file_path: str) -> bool:
    """验证文件路径
    
    Args:
        file_path: 文件路径
        
    Returns:
        bool: 路径是否有效
    """
    if not file_path:
        return False
    
    # 检查路径格式
    try:
        normalized_path = os.path.normpath(file_path)
        return os.path.isabs(normalized_path) or os.path.exists(file_path)
    except (OSError, ValueError):
        return False


def format_file_size(size_bytes: int) -> str:
    """格式化文件大小
    
    Args:
        size_bytes: 文件大小（字节）
        
    Returns:
        str: 格式化的文件大小字符串
    """
    if size_bytes < 1024:
        return f"{size_bytes} B"
    elif size_bytes < 1024 * 1024:
        return f"{size_bytes / 1024:.1f} KB"
    elif size_bytes < 1024 * 1024 * 1024:
        return f"{size_bytes / (1024 * 1024):.1f} MB"
    else:
        return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"


def format_duration(duration_seconds: Optional[float]) -> str:
    """格式化音频时长
    
    Args:
        duration_seconds: 时长（秒）
        
    Returns:
        str: 格式化的时长字符串
    """
    if duration_seconds is None or duration_seconds <= 0:
        return "未知"
    
    hours = int(duration_seconds // 3600)
    minutes = int((duration_seconds % 3600) // 60)
    seconds = int(duration_seconds % 60)
    
    if hours > 0:
        return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
    else:
        return f"{minutes:02d}:{seconds:02d}"


def is_supported_audio_format(file_path: str) -> bool:
    """检查是否为支持的音频格式（简化版）
    
    对于320k MP3统一输出，所有FFmpeg能读取的格式都支持。
    这个函数主要用于基础文件验证。
    
    Args:
        file_path: 文件路径
        
    Returns:
        bool: 是否支持（基本上都返回True，除非文件不存在）
    """
    if not os.path.exists(file_path):
        return False
    
    # 简单检查：文件存在且有扩展名
    _, ext = os.path.splitext(file_path.lower())
    
    # 常见音频/视频扩展名（FFmpeg通常都支持）
    common_extensions = {
        '.mp3', '.wav', '.flac', '.aac', '.m4a', '.ogg', '.wma', '.opus',
        '.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm'
    }
    
    return ext in common_extensions or len(ext) > 0  # 有扩展名就认为可能支持


def get_file_info(file_path: str) -> dict:
    """获取基本文件信息
    
    Args:
        file_path: 文件路径
        
    Returns:
        dict: 文件信息字典
    """
    if not os.path.exists(file_path):
        return {
            'exists': False,
            'size_bytes': 0,
            'size_formatted': '0 B',
            'filename': os.path.basename(file_path) if file_path else ''
        }
    
    try:
        size_bytes = os.path.getsize(file_path)
        return {
            'exists': True,
            'size_bytes': size_bytes,
            'size_formatted': format_file_size(size_bytes),
            'filename': os.path.basename(file_path)
        }
    except OSError:
        return {
            'exists': False,
            'size_bytes': 0,
            'size_formatted': '0 B',
            'filename': os.path.basename(file_path) if file_path else ''
        }
