{"success": true, "service": "elevenlabs", "transcription_id": "elevenlabs_1754319678", "text": "Le trovate? <PERSON><PERSON>. <PERSON><PERSON>, che cosa guardi? Non hai mai visto una donna fare il bagno? Mmm? C'è qualcosa che non ti va? Nulla. No, no, nulla. Per quale motivo non avvertisti tuo padre, eh? Anche come padre Ingarsia è un tipo difficile. Del resto non disse niente nemmeno a me. Non ebbe il coraggio. Mio padre viveva solamente per lei. Però mi ripromisi di fare delle ricerche. Dove? Qui nell'isola? Oh no, a Palermo, dove mio padre mi disse che l'aveva conosciuta. Ingarsia andava dicendo che Carla faceva la maestra a Palermo. Belle cose insegnava. E per quel che mi risulta aveva molti allievi a pagamento. Purtroppo l'occasione di fare queste ricerche mi si presentò molto presto. Una sera, durante una delle solite e burrascose discussioni, mio padre mi annunciò che mi avrebbe riportata in collegio. Fu Carla a suggerirgli questa idea per separarmi da Rosario e per allontanare mio padre. Disperatamente decisa a non partire- Ciao amore. Rosario. Rosario, domani mattina mio padre mi condurrà a Palermo, al mio vecchio collegio. Ancora lei. Sì, è stata lei. Ha spinto papà a sbarattarsi di me. Ho paura, amore, ho paura. Quanto ti fermerai? Non lo so. Il convitto è chiuso di questa stagione, ma suor Teresa mi terrà per accontentare papà. Sarò sola. Io verrò a Palermo giovedì. Vendiamo il primo carico di spugne della cooperativa a dei commercianti di città. Ti aspetterò al porto all'imbarco dei pescherecci fino al tramonto. Sì. E Carla? Cosa vuoi fare con lei? Quella donna non ti tormenterà più, cara. Quando ritornerai le sarà rimasto ben poco veleno, stai tranquilla. Appena arrivata in collegio decisi di fuggire per fare delle indagini sulla vera personalità di Carla. Avevo dei tristi presentimenti e sentivo che dovevo smascherare a qualunque costo quella donna prima che qualcosa di grave accadesse. Ma purtroppo non feci in tempo. Tornerò presto, cara. Come crede, signor Ingarsia, anche se non abbiamo allieve in questa stagione, Maria si troverà benissimo. Suor Teresa ti accompagnerà a visitare la città, avrai modo di distrarti. Certamente. Godrà di tutta la libertà possibile. Non ci eravamo mai dimenticate della piccola Maria. Uh, per la rette della figliola ci metteremo d'accordo. Come vuole, don Ingarsia, non abbia preoccupazioni per questo. Arrivederci, Maria. Sorella. Buongiorno. Suor Teresa, ho dimenticato di dire una cosa importante a papà. Mi scusi un attimo. ", "words": [{"text": "Le", "start": 13.359, "end": 13.539, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 13.539, "end": 13.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "trovate?", "start": 13.539, "end": 14.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 14.199, "end": 14.96, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Sì.", "start": 14.96, "end": 15.379, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 15.379, "end": 26.439, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON>,", "start": 26.439, "end": 26.639, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 26.639, "end": 26.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 26.659, "end": 26.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 26.799, "end": 26.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "cosa", "start": 26.819, "end": 27.0, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 27.0, "end": 27.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "guardi?", "start": 27.059, "end": 27.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 27.559, "end": 27.76, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Non", "start": 27.76, "end": 27.88, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 27.88, "end": 27.899, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "hai", "start": 27.899, "end": 28.019, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 28.019, "end": 28.039, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "mai", "start": 28.039, "end": 28.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 28.159, "end": 28.18, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "visto", "start": 28.18, "end": 28.36, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 28.36, "end": 28.379, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "una", "start": 28.379, "end": 28.499, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 28.499, "end": 28.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "donna", "start": 28.539, "end": 28.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 28.779, "end": 28.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "fare", "start": 28.819, "end": 28.999, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 28.999, "end": 29.0, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "il", "start": 29.0, "end": 29.059, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 29.059, "end": 29.099, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "bagno?", "start": 29.099, "end": 29.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 29.559, "end": 30.399, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Mmm?", "start": 30.399, "end": 30.739, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 30.739, "end": 35.939, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "C'è", "start": 35.939, "end": 36.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 36.139, "end": 36.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "qualcosa", "start": 36.159, "end": 36.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 36.659, "end": 36.68, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 36.68, "end": 36.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 36.819, "end": 36.84, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "non", "start": 36.84, "end": 36.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 36.959, "end": 37.02, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ti", "start": 37.02, "end": 37.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 37.139, "end": 37.2, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "va?", "start": 37.2, "end": 37.48, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 37.48, "end": 38.0, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>.", "start": 38.0, "end": 38.539, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 38.539, "end": 38.86, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "No,", "start": 38.86, "end": 39.04, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 39.04, "end": 39.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "no,", "start": 39.059, "end": 39.259, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 39.259, "end": 39.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "nulla.", "start": 39.299, "end": 39.78, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 39.78, "end": 43.879, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Per", "start": 43.879, "end": 44.02, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 44.02, "end": 44.02, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "quale", "start": 44.02, "end": 44.219, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 44.219, "end": 44.219, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "motivo", "start": 44.219, "end": 44.579, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 44.579, "end": 44.579, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "non", "start": 44.579, "end": 44.739, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 44.739, "end": 44.739, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "avvertisti", "start": 44.739, "end": 45.24, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 45.24, "end": 45.319, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "tuo", "start": 45.319, "end": 45.519, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 45.519, "end": 45.539, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "padre,", "start": 45.539, "end": 45.96, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 45.96, "end": 46.159, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "eh?", "start": 46.159, "end": 46.34, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 46.34, "end": 46.879, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 46.879, "end": 47.159, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 47.159, "end": 47.18, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "come", "start": 47.18, "end": 47.38, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 47.38, "end": 47.419, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "padre", "start": 47.419, "end": 47.7, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 47.7, "end": 47.719, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Ingarsia", "start": 47.719, "end": 48.139, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 48.139, "end": 48.18, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "è", "start": 48.18, "end": 48.2, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 48.2, "end": 48.2, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "un", "start": 48.2, "end": 48.259, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 48.259, "end": 48.259, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "tipo", "start": 48.259, "end": 48.479, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 48.479, "end": 48.479, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "difficile.", "start": 48.479, "end": 49.079, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 49.079, "end": 50.039, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Del", "start": 50.039, "end": 50.219, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 50.219, "end": 50.219, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "resto", "start": 50.219, "end": 50.459, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 50.459, "end": 50.459, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "non", "start": 50.459, "end": 50.559, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 50.559, "end": 50.559, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "disse", "start": 50.559, "end": 50.759, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 50.759, "end": 50.779, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "niente", "start": 50.779, "end": 50.979, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 50.979, "end": 51.02, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 51.02, "end": 51.279, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 51.279, "end": 51.299, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "a", "start": 51.299, "end": 51.34, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 51.34, "end": 51.36, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "me.", "start": 51.36, "end": 51.539, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 51.539, "end": 51.759, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Non", "start": 51.759, "end": 51.94, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 51.94, "end": 51.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ebbe", "start": 51.979, "end": 52.219, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 52.219, "end": 52.219, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "il", "start": 52.219, "end": 52.279, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 52.279, "end": 52.319, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "coraggio.", "start": 52.319, "end": 52.819, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 52.819, "end": 53.319, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 53.319, "end": 53.459, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 53.459, "end": 53.539, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "padre", "start": 53.539, "end": 53.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 53.799, "end": 53.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "viveva", "start": 53.799, "end": 54.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 54.139, "end": 54.18, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "solamente", "start": 54.18, "end": 54.659, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 54.659, "end": 54.699, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "per", "start": 54.699, "end": 54.86, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 54.86, "end": 54.899, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "lei.", "start": 54.899, "end": 55.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 55.159, "end": 55.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 55.379, "end": 55.599, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 55.599, "end": 55.659, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mi", "start": 55.659, "end": 55.72, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 55.72, "end": 55.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 55.739, "end": 56.24, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 56.24, "end": 56.259, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 56.259, "end": 56.399, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 56.399, "end": 56.399, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "fare", "start": 56.399, "end": 56.599, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 56.599, "end": 56.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "delle", "start": 56.639, "end": 56.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 56.799, "end": 56.84, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ricerche.", "start": 56.84, "end": 57.419, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 57.419, "end": 57.439, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Dove?", "start": 57.439, "end": 57.779, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 57.779, "end": 58.319, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON>ui", "start": 58.319, "end": 58.479, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 58.479, "end": 58.539, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "nell'isola?", "start": 58.539, "end": 59.079, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 59.079, "end": 59.599, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Oh", "start": 59.599, "end": 59.76, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 59.76, "end": 59.84, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "no,", "start": 59.84, "end": 60.04, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 60.04, "end": 60.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "a", "start": 60.059, "end": 60.119, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 60.119, "end": 60.199, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Palermo,", "start": 60.199, "end": 60.639, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 60.639, "end": 60.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "dove", "start": 60.639, "end": 60.819, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 60.819, "end": 60.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mio", "start": 60.819, "end": 60.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 60.979, "end": 61.02, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "padre", "start": 61.02, "end": 61.259, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 61.259, "end": 61.259, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mi", "start": 61.259, "end": 61.34, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 61.34, "end": 61.399, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "disse", "start": 61.399, "end": 61.58, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 61.58, "end": 61.619, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "che", "start": 61.619, "end": 61.699, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 61.699, "end": 61.699, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "l'aveva", "start": 61.699, "end": 62.02, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 62.02, "end": 62.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "conosciuta.", "start": 62.059, "end": 62.379, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 62.379, "end": 62.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Ingarsia", "start": 62.379, "end": 62.919, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 62.919, "end": 62.919, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "andava", "start": 62.919, "end": 63.2, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 63.2, "end": 63.219, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "dicendo", "start": 63.219, "end": 63.639, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 63.639, "end": 63.639, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "che", "start": 63.639, "end": 63.759, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 63.759, "end": 63.779, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON>", "start": 63.779, "end": 64.08, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 64.08, "end": 64.099, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "faceva", "start": 64.099, "end": 64.439, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 64.439, "end": 64.439, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "la", "start": 64.439, "end": 64.519, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 64.519, "end": 64.559, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "maestra", "start": 64.559, "end": 64.979, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 64.979, "end": 64.979, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "a", "start": 64.979, "end": 65.039, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 65.039, "end": 65.08, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Palermo.", "start": 65.08, "end": 65.64, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 65.64, "end": 66.739, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON>", "start": 66.739, "end": 67.0, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 67.0, "end": 67.059, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "cose", "start": 67.059, "end": 67.4, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 67.4, "end": 67.479, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "insegnava.", "start": 67.479, "end": 68.259, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 68.259, "end": 68.639, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "E", "start": 68.639, "end": 68.86, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 68.86, "end": 68.939, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "per", "start": 68.939, "end": 69.12, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 69.12, "end": 69.139, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "quel", "start": 69.139, "end": 69.279, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 69.279, "end": 69.279, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "che", "start": 69.279, "end": 69.379, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 69.379, "end": 69.379, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "mi", "start": 69.379, "end": 69.5, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 69.5, "end": 69.519, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "risulta", "start": 69.519, "end": 69.879, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 69.879, "end": 69.919, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "aveva", "start": 69.919, "end": 70.139, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 70.139, "end": 70.18, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "molti", "start": 70.18, "end": 70.48, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 70.48, "end": 70.5, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "allievi", "start": 70.5, "end": 71.04, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 71.04, "end": 71.18, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "a", "start": 71.18, "end": 71.279, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 71.279, "end": 71.299, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "pagamento.", "start": 71.299, "end": 72.0, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 72.0, "end": 72.839, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 72.839, "end": 73.419, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 73.419, "end": 73.439, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "l'occasione", "start": 73.439, "end": 73.94, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 73.94, "end": 73.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 73.979, "end": 74.039, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 74.039, "end": 74.08, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "fare", "start": 74.08, "end": 74.279, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 74.279, "end": 74.319, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "queste", "start": 74.319, "end": 74.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 74.559, "end": 74.58, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ricerche", "start": 74.58, "end": 75.059, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 75.059, "end": 75.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mi", "start": 75.059, "end": 75.18, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 75.18, "end": 75.22, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "si", "start": 75.22, "end": 75.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 75.299, "end": 75.339, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "presentò", "start": 75.339, "end": 75.739, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 75.739, "end": 75.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "molto", "start": 75.799, "end": 76.099, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 76.099, "end": 76.099, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "presto.", "start": 76.099, "end": 76.599, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 76.599, "end": 77.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Una", "start": 77.739, "end": 77.919, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 77.919, "end": 78.0, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sera,", "start": 78.0, "end": 78.319, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 78.319, "end": 78.319, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "durante", "start": 78.319, "end": 78.72, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 78.72, "end": 78.759, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "una", "start": 78.759, "end": 78.919, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 78.919, "end": 78.939, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "delle", "start": 78.939, "end": 79.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 79.159, "end": 79.159, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "solite", "start": 79.159, "end": 79.62, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 79.62, "end": 79.72, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "e", "start": 79.72, "end": 79.739, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 79.739, "end": 79.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "burrascose", "start": 79.739, "end": 80.319, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 80.319, "end": 80.339, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "discussioni,", "start": 80.339, "end": 81.079, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 81.079, "end": 81.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mio", "start": 81.379, "end": 81.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 81.559, "end": 81.619, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "padre", "start": 81.619, "end": 81.9, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 81.9, "end": 81.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mi", "start": 81.919, "end": 82.019, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 82.019, "end": 82.019, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "annunci<PERSON>", "start": 82.019, "end": 82.379, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 82.379, "end": 82.4, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "che", "start": 82.4, "end": 82.499, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 82.499, "end": 82.54, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mi", "start": 82.54, "end": 82.62, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 82.62, "end": 82.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 82.639, "end": 82.919, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 82.919, "end": 82.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "riportata", "start": 82.919, "end": 83.459, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 83.459, "end": 83.459, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "in", "start": 83.459, "end": 83.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 83.559, "end": 83.599, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "collegio.", "start": 83.599, "end": 84.079, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 84.079, "end": 84.619, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>", "start": 84.619, "end": 84.779, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 84.779, "end": 84.839, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>", "start": 84.839, "end": 85.18, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 85.18, "end": 85.18, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "a", "start": 85.18, "end": 85.279, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 85.279, "end": 85.279, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sugger<PERSON><PERSON>", "start": 85.279, "end": 85.819, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 85.819, "end": 85.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "questa", "start": 85.819, "end": 86.08, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 86.08, "end": 86.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "idea", "start": 86.119, "end": 86.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 86.479, "end": 86.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "per", "start": 86.919, "end": 87.08, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 87.08, "end": 87.139, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "separarmi", "start": 87.139, "end": 87.639, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 87.639, "end": 87.68, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "da", "start": 87.68, "end": 87.759, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 87.759, "end": 87.779, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Rosario", "start": 87.779, "end": 88.199, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 88.199, "end": 88.199, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "e", "start": 88.199, "end": 88.319, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 88.319, "end": 88.339, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "per", "start": 88.339, "end": 88.44, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 88.44, "end": 88.459, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "allontanare", "start": 88.459, "end": 88.94, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 88.94, "end": 88.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mio", "start": 88.979, "end": 89.119, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 89.119, "end": 89.199, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "padre.", "start": 89.199, "end": 89.54, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 89.54, "end": 90.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Disperatamente", "start": 90.059, "end": 90.859, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 90.859, "end": 90.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "decisa", "start": 90.919, "end": 91.259, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 91.259, "end": 91.259, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "a", "start": 91.259, "end": 91.339, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 91.339, "end": 91.339, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "non", "start": 91.339, "end": 91.459, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 91.459, "end": 91.519, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "partire-", "start": 91.519, "end": 92.019, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 92.019, "end": 94.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Ciao", "start": 94.919, "end": 95.22, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 95.22, "end": 95.22, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "amore.", "start": 95.22, "end": 95.5, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 95.5, "end": 95.559, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Rosario.", "start": 95.559, "end": 96.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 96.159, "end": 97.36, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Rosario,", "start": 97.36, "end": 97.839, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 97.839, "end": 97.839, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "domani", "start": 97.839, "end": 98.099, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 98.099, "end": 98.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mattina", "start": 98.119, "end": 98.439, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 98.439, "end": 98.439, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mio", "start": 98.439, "end": 98.599, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 98.599, "end": 98.619, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "padre", "start": 98.619, "end": 98.839, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 98.839, "end": 98.86, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mi", "start": 98.86, "end": 98.959, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 98.959, "end": 98.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "condurr<PERSON>", "start": 98.979, "end": 99.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 99.299, "end": 99.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "a", "start": 99.299, "end": 99.36, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 99.36, "end": 99.36, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Palermo,", "start": 99.36, "end": 99.739, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 99.739, "end": 99.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "al", "start": 99.739, "end": 99.819, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 99.819, "end": 99.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mio", "start": 99.819, "end": 99.919, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 99.919, "end": 99.939, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "vecchio", "start": 99.939, "end": 100.199, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 100.199, "end": 100.199, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "collegio.", "start": 100.199, "end": 100.719, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 100.719, "end": 101.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Ancora", "start": 101.119, "end": 101.639, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 101.639, "end": 101.659, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "lei.", "start": 101.659, "end": 101.979, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 101.979, "end": 102.18, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Sì,", "start": 102.18, "end": 102.379, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 102.379, "end": 102.399, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "è", "start": 102.399, "end": 102.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 102.479, "end": 102.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "stata", "start": 102.479, "end": 102.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 102.799, "end": 102.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "lei.", "start": 102.799, "end": 103.079, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 103.079, "end": 103.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Ha", "start": 103.379, "end": 103.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 103.479, "end": 103.5, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "spinto", "start": 103.5, "end": 103.819, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 103.819, "end": 103.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "papà", "start": 103.819, "end": 104.079, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 104.079, "end": 104.079, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "a", "start": 104.079, "end": 104.099, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 104.099, "end": 104.099, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "start": 104.099, "end": 104.679, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 104.679, "end": 104.72, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 104.72, "end": 104.839, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 104.839, "end": 104.839, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "me.", "start": 104.839, "end": 105.079, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 105.079, "end": 106.0, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>", "start": 106.0, "end": 106.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 106.139, "end": 106.199, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "paura,", "start": 106.199, "end": 106.619, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 106.619, "end": 106.619, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "amore,", "start": 106.619, "end": 106.899, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 106.899, "end": 106.899, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ho", "start": 106.899, "end": 107.04, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 107.04, "end": 107.04, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "paura.", "start": 107.04, "end": 107.519, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 107.519, "end": 107.759, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Quanto", "start": 107.759, "end": 108.079, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 108.079, "end": 108.079, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "ti", "start": 108.079, "end": 108.199, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 108.199, "end": 108.199, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "fermerai?", "start": 108.199, "end": 108.78, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 108.78, "end": 109.299, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Non", "start": 109.299, "end": 109.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 109.479, "end": 109.5, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "lo", "start": 109.5, "end": 109.599, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 109.599, "end": 109.659, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "so.", "start": 109.659, "end": 109.919, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 109.919, "end": 110.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Il", "start": 110.479, "end": 110.599, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 110.599, "end": 110.659, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "convitto", "start": 110.659, "end": 111.019, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 111.019, "end": 111.079, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "è", "start": 111.079, "end": 111.099, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 111.099, "end": 111.099, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "chiuso", "start": 111.099, "end": 111.399, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 111.399, "end": 111.399, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 111.399, "end": 111.5, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 111.5, "end": 111.519, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "questa", "start": 111.519, "end": 111.759, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 111.759, "end": 111.779, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "stagione,", "start": 111.779, "end": 112.28, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 112.28, "end": 112.36, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ma", "start": 112.36, "end": 112.499, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 112.499, "end": 112.519, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "suor", "start": 112.519, "end": 112.699, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 112.699, "end": 112.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>", "start": 112.739, "end": 113.059, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 113.059, "end": 113.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mi", "start": 113.059, "end": 113.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 113.159, "end": 113.18, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "terrà", "start": 113.18, "end": 113.44, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 113.44, "end": 113.459, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "per", "start": 113.459, "end": 113.599, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 113.599, "end": 113.599, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "accontentare", "start": 113.599, "end": 114.119, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 114.119, "end": 114.18, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "papà.", "start": 114.18, "end": 114.54, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 114.54, "end": 114.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 114.819, "end": 115.119, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 115.119, "end": 115.139, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sola.", "start": 115.139, "end": 115.539, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 115.539, "end": 117.779, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Io", "start": 117.779, "end": 117.879, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 117.879, "end": 117.899, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "verr<PERSON>", "start": 117.899, "end": 118.119, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 118.119, "end": 118.119, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "a", "start": 118.119, "end": 118.239, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 118.239, "end": 118.259, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Palermo", "start": 118.259, "end": 118.599, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 118.599, "end": 118.619, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>.", "start": 118.619, "end": 119.059, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 119.059, "end": 119.759, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 119.759, "end": 120.199, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 120.199, "end": 120.199, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "il", "start": 120.199, "end": 120.279, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 120.279, "end": 120.299, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "primo", "start": 120.299, "end": 120.5, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 120.5, "end": 120.5, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "carico", "start": 120.5, "end": 120.819, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 120.819, "end": 120.839, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "di", "start": 120.839, "end": 120.919, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 120.919, "end": 120.939, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "spugne", "start": 120.939, "end": 121.259, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 121.259, "end": 121.259, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "della", "start": 121.259, "end": 121.419, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 121.419, "end": 121.459, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "cooperativa", "start": 121.459, "end": 122.059, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 122.059, "end": 122.839, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "a", "start": 122.839, "end": 122.959, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 122.959, "end": 122.959, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "dei", "start": 122.959, "end": 123.08, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 123.08, "end": 123.099, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 123.099, "end": 123.68, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 123.68, "end": 123.699, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "di", "start": 123.699, "end": 123.779, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 123.779, "end": 123.819, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "città.", "start": 123.819, "end": 124.159, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 124.159, "end": 124.339, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Ti", "start": 124.339, "end": 124.459, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 124.459, "end": 124.459, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 124.459, "end": 124.839, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 124.839, "end": 124.839, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "al", "start": 124.839, "end": 124.939, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 124.939, "end": 124.939, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "porto", "start": 124.939, "end": 125.18, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 125.18, "end": 125.18, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "all'imbarco", "start": 125.18, "end": 125.599, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 125.599, "end": 125.619, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "dei", "start": 125.619, "end": 125.739, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 125.739, "end": 125.759, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "start": 125.759, "end": 126.259, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 126.259, "end": 126.299, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "fino", "start": 126.299, "end": 126.439, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 126.439, "end": 126.439, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "al", "start": 126.439, "end": 126.559, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 126.559, "end": 126.579, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "tramonto.", "start": 126.579, "end": 127.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 127.019, "end": 127.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Sì.", "start": 127.019, "end": 127.339, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 127.339, "end": 128.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "E", "start": 128.119, "end": 128.239, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 128.239, "end": 128.3, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>?", "start": 128.3, "end": 128.719, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 128.719, "end": 128.959, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Cosa", "start": 128.959, "end": 129.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 129.159, "end": 129.199, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "vuoi", "start": 129.199, "end": 129.36, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 129.36, "end": 129.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "fare", "start": 129.379, "end": 129.619, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 129.619, "end": 129.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "con", "start": 129.639, "end": 129.8, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 129.8, "end": 129.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "lei?", "start": 129.819, "end": 130.099, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 130.099, "end": 130.099, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 130.099, "end": 130.799, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 130.799, "end": 130.86, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "donna", "start": 130.86, "end": 131.139, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 131.139, "end": 131.139, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "non", "start": 131.139, "end": 131.259, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 131.259, "end": 131.279, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "ti", "start": 131.279, "end": 131.379, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 131.379, "end": 131.379, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "tormenter<PERSON>", "start": 131.379, "end": 131.919, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 131.919, "end": 131.919, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "pi<PERSON>,", "start": 131.919, "end": 132.099, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 132.099, "end": 132.119, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "cara.", "start": 132.119, "end": 132.44, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 132.44, "end": 132.66, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Quando", "start": 132.66, "end": 132.899, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 132.899, "end": 132.899, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "ritornerai", "start": 132.899, "end": 133.379, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 133.379, "end": 133.419, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "le", "start": 133.419, "end": 133.539, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 133.539, "end": 133.539, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "sarà", "start": 133.539, "end": 133.719, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 133.719, "end": 133.72, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "rimasto", "start": 133.72, "end": 134.059, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 134.059, "end": 134.059, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "ben", "start": 134.059, "end": 134.199, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 134.199, "end": 134.199, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "poco", "start": 134.199, "end": 134.419, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 134.419, "end": 134.419, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "veleno,", "start": 134.419, "end": 134.779, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 134.779, "end": 134.8, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "stai", "start": 134.8, "end": 134.979, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 134.979, "end": 134.979, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "tranquilla.", "start": 134.979, "end": 135.479, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 135.479, "end": 135.479, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 135.479, "end": 137.619, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 137.619, "end": 137.619, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "arrivata", "start": 137.619, "end": 137.94, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 137.94, "end": 137.94, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "in", "start": 137.94, "end": 138.039, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 138.039, "end": 138.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "collegio", "start": 138.059, "end": 138.44, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 138.44, "end": 138.459, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "decisi", "start": 138.459, "end": 138.819, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 138.819, "end": 138.839, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 138.839, "end": 138.94, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 138.94, "end": 138.959, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "fuggire", "start": 138.959, "end": 139.379, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 139.379, "end": 139.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "per", "start": 139.379, "end": 139.539, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 139.539, "end": 139.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "fare", "start": 139.559, "end": 139.739, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 139.739, "end": 139.779, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "delle", "start": 139.779, "end": 139.919, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 139.919, "end": 139.959, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "in<PERSON><PERSON>i", "start": 139.959, "end": 140.419, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 140.419, "end": 140.44, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sulla", "start": 140.44, "end": 140.639, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 140.639, "end": 140.66, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "vera", "start": 140.66, "end": 140.839, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 140.839, "end": 140.899, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "personalità", "start": 140.899, "end": 141.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 141.479, "end": 141.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 141.479, "end": 141.58, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 141.58, "end": 141.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>.", "start": 141.639, "end": 142.019, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 142.019, "end": 142.3, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Ave<PERSON>", "start": 142.3, "end": 142.619, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 142.619, "end": 142.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "dei", "start": 142.639, "end": 142.8, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 142.8, "end": 142.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "tristi", "start": 142.819, "end": 143.16, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 143.16, "end": 143.179, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "presentimenti", "start": 143.179, "end": 144.039, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 144.039, "end": 144.599, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "e", "start": 144.599, "end": 144.739, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 144.739, "end": 144.779, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sentivo", "start": 144.779, "end": 145.179, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 145.179, "end": 145.22, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "che", "start": 145.22, "end": 145.3, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 145.3, "end": 145.339, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "dovevo", "start": 145.339, "end": 145.619, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 145.619, "end": 145.66, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "smascherare", "start": 145.66, "end": 146.179, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 146.179, "end": 146.179, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "a", "start": 146.179, "end": 146.239, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 146.239, "end": 146.279, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "qualunque", "start": 146.279, "end": 146.679, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 146.679, "end": 146.679, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "costo", "start": 146.679, "end": 146.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 146.979, "end": 147.0, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "quella", "start": 147.0, "end": 147.18, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 147.18, "end": 147.239, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "donna", "start": 147.239, "end": 147.519, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 147.519, "end": 147.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "prima", "start": 147.559, "end": 147.779, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 147.779, "end": 147.779, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "che", "start": 147.779, "end": 147.919, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 147.919, "end": 147.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "qualcosa", "start": 147.919, "end": 148.36, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 148.36, "end": 148.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 148.379, "end": 148.459, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 148.459, "end": 148.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "grave", "start": 148.479, "end": 148.759, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 148.759, "end": 148.759, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "accadesse.", "start": 148.759, "end": 149.419, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 149.419, "end": 149.94, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Ma", "start": 149.94, "end": 150.08, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 150.08, "end": 150.16, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "pu<PERSON><PERSON><PERSON>", "start": 150.16, "end": 150.66, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 150.66, "end": 150.679, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "non", "start": 150.679, "end": 150.819, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 150.819, "end": 150.86, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "feci", "start": 150.86, "end": 151.16, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 151.16, "end": 151.16, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "in", "start": 151.16, "end": 151.279, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 151.279, "end": 151.3, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "tempo.", "start": 151.3, "end": 151.779, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 151.779, "end": 153.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 153.639, "end": 154.539, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 154.539, "end": 154.539, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "presto,", "start": 154.539, "end": 154.879, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 154.879, "end": 154.879, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "cara.", "start": 154.879, "end": 155.219, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 155.219, "end": 155.279, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Come", "start": 155.279, "end": 155.5, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 155.5, "end": 155.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "crede,", "start": 155.539, "end": 155.839, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 155.839, "end": 155.86, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "signor", "start": 155.86, "end": 156.099, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 156.099, "end": 156.099, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Ingarsia,", "start": 156.099, "end": 156.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 156.659, "end": 156.679, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "anche", "start": 156.679, "end": 156.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 156.919, "end": 156.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "se", "start": 156.959, "end": 157.059, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 157.059, "end": 157.08, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "non", "start": 157.08, "end": 157.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 157.199, "end": 157.22, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "abbiamo", "start": 157.22, "end": 157.44, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 157.44, "end": 157.44, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "allieve", "start": 157.44, "end": 157.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 157.779, "end": 157.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "in", "start": 157.779, "end": 157.879, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 157.879, "end": 157.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "questa", "start": 157.879, "end": 158.079, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 158.079, "end": 158.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "stagione,", "start": 158.139, "end": 158.5, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 158.5, "end": 158.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>", "start": 158.559, "end": 158.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 158.819, "end": 158.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "si", "start": 158.819, "end": 158.94, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 158.94, "end": 158.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "troverà", "start": 158.959, "end": 159.3, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 159.3, "end": 159.3, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "benissimo.", "start": 159.3, "end": 159.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 159.819, "end": 159.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 159.819, "end": 160.539, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 160.539, "end": 160.539, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON>", "start": 160.539, "end": 160.899, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 160.899, "end": 160.899, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "ti", "start": 160.899, "end": 161.0, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 161.0, "end": 161.0, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "accompag<PERSON><PERSON>", "start": 161.0, "end": 161.5, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 161.5, "end": 161.559, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "a", "start": 161.559, "end": 161.58, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 161.58, "end": 161.599, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "visitare", "start": 161.599, "end": 161.979, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 161.979, "end": 161.979, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "la", "start": 161.979, "end": 162.099, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 162.099, "end": 162.099, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "città,", "start": 162.099, "end": 162.379, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 162.379, "end": 162.379, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "avrai", "start": 162.379, "end": 162.599, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 162.599, "end": 162.599, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "modo", "start": 162.599, "end": 162.879, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 162.879, "end": 162.879, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "di", "start": 162.879, "end": 162.959, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 162.959, "end": 162.959, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "distrarti.", "start": 162.959, "end": 163.639, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 163.639, "end": 163.699, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Certamente.", "start": 163.699, "end": 164.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 164.919, "end": 165.099, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 165.099, "end": 165.44, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 165.44, "end": 165.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 165.459, "end": 165.6, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 165.6, "end": 165.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tutta", "start": 165.619, "end": 165.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 165.799, "end": 165.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "la", "start": 165.819, "end": 165.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 165.899, "end": 165.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "libertà", "start": 165.919, "end": 166.339, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 166.339, "end": 166.339, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "possibile.", "start": 166.339, "end": 166.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 166.959, "end": 167.039, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Non", "start": 167.039, "end": 167.179, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 167.179, "end": 167.22, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ci", "start": 167.22, "end": 167.279, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 167.279, "end": 167.3, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "eravamo", "start": 167.3, "end": 167.72, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 167.72, "end": 167.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "mai", "start": 167.739, "end": 167.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 167.919, "end": 167.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "dimenticate", "start": 167.919, "end": 168.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 168.619, "end": 168.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "della", "start": 168.619, "end": 168.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 168.819, "end": 168.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "piccola", "start": 168.879, "end": 169.24, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 169.24, "end": 169.279, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>.", "start": 169.279, "end": 169.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 169.779, "end": 170.199, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Uh,", "start": 170.199, "end": 171.259, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 171.259, "end": 171.339, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "per", "start": 171.339, "end": 171.5, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 171.5, "end": 171.519, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "la", "start": 171.519, "end": 171.619, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 171.619, "end": 171.619, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "rette", "start": 171.619, "end": 171.88, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 171.88, "end": 171.899, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "della", "start": 171.899, "end": 172.059, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 172.059, "end": 172.119, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "figliola", "start": 172.119, "end": 172.44, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 172.44, "end": 172.44, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "ci", "start": 172.44, "end": 172.539, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 172.539, "end": 172.559, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "metteremo", "start": 172.559, "end": 172.919, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 172.919, "end": 172.919, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "d'accordo.", "start": 172.919, "end": 173.419, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 173.419, "end": 173.419, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Come", "start": 173.419, "end": 174.22, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 174.22, "end": 174.279, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "vuole,", "start": 174.279, "end": 174.52, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 174.52, "end": 174.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "don", "start": 174.539, "end": 174.679, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 174.679, "end": 174.72, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Ingarsia,", "start": 174.72, "end": 175.18, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 175.18, "end": 175.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "non", "start": 175.239, "end": 175.339, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 175.339, "end": 175.36, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "abbia", "start": 175.36, "end": 175.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 175.559, "end": 175.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "preoccupazioni", "start": 175.559, "end": 176.259, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 176.259, "end": 176.3, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "per", "start": 176.3, "end": 176.419, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 176.419, "end": 176.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "questo.", "start": 176.479, "end": 176.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 176.819, "end": 176.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON>,", "start": 176.819, "end": 177.419, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 177.419, "end": 177.44, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON>.", "start": 177.44, "end": 177.919, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 177.919, "end": 179.559, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON>.", "start": 179.559, "end": 180.079, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 180.079, "end": 180.08, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON>ngior<PERSON>.", "start": 180.08, "end": 185.499, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 185.499, "end": 185.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 185.699, "end": 186.02, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 186.02, "end": 186.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>,", "start": 186.059, "end": 186.52, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 186.52, "end": 186.539, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ho", "start": 186.539, "end": 186.619, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 186.619, "end": 186.659, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "dimenticato", "start": 186.659, "end": 187.219, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 187.219, "end": 187.259, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 187.259, "end": 187.339, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 187.339, "end": 187.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "dire", "start": 187.379, "end": 187.539, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 187.539, "end": 187.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "una", "start": 187.559, "end": 187.679, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 187.679, "end": 187.699, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "cosa", "start": 187.699, "end": 187.899, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 187.899, "end": 187.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "importante", "start": 187.919, "end": 188.44, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 188.44, "end": 188.44, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "a", "start": 188.44, "end": 188.539, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 188.539, "end": 188.539, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "papà.", "start": 188.539, "end": 188.879, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 188.879, "end": 189.199, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>", "start": 189.199, "end": 189.459, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 189.459, "end": 189.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "scusi", "start": 189.479, "end": 189.719, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 189.719, "end": 189.72, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "un", "start": 189.72, "end": 189.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 189.799, "end": 189.839, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "attimo.", "start": 189.839, "end": 190.22, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 190.22, "end": 190.22, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}], "language_detected": null, "confidence": null, "processing_time": 20.61598229408264, "metadata": {"service": "elevenlabs", "mode": "free", "model": "scribe_v1", "raw_result": {"language_code": "ita", "language_probability": 0.9909329414367676, "text": "Le trovate? <PERSON><PERSON>. <PERSON><PERSON>, che cosa guardi? Non hai mai visto una donna fare il bagno? Mmm? C'è qualcosa che non ti va? Nulla. No, no, nulla. Per quale motivo non avvertisti tuo padre, eh? Anche come padre Ingarsia è un tipo difficile. Del resto non disse niente nemmeno a me. Non ebbe il coraggio. Mio padre viveva solamente per lei. Però mi ripromisi di fare delle ricerche. Dove? Qui nell'isola? Oh no, a Palermo, dove mio padre mi disse che l'aveva conosciuta. Ingarsia andava dicendo che Carla faceva la maestra a Palermo. Belle cose insegnava. E per quel che mi risulta aveva molti allievi a pagamento. Purtroppo l'occasione di fare queste ricerche mi si presentò molto presto. Una sera, durante una delle solite e burrascose discussioni, mio padre mi annunciò che mi avrebbe riportata in collegio. Fu Carla a suggerirgli questa idea per separarmi da Rosario e per allontanare mio padre. Disperatamente decisa a non partire- Ciao amore. Rosario. Rosario, domani mattina mio padre mi condurrà a Palermo, al mio vecchio collegio. Ancora lei. Sì, è stata lei. Ha spinto papà a sbarattarsi di me. Ho paura, amore, ho paura. Quanto ti fermerai? Non lo so. Il convitto è chiuso di questa stagione, ma suor Teresa mi terrà per accontentare papà. Sarò sola. Io verrò a Palermo giovedì. Vendiamo il primo carico di spugne della cooperativa a dei commercianti di città. Ti aspetterò al porto all'imbarco dei pescherecci fino al tramonto. Sì. E Carla? Cosa vuoi fare con lei? Quella donna non ti tormenterà più, cara. Quando ritornerai le sarà rimasto ben poco veleno, stai tranquilla. Appena arrivata in collegio decisi di fuggire per fare delle indagini sulla vera personalità di Carla. Avevo dei tristi presentimenti e sentivo che dovevo smascherare a qualunque costo quella donna prima che qualcosa di grave accadesse. Ma purtroppo non feci in tempo. Tornerò presto, cara. Come crede, signor Ingarsia, anche se non abbiamo allieve in questa stagione, Maria si troverà benissimo. Suor Teresa ti accompagnerà a visitare la città, avrai modo di distrarti. Certamente. Godrà di tutta la libertà possibile. Non ci eravamo mai dimenticate della piccola Maria. Uh, per la rette della figliola ci metteremo d'accordo. Come vuole, don Ingarsia, non abbia preoccupazioni per questo. Arrivederci, Maria. Sorella. Buongiorno. Suor Teresa, ho dimenticato di dire una cosa importante a papà. Mi scusi un attimo. ", "words": [{"text": "Le", "start": 13.359, "end": 13.539, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 13.539, "end": 13.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "trovate?", "start": 13.539, "end": 14.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 14.199, "end": 14.96, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Sì.", "start": 14.96, "end": 15.379, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 15.379, "end": 26.439, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON>,", "start": 26.439, "end": 26.639, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 26.639, "end": 26.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 26.659, "end": 26.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 26.799, "end": 26.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "cosa", "start": 26.819, "end": 27.0, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 27.0, "end": 27.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "guardi?", "start": 27.059, "end": 27.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 27.559, "end": 27.76, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Non", "start": 27.76, "end": 27.88, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 27.88, "end": 27.899, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "hai", "start": 27.899, "end": 28.019, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 28.019, "end": 28.039, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "mai", "start": 28.039, "end": 28.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 28.159, "end": 28.18, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "visto", "start": 28.18, "end": 28.36, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 28.36, "end": 28.379, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "una", "start": 28.379, "end": 28.499, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 28.499, "end": 28.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "donna", "start": 28.539, "end": 28.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 28.779, "end": 28.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "fare", "start": 28.819, "end": 28.999, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 28.999, "end": 29.0, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "il", "start": 29.0, "end": 29.059, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 29.059, "end": 29.099, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "bagno?", "start": 29.099, "end": 29.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 29.559, "end": 30.399, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Mmm?", "start": 30.399, "end": 30.739, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 30.739, "end": 35.939, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "C'è", "start": 35.939, "end": 36.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 36.139, "end": 36.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "qualcosa", "start": 36.159, "end": 36.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 36.659, "end": 36.68, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 36.68, "end": 36.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 36.819, "end": 36.84, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "non", "start": 36.84, "end": 36.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 36.959, "end": 37.02, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ti", "start": 37.02, "end": 37.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 37.139, "end": 37.2, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "va?", "start": 37.2, "end": 37.48, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 37.48, "end": 38.0, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>.", "start": 38.0, "end": 38.539, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 38.539, "end": 38.86, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "No,", "start": 38.86, "end": 39.04, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 39.04, "end": 39.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "no,", "start": 39.059, "end": 39.259, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 39.259, "end": 39.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "nulla.", "start": 39.299, "end": 39.78, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 39.78, "end": 43.879, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Per", "start": 43.879, "end": 44.02, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 44.02, "end": 44.02, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "quale", "start": 44.02, "end": 44.219, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 44.219, "end": 44.219, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "motivo", "start": 44.219, "end": 44.579, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 44.579, "end": 44.579, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "non", "start": 44.579, "end": 44.739, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 44.739, "end": 44.739, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "avvertisti", "start": 44.739, "end": 45.24, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 45.24, "end": 45.319, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "tuo", "start": 45.319, "end": 45.519, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 45.519, "end": 45.539, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "padre,", "start": 45.539, "end": 45.96, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 45.96, "end": 46.159, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "eh?", "start": 46.159, "end": 46.34, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 46.34, "end": 46.879, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 46.879, "end": 47.159, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 47.159, "end": 47.18, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "come", "start": 47.18, "end": 47.38, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 47.38, "end": 47.419, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "padre", "start": 47.419, "end": 47.7, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 47.7, "end": 47.719, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Ingarsia", "start": 47.719, "end": 48.139, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 48.139, "end": 48.18, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "è", "start": 48.18, "end": 48.2, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 48.2, "end": 48.2, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "un", "start": 48.2, "end": 48.259, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 48.259, "end": 48.259, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "tipo", "start": 48.259, "end": 48.479, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 48.479, "end": 48.479, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "difficile.", "start": 48.479, "end": 49.079, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 49.079, "end": 50.039, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Del", "start": 50.039, "end": 50.219, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 50.219, "end": 50.219, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "resto", "start": 50.219, "end": 50.459, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 50.459, "end": 50.459, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "non", "start": 50.459, "end": 50.559, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 50.559, "end": 50.559, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "disse", "start": 50.559, "end": 50.759, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 50.759, "end": 50.779, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "niente", "start": 50.779, "end": 50.979, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 50.979, "end": 51.02, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 51.02, "end": 51.279, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 51.279, "end": 51.299, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "a", "start": 51.299, "end": 51.34, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 51.34, "end": 51.36, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "me.", "start": 51.36, "end": 51.539, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 51.539, "end": 51.759, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Non", "start": 51.759, "end": 51.94, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 51.94, "end": 51.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ebbe", "start": 51.979, "end": 52.219, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 52.219, "end": 52.219, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "il", "start": 52.219, "end": 52.279, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 52.279, "end": 52.319, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "coraggio.", "start": 52.319, "end": 52.819, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 52.819, "end": 53.319, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 53.319, "end": 53.459, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 53.459, "end": 53.539, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "padre", "start": 53.539, "end": 53.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 53.799, "end": 53.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "viveva", "start": 53.799, "end": 54.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 54.139, "end": 54.18, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "solamente", "start": 54.18, "end": 54.659, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 54.659, "end": 54.699, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "per", "start": 54.699, "end": 54.86, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 54.86, "end": 54.899, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "lei.", "start": 54.899, "end": 55.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 55.159, "end": 55.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 55.379, "end": 55.599, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 55.599, "end": 55.659, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mi", "start": 55.659, "end": 55.72, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 55.72, "end": 55.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 55.739, "end": 56.24, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 56.24, "end": 56.259, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 56.259, "end": 56.399, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 56.399, "end": 56.399, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "fare", "start": 56.399, "end": 56.599, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 56.599, "end": 56.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "delle", "start": 56.639, "end": 56.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 56.799, "end": 56.84, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ricerche.", "start": 56.84, "end": 57.419, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 57.419, "end": 57.439, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Dove?", "start": 57.439, "end": 57.779, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 57.779, "end": 58.319, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON>ui", "start": 58.319, "end": 58.479, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 58.479, "end": 58.539, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "nell'isola?", "start": 58.539, "end": 59.079, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 59.079, "end": 59.599, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Oh", "start": 59.599, "end": 59.76, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 59.76, "end": 59.84, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "no,", "start": 59.84, "end": 60.04, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 60.04, "end": 60.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "a", "start": 60.059, "end": 60.119, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 60.119, "end": 60.199, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Palermo,", "start": 60.199, "end": 60.639, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 60.639, "end": 60.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "dove", "start": 60.639, "end": 60.819, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 60.819, "end": 60.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mio", "start": 60.819, "end": 60.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 60.979, "end": 61.02, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "padre", "start": 61.02, "end": 61.259, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 61.259, "end": 61.259, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mi", "start": 61.259, "end": 61.34, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 61.34, "end": 61.399, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "disse", "start": 61.399, "end": 61.58, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 61.58, "end": 61.619, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "che", "start": 61.619, "end": 61.699, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 61.699, "end": 61.699, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "l'aveva", "start": 61.699, "end": 62.02, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 62.02, "end": 62.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "conosciuta.", "start": 62.059, "end": 62.379, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 62.379, "end": 62.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Ingarsia", "start": 62.379, "end": 62.919, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 62.919, "end": 62.919, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "andava", "start": 62.919, "end": 63.2, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 63.2, "end": 63.219, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "dicendo", "start": 63.219, "end": 63.639, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 63.639, "end": 63.639, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "che", "start": 63.639, "end": 63.759, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 63.759, "end": 63.779, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON>", "start": 63.779, "end": 64.08, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 64.08, "end": 64.099, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "faceva", "start": 64.099, "end": 64.439, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 64.439, "end": 64.439, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "la", "start": 64.439, "end": 64.519, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 64.519, "end": 64.559, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "maestra", "start": 64.559, "end": 64.979, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 64.979, "end": 64.979, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "a", "start": 64.979, "end": 65.039, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 65.039, "end": 65.08, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Palermo.", "start": 65.08, "end": 65.64, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 65.64, "end": 66.739, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON>", "start": 66.739, "end": 67.0, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 67.0, "end": 67.059, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "cose", "start": 67.059, "end": 67.4, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 67.4, "end": 67.479, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "insegnava.", "start": 67.479, "end": 68.259, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 68.259, "end": 68.639, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "E", "start": 68.639, "end": 68.86, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 68.86, "end": 68.939, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "per", "start": 68.939, "end": 69.12, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 69.12, "end": 69.139, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "quel", "start": 69.139, "end": 69.279, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 69.279, "end": 69.279, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "che", "start": 69.279, "end": 69.379, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 69.379, "end": 69.379, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "mi", "start": 69.379, "end": 69.5, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 69.5, "end": 69.519, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "risulta", "start": 69.519, "end": 69.879, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 69.879, "end": 69.919, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "aveva", "start": 69.919, "end": 70.139, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 70.139, "end": 70.18, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "molti", "start": 70.18, "end": 70.48, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 70.48, "end": 70.5, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "allievi", "start": 70.5, "end": 71.04, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 71.04, "end": 71.18, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "a", "start": 71.18, "end": 71.279, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 71.279, "end": 71.299, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "pagamento.", "start": 71.299, "end": 72.0, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 72.0, "end": 72.839, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 72.839, "end": 73.419, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 73.419, "end": 73.439, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "l'occasione", "start": 73.439, "end": 73.94, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 73.94, "end": 73.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 73.979, "end": 74.039, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 74.039, "end": 74.08, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "fare", "start": 74.08, "end": 74.279, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 74.279, "end": 74.319, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "queste", "start": 74.319, "end": 74.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 74.559, "end": 74.58, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ricerche", "start": 74.58, "end": 75.059, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 75.059, "end": 75.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mi", "start": 75.059, "end": 75.18, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 75.18, "end": 75.22, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "si", "start": 75.22, "end": 75.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 75.299, "end": 75.339, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "presentò", "start": 75.339, "end": 75.739, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 75.739, "end": 75.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "molto", "start": 75.799, "end": 76.099, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 76.099, "end": 76.099, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "presto.", "start": 76.099, "end": 76.599, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 76.599, "end": 77.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Una", "start": 77.739, "end": 77.919, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 77.919, "end": 78.0, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sera,", "start": 78.0, "end": 78.319, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 78.319, "end": 78.319, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "durante", "start": 78.319, "end": 78.72, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 78.72, "end": 78.759, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "una", "start": 78.759, "end": 78.919, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 78.919, "end": 78.939, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "delle", "start": 78.939, "end": 79.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 79.159, "end": 79.159, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "solite", "start": 79.159, "end": 79.62, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 79.62, "end": 79.72, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "e", "start": 79.72, "end": 79.739, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 79.739, "end": 79.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "burrascose", "start": 79.739, "end": 80.319, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 80.319, "end": 80.339, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "discussioni,", "start": 80.339, "end": 81.079, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 81.079, "end": 81.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mio", "start": 81.379, "end": 81.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 81.559, "end": 81.619, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "padre", "start": 81.619, "end": 81.9, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 81.9, "end": 81.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mi", "start": 81.919, "end": 82.019, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 82.019, "end": 82.019, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "annunci<PERSON>", "start": 82.019, "end": 82.379, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 82.379, "end": 82.4, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "che", "start": 82.4, "end": 82.499, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 82.499, "end": 82.54, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mi", "start": 82.54, "end": 82.62, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 82.62, "end": 82.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 82.639, "end": 82.919, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 82.919, "end": 82.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "riportata", "start": 82.919, "end": 83.459, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 83.459, "end": 83.459, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "in", "start": 83.459, "end": 83.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 83.559, "end": 83.599, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "collegio.", "start": 83.599, "end": 84.079, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 84.079, "end": 84.619, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>", "start": 84.619, "end": 84.779, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 84.779, "end": 84.839, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>", "start": 84.839, "end": 85.18, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 85.18, "end": 85.18, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "a", "start": 85.18, "end": 85.279, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 85.279, "end": 85.279, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sugger<PERSON><PERSON>", "start": 85.279, "end": 85.819, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 85.819, "end": 85.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "questa", "start": 85.819, "end": 86.08, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 86.08, "end": 86.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "idea", "start": 86.119, "end": 86.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 86.479, "end": 86.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "per", "start": 86.919, "end": 87.08, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 87.08, "end": 87.139, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "separarmi", "start": 87.139, "end": 87.639, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 87.639, "end": 87.68, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "da", "start": 87.68, "end": 87.759, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 87.759, "end": 87.779, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Rosario", "start": 87.779, "end": 88.199, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 88.199, "end": 88.199, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "e", "start": 88.199, "end": 88.319, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 88.319, "end": 88.339, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "per", "start": 88.339, "end": 88.44, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 88.44, "end": 88.459, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "allontanare", "start": 88.459, "end": 88.94, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 88.94, "end": 88.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mio", "start": 88.979, "end": 89.119, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 89.119, "end": 89.199, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "padre.", "start": 89.199, "end": 89.54, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 89.54, "end": 90.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Disperatamente", "start": 90.059, "end": 90.859, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 90.859, "end": 90.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "decisa", "start": 90.919, "end": 91.259, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 91.259, "end": 91.259, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "a", "start": 91.259, "end": 91.339, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 91.339, "end": 91.339, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "non", "start": 91.339, "end": 91.459, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 91.459, "end": 91.519, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "partire-", "start": 91.519, "end": 92.019, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 92.019, "end": 94.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Ciao", "start": 94.919, "end": 95.22, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 95.22, "end": 95.22, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "amore.", "start": 95.22, "end": 95.5, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 95.5, "end": 95.559, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Rosario.", "start": 95.559, "end": 96.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 96.159, "end": 97.36, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Rosario,", "start": 97.36, "end": 97.839, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 97.839, "end": 97.839, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "domani", "start": 97.839, "end": 98.099, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 98.099, "end": 98.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mattina", "start": 98.119, "end": 98.439, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 98.439, "end": 98.439, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mio", "start": 98.439, "end": 98.599, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 98.599, "end": 98.619, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "padre", "start": 98.619, "end": 98.839, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 98.839, "end": 98.86, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mi", "start": 98.86, "end": 98.959, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 98.959, "end": 98.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "condurr<PERSON>", "start": 98.979, "end": 99.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 99.299, "end": 99.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "a", "start": 99.299, "end": 99.36, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 99.36, "end": 99.36, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Palermo,", "start": 99.36, "end": 99.739, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 99.739, "end": 99.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "al", "start": 99.739, "end": 99.819, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 99.819, "end": 99.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mio", "start": 99.819, "end": 99.919, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 99.919, "end": 99.939, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "vecchio", "start": 99.939, "end": 100.199, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 100.199, "end": 100.199, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "collegio.", "start": 100.199, "end": 100.719, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 100.719, "end": 101.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Ancora", "start": 101.119, "end": 101.639, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 101.639, "end": 101.659, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "lei.", "start": 101.659, "end": 101.979, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 101.979, "end": 102.18, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Sì,", "start": 102.18, "end": 102.379, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 102.379, "end": 102.399, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "è", "start": 102.399, "end": 102.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 102.479, "end": 102.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "stata", "start": 102.479, "end": 102.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 102.799, "end": 102.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "lei.", "start": 102.799, "end": 103.079, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 103.079, "end": 103.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Ha", "start": 103.379, "end": 103.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 103.479, "end": 103.5, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "spinto", "start": 103.5, "end": 103.819, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 103.819, "end": 103.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "papà", "start": 103.819, "end": 104.079, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 104.079, "end": 104.079, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "a", "start": 104.079, "end": 104.099, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 104.099, "end": 104.099, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "start": 104.099, "end": 104.679, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 104.679, "end": 104.72, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 104.72, "end": 104.839, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 104.839, "end": 104.839, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "me.", "start": 104.839, "end": 105.079, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 105.079, "end": 106.0, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>", "start": 106.0, "end": 106.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 106.139, "end": 106.199, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "paura,", "start": 106.199, "end": 106.619, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 106.619, "end": 106.619, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "amore,", "start": 106.619, "end": 106.899, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 106.899, "end": 106.899, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ho", "start": 106.899, "end": 107.04, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 107.04, "end": 107.04, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "paura.", "start": 107.04, "end": 107.519, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 107.519, "end": 107.759, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Quanto", "start": 107.759, "end": 108.079, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 108.079, "end": 108.079, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "ti", "start": 108.079, "end": 108.199, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 108.199, "end": 108.199, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "fermerai?", "start": 108.199, "end": 108.78, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 108.78, "end": 109.299, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Non", "start": 109.299, "end": 109.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 109.479, "end": 109.5, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "lo", "start": 109.5, "end": 109.599, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 109.599, "end": 109.659, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "so.", "start": 109.659, "end": 109.919, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 109.919, "end": 110.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Il", "start": 110.479, "end": 110.599, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 110.599, "end": 110.659, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "convitto", "start": 110.659, "end": 111.019, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 111.019, "end": 111.079, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "è", "start": 111.079, "end": 111.099, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 111.099, "end": 111.099, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "chiuso", "start": 111.099, "end": 111.399, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 111.399, "end": 111.399, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 111.399, "end": 111.5, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 111.5, "end": 111.519, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "questa", "start": 111.519, "end": 111.759, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 111.759, "end": 111.779, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "stagione,", "start": 111.779, "end": 112.28, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 112.28, "end": 112.36, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ma", "start": 112.36, "end": 112.499, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 112.499, "end": 112.519, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "suor", "start": 112.519, "end": 112.699, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 112.699, "end": 112.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>", "start": 112.739, "end": 113.059, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 113.059, "end": 113.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mi", "start": 113.059, "end": 113.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 113.159, "end": 113.18, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "terrà", "start": 113.18, "end": 113.44, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 113.44, "end": 113.459, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "per", "start": 113.459, "end": 113.599, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 113.599, "end": 113.599, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "accontentare", "start": 113.599, "end": 114.119, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 114.119, "end": 114.18, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "papà.", "start": 114.18, "end": 114.54, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 114.54, "end": 114.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 114.819, "end": 115.119, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 115.119, "end": 115.139, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sola.", "start": 115.139, "end": 115.539, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 115.539, "end": 117.779, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Io", "start": 117.779, "end": 117.879, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 117.879, "end": 117.899, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "verr<PERSON>", "start": 117.899, "end": 118.119, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 118.119, "end": 118.119, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "a", "start": 118.119, "end": 118.239, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 118.239, "end": 118.259, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Palermo", "start": 118.259, "end": 118.599, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 118.599, "end": 118.619, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>.", "start": 118.619, "end": 119.059, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 119.059, "end": 119.759, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 119.759, "end": 120.199, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 120.199, "end": 120.199, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "il", "start": 120.199, "end": 120.279, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 120.279, "end": 120.299, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "primo", "start": 120.299, "end": 120.5, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 120.5, "end": 120.5, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "carico", "start": 120.5, "end": 120.819, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 120.819, "end": 120.839, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "di", "start": 120.839, "end": 120.919, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 120.919, "end": 120.939, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "spugne", "start": 120.939, "end": 121.259, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 121.259, "end": 121.259, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "della", "start": 121.259, "end": 121.419, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 121.419, "end": 121.459, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "cooperativa", "start": 121.459, "end": 122.059, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 122.059, "end": 122.839, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "a", "start": 122.839, "end": 122.959, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 122.959, "end": 122.959, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "dei", "start": 122.959, "end": 123.08, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 123.08, "end": 123.099, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 123.099, "end": 123.68, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 123.68, "end": 123.699, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "di", "start": 123.699, "end": 123.779, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 123.779, "end": 123.819, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "città.", "start": 123.819, "end": 124.159, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 124.159, "end": 124.339, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Ti", "start": 124.339, "end": 124.459, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 124.459, "end": 124.459, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 124.459, "end": 124.839, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 124.839, "end": 124.839, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "al", "start": 124.839, "end": 124.939, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 124.939, "end": 124.939, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "porto", "start": 124.939, "end": 125.18, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 125.18, "end": 125.18, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "all'imbarco", "start": 125.18, "end": 125.599, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 125.599, "end": 125.619, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "dei", "start": 125.619, "end": 125.739, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 125.739, "end": 125.759, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "start": 125.759, "end": 126.259, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 126.259, "end": 126.299, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "fino", "start": 126.299, "end": 126.439, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 126.439, "end": 126.439, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "al", "start": 126.439, "end": 126.559, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 126.559, "end": 126.579, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "tramonto.", "start": 126.579, "end": 127.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 127.019, "end": 127.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Sì.", "start": 127.019, "end": 127.339, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 127.339, "end": 128.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "E", "start": 128.119, "end": 128.239, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 128.239, "end": 128.3, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>?", "start": 128.3, "end": 128.719, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 128.719, "end": 128.959, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Cosa", "start": 128.959, "end": 129.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 129.159, "end": 129.199, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "vuoi", "start": 129.199, "end": 129.36, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 129.36, "end": 129.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "fare", "start": 129.379, "end": 129.619, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 129.619, "end": 129.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "con", "start": 129.639, "end": 129.8, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 129.8, "end": 129.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "lei?", "start": 129.819, "end": 130.099, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 130.099, "end": 130.099, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 130.099, "end": 130.799, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 130.799, "end": 130.86, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "donna", "start": 130.86, "end": 131.139, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 131.139, "end": 131.139, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "non", "start": 131.139, "end": 131.259, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 131.259, "end": 131.279, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "ti", "start": 131.279, "end": 131.379, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 131.379, "end": 131.379, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "tormenter<PERSON>", "start": 131.379, "end": 131.919, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 131.919, "end": 131.919, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "pi<PERSON>,", "start": 131.919, "end": 132.099, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 132.099, "end": 132.119, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "cara.", "start": 132.119, "end": 132.44, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 132.44, "end": 132.66, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Quando", "start": 132.66, "end": 132.899, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 132.899, "end": 132.899, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "ritornerai", "start": 132.899, "end": 133.379, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 133.379, "end": 133.419, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "le", "start": 133.419, "end": 133.539, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 133.539, "end": 133.539, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "sarà", "start": 133.539, "end": 133.719, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 133.719, "end": 133.72, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "rimasto", "start": 133.72, "end": 134.059, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 134.059, "end": 134.059, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "ben", "start": 134.059, "end": 134.199, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 134.199, "end": 134.199, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "poco", "start": 134.199, "end": 134.419, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 134.419, "end": 134.419, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "veleno,", "start": 134.419, "end": 134.779, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 134.779, "end": 134.8, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "stai", "start": 134.8, "end": 134.979, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 134.979, "end": 134.979, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "tranquilla.", "start": 134.979, "end": 135.479, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 135.479, "end": 135.479, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 135.479, "end": 137.619, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 137.619, "end": 137.619, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "arrivata", "start": 137.619, "end": 137.94, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 137.94, "end": 137.94, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "in", "start": 137.94, "end": 138.039, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 138.039, "end": 138.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "collegio", "start": 138.059, "end": 138.44, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 138.44, "end": 138.459, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "decisi", "start": 138.459, "end": 138.819, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 138.819, "end": 138.839, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 138.839, "end": 138.94, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 138.94, "end": 138.959, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "fuggire", "start": 138.959, "end": 139.379, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 139.379, "end": 139.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "per", "start": 139.379, "end": 139.539, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 139.539, "end": 139.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "fare", "start": 139.559, "end": 139.739, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 139.739, "end": 139.779, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "delle", "start": 139.779, "end": 139.919, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 139.919, "end": 139.959, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "in<PERSON><PERSON>i", "start": 139.959, "end": 140.419, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 140.419, "end": 140.44, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sulla", "start": 140.44, "end": 140.639, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 140.639, "end": 140.66, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "vera", "start": 140.66, "end": 140.839, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 140.839, "end": 140.899, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "personalità", "start": 140.899, "end": 141.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 141.479, "end": 141.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 141.479, "end": 141.58, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 141.58, "end": 141.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>.", "start": 141.639, "end": 142.019, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 142.019, "end": 142.3, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Ave<PERSON>", "start": 142.3, "end": 142.619, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 142.619, "end": 142.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "dei", "start": 142.639, "end": 142.8, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 142.8, "end": 142.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "tristi", "start": 142.819, "end": 143.16, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 143.16, "end": 143.179, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "presentimenti", "start": 143.179, "end": 144.039, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 144.039, "end": 144.599, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "e", "start": 144.599, "end": 144.739, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 144.739, "end": 144.779, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sentivo", "start": 144.779, "end": 145.179, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 145.179, "end": 145.22, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "che", "start": 145.22, "end": 145.3, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 145.3, "end": 145.339, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "dovevo", "start": 145.339, "end": 145.619, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 145.619, "end": 145.66, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "smascherare", "start": 145.66, "end": 146.179, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 146.179, "end": 146.179, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "a", "start": 146.179, "end": 146.239, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 146.239, "end": 146.279, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "qualunque", "start": 146.279, "end": 146.679, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 146.679, "end": 146.679, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "costo", "start": 146.679, "end": 146.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 146.979, "end": 147.0, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "quella", "start": 147.0, "end": 147.18, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 147.18, "end": 147.239, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "donna", "start": 147.239, "end": 147.519, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 147.519, "end": 147.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "prima", "start": 147.559, "end": 147.779, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 147.779, "end": 147.779, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "che", "start": 147.779, "end": 147.919, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 147.919, "end": 147.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "qualcosa", "start": 147.919, "end": 148.36, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 148.36, "end": 148.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 148.379, "end": 148.459, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 148.459, "end": 148.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "grave", "start": 148.479, "end": 148.759, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 148.759, "end": 148.759, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "accadesse.", "start": 148.759, "end": 149.419, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 149.419, "end": 149.94, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Ma", "start": 149.94, "end": 150.08, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 150.08, "end": 150.16, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "pu<PERSON><PERSON><PERSON>", "start": 150.16, "end": 150.66, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 150.66, "end": 150.679, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "non", "start": 150.679, "end": 150.819, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 150.819, "end": 150.86, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "feci", "start": 150.86, "end": 151.16, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 151.16, "end": 151.16, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "in", "start": 151.16, "end": 151.279, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 151.279, "end": 151.3, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "tempo.", "start": 151.3, "end": 151.779, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 151.779, "end": 153.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 153.639, "end": 154.539, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 154.539, "end": 154.539, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "presto,", "start": 154.539, "end": 154.879, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 154.879, "end": 154.879, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "cara.", "start": 154.879, "end": 155.219, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 155.219, "end": 155.279, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Come", "start": 155.279, "end": 155.5, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 155.5, "end": 155.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "crede,", "start": 155.539, "end": 155.839, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 155.839, "end": 155.86, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "signor", "start": 155.86, "end": 156.099, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 156.099, "end": 156.099, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Ingarsia,", "start": 156.099, "end": 156.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 156.659, "end": 156.679, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "anche", "start": 156.679, "end": 156.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 156.919, "end": 156.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "se", "start": 156.959, "end": 157.059, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 157.059, "end": 157.08, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "non", "start": 157.08, "end": 157.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 157.199, "end": 157.22, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "abbiamo", "start": 157.22, "end": 157.44, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 157.44, "end": 157.44, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "allieve", "start": 157.44, "end": 157.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 157.779, "end": 157.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "in", "start": 157.779, "end": 157.879, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 157.879, "end": 157.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "questa", "start": 157.879, "end": 158.079, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 158.079, "end": 158.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "stagione,", "start": 158.139, "end": 158.5, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 158.5, "end": 158.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>", "start": 158.559, "end": 158.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 158.819, "end": 158.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "si", "start": 158.819, "end": 158.94, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 158.94, "end": 158.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "troverà", "start": 158.959, "end": 159.3, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 159.3, "end": 159.3, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "benissimo.", "start": 159.3, "end": 159.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 159.819, "end": 159.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 159.819, "end": 160.539, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 160.539, "end": 160.539, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON>", "start": 160.539, "end": 160.899, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 160.899, "end": 160.899, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "ti", "start": 160.899, "end": 161.0, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 161.0, "end": 161.0, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "accompag<PERSON><PERSON>", "start": 161.0, "end": 161.5, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 161.5, "end": 161.559, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "a", "start": 161.559, "end": 161.58, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 161.58, "end": 161.599, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "visitare", "start": 161.599, "end": 161.979, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 161.979, "end": 161.979, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "la", "start": 161.979, "end": 162.099, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 162.099, "end": 162.099, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "città,", "start": 162.099, "end": 162.379, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 162.379, "end": 162.379, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "avrai", "start": 162.379, "end": 162.599, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 162.599, "end": 162.599, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "modo", "start": 162.599, "end": 162.879, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 162.879, "end": 162.879, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "di", "start": 162.879, "end": 162.959, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 162.959, "end": 162.959, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "distrarti.", "start": 162.959, "end": 163.639, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 163.639, "end": 163.699, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Certamente.", "start": 163.699, "end": 164.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 164.919, "end": 165.099, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 165.099, "end": 165.44, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 165.44, "end": 165.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 165.459, "end": 165.6, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 165.6, "end": 165.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tutta", "start": 165.619, "end": 165.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 165.799, "end": 165.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "la", "start": 165.819, "end": 165.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 165.899, "end": 165.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "libertà", "start": 165.919, "end": 166.339, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 166.339, "end": 166.339, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "possibile.", "start": 166.339, "end": 166.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 166.959, "end": 167.039, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Non", "start": 167.039, "end": 167.179, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 167.179, "end": 167.22, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ci", "start": 167.22, "end": 167.279, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 167.279, "end": 167.3, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "eravamo", "start": 167.3, "end": 167.72, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 167.72, "end": 167.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "mai", "start": 167.739, "end": 167.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 167.919, "end": 167.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "dimenticate", "start": 167.919, "end": 168.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 168.619, "end": 168.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "della", "start": 168.619, "end": 168.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 168.819, "end": 168.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "piccola", "start": 168.879, "end": 169.24, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 169.24, "end": 169.279, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>.", "start": 169.279, "end": 169.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 169.779, "end": 170.199, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Uh,", "start": 170.199, "end": 171.259, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 171.259, "end": 171.339, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "per", "start": 171.339, "end": 171.5, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 171.5, "end": 171.519, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "la", "start": 171.519, "end": 171.619, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 171.619, "end": 171.619, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "rette", "start": 171.619, "end": 171.88, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 171.88, "end": 171.899, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "della", "start": 171.899, "end": 172.059, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 172.059, "end": 172.119, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "figliola", "start": 172.119, "end": 172.44, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 172.44, "end": 172.44, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "ci", "start": 172.44, "end": 172.539, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 172.539, "end": 172.559, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "metteremo", "start": 172.559, "end": 172.919, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 172.919, "end": 172.919, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "d'accordo.", "start": 172.919, "end": 173.419, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 173.419, "end": 173.419, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Come", "start": 173.419, "end": 174.22, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 174.22, "end": 174.279, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "vuole,", "start": 174.279, "end": 174.52, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 174.52, "end": 174.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "don", "start": 174.539, "end": 174.679, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 174.679, "end": 174.72, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Ingarsia,", "start": 174.72, "end": 175.18, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 175.18, "end": 175.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "non", "start": 175.239, "end": 175.339, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 175.339, "end": 175.36, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "abbia", "start": 175.36, "end": 175.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 175.559, "end": 175.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "preoccupazioni", "start": 175.559, "end": 176.259, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 176.259, "end": 176.3, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "per", "start": 176.3, "end": 176.419, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 176.419, "end": 176.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "questo.", "start": 176.479, "end": 176.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 176.819, "end": 176.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON>,", "start": 176.819, "end": 177.419, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 177.419, "end": 177.44, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON>.", "start": 177.44, "end": 177.919, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 177.919, "end": 179.559, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON>.", "start": 179.559, "end": 180.079, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 180.079, "end": 180.08, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON>ngior<PERSON>.", "start": 180.08, "end": 185.499, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 185.499, "end": 185.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 185.699, "end": 186.02, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 186.02, "end": 186.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>,", "start": 186.059, "end": 186.52, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 186.52, "end": 186.539, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ho", "start": 186.539, "end": 186.619, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 186.619, "end": 186.659, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "dimenticato", "start": 186.659, "end": 187.219, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 187.219, "end": 187.259, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 187.259, "end": 187.339, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 187.339, "end": 187.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "dire", "start": 187.379, "end": 187.539, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 187.539, "end": 187.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "una", "start": 187.559, "end": 187.679, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 187.679, "end": 187.699, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "cosa", "start": 187.699, "end": 187.899, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 187.899, "end": 187.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "importante", "start": 187.919, "end": 188.44, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 188.44, "end": 188.44, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "a", "start": 188.44, "end": 188.539, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 188.539, "end": 188.539, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "papà.", "start": 188.539, "end": 188.879, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 188.879, "end": 189.199, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>", "start": 189.199, "end": 189.459, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 189.459, "end": 189.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "scusi", "start": 189.479, "end": 189.719, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 189.719, "end": 189.72, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "un", "start": 189.72, "end": 189.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 189.799, "end": 189.839, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "attimo.", "start": 189.839, "end": 190.22, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 190.22, "end": 190.22, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}]}}, "created_at": 1754319699.14625}