"""
ASR统一日志混入类

四级分类图标日志系统，与字幕生成和音频处理系统保持一致。
支持SIMPLE模式（图标）和DETAILED模式（文本标签）。

图标方案：
🔍 DEBUG   - 调试分析
ℹ️  INFO    - 重要信息
⚠️  WARNING - 需要注意
❌ ERROR   - 严重问题
"""

import logging
from typing import Optional


class IconLoggerMixin:
    """四级分类图标日志混入类

    为ASR系统提供统一日志接口，与字幕和音频系统保持一致。
    """
    
    def __init__(self):
        """日志混入类初始化"""
        # 调用类logger获取
        self.logger = logging.getLogger(self.__class__.__module__)
        
        # 日志模式：SIMPLE图标，DETAILED文本
        self.log_mode = "SIMPLE"
        
        # 日志级别控制
        self.log_level_filter = {
            "DEBUG": True,
            "INFO": True, 
            "WARNING": True,
            "ERROR": True
        }
    
    def _should_log(self, level: str) -> bool:
        """日志级别检查

        Args:
            level: 日志级别

        Returns:
            bool: 是否记录日志
        """
        # DEBUG级别由UI负责过滤
        # 此方法处理INFO/WARNING/ERROR级别

        # 自定义过滤器检查
        if not self.log_level_filter.get(level, True):
            return False

        # Python logging级别检查
        level_mapping = {
            "DEBUG": logging.DEBUG,
            "INFO": logging.INFO,
            "WARNING": logging.WARNING,
            "ERROR": logging.ERROR
        }

        return self.logger.isEnabledFor(level_mapping.get(level, logging.INFO))
    
    def _log_debug(self, message: str) -> None:
        """🔍 调试日志

        记录详细技术信息：API调用、请求参数、处理流程

        Args:
            message: 日志消息
        """
        # 始终生成DEBUG日志，由UI过滤显示
        prefix = "[DEBUG] " if self.log_mode == "DETAILED" else "🔍 "
        self.logger.debug(f"{prefix}{message}")
    
    def _log_info(self, message: str) -> None:
        """ℹ️ 信息日志

        记录重要进度信息：服务创建、转录进度、状态变更

        Args:
            message: 日志消息
        """
        if self._should_log("INFO"):
            prefix = "[INFO] " if self.log_mode == "DETAILED" else "ℹ️ "
            self.logger.info(f"{prefix}{message}")
    
    def _log_warning(self, message: str) -> None:
        """⚠️ 警告日志

        记录需要注意但可恢复的问题：失败重试、配置缺失、网络超时

        Args:
            message: 日志消息
        """
        if self._should_log("WARNING"):
            prefix = "[WARNING] " if self.log_mode == "DETAILED" else "⚠️ "
            self.logger.warning(f"{prefix}{message}")
    
    def _log_error(self, message: str) -> None:
        """❌ 错误日志

        记录严重错误：服务创建失败、转录异常、配置错误

        Args:
            message: 日志消息
        """
        if self._should_log("ERROR"):
            prefix = "[ERROR] " if self.log_mode == "DETAILED" else "❌ "
            self.logger.error(f"{prefix}{message}")


class ASRLoggerMixin(IconLoggerMixin):
    """ASR专用日志混入类

    在基础图标日志基础上，添加ASR特有的日志方法。
    保持与字幕和音频系统的一致性。
    """
    pass
    
    def _log_service_creation(self, service_name: str, success: bool, error: str = "") -> None:
        """服务创建记录

        Args:
            service_name: 服务名称
            success: 是否成功
            error: 错误信息
        """
        if success:
            self._log_info(f"已创建ASR服务实例: {service_name}")
        else:
            self._log_error(f"创建ASR服务失败: {service_name} - {error}")
    

