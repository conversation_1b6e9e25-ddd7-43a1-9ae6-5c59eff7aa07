"""
LLM系统的统一日志混入类

提供四级分类图标日志系统，与字幕生成系统、音频处理系统、ASR系统保持一致的视觉风格。
支持SIMPLE模式（显示图标）和DETAILED模式（显示文本标签）。

图标方案：
🔍 DEBUG   - 放大镜，表示调试和深入分析
ℹ️  INFO    - 信息图标，表示重要信息
⚠️  WARNING - 警告三角，表示需要注意
❌ ERROR   - 错误叉号，表示严重问题

使用方法：
    class LLMAPIService(LLMLoggerMixin):
        def __init__(self):
            super().__init__()
            self._log_info("LLMAPIService initialized")
            
        def call_api(self, text):
            self._log_api_call_start("openai", "gpt-4", len(text))
            try:
                # 调用逻辑
                self._log_api_call_success("openai", 2.3, 512)
            except Exception as e:
                self._log_api_call_failed("openai", str(e))
"""

import logging
from typing import Optional


class IconLoggerMixin:
    """四级分类图标日志混入类
    
    为LLM系统提供统一的日志接口，与字幕、音频、ASR系统保持一致的视觉风格。
    """
    
    def __init__(self):
        """初始化日志混入类"""
        self.logger = logging.getLogger(self.__class__.__module__)

        # 日志模式：SIMPLE显示图标，DETAILED显示文本标签
        self.log_mode = "SIMPLE"

        # 日志级别控制
        self.log_level_filter = {
            "DEBUG": True,
            "INFO": True, 
            "WARNING": True,
            "ERROR": True
        }
    
    def _should_log(self, level: str) -> bool:
        """检查是否应该记录指定级别的日志
        
        结合Python logging的级别控制和自定义过滤器。
        
        Args:
            level: 日志级别 (DEBUG, INFO, WARNING, ERROR)
            
        Returns:
            bool: 是否应该记录日志
        """
        # 检查自定义过滤器
        if not self.log_level_filter.get(level, True):
            return False

        # 检查日志级别设置
        level_mapping = {
            "DEBUG": logging.DEBUG,
            "INFO": logging.INFO,
            "WARNING": logging.WARNING,
            "ERROR": logging.ERROR
        }
        
        return self.logger.isEnabledFor(level_mapping.get(level, logging.INFO))
    
    def _log_debug(self, message: str) -> None:
        """🔍 调试级别日志
        
        用于记录详细的技术信息，如：
        - API请求/响应详情
        - 内部状态变化
        - 性能指标统计
        - 缓存操作细节
        
        Args:
            message: 日志消息
        """
        if self._should_log("DEBUG"):
            prefix = "[DEBUG] " if self.log_mode == "DETAILED" else "🔍 "
            self.logger.debug(f"{prefix}{message}")
    
    def _log_info(self, message: str) -> None:
        """ℹ️ 信息级别日志
        
        用于记录重要的进度信息，如：
        - API调用开始和完成
        - 配置加载成功
        - 重要状态变更
        - 处理进度更新
        
        Args:
            message: 日志消息
        """
        if self._should_log("INFO"):
            prefix = "[INFO] " if self.log_mode == "DETAILED" else "ℹ️ "
            self.logger.info(f"{prefix}{message}")
    
    def _log_warning(self, message: str) -> None:
        """⚠️ 警告级别日志
        
        用于记录需要注意但可恢复的问题，如：
        - API调用失败但可重试
        - 配置缺失但有默认值
        - 网络超时但可故障转移
        - 缓存未命中但可重新计算
        
        Args:
            message: 日志消息
        """
        if self._should_log("WARNING"):
            prefix = "[WARNING] " if self.log_mode == "DETAILED" else "⚠️ "
            self.logger.warning(f"{prefix}{message}")
    
    def _log_error(self, message: str) -> None:
        """❌ 错误级别日志
        
        用于记录严重错误，如：
        - API调用完全失败
        - 配置加载错误
        - 网络连接失败
        - 不可恢复的错误状态
        
        Args:
            message: 日志消息
        """
        if self._should_log("ERROR"):
            prefix = "[ERROR] " if self.log_mode == "DETAILED" else "❌ "
            self.logger.error(f"{prefix}{message}")



class LLMLoggerMixin(IconLoggerMixin):
    """LLM处理专用的日志混入类
    
    在基础图标日志的基础上，添加LLM处理特有的日志方法。
    
    命名规范：
    - _log_api_*: API调用相关的专用日志方法
    - _log_config_*: 配置管理相关的专用日志方法
    - _log_cache_*: 缓存操作相关的专用日志方法
    - 保持与字幕、音频、ASR系统的一致性
    """
    
    def _log_api_call_start(self, api_name: str, model: str, text_length: int) -> None:
        """记录API调用开始
        
        Args:
            api_name: API服务名称
            model: 模型名称
            text_length: 输入文本长度
        """
        self._log_info(f"开始调用API: {api_name}/{model} 处理文本({text_length}字符)")
    
    def _log_api_call_success(self, api_name: str, duration: float, response_length: int = 0) -> None:
        """记录API调用成功
        
        Args:
            api_name: API服务名称
            duration: 调用耗时（秒）
            response_length: 响应长度
        """
        if response_length > 0:
            self._log_info(f"API调用成功: {api_name} (耗时: {duration:.2f}s, 响应: {response_length}字符)")
        else:
            self._log_info(f"API调用成功: {api_name} (耗时: {duration:.2f}s)")
    
    def _log_api_call_failed(self, api_name: str, error: str, retry_count: int = 0) -> None:
        """记录API调用失败
        
        Args:
            api_name: API服务名称
            error: 错误信息
            retry_count: 重试次数
        """
        if retry_count > 0:
            self._log_error(f"API调用失败: {api_name} - {error} (重试: {retry_count})")
        else:
            self._log_error(f"API调用失败: {api_name} - {error}")
    

    
    def _log_config_operation(self, operation: str, success: bool, details: str = "") -> None:
        """记录配置操作
        
        Args:
            operation: 操作类型（加载、保存等）
            success: 是否成功
            details: 详细信息
        """
        if success:
            self._log_info(f"配置{operation}成功" + (f": {details}" if details else ""))
        else:
            self._log_error(f"配置{operation}失败" + (f": {details}" if details else ""))
    

    

