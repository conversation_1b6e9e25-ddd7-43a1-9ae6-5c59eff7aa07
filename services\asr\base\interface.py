"""
ASR服务统一接口定义
定义所有ASR服务必须实现的标准接口
"""

from abc import ABC, abstractmethod
from typing import Optional, Dict, Any, List
from dataclasses import dataclass
from enum import Enum
import time


class ASRStatus(Enum):
    """ASR服务状态枚举 - 简化版本，只保留核心状态"""
    COMPLETED = "completed"
    FAILED = "failed"





@dataclass
class ASRRequest:
    """ASR请求模型"""
    audio_file_path: str                 # 音频文件路径
    language: Optional[str] = "auto"     # 语言代码
    model: Optional[str] = None          # 模型名称
    enable_diarization: bool = False     # 说话人分离
    enable_punctuation: bool = True      # 标点符号
    enable_timestamps: bool = True       # 时间戳
    num_speakers: Optional[int] = None   # 说话人数量
    custom_vocabulary: Optional[List[str]] = None  # 自定义词汇
    metadata: Optional[Dict[str, Any]] = None      # 额外元数据
    
    def __post_init__(self):
        """参数验证"""
        if not self.audio_file_path:
            raise ValueError("音频文件路径不能为空")
        
        if self.num_speakers is not None:
            if not isinstance(self.num_speakers, int) or self.num_speakers < 0:
                raise ValueError("说话人数量必须是非负整数")


@dataclass
class ASRResponse:
    """ASR响应模型"""
    transcription_id: str                # 转录ID
    status: ASRStatus                    # 状态
    full_text: str                       # 完整文本
    words: List[Dict[str, Any]]          # 词级信息
    language_detected: Optional[str] = None      # 检测语言
    confidence: Optional[float] = None           # 置信度
    processing_time: Optional[float] = None     # 处理时间
    metadata: Optional[Dict[str, Any]] = None   # 元数据
    error_message: Optional[str] = None         # 错误信息
    created_at: float = None                    # 创建时间
    
    def __post_init__(self):
        """默认值设置"""
        if self.created_at is None:
            self.created_at = time.time()


class ASRServiceInterface(ABC):
    """ASR服务接口

    核心功能：
    - 统一转录调用
    - 标准化数据格式
    - 一致错误处理
    """

    @property
    @abstractmethod
    def service_name(self) -> str:
        """服务名称

        Returns:
            str: 服务唯一标识
        """
        pass

    @abstractmethod
    def transcribe(self, request: ASRRequest) -> ASRResponse:
        """转录执行

        Args:
            request: 转录请求

        Returns:
            ASRResponse: 转录结果

        Raises:
            ASRException: 转录异常
        """
        pass
