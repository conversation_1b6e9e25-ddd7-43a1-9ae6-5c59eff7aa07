"""
转录结果解析器
解析ASR服务的JSON输出，转换为统一的TimestampedWord数据模型
支持ElevenLabs、AssemblyAI、Deepgram的标准化和原始API格式
"""

import os
import json
import time
import logging
import re
from typing import Optional, Dict, List, Any
from dataclasses import dataclass

# ASR数据模型
from .base.models import TimestampedWord, TranscriptionMetadata
from .logger_mixin import ASRLoggerMixin

# 日志配置
logger = logging.getLogger(__name__)


@dataclass
class ParsedTranscription:
    """解析后的转录结果

    统一格式，兼容所有ASR服务输出
    """
    full_text: str                                      # 完整文本
    words: List[TimestampedWord]                        # 词级信息
    language: str = "unknown"                           # 检测语言
    confidence: float = 0.0                             # 整体置信度
    speaker_count: int = 0                              # 说话人数
    metadata: Optional[TranscriptionMetadata] = None    # 元数据


class TranscriptionParser(ASRLoggerMixin):
    """转录解析器

    支持格式：
    - ElevenLabs：标准化格式和原始API响应
    - Deepgram：标准化格式和原始API响应
    - AssemblyAI：标准化格式和原始API响应

    自动检测格式并转换为TimestampedWord模型
    """

    def __init__(self, signals_forwarder=None):
        """初始化解析器

        Args:
            signals_forwarder: 信号转发器，用于日志
        """
        super().__init__()
        self._signals = signals_forwarder

        # 格式解析函数映射
        self.supported_formats = {
            'elevenlabs': self._parse_elevenlabs,
            'deepgram': self._parse_deepgram,
            'assemblyai': self._parse_assemblyai
        }

    def log(self, message: str):
        """日志记录"""
        self._log_info(message)
        if self._signals:
            try:
                self._signals.log_message.emit(message)
            except:
                pass

    def parse_file(self, json_path: str, source_format: str = 'auto',
                   validate_result: bool = True, encoding: str = 'utf-8') -> Optional[ParsedTranscription]:
        """解析转录文件

        Args:
            json_path: JSON文件路径
            source_format: 格式类型 (auto/elevenlabs/deepgram/assemblyai)
            validate_result: 是否验证结果质量
            encoding: 文件编码

        Returns:
            ParsedTranscription: 解析结果，失败返回None
        """
        start_time = time.time()

        try:
            # 文件存在性检查
            if not os.path.exists(json_path):
                raise FileNotFoundError(f"Transcription file not found: {json_path}")

            # 读取JSON文件
            with open(json_path, 'r', encoding=encoding) as f:
                data = json.load(f)

            # 格式自动检测
            if source_format == 'auto':
                source_format = self.auto_detect_format(data)

            if source_format == 'unknown':
                raise ValueError(f"Cannot detect transcription file format: {json_path}")

            # 数据解析
            result = self._parse_data(data, source_format)

            parse_time = time.time() - start_time
            self.log(f"Parsed {os.path.basename(json_path)} in {parse_time:.3f}s")

            return result

        except Exception as e:
            self._log_error(f"Failed to parse {json_path}: {e}")
            return None

    def parse_data(self, data: dict, source_format: str) -> Optional[ParsedTranscription]:
        """解析转录数据

        Args:
            data: JSON数据
            source_format: 格式类型

        Returns:
            ParsedTranscription: 解析结果
        """
        return self._parse_data(data, source_format)

    def auto_detect_format(self, data: dict) -> str:
        """自动检测JSON格式

        检测顺序：
        1. 标准化格式：检查service字段
        2. 原始API格式：检查服务特征
        3. 备用检测：基于words结构和时间戳
        """
        try:
            logger.debug(f"Detecting format for data with keys: {list(data.keys())}")

            # 检查service字段（标准化格式标识）
            if isinstance(data, dict) and 'service' in data:
                service = data.get('service', '').lower()
                if service in ['elevenlabs', 'assemblyai', 'deepgram']:
                    logger.debug(f"Detected {service} standardized format from service field")
                    return service

            # Deepgram API响应格式检测
            if isinstance(data, dict) and 'channels' in data:
                channels = data['channels']
                if isinstance(channels, list) and len(channels) > 0:
                    channel = channels[0]
                    if isinstance(channel, dict) and 'alternatives' in channel:
                        logger.debug("Detected Deepgram API response format: has channels.alternatives structure")
                        return 'deepgram'

            # AssemblyAI API响应格式检测
            if isinstance(data, dict) and 'id' in data and 'status' in data:
                # 检查AssemblyAI特有字段
                if ('audio_url' in data or 'language_model' in data or
                    'acoustic_model' in data or str(data.get('status', '')).startswith('TranscriptStatus')):
                    logger.debug("Detected AssemblyAI API response format: has id, status and AssemblyAI-specific fields")
                    return 'assemblyai'

            # ElevenLabs API响应格式检测
            if isinstance(data, dict) and 'language_code' in data and 'language_probability' in data:
                logger.debug("Detected ElevenLabs API response format: has language_code and language_probability")
                return 'elevenlabs'

            # words数组检测（备用方法）
            if isinstance(data, dict) and 'words' in data:
                words = data['words']
                if isinstance(words, list) and len(words) > 0:
                    first_word = words[0]
                    logger.debug(f"First word structure: {first_word}")

                    if isinstance(first_word, dict):
                        # ElevenLabs特征：text + speaker_id + type字段
                        if ('text' in first_word and 'start' in first_word and
                            ('speaker_id' in first_word or 'type' in first_word)):
                            logger.debug("Detected ElevenLabs API response: has text, start and speaker_id/type fields")
                            return 'elevenlabs'

                        # AssemblyAI特征：text + speaker字段（非speaker_id）
                        if ('text' in first_word and 'start' in first_word and
                            'speaker' in first_word and 'speaker_id' not in first_word):
                            logger.debug("Detected AssemblyAI API response: has text, start and speaker fields")
                            return 'assemblyai'

                        # 通过时间戳范围判断服务类型
                        if 'text' in first_word and 'start' in first_word:
                            start_value = first_word.get('start', 0)
                            try:
                                start_float = float(start_value)
                                # AssemblyAI使用毫秒，ElevenLabs/Deepgram使用秒
                                if start_float > 10000:  # 毫秒值判断
                                    logger.debug("Detected AssemblyAI API response: large timestamp suggests milliseconds")
                                    return 'assemblyai'
                                else:
                                    logger.debug("Detected ElevenLabs API response: small timestamp suggests seconds")
                                    return 'elevenlabs'
                            except (ValueError, TypeError):
                                pass

                    elif isinstance(first_word, TimestampedWord):
                        # TimestampedWord对象，检查service字段
                        service = data.get('service', '').lower()
                        if service in ['elevenlabs', 'assemblyai', 'deepgram']:
                            logger.debug(f"Detected {service} format from service field")
                            return service
                        return 'elevenlabs'  # 默认格式

            # 无words但有text字段的简化格式
            if isinstance(data, dict) and 'text' in data:
                # service字段检查
                service = data.get('service', '').lower()
                if service in ['elevenlabs', 'assemblyai', 'deepgram']:
                    logger.debug(f"Detected {service} format from service field")
                    return service

            self._log_warning("Could not detect format, returning unknown")
            return 'unknown'

        except Exception as e:
            self._log_error(f"Format detection failed: {e}")
            return 'unknown'

    def _parse_data(self, data: dict, source_format: str) -> Optional[ParsedTranscription]:
        """数据解析核心方法"""
        # auto格式自动检测
        if source_format == 'auto':
            source_format = self.auto_detect_format(data)
            logger.debug(f"Auto-detected format: {source_format}")

        if source_format not in self.supported_formats:
            raise ValueError(f"Unsupported format: {source_format}")

        # 调用对应解析器
        parser_func = self.supported_formats[source_format]
        parsed_result = parser_func(data)

        # 应用词汇分离处理
        if parsed_result:
            try:
                # 将ParsedTranscription转换为字典进行处理
                parsed_dict = {
                    'full_text': parsed_result.full_text,
                    'words': parsed_result.words
                }

                # 应用词汇分离
                processed_dict = self._apply_word_separation(parsed_dict)

                # 转换回ParsedTranscription对象
                parsed_result = ParsedTranscription(
                    full_text=processed_dict['full_text'],
                    words=processed_dict['words'],
                    language=parsed_result.language,
                    confidence=parsed_result.confidence,
                    speaker_count=parsed_result.speaker_count,
                    metadata=parsed_result.metadata
                )
            except Exception as e:
                self._log_warning(f"词汇分离处理失败: {e}")
                # 如果分离失败，返回原始结果

        return parsed_result

    def _parse_elevenlabs(self, data: dict) -> Optional[ParsedTranscription]:
        """解析ElevenLabs格式"""
        try:
            logger.debug("Parsing ElevenLabs format")
            logger.debug(f"ElevenLabs data keys: {list(data.keys())}")

            # ElevenLabs API响应格式解析

            full_text = data.get('text', '')
            words_data = data.get('words', [])

            logger.debug(f"ElevenLabs full_text length: {len(full_text)}")
            logger.debug(f"ElevenLabs words count: {len(words_data)}")

            if not full_text.strip() and not words_data:
                self._log_warning("ElevenLabs: No text or words data")
                return None

            words = []
            for i, word_data in enumerate(words_data):
                try:
                    if isinstance(word_data, dict):
                        # 提取text字段
                        word = word_data.get('text', '').strip()

                        # 过滤空格类型
                        word_type = word_data.get('type', 'word')
                        if word_type == 'spacing' or not word.strip():
                            continue

                        start_time = self._normalize_timestamp(word_data.get('start', 0), 'elevenlabs')
                        end_time = self._normalize_timestamp(word_data.get('end', 0), 'elevenlabs')

                        # 置信度处理
                        confidence = float(word_data.get('confidence', word_data.get('logprob', 0)))
                        if confidence < 0:
                            confidence = max(0, 1 + confidence)

                        # 说话人ID处理
                        speaker_id = self._normalize_speaker_id(
                            word_data.get('speaker_id'),
                            'elevenlabs'
                        )

                        if word:
                            words.append(TimestampedWord(
                                word=word,
                                start_time=start_time,
                                end_time=end_time,
                                confidence=confidence,
                                speaker_id=speaker_id
                            ))

                    elif isinstance(word_data, TimestampedWord):
                        # TimestampedWord对象直接使用
                        words.append(word_data)

                except Exception as e:
                    self._log_warning(f"ElevenLabs: Error parsing word {i}: {e}")
                    continue

            # 无words但有text时创建简单结果
            if not words and full_text.strip():
                logger.debug("ElevenLabs: No words data, creating simple result")
                return ParsedTranscription(
                    full_text=full_text,
                    words=[],
                    confidence=0.0
                )

            # 计算统计信息
            confidence_words = [w for w in words if w.confidence and w.confidence > 0]
            avg_confidence = sum(w.confidence for w in confidence_words) / len(confidence_words) if confidence_words else 0.0

            # 计算说话人数量
            unique_speakers = set()
            for word in words:
                if word.speaker_id:
                    unique_speakers.add(word.speaker_id)
            speaker_count = len(unique_speakers)

            # 如果没有full_text，从words重建
            if not full_text.strip() and words:
                full_text = ' '.join(w.word for w in words)

            logger.debug(f"ElevenLabs result: {len(words)} words, confidence: {avg_confidence:.3f}, speakers: {speaker_count}")

            return ParsedTranscription(
                full_text=full_text,
                words=words,
                confidence=avg_confidence,
                speaker_count=speaker_count
            )

        except Exception as e:
            self._log_error(f"Failed to parse ElevenLabs format: {e}")
            return None

    def _parse_deepgram(self, data: dict) -> Optional[ParsedTranscription]:
        """解析Deepgram格式（标准化格式或API响应格式）"""
        try:
            logger.debug("Parsing Deepgram format")
            logger.debug(f"Deepgram data keys: {list(data.keys())}")

            # 检查是否是标准化格式（有service字段和words数组）
            if 'service' in data and 'words' in data and data.get('service') == 'deepgram':
                logger.debug("Detected standardized Deepgram format, checking for raw_result")

                # 如果有metadata.raw_result，使用API响应数据
                if 'metadata' in data and 'raw_result' in data['metadata']:
                    logger.debug("Using API response data from metadata.raw_result")
                    return self._parse_deepgram_raw(data['metadata']['raw_result'])
                else:
                    logger.debug("Using standardized words data directly")
                    return self._parse_deepgram_processed(data)

            # Deepgram API响应格式：
            # {
            #   "channels": [
            #     {
            #       "alternatives": [
            #         {
            #           "transcript": "完整文本",
            #           "words": [
            #             {
            #               "word": "词汇",
            #               "start": 244.94,        // 秒
            #               "end": 245.44,          // 秒
            #               "confidence": 0.3971523,
            #               "punctuated_word": "Salut.",
            #               "speaker": 0,           // 数字格式
            #               "speaker_confidence": 0.16410244
            #             }
            #           ]
            #         }
            #       ]
            #     }
            #   ]
            # }

            return self._parse_deepgram_raw(data)

        except Exception as e:
            self._log_error(f"Failed to parse Deepgram format: {e}")
            return None

    def _parse_deepgram_raw(self, data: dict) -> Optional[ParsedTranscription]:
        """解析Deepgram API响应格式"""
        try:
            channels = data.get('channels', [])

            logger.debug(f"Deepgram channels count: {len(channels)}")

            if not channels:
                self._log_warning("Deepgram: No channels found")
                return None

            # 使用第一个通道
            channel = channels[0]
            alternatives = channel.get('alternatives', [])

            logger.debug(f"Deepgram alternatives count: {len(alternatives)}")

            if not alternatives:
                self._log_warning("Deepgram: No alternatives found")
                return None

            # 使用第一个候选
            alternative = alternatives[0]
            full_text = alternative.get('transcript', '')
            words_data = alternative.get('words', [])

            logger.debug(f"Deepgram full_text length: {len(full_text)}")
            logger.debug(f"Deepgram words count: {len(words_data)}")

            if not full_text.strip() and not words_data:
                self._log_warning("Deepgram: No transcript or words data")
                return None

            words = []
            for i, word_data in enumerate(words_data):
                try:
                    if isinstance(word_data, dict):
                        # 优先使用punctuated_word，回退到word
                        word = word_data.get('punctuated_word', word_data.get('word', '')).strip()

                        # 处理时间戳
                        start_time = self._normalize_timestamp(word_data.get('start', 0), 'deepgram')
                        end_time = self._normalize_timestamp(word_data.get('end', 0), 'deepgram')

                        confidence = float(word_data.get('confidence', 0))

                        # 处理说话人ID
                        speaker_id = self._normalize_speaker_id(word_data.get('speaker'), 'deepgram')

                        if word:
                            words.append(TimestampedWord(
                                word=word,
                                start_time=start_time,
                                end_time=end_time,
                                confidence=confidence,
                                speaker_id=speaker_id
                            ))

                    elif isinstance(word_data, TimestampedWord):
                        # 如果已经是TimestampedWord对象，直接使用
                        words.append(word_data)

                except Exception as e:
                    self._log_warning(f"Deepgram: Error parsing word {i}: {e}")
                    continue

            return self._build_deepgram_result(full_text, words)

        except Exception as e:
            self._log_error(f"Failed to parse Deepgram API response format: {e}")
            return None

    def _parse_deepgram_processed(self, data: dict) -> Optional[ParsedTranscription]:
        """解析标准化的Deepgram格式"""
        try:
            logger.debug("Parsing standardized Deepgram format")

            full_text = data.get('text', '')
            words_data = data.get('words', [])

            logger.debug(f"Standardized Deepgram full_text length: {len(full_text)}")
            logger.debug(f"Standardized Deepgram words count: {len(words_data)}")

            if not full_text.strip() and not words_data:
                self._log_warning("Standardized Deepgram: No text or words data")
                return None

            words = []
            for i, word_data in enumerate(words_data):
                try:
                    if isinstance(word_data, dict):
                        # 标准化格式使用'text'字段
                        word = word_data.get('text', '').strip()

                        # 时间戳已经是秒格式，直接使用
                        start_time = float(word_data.get('start', 0))
                        end_time = float(word_data.get('end', 0))

                        confidence = float(word_data.get('confidence', 0))

                        # 处理说话人ID
                        speaker_id = self._normalize_speaker_id(word_data.get('speaker'), 'deepgram')

                        if word:
                            words.append(TimestampedWord(
                                word=word,
                                start_time=start_time,
                                end_time=end_time,
                                confidence=confidence,
                                speaker_id=speaker_id
                            ))

                except Exception as e:
                    self._log_warning(f"Standardized Deepgram: Error parsing word {i}: {e}")
                    continue

            return self._build_deepgram_result(full_text, words)

        except Exception as e:
            self._log_error(f"Failed to parse standardized Deepgram format: {e}")
            return None

    def _build_deepgram_result(self, full_text: str, words: list) -> Optional[ParsedTranscription]:
        """构建Deepgram解析结果"""
        try:
            # 如果没有words但有full_text，创建一个简单的结果
            if not words and full_text.strip():
                logger.debug("Deepgram: No words data, creating simple result")
                return ParsedTranscription(
                    full_text=full_text,
                    words=[],
                    confidence=0.0
                )

            # 计算统计信息
            confidence_words = [w for w in words if w.confidence and w.confidence > 0]
            avg_confidence = sum(w.confidence for w in confidence_words) / len(confidence_words) if confidence_words else 0.0

            # 计算说话人数量
            unique_speakers = set()
            for word in words:
                if word.speaker_id:
                    unique_speakers.add(word.speaker_id)
            speaker_count = len(unique_speakers)

            # 如果没有full_text，从words重建
            if not full_text.strip() and words:
                full_text = ' '.join(w.word for w in words)

            logger.debug(f"Deepgram result: {len(words)} words, confidence: {avg_confidence:.3f}, speakers: {speaker_count}")

            return ParsedTranscription(
                full_text=full_text,
                words=words,
                confidence=avg_confidence,
                speaker_count=speaker_count
            )

        except Exception as e:
            self._log_error(f"Failed to build Deepgram result: {e}")
            return None

    def _parse_assemblyai(self, data: dict) -> Optional[ParsedTranscription]:
        """解析AssemblyAI格式（标准化格式或API响应格式）"""
        try:
            logger.debug("Parsing AssemblyAI format")
            logger.debug(f"AssemblyAI data keys: {list(data.keys())}")

            # AssemblyAI API响应格式：
            # {
            #   "id": "511cb277-8c69-4619-a52b-0951b7b8dd10",
            #   "language_model": "",
            #   "acoustic_model": "",
            #   "language_code": "",
            #   "status": "TranscriptStatus.completed",
            #   "audio_url": "https://cdn.assemblyai.com/upload/...",
            #   "text": "完整文本",
            #   "words": [
            #     {
            #       "text": "...",
            #       "start": 8024,           // 毫秒！
            #       "end": 11024,            // 毫秒！
            #       "confidence": 0.103271484,
            #       "speaker": "A"           // 注意：字段名是speaker不是speaker_id
            #     }
            #   ],
            #   "utterances": [...],
            #   "confidence": 0.95,
            #   "audio_duration": 5000,
            #   ...
            # }

            full_text = data.get('text', '')
            words_data = data.get('words', [])

            logger.debug(f"AssemblyAI full_text length: {len(full_text)}")
            logger.debug(f"AssemblyAI words count: {len(words_data)}")

            if not full_text.strip() and not words_data:
                self._log_warning("AssemblyAI: No text or words data")
                return None

            words = []
            for i, word_data in enumerate(words_data):
                try:
                    if i < 3:  # 记录前3个词的详细信息
                        logger.debug(f"AssemblyAI word {i}: {word_data}")

                    if isinstance(word_data, dict):
                        # 使用'text'字段
                        word = word_data.get('text', '').strip()

                        # 处理时间戳（毫秒转秒）
                        start_time = self._normalize_timestamp(word_data.get('start', 0), 'assemblyai')
                        end_time = self._normalize_timestamp(word_data.get('end', 0), 'assemblyai')

                        confidence = float(word_data.get('confidence', 0))

                        # 处理说话人ID
                        speaker_id = self._normalize_speaker_id(word_data.get('speaker'), 'assemblyai')

                        if i < 3:
                            logger.debug(f"AssemblyAI parsed word {i}: '{word}' [{start_time:.3f}-{end_time:.3f}] conf:{confidence} speaker:{speaker_id}")

                        if word:
                            words.append(TimestampedWord(
                                word=word,
                                start_time=start_time,
                                end_time=end_time,
                                confidence=confidence,
                                speaker_id=speaker_id
                            ))

                    elif isinstance(word_data, TimestampedWord):
                        # 如果已经是TimestampedWord对象，直接使用
                        words.append(word_data)

                    else:
                        self._log_warning(f"AssemblyAI word {i}: unknown format {type(word_data)}")
                        continue

                except Exception as e:
                    self._log_warning(f"AssemblyAI: Error parsing word {i}: {e}")
                    continue

            # 如果没有words但有full_text，创建一个简单的结果
            if not words and full_text.strip():
                logger.debug("AssemblyAI: No words data, creating simple result")
                return ParsedTranscription(
                    full_text=full_text,
                    words=[],
                    confidence=0.0
                )

            # 计算统计信息
            # 安全的置信度计算，避免除零错误
            confidence_words = [w for w in words if w.confidence and w.confidence > 0]
            avg_confidence = sum(w.confidence for w in confidence_words) / len(confidence_words) if confidence_words else 0.0

            # 计算说话人数量
            unique_speakers = set()
            for word in words:
                if word.speaker_id:
                    unique_speakers.add(word.speaker_id)
            speaker_count = len(unique_speakers)

            # 如果没有full_text，从words重建
            if not full_text.strip() and words:
                full_text = ' '.join(w.word for w in words)

            logger.debug(f"AssemblyAI result: {len(words)} words, confidence: {avg_confidence:.3f}, speakers: {speaker_count}")

            return ParsedTranscription(
                full_text=full_text,
                words=words,
                confidence=avg_confidence,
                speaker_count=speaker_count
            )

        except Exception as e:
            self._log_error(f"Failed to parse AssemblyAI format: {e}")
            return None

    def _normalize_timestamp(self, timestamp, service_type: str) -> float:
        """统一时间戳格式为秒

        根据各服务API响应格式分析：
        - ElevenLabs: 使用秒格式 (如 245.039)
        - AssemblyAI: 使用毫秒格式 (如 245067)
        - Deepgram: 使用秒格式 (如 244.94)

        注意：标准化格式的时间戳已经统一为秒格式
        """
        try:
            timestamp = float(timestamp)

            if service_type == 'assemblyai':
                # AssemblyAI使用毫秒，需要转换为秒
                return timestamp / 1000.0
            elif service_type == 'deepgram':
                # Deepgram使用秒，直接返回
                return timestamp
            else:
                # ElevenLabs使用秒，直接返回
                return timestamp

        except (ValueError, TypeError):
            self._log_warning(f"Invalid timestamp: {timestamp}")
            return 0.0

    def convert_to_template_format(self, parsed_result: ParsedTranscription, service_name: str) -> dict:
        """将解析结果转换为正确的模板格式

        Args:
            parsed_result: 解析后的转录结果
            service_name: 服务名称 ('elevenlabs', 'assemblyai', 'deepgram')

        Returns:
            dict: 符合模板格式的字典
        """
        try:
            import time

            # 构建符合模板格式的结果
            result = {
                "service": service_name.lower(),
                "full_text": parsed_result.full_text,
                "words": [],
                "language": "auto",  # 默认语言
                "confidence": parsed_result.confidence,
                "speaker_count": parsed_result.speaker_count,
                "word_count": len(parsed_result.words),
                "parsed_at": time.time()
            }

            # 转换words格式
            for word in parsed_result.words:
                word_dict = {
                    "word": word.word,
                    "start_time": word.start_time,  # 已经是秒格式
                    "end_time": word.end_time,      # 已经是秒格式
                    "confidence": word.confidence,
                    "speaker_id": word.speaker_id or "speaker_0"  # 确保有默认值
                }
                result["words"].append(word_dict)

            logger.debug(f"Converted to template format: {len(result['words'])} words, {result['speaker_count']} speakers")
            return result

        except Exception as e:
            self._log_error(f"Failed to convert to template format: {e}")
            return {
                "service": service_name.lower(),
                "full_text": "",
                "words": [],
                "language": "auto",
                "confidence": 0.0,
                "speaker_count": 0,
                "word_count": 0,
                "parsed_at": time.time()
            }

    def parse_raw_json_to_template(self, json_file_path: str, output_file_path: str = None) -> Optional[dict]:
        """解析JSON文件并转换为模板格式

        Args:
            json_file_path: JSON文件路径（标准化格式或API响应格式）
            output_file_path: 可选的输出文件路径，如果提供则保存结果

        Returns:
            dict: 符合模板格式的字典，失败时返回None
        """
        try:
            # 解析JSON文件
            parsed_result = self.parse_file(json_file_path)
            if not parsed_result:
                self._log_error(f"Failed to parse JSON: {json_file_path}")
                return None

            # 检测服务类型
            with open(json_file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            service_type = self.auto_detect_format(data)

            if service_type == 'unknown':
                self._log_error(f"Unknown service type for: {json_file_path}")
                return None

            # 转换为模板格式
            template_result = self.convert_to_template_format(parsed_result, service_type)

            # 如果提供了输出路径，保存结果
            if output_file_path:
                with open(output_file_path, 'w', encoding='utf-8') as f:
                    json.dump(template_result, f, ensure_ascii=False, indent=2)
                self._log_info(f"Saved template format to: {output_file_path}")

            return template_result

        except Exception as e:
            self._log_error(f"Failed to parse JSON to template: {e}")
            return None

    def _normalize_speaker_id(self, speaker, service_type: str) -> Optional[str]:
        """统一说话人ID格式为speaker_N格式"""
        if speaker is None:
            return None

        try:
            # 转换为字符串
            speaker_str = str(speaker).strip()

            # 如果已经是标准格式，直接返回
            if speaker_str.startswith('speaker_'):
                return speaker_str

            if service_type == 'deepgram':
                # Deepgram使用数字ID (0, 1, 2...)，转换为speaker_N格式 (speaker_1, speaker_2...)
                speaker_num = int(speaker_str)
                return f"speaker_{speaker_num + 1}"  # 从1开始编号

            elif service_type == 'assemblyai':
                # AssemblyAI使用字母ID (A, B, C...)，转换为speaker_N格式 (speaker_1, speaker_2...)
                if speaker_str.upper() in ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J']:
                    speaker_num = ord(speaker_str.upper()) - ord('A') + 1
                    return f"speaker_{speaker_num}"
                else:
                    return f"speaker_{speaker_str}"

            else:
                # ElevenLabs等其他服务
                if speaker_str.isdigit():
                    # 如果是纯数字，从1开始编号
                    return f"speaker_{int(speaker_str) + 1}"
                else:
                    # 其他格式，直接拼接
                    return f"speaker_{speaker_str}"

        except (ValueError, TypeError):
            self._log_warning(f"Invalid speaker ID: {speaker}")
            return f"speaker_{speaker}" if speaker else None

    # ==================== 词汇分离功能 ====================

    def _is_punctuation(self, text: str) -> bool:
        """判断文本是否为纯标点符号"""
        # 西文标点符号
        western_punct = r'[.!?,:;()[\]{}\"\'«»@#%&*~+=<>$€¥£]'
        # 中文标点符号
        chinese_punct = r'[。！？，、；：""''「」『』（）【】〔〕〈〉《》……—·]'

        punct_pattern = f'^({western_punct}|{chinese_punct})+$'
        return bool(re.match(punct_pattern, text))

    def _is_mixed_word_basic(self, word: str) -> bool:
        """基础混合词汇检测（不考虑内部符号）"""
        # 检测文字+符号或符号+文字的混合
        pattern = (
            r'[^"]*[.!?,:;()[\]{}\"«»@#%&*~+=<>$€¥£。！？，、；：""「」『』（）【】〔〕〈〉《》……—·][^"]*[a-zA-Z0-9À-ÿ\u4e00-\u9fff\u3040-\u309f\u30a0-\u30ff\u0400-\u04ff]|'
            r'[^"]*[a-zA-Z0-9À-ÿ\u4e00-\u9fff\u3040-\u309f\u30a0-\u30ff\u0400-\u04ff][^"]*[.!?,:;()[\]{}\"«»@#%&*~+=<>$€¥£。！？，、；：""「」『』（）【】〔〕〈〉《》……—·]'
        )
        return bool(re.search(pattern, word))

    def _is_mixed_word(self, word: str) -> bool:
        """改进的混合词汇检测 - 两步检测法"""
        # 第一步：移除词汇内部符号（撇号和连字符）
        cleaned_word = word.replace("'", "").replace("-", "")

        # 第二步：检查清理后的词汇是否为混合
        if len(cleaned_word) == len(word):
            # 没有内部符号，使用基础检测
            return self._is_mixed_word_basic(word)
        else:
            # 有内部符号，检查清理后的版本
            return self._is_mixed_word_basic(cleaned_word)

    def _split_mixed_word(self, word_text: str) -> List[str]:
        """
        将混合词汇分离成纯文字和纯符号部分

        例如：'tombe.Heu...' -> ['tombe', '.', 'Heu', '...']
        """
        # 定义字符类型
        LETTER_PATTERN = r'[a-zA-Z0-9À-ÿ\u4e00-\u9fff\u3040-\u309f\u30a0-\u30ff\u0400-\u04ff]'
        INTERNAL_PATTERN = r"['-]"  # 词汇内部符号
        PUNCT_PATTERN = r'[.!?,:;()[\]{}\"«»@#%&*~+=<>$€¥£。！？，、；：""''「」『』（）【】〔〕〈〉《》……—·]'

        result = []
        current_segment = ""
        current_type = None

        for char in word_text:
            # 判断字符类型
            if re.match(LETTER_PATTERN, char):
                char_type = "letter"
            elif re.match(INTERNAL_PATTERN, char):
                char_type = "letter"  # 内部符号视为文字的一部分
            elif re.match(PUNCT_PATTERN, char):
                char_type = "punct"
            else:
                char_type = "other"  # 空格等其他字符

            # 如果类型改变，保存当前段落
            if current_type is not None and current_type != char_type:
                if current_segment:
                    result.append(current_segment)
                current_segment = char
                current_type = char_type
            else:
                # 类型相同，继续累积
                current_segment += char
                current_type = char_type

        # 保存最后一个段落
        if current_segment:
            result.append(current_segment)

        return result

    def _universal_split_time(self, original_word: TimestampedWord, segments: List[str]) -> List[TimestampedWord]:
        """
        通用分离策略 - 统一处理所有情况
        文字部分按字符数比例分配时间，符号部分零持续时间
        """
        original_start = original_word.start_time
        original_end = original_word.end_time
        total_duration = original_end - original_start

        # 1. 分离文字和符号
        text_segments = []
        punct_positions = []

        for i, segment in enumerate(segments):
            if self._is_punctuation(segment):
                punct_positions.append(i)
            else:
                text_segments.append((i, segment))

        # 2. 处理特殊情况：没有文字部分
        if not text_segments:
            # 全是符号，都使用零持续时间
            result = []
            for segment in segments:
                result.append(TimestampedWord(
                    word=segment,
                    start_time=original_start,
                    end_time=original_start,
                    confidence=original_word.confidence,
                    speaker_id=original_word.speaker_id
                ))
            return result

        # 3. 计算文字部分的总字符数
        total_text_chars = sum(len(seg) for _, seg in text_segments)

        # 4. 为文字部分分配时间（按比例）
        current_time = original_start
        result = [None] * len(segments)

        for i, (pos, segment) in enumerate(text_segments):
            char_ratio = len(segment) / total_text_chars
            segment_duration = total_duration * char_ratio

            start_time = current_time
            end_time = current_time + segment_duration

            # 最后一个文字段落使用原始结束时间，避免浮点误差
            if i == len(text_segments) - 1:
                end_time = original_end

            result[pos] = TimestampedWord(
                word=segment,
                start_time=start_time,
                end_time=end_time,
                confidence=original_word.confidence,
                speaker_id=original_word.speaker_id
            )

            current_time = end_time

        # 5. 为标点符号分配零持续时间
        for pos in punct_positions:
            # 确定符号的时间点
            if pos == 0:
                # 第一个位置的符号
                time_point = original_start
            elif pos == len(segments) - 1:
                # 最后一个位置的符号
                time_point = original_end
            else:
                # 中间位置的符号，使用前一个文字段落的结束时间
                prev_text_pos = pos - 1
                while prev_text_pos >= 0 and result[prev_text_pos] is None:
                    prev_text_pos -= 1
                if prev_text_pos >= 0:
                    time_point = result[prev_text_pos].end_time
                else:
                    time_point = original_start

            result[pos] = TimestampedWord(
                word=segments[pos],
                start_time=time_point,
                end_time=time_point,
                confidence=original_word.confidence,
                speaker_id=original_word.speaker_id
            )

        return result

    def _apply_word_separation(self, parsed_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        应用词汇分离算法到解析后的数据
        """
        if 'words' not in parsed_data:
            return parsed_data

        original_words = parsed_data['words']
        new_words = []
        mixed_count = 0

        self._log_info(f"开始词汇分离处理，原始词汇数量: {len(original_words)}")

        # 处理每个词汇
        for word_obj in original_words:
            word_text = word_obj.word

            if self._is_mixed_word(word_text):
                # 混合词汇，需要分离
                mixed_count += 1
                segments = self._split_mixed_word(word_text)

                self._log_debug(f"分离词汇: '{word_text}' -> {segments}")

                if len(segments) == 1:
                    # 分离后只有一个部分，保持原样
                    new_words.append(word_obj)
                else:
                    # 分离后有多个部分，应用时间分配策略
                    separated_words = self._universal_split_time(word_obj, segments)
                    new_words.extend(separated_words)
            else:
                # 正常词汇，保持不变
                new_words.append(word_obj)

        # 更新数据
        parsed_data['words'] = new_words

        self._log_info(f"词汇分离完成，分离了 {mixed_count} 个混合词汇，最终词汇数量: {len(new_words)}")

        return parsed_data


