{"success": true, "service": "elevenlabs", "transcription_id": "elevenlabs_1754319787", "text": "Rosario? Mi mandò via di casa, ma ci vuole altro per impressionarmi. Poi cominciò la solita vita noiosa. L'unica distrazione era quella di guardare le liti fra ingarsi i pescatori, ma non parteggevo per nessuno. Pensavo soltanto a Carla e al modo di arrivare a lei. Non deve esserti stato difficile diventare il suo amante. Questo ve l'ha raccontato quello spione di Rosario, vero? Cosa succederebbe se io dicessi di no? Nulla. Lo so già. Questa te l'ha data lei. Sì. Me l'ha regalato la moglie di un Garcia, lo stesso giorno in cui Rosario partì per Palermo. Incontrai Carla al porto. Mi venne vicino come se fosse la cosa più naturale del mondo. Mi disse che andava a fare il bagno alla grotta di Ponente, da giù. Non ebbi nemmeno il tempo di risponderle. Mi accorsi che Carmela aveva inteso tutto e per non darle sospetto mi allontanai. Non volevo che sofferesse. Ma era molto gelosa, non seppe trattenersi e mi fermò. E così io, eccitato dal pensiero dell'invito di Carla, la trattai duramente, come non avrei voluto. Francisco. Ah, sei tu. Dove vai? Ho da fare alla piccola Darsena con padron Grava. Forse mi affiderà un trasporto sul continente o un lavoro qualsiasi, ancora non so bene. Stavi parlando con la moglie di un Garcia? Che voleva? Siamo alle solite. Per tuo fratello, se non trovo lavoro, sono un vagabondo. Se lo cerco, devo sopportare le tue prediche. E sempre con quella faccia. Ma cosa vuoi che mi abbia detto? Si parlava di una barca che il marito vuole comprare. E si rivolge a te? E perché no? Io mi intendo di barche, dopo tutto. Beh, ma che cos'hai da guardarmi così? Ti senti male? Sono incinta, lo sai? Senti, se stai scherzando hai sbagliato il momento. Ma allora vieni a casa. Rosario è partito e ti dirò tutto. Devi ascoltarmi. Senti, smettiamola. Io sono stanco delle tue lacrime, della tua faccia, di averti sempre tra i piedi. Io sono stanco, hai capito? E poi finiamola una buona volta con questa storia. Non voglio camminare tutta la vita con un'ostrica attaccata al corpo. E poi ho da fare, va bene? Ma Francisco. Insomma, puoi fare quello che ti pare, se non ti vedrò più tanto di guadagnato. Se invece starai quieta, senza storie, cercheremo di... Insomma, ad ogni guaio c'è rimedio. Non mi sembra il caso di preoccuparsi. E poi potrei vederti ancora qualche volta, come in passato. Quella donna mi fece perdere la testa. Trascurai ogni prudenza. Sapeva baciare in un modo che non dimenticherò mai. Purtroppo Carmela mi aveva seguito e mi vide abbracciato con Carla e si sentì perduta. Abboe mi disse poi che l'aveva vista piangere disperatamente.", "words": [{"text": "Rosario?", "start": 3.519, "end": 4.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 4.299, "end": 5.5, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>", "start": 5.5, "end": 5.599, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 5.599, "end": 5.599, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mandò", "start": 5.599, "end": 5.859, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 5.859, "end": 5.879, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "via", "start": 5.879, "end": 6.019, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 6.019, "end": 6.039, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 6.039, "end": 6.119, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 6.119, "end": 6.139, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "casa,", "start": 6.139, "end": 6.499, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 6.499, "end": 7.039, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ma", "start": 7.039, "end": 7.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 7.159, "end": 7.159, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ci", "start": 7.159, "end": 7.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 7.299, "end": 7.319, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "vuole", "start": 7.319, "end": 7.519, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 7.519, "end": 7.519, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "altro", "start": 7.519, "end": 7.779, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 7.779, "end": 7.779, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "per", "start": 7.779, "end": 7.879, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 7.879, "end": 7.899, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "impressionarmi.", "start": 7.899, "end": 8.579, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 8.579, "end": 9.659, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 9.659, "end": 9.819, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 9.819, "end": 9.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "cominci<PERSON>", "start": 9.819, "end": 10.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 10.139, "end": 10.139, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "la", "start": 10.139, "end": 10.259, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 10.259, "end": 10.279, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "solita", "start": 10.279, "end": 10.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 10.559, "end": 10.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "vita", "start": 10.559, "end": 10.719, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 10.719, "end": 10.76, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "noiosa.", "start": 10.76, "end": 11.319, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 11.319, "end": 12.359, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "L'unica", "start": 12.359, "end": 12.679, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 12.679, "end": 12.699, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "distrazione", "start": 12.699, "end": 13.199, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 13.199, "end": 13.199, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "era", "start": 13.199, "end": 13.34, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 13.34, "end": 13.34, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "quella", "start": 13.34, "end": 13.519, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 13.519, "end": 13.519, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 13.519, "end": 13.619, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 13.619, "end": 13.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "guardare", "start": 13.639, "end": 14.019, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 14.019, "end": 14.019, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "le", "start": 14.019, "end": 14.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 14.159, "end": 14.179, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "liti", "start": 14.179, "end": 14.42, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 14.42, "end": 14.42, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "fra", "start": 14.42, "end": 14.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 14.559, "end": 14.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ingarsi", "start": 14.559, "end": 14.919, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 14.919, "end": 14.939, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "i", "start": 14.939, "end": 15.019, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 15.019, "end": 15.039, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "pescat<PERSON>,", "start": 15.039, "end": 15.679, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 15.679, "end": 15.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ma", "start": 15.819, "end": 15.919, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 15.919, "end": 15.92, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "non", "start": 15.92, "end": 16.02, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 16.02, "end": 16.039, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "parteggevo", "start": 16.039, "end": 16.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 16.479, "end": 16.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "per", "start": 16.479, "end": 16.599, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 16.599, "end": 16.6, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "nessuno.", "start": 16.6, "end": 17.04, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 17.04, "end": 18.079, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Pensavo", "start": 18.079, "end": 18.539, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 18.539, "end": 18.579, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "soltanto", "start": 18.579, "end": 18.959, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 18.959, "end": 18.959, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "a", "start": 18.959, "end": 19.079, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 19.079, "end": 19.1, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>", "start": 19.1, "end": 19.52, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 19.52, "end": 20.579, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "e", "start": 20.579, "end": 20.68, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 20.68, "end": 20.68, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "al", "start": 20.68, "end": 20.84, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 20.84, "end": 20.84, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "modo", "start": 20.84, "end": 21.079, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 21.079, "end": 21.1, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 21.1, "end": 21.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 21.159, "end": 21.18, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 21.18, "end": 21.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 21.479, "end": 21.5, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "a", "start": 21.5, "end": 21.54, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 21.54, "end": 21.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "lei.", "start": 21.559, "end": 21.859, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 21.859, "end": 21.859, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Non", "start": 21.859, "end": 22.719, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 22.719, "end": 22.739, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "deve", "start": 22.739, "end": 22.92, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 22.92, "end": 22.92, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 22.92, "end": 23.3, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 23.3, "end": 23.379, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "stato", "start": 23.379, "end": 23.659, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 23.659, "end": 23.659, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "difficile", "start": 23.659, "end": 24.219, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 24.219, "end": 24.239, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "diventare", "start": 24.239, "end": 24.699, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 24.699, "end": 24.719, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "il", "start": 24.719, "end": 24.78, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 24.78, "end": 24.84, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "suo", "start": 24.84, "end": 25.019, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 25.019, "end": 25.039, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "amante.", "start": 25.039, "end": 25.659, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 25.659, "end": 26.219, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 26.219, "end": 26.439, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 26.439, "end": 26.439, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ve", "start": 26.439, "end": 26.519, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 26.519, "end": 26.519, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "l'ha", "start": 26.519, "end": 26.619, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 26.619, "end": 26.619, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "raccontato", "start": 26.619, "end": 27.039, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 27.039, "end": 27.039, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "quello", "start": 27.039, "end": 27.219, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 27.219, "end": 27.219, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "spione", "start": 27.219, "end": 27.499, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 27.499, "end": 27.519, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 27.519, "end": 27.579, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 27.579, "end": 27.579, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Rosario,", "start": 27.579, "end": 27.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 27.979, "end": 27.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "vero?", "start": 27.979, "end": 28.319, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 28.319, "end": 29.219, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Cosa", "start": 29.219, "end": 29.419, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 29.419, "end": 29.459, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "succedere<PERSON>", "start": 29.459, "end": 29.959, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 29.959, "end": 29.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "se", "start": 29.979, "end": 30.079, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 30.079, "end": 30.099, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "io", "start": 30.099, "end": 30.179, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 30.179, "end": 30.179, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "dicessi", "start": 30.179, "end": 30.499, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 30.499, "end": 30.519, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 30.519, "end": 30.599, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 30.599, "end": 30.619, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "no?", "start": 30.619, "end": 30.86, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 30.86, "end": 30.959, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>.", "start": 30.959, "end": 31.459, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 31.459, "end": 31.739, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Lo", "start": 31.739, "end": 31.879, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 31.879, "end": 31.899, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "so", "start": 31.899, "end": 32.059, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 32.059, "end": 32.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "già.", "start": 32.119, "end": 32.419, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 32.419, "end": 35.52, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 35.52, "end": 36.08, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 36.08, "end": 36.719, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "te", "start": 36.719, "end": 36.86, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 36.86, "end": 36.86, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "l'ha", "start": 36.86, "end": 37.0, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 37.0, "end": 37.0, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "data", "start": 37.0, "end": 37.279, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 37.279, "end": 37.34, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "lei.", "start": 37.34, "end": 37.78, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 37.78, "end": 38.319, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Sì.", "start": 38.319, "end": 38.6, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 38.6, "end": 39.86, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Me", "start": 39.86, "end": 39.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 39.979, "end": 39.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "l'ha", "start": 39.979, "end": 40.1, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 40.1, "end": 40.139, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "regal<PERSON>", "start": 40.139, "end": 40.52, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 40.52, "end": 40.52, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "la", "start": 40.52, "end": 40.619, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 40.619, "end": 40.619, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "moglie", "start": 40.619, "end": 40.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 40.799, "end": 40.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 40.799, "end": 40.86, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 40.86, "end": 40.86, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "un", "start": 40.86, "end": 40.939, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 40.939, "end": 40.939, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>,", "start": 40.939, "end": 41.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 41.479, "end": 41.52, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "lo", "start": 41.52, "end": 41.619, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 41.619, "end": 41.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "stesso", "start": 41.639, "end": 41.9, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 41.9, "end": 41.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>ior<PERSON>", "start": 41.919, "end": 42.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 42.139, "end": 42.139, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "in", "start": 42.139, "end": 42.2, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 42.2, "end": 42.2, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "cui", "start": 42.2, "end": 42.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 42.299, "end": 42.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Rosario", "start": 42.299, "end": 42.619, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 42.619, "end": 42.619, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "partì", "start": 42.619, "end": 42.879, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 42.879, "end": 42.879, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "per", "start": 42.879, "end": 43.0, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 43.0, "end": 43.02, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Palermo.", "start": 43.02, "end": 43.519, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 43.519, "end": 44.419, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Incontrai", "start": 44.419, "end": 44.879, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 44.879, "end": 44.879, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>", "start": 44.879, "end": 45.119, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 45.119, "end": 45.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "al", "start": 45.119, "end": 45.2, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 45.2, "end": 45.239, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "porto.", "start": 45.239, "end": 45.619, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 45.619, "end": 45.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>", "start": 45.919, "end": 46.02, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 46.02, "end": 46.02, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "venne", "start": 46.02, "end": 46.219, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 46.219, "end": 46.219, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "vicino", "start": 46.219, "end": 46.52, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 46.52, "end": 46.599, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "come", "start": 46.599, "end": 46.74, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 46.74, "end": 46.779, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "se", "start": 46.779, "end": 46.879, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 46.879, "end": 46.879, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "fosse", "start": 46.879, "end": 47.099, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 47.099, "end": 47.099, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "la", "start": 47.099, "end": 47.18, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 47.18, "end": 47.18, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "cosa", "start": 47.18, "end": 47.36, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 47.36, "end": 47.399, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "più", "start": 47.399, "end": 47.52, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 47.52, "end": 47.52, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "naturale", "start": 47.52, "end": 47.86, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 47.86, "end": 47.879, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "del", "start": 47.879, "end": 47.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 47.979, "end": 48.039, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mondo.", "start": 48.039, "end": 48.36, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 48.36, "end": 49.259, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>", "start": 49.259, "end": 49.4, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 49.4, "end": 49.419, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "disse", "start": 49.419, "end": 49.619, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 49.619, "end": 49.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "che", "start": 49.639, "end": 49.719, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 49.719, "end": 49.719, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "andava", "start": 49.719, "end": 49.899, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 49.899, "end": 49.899, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "a", "start": 49.899, "end": 49.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 49.979, "end": 49.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "fare", "start": 49.979, "end": 50.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 50.139, "end": 50.139, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "il", "start": 50.139, "end": 50.22, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 50.22, "end": 50.239, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "bagno", "start": 50.239, "end": 50.419, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 50.419, "end": 50.419, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "alla", "start": 50.419, "end": 50.539, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 50.539, "end": 50.539, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "grotta", "start": 50.539, "end": 50.779, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 50.779, "end": 50.779, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 50.779, "end": 50.879, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 50.879, "end": 50.879, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>,", "start": 50.879, "end": 51.359, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 51.359, "end": 51.36, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "da", "start": 51.36, "end": 51.5, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 51.5, "end": 51.5, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "giù.", "start": 51.5, "end": 51.74, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 51.74, "end": 52.84, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Non", "start": 52.84, "end": 52.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 52.979, "end": 52.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ebbi", "start": 52.979, "end": 53.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 53.139, "end": 53.139, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 53.139, "end": 53.36, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 53.36, "end": 53.36, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "il", "start": 53.36, "end": 53.439, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 53.439, "end": 53.459, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "tempo", "start": 53.459, "end": 53.659, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 53.659, "end": 53.659, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 53.659, "end": 53.739, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 53.739, "end": 53.759, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "risponderle.", "start": 53.759, "end": 54.36, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 54.36, "end": 54.86, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>", "start": 54.86, "end": 54.939, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 54.939, "end": 54.959, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "accorsi", "start": 54.959, "end": 55.319, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 55.319, "end": 55.319, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "che", "start": 55.319, "end": 55.419, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 55.419, "end": 55.439, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Carmel<PERSON>", "start": 55.439, "end": 55.779, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 55.779, "end": 55.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "aveva", "start": 55.799, "end": 55.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 55.979, "end": 55.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "inteso", "start": 55.979, "end": 56.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 56.299, "end": 56.34, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "tutto", "start": 56.34, "end": 56.659, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 56.659, "end": 56.68, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "e", "start": 56.68, "end": 56.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 56.799, "end": 57.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "per", "start": 57.299, "end": 57.439, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 57.439, "end": 57.439, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "non", "start": 57.439, "end": 57.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 57.559, "end": 57.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "darle", "start": 57.559, "end": 57.72, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 57.72, "end": 57.759, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "so<PERSON><PERSON>", "start": 57.759, "end": 58.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 58.159, "end": 58.159, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mi", "start": 58.159, "end": 58.259, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 58.259, "end": 58.279, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "allontanai.", "start": 58.279, "end": 58.899, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 58.899, "end": 58.899, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Non", "start": 58.899, "end": 59.939, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 59.939, "end": 59.959, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "volevo", "start": 59.959, "end": 60.219, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 60.219, "end": 60.219, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "che", "start": 60.219, "end": 60.339, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 60.339, "end": 60.339, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sofferesse.", "start": 60.339, "end": 60.959, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 60.959, "end": 62.199, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Ma", "start": 62.199, "end": 62.319, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 62.319, "end": 62.339, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "era", "start": 62.339, "end": 62.459, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 62.459, "end": 62.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "molto", "start": 62.479, "end": 62.719, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 62.719, "end": 62.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "gelosa,", "start": 62.739, "end": 63.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 63.139, "end": 63.139, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "non", "start": 63.139, "end": 63.279, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 63.279, "end": 63.279, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "seppe", "start": 63.279, "end": 63.499, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 63.499, "end": 63.519, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "start": 63.519, "end": 64.059, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 64.059, "end": 64.079, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "e", "start": 64.079, "end": 64.179, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 64.179, "end": 64.179, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mi", "start": 64.179, "end": 64.279, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 64.279, "end": 64.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "fermò.", "start": 64.299, "end": 64.859, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 64.859, "end": 66.179, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "E", "start": 66.179, "end": 66.259, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 66.259, "end": 66.279, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "così", "start": 66.279, "end": 66.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 66.559, "end": 66.599, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "io,", "start": 66.599, "end": 66.759, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 66.759, "end": 66.759, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "eccitato", "start": 66.759, "end": 67.179, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 67.179, "end": 67.179, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "dal", "start": 67.179, "end": 67.319, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 67.319, "end": 67.319, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "pensiero", "start": 67.319, "end": 67.699, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 67.699, "end": 67.719, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "dell'invito", "start": 67.719, "end": 68.079, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 68.079, "end": 68.079, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 68.079, "end": 68.179, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 68.179, "end": 68.199, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>,", "start": 68.199, "end": 68.599, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 68.599, "end": 68.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "la", "start": 68.919, "end": 69.059, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 69.059, "end": 69.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "trattai", "start": 69.059, "end": 69.399, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 69.399, "end": 69.399, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>,", "start": 69.399, "end": 69.939, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 69.939, "end": 70.619, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "come", "start": 70.619, "end": 70.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 70.799, "end": 70.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "non", "start": 70.819, "end": 70.919, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 70.919, "end": 70.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "avrei", "start": 70.919, "end": 71.119, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 71.119, "end": 71.139, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "voluto.", "start": 71.139, "end": 71.519, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 71.519, "end": 100.619, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Francisco.", "start": 100.619, "end": 101.379, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 101.379, "end": 103.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Ah,", "start": 103.599, "end": 103.859, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 103.859, "end": 103.939, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sei", "start": 103.939, "end": 104.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 104.139, "end": 104.159, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "tu.", "start": 104.159, "end": 104.379, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 104.379, "end": 104.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Dove", "start": 104.639, "end": 104.839, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 104.839, "end": 104.859, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "vai?", "start": 104.859, "end": 105.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 105.199, "end": 106.079, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>", "start": 106.079, "end": 106.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 106.159, "end": 106.179, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "da", "start": 106.179, "end": 106.279, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 106.279, "end": 106.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "fare", "start": 106.299, "end": 106.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 106.479, "end": 106.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "alla", "start": 106.479, "end": 106.579, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 106.579, "end": 106.599, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "piccola", "start": 106.599, "end": 106.839, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 106.839, "end": 106.879, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 106.879, "end": 107.319, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 107.319, "end": 107.339, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "con", "start": 107.339, "end": 107.459, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 107.459, "end": 107.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "padron", "start": 107.479, "end": 107.739, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 107.739, "end": 107.759, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Grava.", "start": 107.759, "end": 108.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 108.159, "end": 108.859, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 108.859, "end": 109.079, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 109.079, "end": 109.079, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mi", "start": 109.079, "end": 109.179, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 109.179, "end": 109.199, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "affiderà", "start": 109.199, "end": 109.539, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 109.539, "end": 109.539, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "un", "start": 109.539, "end": 109.619, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 109.619, "end": 109.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "trasporto", "start": 109.639, "end": 110.059, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 110.059, "end": 110.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sul", "start": 110.059, "end": 110.219, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 110.219, "end": 110.239, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "continente", "start": 110.239, "end": 110.699, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 110.699, "end": 110.699, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "o", "start": 110.699, "end": 110.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 110.799, "end": 110.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "un", "start": 110.799, "end": 110.859, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 110.859, "end": 110.859, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "lavoro", "start": 110.859, "end": 111.099, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 111.099, "end": 111.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "qualsia<PERSON>,", "start": 111.119, "end": 111.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 111.559, "end": 111.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ancora", "start": 111.559, "end": 111.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 111.799, "end": 111.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "non", "start": 111.799, "end": 111.919, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 111.919, "end": 111.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "so", "start": 111.919, "end": 112.019, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 112.019, "end": 112.019, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "bene.", "start": 112.019, "end": 112.379, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 112.379, "end": 113.019, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Stavi", "start": 113.019, "end": 113.339, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 113.339, "end": 113.339, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 113.339, "end": 113.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 113.699, "end": 113.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "con", "start": 113.739, "end": 113.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 113.819, "end": 113.859, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "la", "start": 113.859, "end": 113.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 113.919, "end": 113.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "moglie", "start": 113.959, "end": 114.179, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 114.179, "end": 114.179, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 114.179, "end": 114.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 114.239, "end": 114.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "un", "start": 114.239, "end": 114.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 114.319, "end": 114.339, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>?", "start": 114.339, "end": 114.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 114.819, "end": 115.179, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Che", "start": 115.179, "end": 115.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 115.299, "end": 115.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "voleva?", "start": 115.299, "end": 115.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 115.779, "end": 116.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Siamo", "start": 116.019, "end": 116.179, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 116.179, "end": 116.179, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "alle", "start": 116.179, "end": 116.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 116.299, "end": 116.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "solite.", "start": 116.299, "end": 116.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 116.799, "end": 117.139, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Per", "start": 117.139, "end": 117.239, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 117.239, "end": 117.279, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "tuo", "start": 117.279, "end": 117.419, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 117.419, "end": 117.439, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "fratello,", "start": 117.439, "end": 117.779, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 117.779, "end": 117.779, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "se", "start": 117.779, "end": 117.859, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 117.859, "end": 117.879, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "non", "start": 117.879, "end": 117.959, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 117.959, "end": 117.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "trovo", "start": 117.979, "end": 118.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 118.159, "end": 118.179, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "lavoro,", "start": 118.179, "end": 118.419, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 118.419, "end": 118.419, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sono", "start": 118.419, "end": 118.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 118.559, "end": 118.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "un", "start": 118.559, "end": 118.619, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 118.619, "end": 118.619, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "vagabondo.", "start": 118.619, "end": 119.219, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 119.219, "end": 119.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Se", "start": 119.559, "end": 119.639, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 119.639, "end": 119.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "lo", "start": 119.639, "end": 119.739, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 119.739, "end": 119.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "cerco,", "start": 119.739, "end": 120.059, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 120.059, "end": 120.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "devo", "start": 120.059, "end": 120.199, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 120.199, "end": 120.199, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sopportare", "start": 120.199, "end": 120.619, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 120.619, "end": 120.619, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "le", "start": 120.619, "end": 120.719, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 120.719, "end": 120.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "tue", "start": 120.739, "end": 120.879, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 120.879, "end": 120.879, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "prediche.", "start": 120.879, "end": 121.379, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 121.379, "end": 121.679, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "E", "start": 121.679, "end": 121.759, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 121.759, "end": 121.779, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sempre", "start": 121.779, "end": 122.019, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 122.019, "end": 122.039, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "con", "start": 122.039, "end": 122.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 122.159, "end": 122.159, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "quella", "start": 122.159, "end": 122.339, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 122.339, "end": 122.359, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "faccia.", "start": 122.359, "end": 122.779, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 122.779, "end": 123.219, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Ma", "start": 123.219, "end": 123.319, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 123.319, "end": 123.339, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "cosa", "start": 123.339, "end": 123.499, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 123.499, "end": 123.499, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "vuoi", "start": 123.499, "end": 123.639, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 123.639, "end": 123.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "che", "start": 123.639, "end": 123.739, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 123.739, "end": 123.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mi", "start": 123.739, "end": 123.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 123.799, "end": 123.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "abbia", "start": 123.799, "end": 123.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 123.979, "end": 123.999, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "detto?", "start": 123.999, "end": 124.259, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 124.259, "end": 124.259, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Si", "start": 124.259, "end": 124.339, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 124.339, "end": 124.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "par<PERSON>", "start": 124.379, "end": 124.659, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 124.659, "end": 124.679, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 124.679, "end": 124.739, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 124.739, "end": 124.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "una", "start": 124.739, "end": 124.839, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 124.839, "end": 124.879, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "barca", "start": 124.879, "end": 125.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 125.139, "end": 125.139, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "che", "start": 125.139, "end": 125.239, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 125.239, "end": 125.239, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "il", "start": 125.239, "end": 125.279, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 125.279, "end": 125.319, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "marito", "start": 125.319, "end": 125.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 125.559, "end": 125.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "vuole", "start": 125.559, "end": 125.759, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 125.759, "end": 125.759, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "comprare.", "start": 125.759, "end": 126.199, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 126.199, "end": 126.199, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "E", "start": 126.199, "end": 126.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 126.319, "end": 126.339, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "si", "start": 126.339, "end": 126.439, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 126.439, "end": 126.439, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "rivolge", "start": 126.439, "end": 126.739, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 126.739, "end": 126.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 126.739, "end": 126.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 126.799, "end": 126.859, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "te?", "start": 126.859, "end": 127.079, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 127.079, "end": 127.759, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "E", "start": 127.759, "end": 127.859, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 127.859, "end": 127.859, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "perché", "start": 127.859, "end": 128.079, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 128.079, "end": 128.139, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "no?", "start": 128.139, "end": 128.319, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 128.319, "end": 128.939, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Io", "start": 128.939, "end": 129.039, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 129.039, "end": 129.079, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mi", "start": 129.079, "end": 129.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 129.139, "end": 129.139, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "intendo", "start": 129.139, "end": 129.459, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 129.459, "end": 129.459, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 129.459, "end": 129.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 129.559, "end": 129.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "barche,", "start": 129.559, "end": 129.839, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 129.839, "end": 129.839, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "dopo", "start": 129.839, "end": 130.019, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 130.019, "end": 130.039, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "tutto.", "start": 130.039, "end": 130.399, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 130.399, "end": 132.019, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON>,", "start": 132.019, "end": 132.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 132.139, "end": 132.139, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ma", "start": 132.139, "end": 132.239, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 132.239, "end": 132.239, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "che", "start": 132.239, "end": 132.339, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 132.339, "end": 132.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "cos'hai", "start": 132.379, "end": 132.579, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 132.579, "end": 132.599, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "da", "start": 132.599, "end": 132.679, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 132.679, "end": 132.679, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "guardarmi", "start": 132.679, "end": 133.039, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 133.039, "end": 133.079, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "così?", "start": 133.079, "end": 133.359, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 133.359, "end": 133.359, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Ti", "start": 133.359, "end": 133.459, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 133.459, "end": 133.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "senti", "start": 133.479, "end": 133.719, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 133.719, "end": 133.759, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "male?", "start": 133.759, "end": 134.119, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 134.119, "end": 134.579, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Sono", "start": 134.579, "end": 134.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 134.799, "end": 134.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "incinta,", "start": 134.819, "end": 135.339, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 135.339, "end": 135.359, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "lo", "start": 135.359, "end": 135.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 135.479, "end": 135.499, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sai?", "start": 135.499, "end": 135.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 135.859, "end": 137.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON>,", "start": 137.799, "end": 138.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 138.159, "end": 138.179, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "se", "start": 138.179, "end": 138.279, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 138.279, "end": 138.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "stai", "start": 138.299, "end": 138.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 138.479, "end": 138.499, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "scherzando", "start": 138.499, "end": 138.919, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 138.919, "end": 138.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "hai", "start": 138.919, "end": 139.019, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 139.019, "end": 139.019, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "s<PERSON><PERSON><PERSON>", "start": 139.019, "end": 139.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 139.299, "end": 139.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "il", "start": 139.299, "end": 139.359, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 139.359, "end": 139.399, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "momento.", "start": 139.399, "end": 139.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 139.799, "end": 139.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Ma", "start": 139.799, "end": 139.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 139.899, "end": 139.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "allora", "start": 139.919, "end": 140.179, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 140.179, "end": 140.179, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "vieni", "start": 140.179, "end": 140.419, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 140.419, "end": 140.439, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 140.439, "end": 140.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 140.459, "end": 140.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "casa.", "start": 140.539, "end": 140.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 140.919, "end": 140.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Rosario", "start": 140.979, "end": 141.399, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 141.399, "end": 141.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "è", "start": 141.459, "end": 141.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 141.479, "end": 141.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "partito", "start": 141.479, "end": 141.839, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 141.839, "end": 141.839, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "e", "start": 141.839, "end": 141.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 141.899, "end": 141.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ti", "start": 141.919, "end": 142.019, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 142.019, "end": 142.039, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "dirò", "start": 142.039, "end": 142.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 142.219, "end": 142.279, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tutto.", "start": 142.279, "end": 142.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 142.699, "end": 142.859, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>", "start": 142.859, "end": 143.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 143.139, "end": 143.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ascoltarmi.", "start": 143.139, "end": 143.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 143.959, "end": 144.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON>,", "start": 144.479, "end": 144.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 144.799, "end": 144.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "s<PERSON><PERSON><PERSON>.", "start": 144.799, "end": 145.519, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 145.519, "end": 145.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Io", "start": 145.819, "end": 145.919, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 145.919, "end": 145.939, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sono", "start": 145.939, "end": 146.119, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 146.119, "end": 146.139, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "stanco", "start": 146.139, "end": 146.499, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 146.499, "end": 146.519, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "delle", "start": 146.519, "end": 146.679, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 146.679, "end": 146.699, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "tue", "start": 146.699, "end": 146.779, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 146.779, "end": 146.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "lacrime,", "start": 146.799, "end": 147.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 147.139, "end": 147.139, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "della", "start": 147.139, "end": 147.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 147.299, "end": 147.319, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "tua", "start": 147.319, "end": 147.459, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 147.459, "end": 147.459, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "faccia,", "start": 147.459, "end": 147.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 147.799, "end": 147.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 147.799, "end": 147.859, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 147.859, "end": 147.879, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "averti", "start": 147.879, "end": 148.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 148.159, "end": 148.179, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sempre", "start": 148.179, "end": 148.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 148.479, "end": 148.499, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "tra", "start": 148.499, "end": 148.619, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 148.619, "end": 148.619, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "i", "start": 148.619, "end": 148.719, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 148.719, "end": 148.719, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "piedi.", "start": 148.719, "end": 149.059, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 149.059, "end": 149.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Io", "start": 149.059, "end": 149.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 149.139, "end": 149.199, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sono", "start": 149.199, "end": 149.359, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 149.359, "end": 149.399, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "stanco,", "start": 149.399, "end": 149.679, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 149.679, "end": 149.679, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "hai", "start": 149.679, "end": 149.779, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 149.779, "end": 149.779, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "capito?", "start": 149.779, "end": 150.199, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 150.199, "end": 151.999, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "E", "start": 151.999, "end": 152.099, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 152.099, "end": 152.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "poi", "start": 152.119, "end": 152.259, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 152.259, "end": 152.259, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "finiamola", "start": 152.259, "end": 152.679, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 152.679, "end": 152.679, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "una", "start": 152.679, "end": 152.819, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 152.819, "end": 152.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "buona", "start": 152.819, "end": 152.999, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 152.999, "end": 153.039, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "volta", "start": 153.039, "end": 153.259, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 153.259, "end": 153.259, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "con", "start": 153.259, "end": 153.379, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 153.379, "end": 153.399, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "questa", "start": 153.399, "end": 153.599, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 153.599, "end": 153.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "storia.", "start": 153.639, "end": 154.019, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 154.019, "end": 155.219, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Non", "start": 155.219, "end": 155.339, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 155.339, "end": 155.359, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "voglio", "start": 155.359, "end": 155.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 155.559, "end": 155.599, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "camminare", "start": 155.599, "end": 155.959, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 155.959, "end": 155.959, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "tutta", "start": 155.959, "end": 156.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 156.139, "end": 156.159, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "la", "start": 156.159, "end": 156.219, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 156.219, "end": 156.239, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "vita", "start": 156.239, "end": 156.399, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 156.399, "end": 156.399, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "con", "start": 156.399, "end": 156.519, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 156.519, "end": 156.519, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "un'ostrica", "start": 156.519, "end": 156.919, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 156.919, "end": 156.939, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "attaccata", "start": 156.939, "end": 157.239, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 157.239, "end": 157.239, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "al", "start": 157.239, "end": 157.319, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 157.319, "end": 157.359, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "corpo.", "start": 157.359, "end": 157.759, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 157.759, "end": 158.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "E", "start": 158.979, "end": 159.079, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 159.079, "end": 159.079, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "poi", "start": 159.079, "end": 159.239, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 159.239, "end": 159.239, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ho", "start": 159.239, "end": 159.319, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 159.319, "end": 159.319, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "da", "start": 159.319, "end": 159.439, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 159.439, "end": 159.459, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "fare,", "start": 159.459, "end": 159.679, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 159.679, "end": 159.699, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "va", "start": 159.699, "end": 159.759, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 159.759, "end": 159.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "bene?", "start": 159.819, "end": 160.099, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 160.099, "end": 160.499, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Ma", "start": 160.499, "end": 160.599, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 160.599, "end": 160.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Francisco.", "start": 160.659, "end": 161.499, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 161.499, "end": 162.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Insomma,", "start": 162.699, "end": 163.099, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 163.099, "end": 163.099, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "puoi", "start": 163.099, "end": 163.239, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 163.239, "end": 163.279, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "fare", "start": 163.279, "end": 163.439, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 163.439, "end": 163.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "quello", "start": 163.479, "end": 163.599, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 163.599, "end": 163.599, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "che", "start": 163.599, "end": 163.699, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 163.699, "end": 163.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ti", "start": 163.739, "end": 163.839, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 163.839, "end": 163.859, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "pare,", "start": 163.859, "end": 164.199, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 164.199, "end": 164.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "se", "start": 164.379, "end": 164.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 164.479, "end": 164.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "non", "start": 164.479, "end": 164.579, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 164.579, "end": 164.599, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ti", "start": 164.599, "end": 164.659, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 164.659, "end": 164.659, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "vedr<PERSON>", "start": 164.659, "end": 164.879, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 164.879, "end": 164.879, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "più", "start": 164.879, "end": 164.999, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 164.999, "end": 165.039, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "tanto", "start": 165.039, "end": 165.219, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 165.219, "end": 165.219, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 165.219, "end": 165.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 165.299, "end": 165.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "guadagnato.", "start": 165.299, "end": 165.899, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 165.899, "end": 166.339, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Se", "start": 166.339, "end": 166.439, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 166.439, "end": 166.439, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "invece", "start": 166.439, "end": 166.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 166.799, "end": 167.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 167.479, "end": 167.779, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 167.779, "end": 167.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "quieta,", "start": 167.799, "end": 168.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 168.299, "end": 168.659, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "senza", "start": 168.659, "end": 168.959, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 168.959, "end": 168.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "storie,", "start": 168.979, "end": 169.439, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 169.439, "end": 170.019, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "cercheremo", "start": 170.019, "end": 170.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 170.559, "end": 170.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di...", "start": 170.559, "end": 171.039, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 171.039, "end": 171.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Insomma,", "start": 171.979, "end": 172.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 172.559, "end": 173.359, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ad", "start": 173.359, "end": 173.519, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 173.519, "end": 173.519, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ogni", "start": 173.519, "end": 173.699, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 173.699, "end": 173.699, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "guaio", "start": 173.699, "end": 173.999, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 173.999, "end": 174.019, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "c'è", "start": 174.019, "end": 174.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 174.159, "end": 174.159, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "rimedio.", "start": 174.159, "end": 174.699, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 174.699, "end": 176.439, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Non", "start": 176.439, "end": 176.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 176.559, "end": 176.579, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mi", "start": 176.579, "end": 176.619, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 176.619, "end": 176.659, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sembra", "start": 176.659, "end": 176.879, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 176.879, "end": 176.879, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "il", "start": 176.879, "end": 176.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 176.979, "end": 176.999, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "caso", "start": 176.999, "end": 177.219, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 177.219, "end": 177.219, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 177.219, "end": 177.319, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 177.319, "end": 177.319, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "preoccup<PERSON>i.", "start": 177.319, "end": 178.079, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 178.079, "end": 179.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "E", "start": 179.299, "end": 179.439, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 179.439, "end": 179.459, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "poi", "start": 179.459, "end": 179.679, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 179.679, "end": 179.699, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "potrei", "start": 179.699, "end": 179.959, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 179.959, "end": 179.959, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ve<PERSON><PERSON>", "start": 179.959, "end": 180.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 180.299, "end": 180.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ancora", "start": 180.299, "end": 180.539, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 180.539, "end": 180.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "qualche", "start": 180.559, "end": 180.779, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 180.779, "end": 180.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "volta,", "start": 180.819, "end": 181.199, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 181.199, "end": 181.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "come", "start": 181.299, "end": 181.439, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 181.439, "end": 181.439, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "in", "start": 181.439, "end": 181.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 181.559, "end": 181.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "passato.", "start": 181.559, "end": 182.119, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 182.119, "end": 186.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 186.379, "end": 186.599, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 186.599, "end": 186.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "donna", "start": 186.639, "end": 186.859, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 186.859, "end": 186.859, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mi", "start": 186.859, "end": 186.959, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 186.959, "end": 186.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "fece", "start": 186.979, "end": 187.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 187.159, "end": 187.179, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "perdere", "start": 187.179, "end": 187.519, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 187.519, "end": 187.519, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "la", "start": 187.519, "end": 187.639, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 187.639, "end": 187.659, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "testa.", "start": 187.659, "end": 188.059, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 188.059, "end": 188.599, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Trascurai", "start": 188.599, "end": 189.079, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 189.079, "end": 189.079, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ogni", "start": 189.079, "end": 189.279, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 189.279, "end": 189.279, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "prudenza.", "start": 189.279, "end": 189.819, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 189.819, "end": 190.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Sa<PERSON><PERSON>", "start": 190.059, "end": 190.399, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 190.399, "end": 190.399, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "baciare", "start": 190.399, "end": 190.739, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 190.739, "end": 190.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "in", "start": 190.739, "end": 190.819, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 190.819, "end": 190.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "un", "start": 190.819, "end": 190.899, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 190.899, "end": 190.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "modo", "start": 190.919, "end": 191.119, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 191.119, "end": 191.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "che", "start": 191.119, "end": 191.219, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 191.219, "end": 191.239, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "non", "start": 191.239, "end": 191.319, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 191.319, "end": 191.339, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "dimenticherò", "start": 191.339, "end": 191.819, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 191.819, "end": 191.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mai.", "start": 191.819, "end": 192.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 192.159, "end": 192.319, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 192.319, "end": 192.759, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 192.759, "end": 192.779, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Carmel<PERSON>", "start": 192.779, "end": 193.119, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 193.119, "end": 193.139, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mi", "start": 193.139, "end": 193.199, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 193.199, "end": 193.219, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "aveva", "start": 193.219, "end": 193.439, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 193.439, "end": 193.459, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 193.459, "end": 193.839, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 193.839, "end": 195.759, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "e", "start": 195.759, "end": 195.859, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 195.859, "end": 195.859, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mi", "start": 195.859, "end": 195.959, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 195.959, "end": 195.959, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "vide", "start": 195.959, "end": 196.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 196.139, "end": 196.159, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "abbracciato", "start": 196.159, "end": 196.579, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 196.579, "end": 196.579, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "con", "start": 196.579, "end": 196.719, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 196.719, "end": 196.759, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>", "start": 196.759, "end": 197.099, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 197.099, "end": 197.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "e", "start": 197.479, "end": 197.579, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 197.579, "end": 197.599, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "si", "start": 197.599, "end": 197.719, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 197.719, "end": 197.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sent<PERSON>", "start": 197.739, "end": 197.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 197.979, "end": 197.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "perduta.", "start": 197.979, "end": 198.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 198.559, "end": 214.759, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 214.759, "end": 215.099, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 215.099, "end": 215.099, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mi", "start": 215.099, "end": 215.219, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 215.219, "end": 215.219, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "disse", "start": 215.219, "end": 215.499, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 215.499, "end": 215.519, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "poi", "start": 215.519, "end": 215.679, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 215.679, "end": 215.699, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "che", "start": 215.699, "end": 215.779, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 215.779, "end": 215.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "l'aveva", "start": 215.799, "end": 216.079, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 216.079, "end": 216.099, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "vista", "start": 216.099, "end": 216.359, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 216.359, "end": 216.399, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 216.399, "end": 216.839, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 216.839, "end": 216.839, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "disperatamente.", "start": 216.839, "end": 217.719, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}], "language_detected": null, "confidence": null, "processing_time": 20.8595232963562, "metadata": {"service": "elevenlabs", "mode": "free", "model": "scribe_v1", "raw_result": {"language_code": "ita", "language_probability": 0.9758124351501465, "text": "Rosario? Mi mandò via di casa, ma ci vuole altro per impressionarmi. Poi cominciò la solita vita noiosa. L'unica distrazione era quella di guardare le liti fra ingarsi i pescatori, ma non parteggevo per nessuno. Pensavo soltanto a Carla e al modo di arrivare a lei. Non deve esserti stato difficile diventare il suo amante. Questo ve l'ha raccontato quello spione di Rosario, vero? Cosa succederebbe se io dicessi di no? Nulla. Lo so già. Questa te l'ha data lei. Sì. Me l'ha regalato la moglie di un Garcia, lo stesso giorno in cui Rosario partì per Palermo. Incontrai Carla al porto. Mi venne vicino come se fosse la cosa più naturale del mondo. Mi disse che andava a fare il bagno alla grotta di Ponente, da giù. Non ebbi nemmeno il tempo di risponderle. Mi accorsi che Carmela aveva inteso tutto e per non darle sospetto mi allontanai. Non volevo che sofferesse. Ma era molto gelosa, non seppe trattenersi e mi fermò. E così io, eccitato dal pensiero dell'invito di Carla, la trattai duramente, come non avrei voluto. Francisco. Ah, sei tu. Dove vai? Ho da fare alla piccola Darsena con padron Grava. Forse mi affiderà un trasporto sul continente o un lavoro qualsiasi, ancora non so bene. Stavi parlando con la moglie di un Garcia? Che voleva? Siamo alle solite. Per tuo fratello, se non trovo lavoro, sono un vagabondo. Se lo cerco, devo sopportare le tue prediche. E sempre con quella faccia. Ma cosa vuoi che mi abbia detto? Si parlava di una barca che il marito vuole comprare. E si rivolge a te? E perché no? Io mi intendo di barche, dopo tutto. Beh, ma che cos'hai da guardarmi così? Ti senti male? Sono incinta, lo sai? Senti, se stai scherzando hai sbagliato il momento. Ma allora vieni a casa. Rosario è partito e ti dirò tutto. Devi ascoltarmi. Senti, smettiamola. Io sono stanco delle tue lacrime, della tua faccia, di averti sempre tra i piedi. Io sono stanco, hai capito? E poi finiamola una buona volta con questa storia. Non voglio camminare tutta la vita con un'ostrica attaccata al corpo. E poi ho da fare, va bene? Ma Francisco. Insomma, puoi fare quello che ti pare, se non ti vedrò più tanto di guadagnato. Se invece starai quieta, senza storie, cercheremo di... Insomma, ad ogni guaio c'è rimedio. Non mi sembra il caso di preoccuparsi. E poi potrei vederti ancora qualche volta, come in passato. Quella donna mi fece perdere la testa. Trascurai ogni prudenza. Sapeva baciare in un modo che non dimenticherò mai. Purtroppo Carmela mi aveva seguito e mi vide abbracciato con Carla e si sentì perduta. Abboe mi disse poi che l'aveva vista piangere disperatamente.", "words": [{"text": "Rosario?", "start": 3.519, "end": 4.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 4.299, "end": 5.5, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>", "start": 5.5, "end": 5.599, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 5.599, "end": 5.599, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mandò", "start": 5.599, "end": 5.859, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 5.859, "end": 5.879, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "via", "start": 5.879, "end": 6.019, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 6.019, "end": 6.039, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 6.039, "end": 6.119, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 6.119, "end": 6.139, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "casa,", "start": 6.139, "end": 6.499, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 6.499, "end": 7.039, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ma", "start": 7.039, "end": 7.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 7.159, "end": 7.159, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ci", "start": 7.159, "end": 7.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 7.299, "end": 7.319, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "vuole", "start": 7.319, "end": 7.519, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 7.519, "end": 7.519, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "altro", "start": 7.519, "end": 7.779, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 7.779, "end": 7.779, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "per", "start": 7.779, "end": 7.879, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 7.879, "end": 7.899, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "impressionarmi.", "start": 7.899, "end": 8.579, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 8.579, "end": 9.659, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 9.659, "end": 9.819, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 9.819, "end": 9.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "cominci<PERSON>", "start": 9.819, "end": 10.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 10.139, "end": 10.139, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "la", "start": 10.139, "end": 10.259, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 10.259, "end": 10.279, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "solita", "start": 10.279, "end": 10.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 10.559, "end": 10.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "vita", "start": 10.559, "end": 10.719, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 10.719, "end": 10.76, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "noiosa.", "start": 10.76, "end": 11.319, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 11.319, "end": 12.359, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "L'unica", "start": 12.359, "end": 12.679, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 12.679, "end": 12.699, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "distrazione", "start": 12.699, "end": 13.199, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 13.199, "end": 13.199, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "era", "start": 13.199, "end": 13.34, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 13.34, "end": 13.34, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "quella", "start": 13.34, "end": 13.519, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 13.519, "end": 13.519, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 13.519, "end": 13.619, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 13.619, "end": 13.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "guardare", "start": 13.639, "end": 14.019, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 14.019, "end": 14.019, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "le", "start": 14.019, "end": 14.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 14.159, "end": 14.179, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "liti", "start": 14.179, "end": 14.42, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 14.42, "end": 14.42, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "fra", "start": 14.42, "end": 14.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 14.559, "end": 14.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ingarsi", "start": 14.559, "end": 14.919, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 14.919, "end": 14.939, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "i", "start": 14.939, "end": 15.019, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 15.019, "end": 15.039, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "pescat<PERSON>,", "start": 15.039, "end": 15.679, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 15.679, "end": 15.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ma", "start": 15.819, "end": 15.919, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 15.919, "end": 15.92, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "non", "start": 15.92, "end": 16.02, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 16.02, "end": 16.039, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "parteggevo", "start": 16.039, "end": 16.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 16.479, "end": 16.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "per", "start": 16.479, "end": 16.599, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 16.599, "end": 16.6, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "nessuno.", "start": 16.6, "end": 17.04, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 17.04, "end": 18.079, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Pensavo", "start": 18.079, "end": 18.539, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 18.539, "end": 18.579, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "soltanto", "start": 18.579, "end": 18.959, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 18.959, "end": 18.959, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "a", "start": 18.959, "end": 19.079, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 19.079, "end": 19.1, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>", "start": 19.1, "end": 19.52, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 19.52, "end": 20.579, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "e", "start": 20.579, "end": 20.68, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 20.68, "end": 20.68, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "al", "start": 20.68, "end": 20.84, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 20.84, "end": 20.84, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "modo", "start": 20.84, "end": 21.079, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 21.079, "end": 21.1, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 21.1, "end": 21.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 21.159, "end": 21.18, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 21.18, "end": 21.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 21.479, "end": 21.5, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "a", "start": 21.5, "end": 21.54, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 21.54, "end": 21.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "lei.", "start": 21.559, "end": 21.859, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 21.859, "end": 21.859, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Non", "start": 21.859, "end": 22.719, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 22.719, "end": 22.739, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "deve", "start": 22.739, "end": 22.92, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 22.92, "end": 22.92, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 22.92, "end": 23.3, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 23.3, "end": 23.379, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "stato", "start": 23.379, "end": 23.659, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 23.659, "end": 23.659, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "difficile", "start": 23.659, "end": 24.219, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 24.219, "end": 24.239, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "diventare", "start": 24.239, "end": 24.699, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 24.699, "end": 24.719, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "il", "start": 24.719, "end": 24.78, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 24.78, "end": 24.84, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "suo", "start": 24.84, "end": 25.019, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 25.019, "end": 25.039, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "amante.", "start": 25.039, "end": 25.659, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 25.659, "end": 26.219, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 26.219, "end": 26.439, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 26.439, "end": 26.439, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ve", "start": 26.439, "end": 26.519, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 26.519, "end": 26.519, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "l'ha", "start": 26.519, "end": 26.619, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 26.619, "end": 26.619, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "raccontato", "start": 26.619, "end": 27.039, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 27.039, "end": 27.039, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "quello", "start": 27.039, "end": 27.219, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 27.219, "end": 27.219, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "spione", "start": 27.219, "end": 27.499, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 27.499, "end": 27.519, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 27.519, "end": 27.579, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 27.579, "end": 27.579, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Rosario,", "start": 27.579, "end": 27.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 27.979, "end": 27.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "vero?", "start": 27.979, "end": 28.319, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 28.319, "end": 29.219, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Cosa", "start": 29.219, "end": 29.419, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 29.419, "end": 29.459, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "succedere<PERSON>", "start": 29.459, "end": 29.959, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 29.959, "end": 29.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "se", "start": 29.979, "end": 30.079, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 30.079, "end": 30.099, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "io", "start": 30.099, "end": 30.179, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 30.179, "end": 30.179, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "dicessi", "start": 30.179, "end": 30.499, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 30.499, "end": 30.519, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 30.519, "end": 30.599, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 30.599, "end": 30.619, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "no?", "start": 30.619, "end": 30.86, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 30.86, "end": 30.959, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>.", "start": 30.959, "end": 31.459, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 31.459, "end": 31.739, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Lo", "start": 31.739, "end": 31.879, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 31.879, "end": 31.899, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "so", "start": 31.899, "end": 32.059, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 32.059, "end": 32.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "già.", "start": 32.119, "end": 32.419, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 32.419, "end": 35.52, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 35.52, "end": 36.08, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 36.08, "end": 36.719, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "te", "start": 36.719, "end": 36.86, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 36.86, "end": 36.86, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "l'ha", "start": 36.86, "end": 37.0, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 37.0, "end": 37.0, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "data", "start": 37.0, "end": 37.279, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 37.279, "end": 37.34, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "lei.", "start": 37.34, "end": 37.78, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 37.78, "end": 38.319, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Sì.", "start": 38.319, "end": 38.6, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 38.6, "end": 39.86, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Me", "start": 39.86, "end": 39.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 39.979, "end": 39.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "l'ha", "start": 39.979, "end": 40.1, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 40.1, "end": 40.139, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "regal<PERSON>", "start": 40.139, "end": 40.52, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 40.52, "end": 40.52, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "la", "start": 40.52, "end": 40.619, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 40.619, "end": 40.619, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "moglie", "start": 40.619, "end": 40.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 40.799, "end": 40.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 40.799, "end": 40.86, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 40.86, "end": 40.86, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "un", "start": 40.86, "end": 40.939, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 40.939, "end": 40.939, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>,", "start": 40.939, "end": 41.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 41.479, "end": 41.52, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "lo", "start": 41.52, "end": 41.619, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 41.619, "end": 41.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "stesso", "start": 41.639, "end": 41.9, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 41.9, "end": 41.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>ior<PERSON>", "start": 41.919, "end": 42.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 42.139, "end": 42.139, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "in", "start": 42.139, "end": 42.2, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 42.2, "end": 42.2, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "cui", "start": 42.2, "end": 42.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 42.299, "end": 42.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Rosario", "start": 42.299, "end": 42.619, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 42.619, "end": 42.619, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "partì", "start": 42.619, "end": 42.879, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 42.879, "end": 42.879, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "per", "start": 42.879, "end": 43.0, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 43.0, "end": 43.02, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Palermo.", "start": 43.02, "end": 43.519, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 43.519, "end": 44.419, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Incontrai", "start": 44.419, "end": 44.879, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 44.879, "end": 44.879, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>", "start": 44.879, "end": 45.119, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 45.119, "end": 45.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "al", "start": 45.119, "end": 45.2, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 45.2, "end": 45.239, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "porto.", "start": 45.239, "end": 45.619, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 45.619, "end": 45.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>", "start": 45.919, "end": 46.02, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 46.02, "end": 46.02, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "venne", "start": 46.02, "end": 46.219, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 46.219, "end": 46.219, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "vicino", "start": 46.219, "end": 46.52, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 46.52, "end": 46.599, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "come", "start": 46.599, "end": 46.74, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 46.74, "end": 46.779, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "se", "start": 46.779, "end": 46.879, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 46.879, "end": 46.879, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "fosse", "start": 46.879, "end": 47.099, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 47.099, "end": 47.099, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "la", "start": 47.099, "end": 47.18, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 47.18, "end": 47.18, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "cosa", "start": 47.18, "end": 47.36, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 47.36, "end": 47.399, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "più", "start": 47.399, "end": 47.52, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 47.52, "end": 47.52, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "naturale", "start": 47.52, "end": 47.86, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 47.86, "end": 47.879, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "del", "start": 47.879, "end": 47.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 47.979, "end": 48.039, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mondo.", "start": 48.039, "end": 48.36, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 48.36, "end": 49.259, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>", "start": 49.259, "end": 49.4, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 49.4, "end": 49.419, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "disse", "start": 49.419, "end": 49.619, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 49.619, "end": 49.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "che", "start": 49.639, "end": 49.719, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 49.719, "end": 49.719, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "andava", "start": 49.719, "end": 49.899, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 49.899, "end": 49.899, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "a", "start": 49.899, "end": 49.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 49.979, "end": 49.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "fare", "start": 49.979, "end": 50.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 50.139, "end": 50.139, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "il", "start": 50.139, "end": 50.22, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 50.22, "end": 50.239, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "bagno", "start": 50.239, "end": 50.419, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 50.419, "end": 50.419, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "alla", "start": 50.419, "end": 50.539, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 50.539, "end": 50.539, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "grotta", "start": 50.539, "end": 50.779, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 50.779, "end": 50.779, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 50.779, "end": 50.879, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 50.879, "end": 50.879, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>,", "start": 50.879, "end": 51.359, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 51.359, "end": 51.36, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "da", "start": 51.36, "end": 51.5, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 51.5, "end": 51.5, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "giù.", "start": 51.5, "end": 51.74, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 51.74, "end": 52.84, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Non", "start": 52.84, "end": 52.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 52.979, "end": 52.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ebbi", "start": 52.979, "end": 53.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 53.139, "end": 53.139, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 53.139, "end": 53.36, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 53.36, "end": 53.36, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "il", "start": 53.36, "end": 53.439, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 53.439, "end": 53.459, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "tempo", "start": 53.459, "end": 53.659, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 53.659, "end": 53.659, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 53.659, "end": 53.739, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 53.739, "end": 53.759, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "risponderle.", "start": 53.759, "end": 54.36, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 54.36, "end": 54.86, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>", "start": 54.86, "end": 54.939, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 54.939, "end": 54.959, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "accorsi", "start": 54.959, "end": 55.319, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 55.319, "end": 55.319, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "che", "start": 55.319, "end": 55.419, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 55.419, "end": 55.439, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Carmel<PERSON>", "start": 55.439, "end": 55.779, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 55.779, "end": 55.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "aveva", "start": 55.799, "end": 55.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 55.979, "end": 55.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "inteso", "start": 55.979, "end": 56.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 56.299, "end": 56.34, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "tutto", "start": 56.34, "end": 56.659, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 56.659, "end": 56.68, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "e", "start": 56.68, "end": 56.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 56.799, "end": 57.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "per", "start": 57.299, "end": 57.439, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 57.439, "end": 57.439, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "non", "start": 57.439, "end": 57.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 57.559, "end": 57.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "darle", "start": 57.559, "end": 57.72, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 57.72, "end": 57.759, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "so<PERSON><PERSON>", "start": 57.759, "end": 58.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 58.159, "end": 58.159, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mi", "start": 58.159, "end": 58.259, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 58.259, "end": 58.279, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "allontanai.", "start": 58.279, "end": 58.899, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 58.899, "end": 58.899, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Non", "start": 58.899, "end": 59.939, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 59.939, "end": 59.959, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "volevo", "start": 59.959, "end": 60.219, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 60.219, "end": 60.219, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "che", "start": 60.219, "end": 60.339, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 60.339, "end": 60.339, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sofferesse.", "start": 60.339, "end": 60.959, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 60.959, "end": 62.199, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Ma", "start": 62.199, "end": 62.319, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 62.319, "end": 62.339, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "era", "start": 62.339, "end": 62.459, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 62.459, "end": 62.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "molto", "start": 62.479, "end": 62.719, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 62.719, "end": 62.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "gelosa,", "start": 62.739, "end": 63.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 63.139, "end": 63.139, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "non", "start": 63.139, "end": 63.279, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 63.279, "end": 63.279, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "seppe", "start": 63.279, "end": 63.499, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 63.499, "end": 63.519, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "start": 63.519, "end": 64.059, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 64.059, "end": 64.079, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "e", "start": 64.079, "end": 64.179, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 64.179, "end": 64.179, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mi", "start": 64.179, "end": 64.279, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 64.279, "end": 64.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "fermò.", "start": 64.299, "end": 64.859, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 64.859, "end": 66.179, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "E", "start": 66.179, "end": 66.259, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 66.259, "end": 66.279, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "così", "start": 66.279, "end": 66.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 66.559, "end": 66.599, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "io,", "start": 66.599, "end": 66.759, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 66.759, "end": 66.759, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "eccitato", "start": 66.759, "end": 67.179, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 67.179, "end": 67.179, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "dal", "start": 67.179, "end": 67.319, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 67.319, "end": 67.319, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "pensiero", "start": 67.319, "end": 67.699, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 67.699, "end": 67.719, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "dell'invito", "start": 67.719, "end": 68.079, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 68.079, "end": 68.079, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 68.079, "end": 68.179, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 68.179, "end": 68.199, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>,", "start": 68.199, "end": 68.599, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 68.599, "end": 68.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "la", "start": 68.919, "end": 69.059, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 69.059, "end": 69.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "trattai", "start": 69.059, "end": 69.399, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 69.399, "end": 69.399, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>,", "start": 69.399, "end": 69.939, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 69.939, "end": 70.619, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "come", "start": 70.619, "end": 70.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 70.799, "end": 70.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "non", "start": 70.819, "end": 70.919, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 70.919, "end": 70.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "avrei", "start": 70.919, "end": 71.119, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 71.119, "end": 71.139, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "voluto.", "start": 71.139, "end": 71.519, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 71.519, "end": 100.619, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Francisco.", "start": 100.619, "end": 101.379, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 101.379, "end": 103.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Ah,", "start": 103.599, "end": 103.859, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 103.859, "end": 103.939, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sei", "start": 103.939, "end": 104.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 104.139, "end": 104.159, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "tu.", "start": 104.159, "end": 104.379, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 104.379, "end": 104.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Dove", "start": 104.639, "end": 104.839, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 104.839, "end": 104.859, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "vai?", "start": 104.859, "end": 105.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 105.199, "end": 106.079, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>", "start": 106.079, "end": 106.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 106.159, "end": 106.179, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "da", "start": 106.179, "end": 106.279, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 106.279, "end": 106.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "fare", "start": 106.299, "end": 106.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 106.479, "end": 106.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "alla", "start": 106.479, "end": 106.579, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 106.579, "end": 106.599, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "piccola", "start": 106.599, "end": 106.839, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 106.839, "end": 106.879, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 106.879, "end": 107.319, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 107.319, "end": 107.339, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "con", "start": 107.339, "end": 107.459, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 107.459, "end": 107.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "padron", "start": 107.479, "end": 107.739, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 107.739, "end": 107.759, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Grava.", "start": 107.759, "end": 108.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 108.159, "end": 108.859, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 108.859, "end": 109.079, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 109.079, "end": 109.079, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mi", "start": 109.079, "end": 109.179, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 109.179, "end": 109.199, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "affiderà", "start": 109.199, "end": 109.539, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 109.539, "end": 109.539, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "un", "start": 109.539, "end": 109.619, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 109.619, "end": 109.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "trasporto", "start": 109.639, "end": 110.059, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 110.059, "end": 110.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sul", "start": 110.059, "end": 110.219, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 110.219, "end": 110.239, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "continente", "start": 110.239, "end": 110.699, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 110.699, "end": 110.699, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "o", "start": 110.699, "end": 110.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 110.799, "end": 110.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "un", "start": 110.799, "end": 110.859, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 110.859, "end": 110.859, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "lavoro", "start": 110.859, "end": 111.099, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 111.099, "end": 111.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "qualsia<PERSON>,", "start": 111.119, "end": 111.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 111.559, "end": 111.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ancora", "start": 111.559, "end": 111.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 111.799, "end": 111.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "non", "start": 111.799, "end": 111.919, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 111.919, "end": 111.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "so", "start": 111.919, "end": 112.019, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 112.019, "end": 112.019, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "bene.", "start": 112.019, "end": 112.379, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 112.379, "end": 113.019, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Stavi", "start": 113.019, "end": 113.339, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 113.339, "end": 113.339, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 113.339, "end": 113.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 113.699, "end": 113.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "con", "start": 113.739, "end": 113.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 113.819, "end": 113.859, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "la", "start": 113.859, "end": 113.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 113.919, "end": 113.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "moglie", "start": 113.959, "end": 114.179, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 114.179, "end": 114.179, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 114.179, "end": 114.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 114.239, "end": 114.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "un", "start": 114.239, "end": 114.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 114.319, "end": 114.339, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>?", "start": 114.339, "end": 114.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 114.819, "end": 115.179, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Che", "start": 115.179, "end": 115.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 115.299, "end": 115.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "voleva?", "start": 115.299, "end": 115.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 115.779, "end": 116.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Siamo", "start": 116.019, "end": 116.179, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 116.179, "end": 116.179, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "alle", "start": 116.179, "end": 116.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 116.299, "end": 116.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "solite.", "start": 116.299, "end": 116.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 116.799, "end": 117.139, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Per", "start": 117.139, "end": 117.239, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 117.239, "end": 117.279, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "tuo", "start": 117.279, "end": 117.419, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 117.419, "end": 117.439, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "fratello,", "start": 117.439, "end": 117.779, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 117.779, "end": 117.779, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "se", "start": 117.779, "end": 117.859, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 117.859, "end": 117.879, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "non", "start": 117.879, "end": 117.959, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 117.959, "end": 117.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "trovo", "start": 117.979, "end": 118.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 118.159, "end": 118.179, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "lavoro,", "start": 118.179, "end": 118.419, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 118.419, "end": 118.419, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sono", "start": 118.419, "end": 118.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 118.559, "end": 118.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "un", "start": 118.559, "end": 118.619, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 118.619, "end": 118.619, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "vagabondo.", "start": 118.619, "end": 119.219, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 119.219, "end": 119.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Se", "start": 119.559, "end": 119.639, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 119.639, "end": 119.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "lo", "start": 119.639, "end": 119.739, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 119.739, "end": 119.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "cerco,", "start": 119.739, "end": 120.059, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 120.059, "end": 120.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "devo", "start": 120.059, "end": 120.199, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 120.199, "end": 120.199, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sopportare", "start": 120.199, "end": 120.619, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 120.619, "end": 120.619, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "le", "start": 120.619, "end": 120.719, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 120.719, "end": 120.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "tue", "start": 120.739, "end": 120.879, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 120.879, "end": 120.879, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "prediche.", "start": 120.879, "end": 121.379, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 121.379, "end": 121.679, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "E", "start": 121.679, "end": 121.759, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 121.759, "end": 121.779, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sempre", "start": 121.779, "end": 122.019, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 122.019, "end": 122.039, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "con", "start": 122.039, "end": 122.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 122.159, "end": 122.159, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "quella", "start": 122.159, "end": 122.339, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 122.339, "end": 122.359, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "faccia.", "start": 122.359, "end": 122.779, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 122.779, "end": 123.219, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Ma", "start": 123.219, "end": 123.319, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 123.319, "end": 123.339, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "cosa", "start": 123.339, "end": 123.499, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 123.499, "end": 123.499, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "vuoi", "start": 123.499, "end": 123.639, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 123.639, "end": 123.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "che", "start": 123.639, "end": 123.739, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 123.739, "end": 123.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mi", "start": 123.739, "end": 123.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 123.799, "end": 123.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "abbia", "start": 123.799, "end": 123.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 123.979, "end": 123.999, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "detto?", "start": 123.999, "end": 124.259, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 124.259, "end": 124.259, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Si", "start": 124.259, "end": 124.339, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 124.339, "end": 124.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "par<PERSON>", "start": 124.379, "end": 124.659, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 124.659, "end": 124.679, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 124.679, "end": 124.739, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 124.739, "end": 124.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "una", "start": 124.739, "end": 124.839, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 124.839, "end": 124.879, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "barca", "start": 124.879, "end": 125.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 125.139, "end": 125.139, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "che", "start": 125.139, "end": 125.239, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 125.239, "end": 125.239, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "il", "start": 125.239, "end": 125.279, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 125.279, "end": 125.319, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "marito", "start": 125.319, "end": 125.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 125.559, "end": 125.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "vuole", "start": 125.559, "end": 125.759, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 125.759, "end": 125.759, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "comprare.", "start": 125.759, "end": 126.199, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 126.199, "end": 126.199, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "E", "start": 126.199, "end": 126.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 126.319, "end": 126.339, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "si", "start": 126.339, "end": 126.439, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 126.439, "end": 126.439, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "rivolge", "start": 126.439, "end": 126.739, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 126.739, "end": 126.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 126.739, "end": 126.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 126.799, "end": 126.859, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "te?", "start": 126.859, "end": 127.079, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 127.079, "end": 127.759, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "E", "start": 127.759, "end": 127.859, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 127.859, "end": 127.859, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "perché", "start": 127.859, "end": 128.079, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 128.079, "end": 128.139, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "no?", "start": 128.139, "end": 128.319, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 128.319, "end": 128.939, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Io", "start": 128.939, "end": 129.039, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 129.039, "end": 129.079, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mi", "start": 129.079, "end": 129.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 129.139, "end": 129.139, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "intendo", "start": 129.139, "end": 129.459, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 129.459, "end": 129.459, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 129.459, "end": 129.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 129.559, "end": 129.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "barche,", "start": 129.559, "end": 129.839, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 129.839, "end": 129.839, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "dopo", "start": 129.839, "end": 130.019, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 130.019, "end": 130.039, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "tutto.", "start": 130.039, "end": 130.399, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 130.399, "end": 132.019, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON>,", "start": 132.019, "end": 132.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 132.139, "end": 132.139, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ma", "start": 132.139, "end": 132.239, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 132.239, "end": 132.239, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "che", "start": 132.239, "end": 132.339, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 132.339, "end": 132.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "cos'hai", "start": 132.379, "end": 132.579, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 132.579, "end": 132.599, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "da", "start": 132.599, "end": 132.679, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 132.679, "end": 132.679, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "guardarmi", "start": 132.679, "end": 133.039, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 133.039, "end": 133.079, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "così?", "start": 133.079, "end": 133.359, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 133.359, "end": 133.359, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Ti", "start": 133.359, "end": 133.459, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 133.459, "end": 133.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "senti", "start": 133.479, "end": 133.719, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 133.719, "end": 133.759, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "male?", "start": 133.759, "end": 134.119, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 134.119, "end": 134.579, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Sono", "start": 134.579, "end": 134.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 134.799, "end": 134.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "incinta,", "start": 134.819, "end": 135.339, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 135.339, "end": 135.359, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "lo", "start": 135.359, "end": 135.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 135.479, "end": 135.499, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sai?", "start": 135.499, "end": 135.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 135.859, "end": 137.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON>,", "start": 137.799, "end": 138.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 138.159, "end": 138.179, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "se", "start": 138.179, "end": 138.279, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 138.279, "end": 138.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "stai", "start": 138.299, "end": 138.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 138.479, "end": 138.499, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "scherzando", "start": 138.499, "end": 138.919, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 138.919, "end": 138.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "hai", "start": 138.919, "end": 139.019, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 139.019, "end": 139.019, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "s<PERSON><PERSON><PERSON>", "start": 139.019, "end": 139.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 139.299, "end": 139.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "il", "start": 139.299, "end": 139.359, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 139.359, "end": 139.399, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "momento.", "start": 139.399, "end": 139.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 139.799, "end": 139.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Ma", "start": 139.799, "end": 139.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 139.899, "end": 139.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "allora", "start": 139.919, "end": 140.179, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 140.179, "end": 140.179, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "vieni", "start": 140.179, "end": 140.419, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 140.419, "end": 140.439, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 140.439, "end": 140.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 140.459, "end": 140.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "casa.", "start": 140.539, "end": 140.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 140.919, "end": 140.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Rosario", "start": 140.979, "end": 141.399, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 141.399, "end": 141.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "è", "start": 141.459, "end": 141.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 141.479, "end": 141.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "partito", "start": 141.479, "end": 141.839, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 141.839, "end": 141.839, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "e", "start": 141.839, "end": 141.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 141.899, "end": 141.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ti", "start": 141.919, "end": 142.019, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 142.019, "end": 142.039, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "dirò", "start": 142.039, "end": 142.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 142.219, "end": 142.279, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tutto.", "start": 142.279, "end": 142.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 142.699, "end": 142.859, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>", "start": 142.859, "end": 143.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 143.139, "end": 143.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ascoltarmi.", "start": 143.139, "end": 143.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 143.959, "end": 144.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON>,", "start": 144.479, "end": 144.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 144.799, "end": 144.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "s<PERSON><PERSON><PERSON>.", "start": 144.799, "end": 145.519, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 145.519, "end": 145.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Io", "start": 145.819, "end": 145.919, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 145.919, "end": 145.939, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sono", "start": 145.939, "end": 146.119, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 146.119, "end": 146.139, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "stanco", "start": 146.139, "end": 146.499, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 146.499, "end": 146.519, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "delle", "start": 146.519, "end": 146.679, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 146.679, "end": 146.699, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "tue", "start": 146.699, "end": 146.779, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 146.779, "end": 146.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "lacrime,", "start": 146.799, "end": 147.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 147.139, "end": 147.139, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "della", "start": 147.139, "end": 147.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 147.299, "end": 147.319, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "tua", "start": 147.319, "end": 147.459, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 147.459, "end": 147.459, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "faccia,", "start": 147.459, "end": 147.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 147.799, "end": 147.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 147.799, "end": 147.859, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 147.859, "end": 147.879, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "averti", "start": 147.879, "end": 148.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 148.159, "end": 148.179, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sempre", "start": 148.179, "end": 148.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 148.479, "end": 148.499, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "tra", "start": 148.499, "end": 148.619, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 148.619, "end": 148.619, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "i", "start": 148.619, "end": 148.719, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 148.719, "end": 148.719, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "piedi.", "start": 148.719, "end": 149.059, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 149.059, "end": 149.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Io", "start": 149.059, "end": 149.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 149.139, "end": 149.199, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sono", "start": 149.199, "end": 149.359, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 149.359, "end": 149.399, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "stanco,", "start": 149.399, "end": 149.679, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 149.679, "end": 149.679, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "hai", "start": 149.679, "end": 149.779, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 149.779, "end": 149.779, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "capito?", "start": 149.779, "end": 150.199, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 150.199, "end": 151.999, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "E", "start": 151.999, "end": 152.099, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 152.099, "end": 152.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "poi", "start": 152.119, "end": 152.259, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 152.259, "end": 152.259, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "finiamola", "start": 152.259, "end": 152.679, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 152.679, "end": 152.679, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "una", "start": 152.679, "end": 152.819, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 152.819, "end": 152.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "buona", "start": 152.819, "end": 152.999, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 152.999, "end": 153.039, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "volta", "start": 153.039, "end": 153.259, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 153.259, "end": 153.259, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "con", "start": 153.259, "end": 153.379, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 153.379, "end": 153.399, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "questa", "start": 153.399, "end": 153.599, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 153.599, "end": 153.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "storia.", "start": 153.639, "end": 154.019, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 154.019, "end": 155.219, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Non", "start": 155.219, "end": 155.339, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 155.339, "end": 155.359, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "voglio", "start": 155.359, "end": 155.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 155.559, "end": 155.599, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "camminare", "start": 155.599, "end": 155.959, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 155.959, "end": 155.959, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "tutta", "start": 155.959, "end": 156.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 156.139, "end": 156.159, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "la", "start": 156.159, "end": 156.219, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 156.219, "end": 156.239, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "vita", "start": 156.239, "end": 156.399, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 156.399, "end": 156.399, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "con", "start": 156.399, "end": 156.519, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 156.519, "end": 156.519, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "un'ostrica", "start": 156.519, "end": 156.919, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 156.919, "end": 156.939, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "attaccata", "start": 156.939, "end": 157.239, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 157.239, "end": 157.239, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "al", "start": 157.239, "end": 157.319, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 157.319, "end": 157.359, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "corpo.", "start": 157.359, "end": 157.759, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 157.759, "end": 158.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "E", "start": 158.979, "end": 159.079, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 159.079, "end": 159.079, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "poi", "start": 159.079, "end": 159.239, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 159.239, "end": 159.239, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ho", "start": 159.239, "end": 159.319, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 159.319, "end": 159.319, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "da", "start": 159.319, "end": 159.439, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 159.439, "end": 159.459, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "fare,", "start": 159.459, "end": 159.679, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 159.679, "end": 159.699, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "va", "start": 159.699, "end": 159.759, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 159.759, "end": 159.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "bene?", "start": 159.819, "end": 160.099, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 160.099, "end": 160.499, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Ma", "start": 160.499, "end": 160.599, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 160.599, "end": 160.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Francisco.", "start": 160.659, "end": 161.499, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 161.499, "end": 162.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Insomma,", "start": 162.699, "end": 163.099, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 163.099, "end": 163.099, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "puoi", "start": 163.099, "end": 163.239, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 163.239, "end": 163.279, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "fare", "start": 163.279, "end": 163.439, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 163.439, "end": 163.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "quello", "start": 163.479, "end": 163.599, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 163.599, "end": 163.599, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "che", "start": 163.599, "end": 163.699, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 163.699, "end": 163.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ti", "start": 163.739, "end": 163.839, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 163.839, "end": 163.859, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "pare,", "start": 163.859, "end": 164.199, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 164.199, "end": 164.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "se", "start": 164.379, "end": 164.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 164.479, "end": 164.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "non", "start": 164.479, "end": 164.579, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 164.579, "end": 164.599, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ti", "start": 164.599, "end": 164.659, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 164.659, "end": 164.659, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "vedr<PERSON>", "start": 164.659, "end": 164.879, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 164.879, "end": 164.879, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "più", "start": 164.879, "end": 164.999, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 164.999, "end": 165.039, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "tanto", "start": 165.039, "end": 165.219, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 165.219, "end": 165.219, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 165.219, "end": 165.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 165.299, "end": 165.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "guadagnato.", "start": 165.299, "end": 165.899, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 165.899, "end": 166.339, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Se", "start": 166.339, "end": 166.439, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 166.439, "end": 166.439, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "invece", "start": 166.439, "end": 166.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 166.799, "end": 167.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 167.479, "end": 167.779, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 167.779, "end": 167.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "quieta,", "start": 167.799, "end": 168.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 168.299, "end": 168.659, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "senza", "start": 168.659, "end": 168.959, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 168.959, "end": 168.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "storie,", "start": 168.979, "end": 169.439, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 169.439, "end": 170.019, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "cercheremo", "start": 170.019, "end": 170.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 170.559, "end": 170.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di...", "start": 170.559, "end": 171.039, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 171.039, "end": 171.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Insomma,", "start": 171.979, "end": 172.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 172.559, "end": 173.359, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ad", "start": 173.359, "end": 173.519, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 173.519, "end": 173.519, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ogni", "start": 173.519, "end": 173.699, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 173.699, "end": 173.699, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "guaio", "start": 173.699, "end": 173.999, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 173.999, "end": 174.019, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "c'è", "start": 174.019, "end": 174.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 174.159, "end": 174.159, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "rimedio.", "start": 174.159, "end": 174.699, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 174.699, "end": 176.439, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Non", "start": 176.439, "end": 176.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 176.559, "end": 176.579, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mi", "start": 176.579, "end": 176.619, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 176.619, "end": 176.659, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sembra", "start": 176.659, "end": 176.879, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 176.879, "end": 176.879, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "il", "start": 176.879, "end": 176.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 176.979, "end": 176.999, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "caso", "start": 176.999, "end": 177.219, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 177.219, "end": 177.219, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 177.219, "end": 177.319, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 177.319, "end": 177.319, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "preoccup<PERSON>i.", "start": 177.319, "end": 178.079, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 178.079, "end": 179.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "E", "start": 179.299, "end": 179.439, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 179.439, "end": 179.459, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "poi", "start": 179.459, "end": 179.679, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 179.679, "end": 179.699, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "potrei", "start": 179.699, "end": 179.959, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 179.959, "end": 179.959, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ve<PERSON><PERSON>", "start": 179.959, "end": 180.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 180.299, "end": 180.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ancora", "start": 180.299, "end": 180.539, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 180.539, "end": 180.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "qualche", "start": 180.559, "end": 180.779, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 180.779, "end": 180.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "volta,", "start": 180.819, "end": 181.199, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 181.199, "end": 181.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "come", "start": 181.299, "end": 181.439, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 181.439, "end": 181.439, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "in", "start": 181.439, "end": 181.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 181.559, "end": 181.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "passato.", "start": 181.559, "end": 182.119, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 182.119, "end": 186.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 186.379, "end": 186.599, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 186.599, "end": 186.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "donna", "start": 186.639, "end": 186.859, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 186.859, "end": 186.859, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mi", "start": 186.859, "end": 186.959, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 186.959, "end": 186.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "fece", "start": 186.979, "end": 187.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 187.159, "end": 187.179, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "perdere", "start": 187.179, "end": 187.519, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 187.519, "end": 187.519, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "la", "start": 187.519, "end": 187.639, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 187.639, "end": 187.659, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "testa.", "start": 187.659, "end": 188.059, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 188.059, "end": 188.599, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Trascurai", "start": 188.599, "end": 189.079, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 189.079, "end": 189.079, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ogni", "start": 189.079, "end": 189.279, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 189.279, "end": 189.279, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "prudenza.", "start": 189.279, "end": 189.819, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 189.819, "end": 190.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Sa<PERSON><PERSON>", "start": 190.059, "end": 190.399, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 190.399, "end": 190.399, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "baciare", "start": 190.399, "end": 190.739, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 190.739, "end": 190.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "in", "start": 190.739, "end": 190.819, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 190.819, "end": 190.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "un", "start": 190.819, "end": 190.899, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 190.899, "end": 190.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "modo", "start": 190.919, "end": 191.119, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 191.119, "end": 191.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "che", "start": 191.119, "end": 191.219, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 191.219, "end": 191.239, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "non", "start": 191.239, "end": 191.319, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 191.319, "end": 191.339, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "dimenticherò", "start": 191.339, "end": 191.819, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 191.819, "end": 191.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mai.", "start": 191.819, "end": 192.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 192.159, "end": 192.319, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 192.319, "end": 192.759, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 192.759, "end": 192.779, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Carmel<PERSON>", "start": 192.779, "end": 193.119, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 193.119, "end": 193.139, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mi", "start": 193.139, "end": 193.199, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 193.199, "end": 193.219, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "aveva", "start": 193.219, "end": 193.439, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 193.439, "end": 193.459, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 193.459, "end": 193.839, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 193.839, "end": 195.759, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "e", "start": 195.759, "end": 195.859, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 195.859, "end": 195.859, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mi", "start": 195.859, "end": 195.959, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 195.959, "end": 195.959, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "vide", "start": 195.959, "end": 196.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 196.139, "end": 196.159, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "abbracciato", "start": 196.159, "end": 196.579, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 196.579, "end": 196.579, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "con", "start": 196.579, "end": 196.719, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 196.719, "end": 196.759, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>", "start": 196.759, "end": 197.099, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 197.099, "end": 197.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "e", "start": 197.479, "end": 197.579, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 197.579, "end": 197.599, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "si", "start": 197.599, "end": 197.719, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 197.719, "end": 197.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sent<PERSON>", "start": 197.739, "end": 197.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 197.979, "end": 197.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "perduta.", "start": 197.979, "end": 198.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 198.559, "end": 214.759, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 214.759, "end": 215.099, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 215.099, "end": 215.099, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mi", "start": 215.099, "end": 215.219, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 215.219, "end": 215.219, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "disse", "start": 215.219, "end": 215.499, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 215.499, "end": 215.519, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "poi", "start": 215.519, "end": 215.679, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 215.679, "end": 215.699, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "che", "start": 215.699, "end": 215.779, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 215.779, "end": 215.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "l'aveva", "start": 215.799, "end": 216.079, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 216.079, "end": 216.099, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "vista", "start": 216.099, "end": 216.359, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 216.359, "end": 216.399, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 216.399, "end": 216.839, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 216.839, "end": 216.839, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "disperatamente.", "start": 216.839, "end": 217.719, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}]}}, "created_at": 1754319808.254924}