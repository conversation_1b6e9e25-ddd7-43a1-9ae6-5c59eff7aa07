"""
ASR异常定义
标准化异常处理机制
"""

import time
from typing import Optional, Dict, Any

class ASRException(Exception):
    """ASR基础异常

    所有ASR异常的基类，提供统一异常信息格式
    """
    
    def __init__(
        self, 
        message: str, 
        service_name: Optional[str] = None,
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        """异常初始化

        Args:
            message: 错误消息
            service_name: 服务名称
            error_code: 错误代码
            details: 详细信息
        """
        super().__init__(message)
        self.message = message
        self.service_name = service_name
        self.error_code = error_code
        self.details = details or {}
        self.timestamp = time.time()
    
    def to_dict(self) -> Dict[str, Any]:
        """字典格式转换"""
        return {
            "error_type": self.__class__.__name__,
            "message": self.message,
            "service_name": self.service_name,
            "error_code": self.error_code,
            "details": self.details,
            "timestamp": self.timestamp
        }
    
    def __str__(self) -> str:
        """字符串表示形式"""
        parts = [self.message]
        if self.service_name:
            parts.append(f"服务: {self.service_name}")
        if self.error_code:
            parts.append(f"错误代码: {self.error_code}")
        return " | ".join(parts)


class ASRConfigurationError(ASRException):
    """ASR配置错误

    服务配置无效或缺失时抛出
    """
    
    def __init__(self, message: str, service_name: Optional[str] = None, **kwargs):
        super().__init__(
            message=message,
            service_name=service_name,
            error_code="CONFIG_ERROR",
            **kwargs
        )
