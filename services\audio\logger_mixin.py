"""
音频系统日志混入类

提供四级日志功能和异常日志记录能力。
专注于日志记录，不包含异常定义。
"""

import logging
import os
from typing import Optional


class AudioLoggerMixin:
    """音频日志混入类
    
    四级日志：🔍 DEBUG, ℹ️ INFO, ⚠️ WARNING, ❌ ERROR
    支持异常自动日志记录
    """
    
    def __init__(self, logger_name: Optional[str] = None):
        """初始化日志混入"""
        if not hasattr(self, 'logger'):
            # 使用模块名作为logger名称，与UI注册保持一致
            self.logger = logging.getLogger(logger_name or self.__class__.__module__)

    
    def _log_debug(self, message: str):
        """🔍 调试信息"""
        self.logger.debug(f"🔍 {message}")
    
    def _log_info(self, message: str):
        """ℹ️ 一般信息"""
        self.logger.info(f"ℹ️ {message}")
    
    def _log_warning(self, message: str):
        """⚠️ 警告信息"""
        self.logger.warning(f"⚠️ {message}")
    
    def _log_error(self, message: str):
        """❌ 错误信息"""
        self.logger.error(f"❌ {message}")
    
    def _log_exception(self, exception, level: str = "ERROR"):
        """记录异常日志
        
        Args:
            exception: AudioError异常实例
            level: 日志级别 (DEBUG/INFO/WARNING/ERROR)
        """
        # 构建日志消息
        log_msg = exception.user_message
        if hasattr(exception, 'file_path') and exception.file_path:
            log_msg += f" | 文件: {os.path.basename(exception.file_path)}"
        if hasattr(exception, 'technical_details') and exception.technical_details:
            log_msg += f" | 详情: {exception.technical_details}"
        
        # 根据级别记录日志
        if level == "ERROR":
            self._log_error(log_msg)
        elif level == "WARNING":
            self._log_warning(log_msg)
        elif level == "INFO":
            self._log_info(log_msg)
        else:
            self._log_debug(log_msg)
