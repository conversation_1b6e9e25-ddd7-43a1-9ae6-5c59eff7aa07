{"success": true, "service": "elevenlabs", "transcription_id": "elevenlabs_1754319581", "text": "C'era anche la moglie con lui, e per tutto il tempo mi guardò in silenzio. La sera stessa la incontrai di nuovo. Era col marito? Era sola. Mi disse che era dalla mia parte, che le ero piaciuto perché mi ero opposto a suo marito e mi promise di aiutarli. E questo non ti meravigliò? Non ti fece pensare? Mi disse che si trovava lì per forza, che non era un posto per lei, che aveva tanto bisogno di amicizia. Con il Garcia a Palermo usò le stesse parole, la stessa tattica. Allora non sapevo nulla di lei. Però quell'incontro mi fece passare di mente il solito appuntamento con Maria. È pronto Rosario? Cosa c'è? Perché non parli? Pensi ancora a quel farabutto? Devi dimenticartelo, <PERSON><PERSON>, non fa per te. Anche in Garcia dice lo stesso di te a Maria. Ma io parlo per il tuo bene, <PERSON><PERSON>. Non voglio vederti piangere per uno che non lo merita. C'è stato forse qualcosa fra voi? Qualcosa di grave? No, no Rosario, no. Cosa fai qua? Come mai non sei venuto, <PERSON>? Ti ho aspettato al pozzo fino al tramonto. Non potevo, stasera non potevo. Devo parlarti. Vieni. Non posso più rimanere in quella casa, non posso. Anche mio padre è cambiato. Prima papà mi voleva bene, ma adesso è lei a istigare. Quella donna è sempre fra noi e mi odia Rosario, ci odia tutti. Ma non esagerare Maria. In fondo si tratta di una donna sola, in un'isola chiusa, ostile. Carla non è poi così malvagia come sembra. Se tu provassi a farle compagnia, a capirla. La chiami Carla adesso? È il suo nome, no? E come vuoi che la chiami? Tu l'hai vista e parlato con lei quando? Dopo il mercato delle spugne. Non potevo mandarla via e poi ci può essere utile. Utile? Ma se è stata lei a convincere mio padre a non cedere sul prezzo delle spugne e minacciare i pescatori. Papà era disposto ad accordarsi con te. Ma lei ha fatto così senza ragione, solo perché è cattiva. Ora capisco molte cose, Maria. Non ho mai avuto bisogno di te come in questo momento. Ti aiuterò come vorrai, caro. Ma che intendi fare? Ho un progetto. C'è un tale a Palermo che da tempo ha messo gli occhi sul mio battello. Domani sarà qui. Vuoi vendere il Porto Salvo? No, solo affittarlo per un anno. Mi serve del denaro per creare una cooperativa. Saremo soli contro tutti, ma se andrà bene, spazzeremo finalmente un po' di miseria da quest'isola. Mettemmo su la cooperativa. La chiamammo Cooperativa del Mare. Fu Avul a battezzarla così. Ci assicurò che quel nome era di buon augurio. Invece ha portato disgrazie a tutti, a lui per primo, anche se gli affari nei primi tempi andavano bene. Fu allora che Maria e suo padre partirono per Palermo, vero? Appunto, fu tutta una macchinazione di Carla. Decisi allora di andarla a trovare per chiarire la situazione. Soltanto per questo? Sì, anche se dopo accade il contrario. Margherita! Margherita! Margherita! Vo' sia chiamato? Senti, quando ti chiamo devi correre subito, hai capito? Raccogli quel coltello che vi è caduto. Quando imparerai che comando io ormai? Sono io la padrona. Vuoi capirlo? Aspetta, non te ne andare. Voglio un fiammifero.", "words": [{"text": "C'era", "start": 2.319, "end": 2.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.579, "end": 2.579, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "anche", "start": 2.579, "end": 2.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.779, "end": 2.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "la", "start": 2.779, "end": 2.879, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.879, "end": 2.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "moglie", "start": 2.919, "end": 3.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 3.239, "end": 3.24, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "con", "start": 3.24, "end": 3.399, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 3.399, "end": 3.439, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "lui,", "start": 3.439, "end": 3.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 3.779, "end": 4.4, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "e", "start": 4.4, "end": 4.519, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 4.519, "end": 4.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "per", "start": 4.539, "end": 4.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 4.659, "end": 4.679, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tutto", "start": 4.679, "end": 4.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 4.859, "end": 4.88, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "il", "start": 4.88, "end": 4.96, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 4.96, "end": 4.98, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tempo", "start": 4.98, "end": 5.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 5.219, "end": 5.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "mi", "start": 5.219, "end": 5.339, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 5.339, "end": 5.339, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 5.339, "end": 5.639, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 5.639, "end": 5.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "in", "start": 5.659, "end": 5.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 5.759, "end": 5.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "silenzio.", "start": 5.779, "end": 6.359, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 6.359, "end": 7.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "La", "start": 7.219, "end": 7.339, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 7.339, "end": 7.359, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sera", "start": 7.359, "end": 7.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 7.579, "end": 7.639, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "stessa", "start": 7.639, "end": 7.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 7.919, "end": 7.94, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "la", "start": 7.94, "end": 8.039, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 8.039, "end": 8.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "incontrai", "start": 8.059, "end": 8.519, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 8.519, "end": 8.519, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 8.519, "end": 8.639, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 8.639, "end": 8.639, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "nuovo.", "start": 8.639, "end": 9.079, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 9.079, "end": 9.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Era", "start": 9.599, "end": 9.819, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 9.819, "end": 9.84, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "col", "start": 9.84, "end": 9.98, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 9.98, "end": 10.0, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "marito?", "start": 10.0, "end": 10.459, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 10.459, "end": 10.92, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Era", "start": 10.92, "end": 11.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 11.199, "end": 11.259, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sola.", "start": 11.259, "end": 11.64, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 11.64, "end": 11.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>", "start": 11.979, "end": 12.079, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 12.079, "end": 12.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "disse", "start": 12.139, "end": 12.359, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 12.359, "end": 12.399, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 12.399, "end": 12.46, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 12.46, "end": 12.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "era", "start": 12.479, "end": 12.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 12.619, "end": 12.639, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "dalla", "start": 12.639, "end": 12.839, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 12.839, "end": 12.859, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "mia", "start": 12.859, "end": 13.059, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 13.059, "end": 13.079, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "parte,", "start": 13.079, "end": 13.499, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 13.499, "end": 13.92, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 13.92, "end": 14.059, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 14.059, "end": 14.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "le", "start": 14.059, "end": 14.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 14.199, "end": 14.199, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ero", "start": 14.199, "end": 14.379, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 14.379, "end": 14.399, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 14.399, "end": 14.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 14.819, "end": 14.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "perché", "start": 14.819, "end": 15.06, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 15.06, "end": 15.079, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "mi", "start": 15.079, "end": 15.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 15.159, "end": 15.179, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ero", "start": 15.179, "end": 15.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 15.319, "end": 15.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "opposto", "start": 15.319, "end": 15.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 15.619, "end": 15.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 15.619, "end": 15.679, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 15.679, "end": 15.679, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "suo", "start": 15.679, "end": 15.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 15.819, "end": 15.839, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "marito", "start": 15.839, "end": 16.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 16.219, "end": 16.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "e", "start": 16.559, "end": 16.68, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 16.68, "end": 16.68, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "mi", "start": 16.68, "end": 16.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 16.819, "end": 16.84, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "promise", "start": 16.84, "end": 17.2, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 17.2, "end": 17.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 17.219, "end": 17.279, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 17.279, "end": 17.279, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "aiutarli.", "start": 17.279, "end": 17.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 17.959, "end": 18.379, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "E", "start": 18.379, "end": 18.5, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 18.5, "end": 18.5, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "questo", "start": 18.5, "end": 18.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 18.799, "end": 18.84, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "non", "start": 18.84, "end": 18.92, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 18.92, "end": 18.959, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ti", "start": 18.959, "end": 19.039, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 19.039, "end": 19.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "meravigliò?", "start": 19.059, "end": 19.7, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 19.7, "end": 19.92, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Non", "start": 19.92, "end": 20.02, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 20.02, "end": 20.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ti", "start": 20.059, "end": 20.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 20.139, "end": 20.159, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "fece", "start": 20.159, "end": 20.399, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 20.399, "end": 20.439, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "pensare?", "start": 20.439, "end": 20.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 20.979, "end": 21.699, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>", "start": 21.699, "end": 21.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 21.859, "end": 21.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "disse", "start": 21.879, "end": 22.079, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 22.079, "end": 22.1, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 22.1, "end": 22.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 22.159, "end": 22.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "si", "start": 22.159, "end": 22.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 22.299, "end": 22.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "trovava", "start": 22.319, "end": 22.599, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 22.599, "end": 22.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "lì", "start": 22.619, "end": 22.739, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 22.739, "end": 22.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "per", "start": 22.739, "end": 22.879, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 22.879, "end": 22.92, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "forza,", "start": 22.92, "end": 23.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 23.479, "end": 23.92, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 23.92, "end": 24.04, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 24.04, "end": 24.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "non", "start": 24.059, "end": 24.179, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 24.179, "end": 24.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "era", "start": 24.219, "end": 24.399, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 24.399, "end": 24.42, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "un", "start": 24.42, "end": 24.519, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 24.519, "end": 24.519, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "posto", "start": 24.519, "end": 24.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 24.799, "end": 24.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "per", "start": 24.819, "end": 24.94, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 24.94, "end": 24.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "lei,", "start": 24.979, "end": 25.339, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 25.339, "end": 25.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 25.479, "end": 25.599, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 25.599, "end": 25.68, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "aveva", "start": 25.68, "end": 25.92, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 25.92, "end": 25.939, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tanto", "start": 25.939, "end": 26.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 26.199, "end": 26.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "bisogno", "start": 26.219, "end": 26.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 26.479, "end": 26.5, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 26.5, "end": 26.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 26.559, "end": 26.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "amicizia.", "start": 26.559, "end": 27.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 27.199, "end": 28.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Con", "start": 28.019, "end": 28.18, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 28.18, "end": 28.219, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "il", "start": 28.219, "end": 28.319, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 28.319, "end": 28.319, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>", "start": 28.319, "end": 28.739, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 28.739, "end": 28.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "a", "start": 28.799, "end": 28.819, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 28.819, "end": 28.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Palermo", "start": 28.819, "end": 29.179, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 29.179, "end": 29.179, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "usò", "start": 29.179, "end": 29.399, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 29.399, "end": 29.42, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "le", "start": 29.42, "end": 29.519, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 29.519, "end": 29.579, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "stesse", "start": 29.579, "end": 29.92, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 29.92, "end": 30.0, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "parole,", "start": 30.0, "end": 30.499, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 30.499, "end": 30.699, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "la", "start": 30.699, "end": 30.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 30.799, "end": 30.859, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "stessa", "start": 30.859, "end": 31.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 31.159, "end": 31.239, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "tattica.", "start": 31.239, "end": 31.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 31.799, "end": 32.159, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 32.159, "end": 32.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 32.559, "end": 32.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "non", "start": 32.559, "end": 32.7, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 32.7, "end": 32.719, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sapevo", "start": 32.719, "end": 33.04, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 33.04, "end": 33.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "nulla", "start": 33.059, "end": 33.24, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 33.24, "end": 33.279, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 33.279, "end": 33.38, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 33.38, "end": 33.38, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "lei.", "start": 33.38, "end": 33.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 33.759, "end": 34.36, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 34.36, "end": 34.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 34.659, "end": 34.7, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "quell'incontro", "start": 34.7, "end": 35.419, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 35.419, "end": 35.419, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "mi", "start": 35.419, "end": 35.54, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 35.54, "end": 35.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "fece", "start": 35.559, "end": 35.74, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 35.74, "end": 35.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "passare", "start": 35.779, "end": 36.04, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 36.04, "end": 36.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 36.059, "end": 36.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 36.159, "end": 36.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "mente", "start": 36.159, "end": 36.38, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 36.38, "end": 36.38, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "il", "start": 36.38, "end": 36.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 36.479, "end": 36.5, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "solito", "start": 36.5, "end": 36.739, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 36.739, "end": 36.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "appuntamento", "start": 36.739, "end": 37.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 37.299, "end": 37.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "con", "start": 37.319, "end": 37.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 37.459, "end": 37.5, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>.", "start": 37.5, "end": 37.94, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 37.94, "end": 39.759, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "È", "start": 39.759, "end": 39.939, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 39.939, "end": 39.939, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "pronto", "start": 39.939, "end": 40.279, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 40.279, "end": 40.299, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Rosario?", "start": 40.299, "end": 40.94, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 40.94, "end": 46.819, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Cosa", "start": 46.819, "end": 47.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 47.119, "end": 47.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "c'è?", "start": 47.159, "end": 47.419, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 47.419, "end": 47.439, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 47.439, "end": 47.72, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 47.72, "end": 47.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "non", "start": 47.739, "end": 47.88, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 47.88, "end": 47.899, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "parli?", "start": 47.899, "end": 48.339, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 48.339, "end": 50.2, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 50.2, "end": 50.5, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 50.5, "end": 50.5, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ancora", "start": 50.5, "end": 50.739, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 50.739, "end": 50.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 50.739, "end": 50.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 50.819, "end": 50.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "quel", "start": 50.819, "end": 50.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 50.979, "end": 50.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "farabutto?", "start": 50.979, "end": 51.54, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 51.54, "end": 53.34, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>", "start": 53.34, "end": 53.599, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 53.599, "end": 53.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "diment<PERSON><PERSON><PERSON>,", "start": 53.599, "end": 54.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 54.319, "end": 54.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Carmela,", "start": 54.319, "end": 54.68, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 54.68, "end": 54.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "non", "start": 54.699, "end": 54.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 54.799, "end": 54.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "fa", "start": 54.819, "end": 54.939, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 54.939, "end": 54.939, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "per", "start": 54.939, "end": 55.06, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 55.06, "end": 55.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "te.", "start": 55.139, "end": 55.339, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 55.339, "end": 56.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 56.539, "end": 56.819, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 56.819, "end": 56.84, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "in", "start": 56.84, "end": 56.9, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 56.9, "end": 56.939, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON>", "start": 56.939, "end": 57.379, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 57.379, "end": 57.399, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "dice", "start": 57.399, "end": 57.619, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 57.619, "end": 57.619, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "lo", "start": 57.619, "end": 57.72, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 57.72, "end": 57.779, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "stesso", "start": 57.779, "end": 58.079, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 58.079, "end": 58.099, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "di", "start": 58.099, "end": 58.219, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 58.219, "end": 58.219, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "te", "start": 58.219, "end": 58.379, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 58.379, "end": 58.379, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "a", "start": 58.379, "end": 58.479, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 58.479, "end": 58.479, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON>.", "start": 58.479, "end": 58.92, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 58.92, "end": 59.999, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Ma", "start": 59.999, "end": 60.14, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 60.14, "end": 60.16, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "io", "start": 60.16, "end": 60.339, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 60.339, "end": 60.359, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "parlo", "start": 60.359, "end": 60.54, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 60.54, "end": 60.54, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "per", "start": 60.54, "end": 60.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 60.659, "end": 60.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "il", "start": 60.699, "end": 60.739, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 60.739, "end": 60.759, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tuo", "start": 60.759, "end": 60.879, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 60.879, "end": 60.899, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "bene,", "start": 60.899, "end": 61.14, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 61.14, "end": 61.14, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Carmela.", "start": 61.14, "end": 61.639, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 61.639, "end": 63.039, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Non", "start": 63.039, "end": 63.179, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 63.179, "end": 63.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "voglio", "start": 63.219, "end": 63.4, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 63.4, "end": 63.4, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ve<PERSON><PERSON>", "start": 63.4, "end": 63.739, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 63.739, "end": 63.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 63.739, "end": 64.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 64.139, "end": 64.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "per", "start": 64.159, "end": 64.259, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 64.259, "end": 64.3, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "uno", "start": 64.3, "end": 64.439, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 64.439, "end": 64.439, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 64.439, "end": 64.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 64.559, "end": 64.579, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "non", "start": 64.579, "end": 64.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 64.699, "end": 64.719, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "lo", "start": 64.719, "end": 64.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 64.799, "end": 64.84, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "merita.", "start": 64.84, "end": 65.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 65.239, "end": 66.199, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "C'è", "start": 66.199, "end": 66.339, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 66.339, "end": 66.339, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "stato", "start": 66.339, "end": 66.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 66.559, "end": 66.579, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "forse", "start": 66.579, "end": 66.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 66.779, "end": 66.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "qualcosa", "start": 66.779, "end": 67.18, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 67.18, "end": 67.18, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "fra", "start": 67.18, "end": 67.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 67.319, "end": 67.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "voi?", "start": 67.319, "end": 67.639, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 67.639, "end": 68.42, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Qualcosa", "start": 68.42, "end": 68.84, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 68.84, "end": 68.859, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 68.859, "end": 68.939, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 68.939, "end": 68.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "grave?", "start": 68.959, "end": 69.36, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 69.36, "end": 69.439, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "No,", "start": 69.439, "end": 69.759, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 69.759, "end": 69.88, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "no", "start": 69.88, "end": 69.979, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 69.979, "end": 70.059, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Rosario,", "start": 70.059, "end": 70.539, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 70.539, "end": 70.559, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "no.", "start": 70.559, "end": 70.799, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 70.799, "end": 72.899, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Cosa", "start": 72.899, "end": 94.34, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 94.34, "end": 94.379, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "fai", "start": 94.379, "end": 94.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 94.619, "end": 94.639, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "qua?", "start": 94.639, "end": 94.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 94.819, "end": 95.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Come", "start": 95.699, "end": 95.879, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 95.879, "end": 95.92, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "mai", "start": 95.92, "end": 96.039, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 96.039, "end": 96.059, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "non", "start": 96.059, "end": 96.159, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 96.159, "end": 96.219, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "sei", "start": 96.219, "end": 96.34, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 96.34, "end": 96.359, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "venuto,", "start": 96.359, "end": 96.679, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 96.679, "end": 96.679, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Rosario?", "start": 96.679, "end": 97.2, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 97.2, "end": 97.76, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Ti", "start": 97.76, "end": 97.879, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 97.879, "end": 97.879, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "ho", "start": 97.879, "end": 97.96, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 97.96, "end": 97.96, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "aspettato", "start": 97.96, "end": 98.54, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 98.54, "end": 98.62, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "al", "start": 98.62, "end": 98.739, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 98.739, "end": 98.799, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "pozzo", "start": 98.799, "end": 99.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 99.1, "end": 99.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "fino", "start": 99.1, "end": 99.26, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 99.26, "end": 99.26, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "al", "start": 99.26, "end": 99.379, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 99.379, "end": 99.379, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "tramonto.", "start": 99.379, "end": 99.76, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 99.76, "end": 99.76, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Non", "start": 99.76, "end": 99.939, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 99.939, "end": 99.96, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "potevo,", "start": 99.96, "end": 100.28, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 100.28, "end": 100.359, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "stasera", "start": 100.359, "end": 100.7, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 100.7, "end": 100.719, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "non", "start": 100.719, "end": 100.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 100.819, "end": 100.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "potevo.", "start": 100.819, "end": 101.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 101.239, "end": 102.339, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Devo", "start": 102.339, "end": 102.579, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 102.579, "end": 102.639, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "parlar<PERSON>.", "start": 102.639, "end": 103.259, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 103.259, "end": 109.42, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Vieni.", "start": 109.42, "end": 109.88, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 109.88, "end": 120.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Non", "start": 120.479, "end": 120.679, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 120.679, "end": 120.719, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "posso", "start": 120.719, "end": 120.98, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 120.98, "end": 121.039, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "più", "start": 121.039, "end": 121.159, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 121.159, "end": 121.159, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "rimanere", "start": 121.159, "end": 121.539, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 121.539, "end": 121.539, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "in", "start": 121.539, "end": 121.6, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 121.6, "end": 121.619, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "quella", "start": 121.619, "end": 121.819, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 121.819, "end": 121.819, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "casa,", "start": 121.819, "end": 122.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 122.1, "end": 122.119, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "non", "start": 122.119, "end": 122.239, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 122.239, "end": 122.339, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "posso.", "start": 122.339, "end": 122.739, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 122.739, "end": 123.259, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 123.259, "end": 123.519, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 123.519, "end": 123.519, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "mio", "start": 123.519, "end": 123.659, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 123.659, "end": 123.719, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "padre", "start": 123.719, "end": 123.979, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 123.979, "end": 124.019, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "è", "start": 124.019, "end": 124.059, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 124.059, "end": 124.059, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "cambiato.", "start": 124.059, "end": 124.619, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 124.619, "end": 125.439, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Prima", "start": 125.439, "end": 125.759, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 125.759, "end": 125.78, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "papà", "start": 125.78, "end": 126.059, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 126.059, "end": 126.079, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "mi", "start": 126.079, "end": 126.179, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 126.179, "end": 126.18, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON>eva", "start": 126.18, "end": 126.519, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 126.519, "end": 126.559, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "bene,", "start": 126.559, "end": 126.92, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 126.92, "end": 127.46, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "ma", "start": 127.46, "end": 127.559, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 127.559, "end": 127.579, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "adesso", "start": 127.579, "end": 127.859, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 127.859, "end": 127.859, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "è", "start": 127.859, "end": 127.939, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 127.939, "end": 127.939, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "lei", "start": 127.939, "end": 128.119, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 128.119, "end": 128.18, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "a", "start": 128.18, "end": 128.239, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 128.239, "end": 128.239, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "istigare.", "start": 128.239, "end": 128.88, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 128.88, "end": 129.019, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 129.019, "end": 129.219, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 129.219, "end": 129.259, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "donna", "start": 129.259, "end": 129.46, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 129.46, "end": 129.519, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "è", "start": 129.519, "end": 129.539, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 129.539, "end": 129.539, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "sempre", "start": 129.539, "end": 129.86, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 129.86, "end": 129.899, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "fra", "start": 129.899, "end": 130.079, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 130.079, "end": 130.119, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "noi", "start": 130.119, "end": 130.379, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 130.379, "end": 130.839, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "e", "start": 130.839, "end": 130.92, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 130.92, "end": 130.96, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "mi", "start": 130.96, "end": 131.12, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 131.12, "end": 131.14, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "odia", "start": 131.14, "end": 131.46, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 131.46, "end": 131.479, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Rosario,", "start": 131.479, "end": 131.939, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 131.939, "end": 131.939, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "ci", "start": 131.939, "end": 132.039, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 132.039, "end": 132.059, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "odia", "start": 132.059, "end": 132.339, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 132.339, "end": 132.339, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "tutti.", "start": 132.339, "end": 132.659, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 132.659, "end": 132.659, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Ma", "start": 132.659, "end": 133.019, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 133.019, "end": 133.039, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "non", "start": 133.039, "end": 133.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 133.159, "end": 133.18, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "esagerare", "start": 133.18, "end": 133.739, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 133.739, "end": 133.759, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>.", "start": 133.759, "end": 134.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 134.199, "end": 134.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "In", "start": 134.479, "end": 134.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 134.559, "end": 134.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "fondo", "start": 134.559, "end": 134.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 134.799, "end": 134.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "si", "start": 134.799, "end": 134.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 134.899, "end": 134.939, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tratta", "start": 134.939, "end": 135.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 135.199, "end": 135.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 135.219, "end": 135.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 135.299, "end": 135.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "una", "start": 135.299, "end": 135.419, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 135.419, "end": 135.46, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "donna", "start": 135.46, "end": 135.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 135.699, "end": 135.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sola,", "start": 135.739, "end": 136.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 136.119, "end": 136.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "in", "start": 136.119, "end": 136.259, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 136.259, "end": 136.259, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "un'isola", "start": 136.259, "end": 136.639, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 136.639, "end": 136.679, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "chiusa,", "start": 136.679, "end": 137.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 137.139, "end": 137.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ostile.", "start": 137.159, "end": 137.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 137.759, "end": 138.679, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>", "start": 138.679, "end": 139.0, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 139.0, "end": 139.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "non", "start": 139.019, "end": 139.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 139.139, "end": 139.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "è", "start": 139.159, "end": 139.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 139.239, "end": 139.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "poi", "start": 139.239, "end": 139.439, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 139.439, "end": 139.439, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "così", "start": 139.439, "end": 139.639, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 139.639, "end": 139.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "malvagia", "start": 139.659, "end": 140.079, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 140.079, "end": 140.1, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "come", "start": 140.1, "end": 140.28, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 140.28, "end": 140.32, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sembra.", "start": 140.32, "end": 140.72, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 140.72, "end": 141.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Se", "start": 141.019, "end": 141.14, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 141.14, "end": 141.14, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tu", "start": 141.14, "end": 141.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 141.239, "end": 141.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 141.239, "end": 141.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 141.559, "end": 141.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 141.559, "end": 141.6, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 141.6, "end": 141.64, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "farle", "start": 141.64, "end": 141.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 141.899, "end": 141.899, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "compagnia,", "start": 141.899, "end": 142.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 142.579, "end": 142.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 142.619, "end": 142.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 142.699, "end": 142.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "capirla.", "start": 142.739, "end": 143.339, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 143.339, "end": 143.939, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "La", "start": 143.939, "end": 144.059, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 144.059, "end": 144.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "chiami", "start": 144.1, "end": 144.42, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 144.42, "end": 144.42, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON>", "start": 144.42, "end": 144.799, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 144.799, "end": 144.839, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "adesso?", "start": 144.839, "end": 145.359, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 145.359, "end": 145.82, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "È", "start": 145.82, "end": 145.92, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 145.92, "end": 145.92, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "il", "start": 145.92, "end": 146.019, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 146.019, "end": 146.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "suo", "start": 146.019, "end": 146.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 146.199, "end": 146.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "nome,", "start": 146.219, "end": 146.499, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 146.499, "end": 146.5, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "no?", "start": 146.5, "end": 146.7, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 146.7, "end": 146.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "E", "start": 146.799, "end": 146.879, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 146.879, "end": 146.899, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "come", "start": 146.899, "end": 147.099, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 147.099, "end": 147.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "vuoi", "start": 147.119, "end": 147.259, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 147.259, "end": 147.259, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 147.259, "end": 147.359, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 147.359, "end": 147.359, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "la", "start": 147.359, "end": 147.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 147.459, "end": 147.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "chiami?", "start": 147.479, "end": 147.88, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 147.88, "end": 148.14, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Tu", "start": 148.14, "end": 148.239, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 148.239, "end": 148.239, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "l'hai", "start": 148.239, "end": 148.399, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 148.399, "end": 148.42, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "vista", "start": 148.42, "end": 148.6, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 148.6, "end": 148.6, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "e", "start": 148.6, "end": 148.719, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 148.719, "end": 148.719, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "parlato", "start": 148.719, "end": 149.059, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 149.059, "end": 149.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "con", "start": 149.1, "end": 149.179, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 149.179, "end": 149.199, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "lei", "start": 149.199, "end": 149.399, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 149.399, "end": 149.46, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "quando?", "start": 149.46, "end": 149.959, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 149.959, "end": 151.019, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 151.019, "end": 152.82, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 152.82, "end": 152.839, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "il", "start": 152.839, "end": 152.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 152.899, "end": 152.92, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "mercato", "start": 152.92, "end": 153.28, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 153.28, "end": 153.28, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "delle", "start": 153.28, "end": 153.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 153.459, "end": 153.46, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "spugne.", "start": 153.46, "end": 153.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 153.899, "end": 154.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Non", "start": 154.119, "end": 154.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 154.219, "end": 154.259, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "pot<PERSON>", "start": 154.259, "end": 154.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 154.559, "end": 154.579, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "man<PERSON><PERSON>", "start": 154.579, "end": 154.939, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 154.939, "end": 154.96, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "via", "start": 154.96, "end": 155.1, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 155.1, "end": 155.14, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "e", "start": 155.14, "end": 155.22, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 155.22, "end": 155.259, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "poi", "start": 155.259, "end": 155.399, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 155.399, "end": 155.399, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ci", "start": 155.399, "end": 155.539, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 155.539, "end": 155.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 155.539, "end": 155.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 155.659, "end": 155.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "essere", "start": 155.659, "end": 155.96, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 155.96, "end": 155.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "utile.", "start": 155.979, "end": 156.38, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 156.38, "end": 156.759, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Utile?", "start": 156.759, "end": 157.28, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 157.28, "end": 157.659, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Ma", "start": 157.659, "end": 157.78, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 157.78, "end": 157.82, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "se", "start": 157.82, "end": 157.96, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 157.96, "end": 157.96, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "è", "start": 157.96, "end": 157.999, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 157.999, "end": 157.999, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "stata", "start": 157.999, "end": 158.279, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 158.279, "end": 158.28, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "lei", "start": 158.28, "end": 158.46, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 158.46, "end": 158.46, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "a", "start": 158.46, "end": 158.5, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 158.5, "end": 158.559, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "convincere", "start": 158.559, "end": 158.999, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 158.999, "end": 158.999, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "mio", "start": 158.999, "end": 159.139, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 159.139, "end": 159.199, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "padre", "start": 159.199, "end": 159.479, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 159.479, "end": 159.519, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "a", "start": 159.519, "end": 159.579, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 159.579, "end": 159.579, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "non", "start": 159.579, "end": 159.759, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 159.759, "end": 159.759, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "cedere", "start": 159.759, "end": 160.059, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 160.059, "end": 160.079, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "sul", "start": 160.079, "end": 160.239, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 160.239, "end": 160.259, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "prezzo", "start": 160.259, "end": 160.5, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 160.5, "end": 160.519, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "delle", "start": 160.519, "end": 160.659, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 160.659, "end": 160.679, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "spugne", "start": 160.679, "end": 160.999, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 160.999, "end": 160.999, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "e", "start": 160.999, "end": 161.079, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 161.079, "end": 161.079, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "min<PERSON><PERSON><PERSON>", "start": 161.079, "end": 161.42, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 161.42, "end": 161.42, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "i", "start": 161.42, "end": 161.479, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 161.479, "end": 161.499, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "pescatori.", "start": 161.499, "end": 162.039, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 162.039, "end": 162.559, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Papà", "start": 162.559, "end": 162.839, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 162.839, "end": 162.859, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "era", "start": 162.859, "end": 163.04, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 163.04, "end": 163.059, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "disposto", "start": 163.059, "end": 163.519, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 163.519, "end": 163.519, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "ad", "start": 163.519, "end": 163.639, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 163.639, "end": 163.64, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 163.64, "end": 164.159, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 164.159, "end": 164.219, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "con", "start": 164.219, "end": 164.339, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 164.339, "end": 164.439, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "te.", "start": 164.439, "end": 164.619, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 164.619, "end": 165.019, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Ma", "start": 165.019, "end": 165.159, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 165.159, "end": 165.159, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "lei", "start": 165.159, "end": 165.359, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 165.359, "end": 165.379, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "ha", "start": 165.379, "end": 165.479, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 165.479, "end": 165.479, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "fatto", "start": 165.479, "end": 165.699, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 165.699, "end": 165.699, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "così", "start": 165.699, "end": 165.919, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 165.919, "end": 165.96, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "senza", "start": 165.96, "end": 166.219, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 166.219, "end": 166.259, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "ragione,", "start": 166.259, "end": 166.7, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 166.7, "end": 166.759, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "solo", "start": 166.759, "end": 166.959, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 166.959, "end": 166.999, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "perché", "start": 166.999, "end": 167.239, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 167.239, "end": 167.319, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "è", "start": 167.319, "end": 167.339, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 167.339, "end": 167.339, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "cattiva.", "start": 167.339, "end": 167.959, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 167.959, "end": 169.879, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON>a", "start": 169.879, "end": 170.079, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 170.079, "end": 170.079, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "capisco", "start": 170.079, "end": 170.519, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 170.519, "end": 170.519, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "molte", "start": 170.519, "end": 170.839, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 170.839, "end": 170.859, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "cose,", "start": 170.859, "end": 171.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 171.139, "end": 171.14, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>.", "start": 171.14, "end": 171.539, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 171.539, "end": 172.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Non", "start": 172.159, "end": 172.279, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 172.279, "end": 172.28, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ho", "start": 172.28, "end": 172.359, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 172.359, "end": 172.379, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "mai", "start": 172.379, "end": 172.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 172.479, "end": 172.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "avuto", "start": 172.479, "end": 172.739, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 172.739, "end": 172.759, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "bisogno", "start": 172.759, "end": 173.039, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 173.039, "end": 173.079, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 173.079, "end": 173.179, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 173.179, "end": 173.179, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "te", "start": 173.179, "end": 173.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 173.299, "end": 173.339, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "come", "start": 173.339, "end": 173.499, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 173.499, "end": 173.499, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "in", "start": 173.499, "end": 173.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 173.579, "end": 173.579, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "questo", "start": 173.579, "end": 173.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 173.819, "end": 173.859, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "momento.", "start": 173.859, "end": 174.439, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 174.439, "end": 176.839, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Ti", "start": 176.839, "end": 176.979, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 176.979, "end": 177.019, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 177.019, "end": 177.42, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 177.42, "end": 177.439, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "come", "start": 177.439, "end": 177.639, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 177.639, "end": 177.639, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "vorrai,", "start": 177.639, "end": 178.159, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 178.159, "end": 178.179, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "caro.", "start": 178.179, "end": 178.599, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 178.599, "end": 179.859, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Ma", "start": 179.859, "end": 180.019, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.019, "end": 180.079, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "che", "start": 180.079, "end": 180.179, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.179, "end": 180.199, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "intendi", "start": 180.199, "end": 180.659, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.659, "end": 180.679, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "fare?", "start": 180.679, "end": 181.039, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 181.039, "end": 182.679, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON>", "start": 182.679, "end": 182.86, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 182.86, "end": 182.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "un", "start": 182.879, "end": 183.0, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 183.0, "end": 183.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "progetto.", "start": 183.019, "end": 183.599, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 183.599, "end": 183.759, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "C'è", "start": 183.759, "end": 183.92, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 183.92, "end": 183.92, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "un", "start": 183.92, "end": 184.019, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 184.019, "end": 184.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tale", "start": 184.019, "end": 184.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 184.219, "end": 184.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 184.219, "end": 184.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 184.299, "end": 184.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Palermo", "start": 184.299, "end": 184.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 184.699, "end": 184.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 184.699, "end": 184.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 184.799, "end": 184.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "da", "start": 184.819, "end": 184.92, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 184.92, "end": 184.939, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tempo", "start": 184.939, "end": 185.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 185.119, "end": 185.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ha", "start": 185.119, "end": 185.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 185.199, "end": 185.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "messo", "start": 185.219, "end": 185.42, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 185.42, "end": 185.42, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "gli", "start": 185.42, "end": 185.519, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 185.519, "end": 185.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "occhi", "start": 185.539, "end": 185.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 185.699, "end": 185.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sul", "start": 185.699, "end": 185.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 185.859, "end": 185.859, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "mio", "start": 185.859, "end": 186.019, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 186.019, "end": 186.039, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "battello.", "start": 186.039, "end": 186.539, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 186.539, "end": 186.839, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 186.839, "end": 187.179, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 187.179, "end": 187.179, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sarà", "start": 187.179, "end": 187.42, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 187.42, "end": 187.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "qui.", "start": 187.459, "end": 187.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 187.699, "end": 188.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 188.119, "end": 188.339, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 188.339, "end": 188.36, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "vendere", "start": 188.36, "end": 188.679, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 188.679, "end": 188.699, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "il", "start": 188.699, "end": 188.82, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 188.82, "end": 188.839, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Porto", "start": 188.839, "end": 189.099, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 189.099, "end": 189.159, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Salvo?", "start": 189.159, "end": 189.659, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 189.659, "end": 189.759, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "No,", "start": 189.759, "end": 190.04, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 190.04, "end": 190.099, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "solo", "start": 190.099, "end": 190.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 190.319, "end": 190.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a<PERSON><PERSON><PERSON><PERSON>", "start": 190.319, "end": 190.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 190.859, "end": 190.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "per", "start": 190.879, "end": 191.019, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 191.019, "end": 191.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "un", "start": 191.019, "end": 191.099, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 191.099, "end": 191.14, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "anno.", "start": 191.14, "end": 191.419, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 191.419, "end": 191.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>", "start": 191.699, "end": 191.78, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 191.78, "end": 191.839, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "serve", "start": 191.839, "end": 192.08, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 192.08, "end": 192.08, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "del", "start": 192.08, "end": 192.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 192.219, "end": 192.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "denaro", "start": 192.239, "end": 192.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 192.579, "end": 192.58, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "per", "start": 192.58, "end": 192.719, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 192.719, "end": 192.78, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "creare", "start": 192.78, "end": 193.099, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 193.099, "end": 193.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "una", "start": 193.119, "end": 193.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 193.239, "end": 193.259, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "cooperativa.", "start": 193.259, "end": 193.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 193.959, "end": 195.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Saremo", "start": 195.019, "end": 195.379, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 195.379, "end": 195.42, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "soli", "start": 195.42, "end": 195.679, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 195.679, "end": 195.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "contro", "start": 195.699, "end": 196.02, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 196.02, "end": 196.08, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tutti,", "start": 196.08, "end": 196.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 196.479, "end": 196.72, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ma", "start": 196.72, "end": 196.86, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 196.86, "end": 196.899, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "se", "start": 196.899, "end": 197.019, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 197.019, "end": 197.039, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 197.039, "end": 197.36, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 197.36, "end": 197.36, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "bene,", "start": 197.36, "end": 197.74, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 197.74, "end": 197.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "spazzere<PERSON>", "start": 197.979, "end": 198.519, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 198.519, "end": 198.519, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "finalmente", "start": 198.519, "end": 198.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 198.959, "end": 198.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "un", "start": 198.979, "end": 199.059, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 199.059, "end": 199.08, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "po'", "start": 199.08, "end": 199.179, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 199.179, "end": 199.199, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 199.199, "end": 199.28, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 199.28, "end": 199.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "miseria", "start": 199.299, "end": 199.64, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 199.64, "end": 199.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "da", "start": 199.659, "end": 199.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 199.759, "end": 199.759, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "quest'isola.", "start": 199.759, "end": 200.38, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 200.38, "end": 201.839, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Mettemmo", "start": 201.839, "end": 202.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 202.239, "end": 202.259, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "su", "start": 202.259, "end": 202.379, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 202.379, "end": 202.379, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "la", "start": 202.379, "end": 202.5, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 202.5, "end": 202.519, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "cooperativa.", "start": 202.519, "end": 203.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 203.219, "end": 205.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "La", "start": 205.059, "end": 205.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 205.199, "end": 205.22, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ch<PERSON><PERSON><PERSON>", "start": 205.22, "end": 205.639, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 205.639, "end": 205.86, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Cooperativa", "start": 205.86, "end": 206.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 206.479, "end": 206.519, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "del", "start": 206.519, "end": 206.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 206.659, "end": 206.72, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Mare.", "start": 206.72, "end": 207.1, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 207.1, "end": 208.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>", "start": 208.459, "end": 208.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 208.619, "end": 208.64, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Avul", "start": 208.64, "end": 208.92, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 208.92, "end": 208.92, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 208.92, "end": 209.0, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 209.0, "end": 209.0, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "battezza<PERSON><PERSON>", "start": 209.0, "end": 209.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 209.559, "end": 209.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "così.", "start": 209.559, "end": 209.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 209.899, "end": 210.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Ci", "start": 210.659, "end": 210.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 210.759, "end": 210.78, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "assicur<PERSON>", "start": 210.78, "end": 211.259, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 211.259, "end": 211.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 211.299, "end": 211.399, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 211.399, "end": 211.399, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "quel", "start": 211.399, "end": 211.599, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 211.599, "end": 211.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "nome", "start": 211.599, "end": 211.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 211.899, "end": 211.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "era", "start": 211.979, "end": 212.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 212.159, "end": 212.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 212.159, "end": 212.28, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 212.28, "end": 212.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "buon", "start": 212.299, "end": 212.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 212.479, "end": 212.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "augurio.", "start": 212.479, "end": 213.039, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 213.039, "end": 214.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Invece", "start": 214.059, "end": 214.539, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 214.539, "end": 214.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ha", "start": 214.539, "end": 214.72, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 214.72, "end": 214.72, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "portato", "start": 214.72, "end": 215.039, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 215.039, "end": 215.08, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "disgrazie", "start": 215.08, "end": 215.539, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 215.539, "end": 215.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 215.539, "end": 215.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 215.659, "end": 215.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tutti,", "start": 215.659, "end": 216.039, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 216.039, "end": 216.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 216.059, "end": 216.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 216.119, "end": 216.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "lui", "start": 216.119, "end": 216.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 216.299, "end": 216.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "per", "start": 216.299, "end": 216.44, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 216.44, "end": 216.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "primo,", "start": 216.459, "end": 216.82, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 216.82, "end": 217.099, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "anche", "start": 217.099, "end": 217.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 217.319, "end": 217.339, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "se", "start": 217.339, "end": 217.399, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 217.399, "end": 217.42, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "gli", "start": 217.42, "end": 217.499, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 217.499, "end": 217.519, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "affari", "start": 217.519, "end": 217.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 217.819, "end": 217.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "nei", "start": 217.819, "end": 217.939, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 217.939, "end": 217.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "primi", "start": 217.959, "end": 218.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 218.119, "end": 218.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tempi", "start": 218.159, "end": 218.379, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 218.379, "end": 218.399, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "and<PERSON><PERSON>", "start": 218.399, "end": 218.739, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 218.739, "end": 218.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "bene.", "start": 218.799, "end": 219.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 219.159, "end": 219.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>", "start": 219.739, "end": 219.899, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 219.899, "end": 219.899, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "allora", "start": 219.899, "end": 220.199, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 220.199, "end": 220.22, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "che", "start": 220.22, "end": 220.379, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 220.379, "end": 220.42, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>", "start": 220.42, "end": 220.759, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 220.759, "end": 220.78, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "e", "start": 220.78, "end": 220.88, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 220.88, "end": 220.939, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "suo", "start": 220.939, "end": 221.16, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 221.16, "end": 221.179, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "padre", "start": 221.179, "end": 221.42, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 221.42, "end": 221.42, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "partirono", "start": 221.42, "end": 221.86, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 221.86, "end": 221.86, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "per", "start": 221.86, "end": 221.959, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 221.959, "end": 222.019, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Palermo,", "start": 222.019, "end": 222.439, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 222.439, "end": 222.439, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "vero?", "start": 222.439, "end": 222.74, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 222.74, "end": 223.14, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "A<PERSON><PERSON><PERSON>,", "start": 223.14, "end": 223.58, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 223.58, "end": 223.64, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "fu", "start": 223.64, "end": 223.739, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 223.739, "end": 223.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tutta", "start": 223.739, "end": 223.939, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 223.939, "end": 223.939, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "una", "start": 223.939, "end": 224.059, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 224.059, "end": 224.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "macchinazione", "start": 224.059, "end": 224.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 224.619, "end": 224.64, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 224.64, "end": 224.739, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 224.739, "end": 224.759, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>.", "start": 224.759, "end": 225.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 225.159, "end": 225.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Decisi", "start": 225.879, "end": 226.259, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 226.259, "end": 226.259, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "allora", "start": 226.259, "end": 226.499, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 226.499, "end": 226.519, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 226.519, "end": 226.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 226.579, "end": 226.58, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 226.58, "end": 226.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 226.859, "end": 226.86, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 226.86, "end": 226.92, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 226.92, "end": 226.92, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "trovare", "start": 226.92, "end": 227.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 227.239, "end": 227.259, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "per", "start": 227.259, "end": 227.399, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 227.399, "end": 227.399, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "chiarire", "start": 227.399, "end": 227.72, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 227.72, "end": 227.72, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "la", "start": 227.72, "end": 227.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 227.819, "end": 227.839, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "situazione.", "start": 227.839, "end": 228.419, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 228.419, "end": 228.5, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Soltanto", "start": 228.5, "end": 229.019, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 229.019, "end": 229.039, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "per", "start": 229.039, "end": 229.16, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 229.16, "end": 229.239, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "questo?", "start": 229.239, "end": 229.619, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 229.619, "end": 230.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Sì,", "start": 230.799, "end": 231.08, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 231.08, "end": 231.099, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "anche", "start": 231.099, "end": 231.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 231.319, "end": 231.339, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "se", "start": 231.339, "end": 231.439, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 231.439, "end": 231.439, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "dopo", "start": 231.439, "end": 231.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 231.659, "end": 231.679, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "accade", "start": 231.679, "end": 231.939, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 231.939, "end": 231.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "il", "start": 231.959, "end": 232.039, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 232.039, "end": 232.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "contrario.", "start": 232.059, "end": 232.68, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 232.68, "end": 248.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Margheri<PERSON>!", "start": 248.299, "end": 249.119, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 249.119, "end": 253.379, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Margheri<PERSON>!", "start": 253.379, "end": 254.24, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 254.24, "end": 258.799, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Margheri<PERSON>!", "start": 258.799, "end": 259.6, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 259.6, "end": 261.92, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Vo'", "start": 261.92, "end": 262.119, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 262.119, "end": 262.119, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "sia", "start": 262.119, "end": 262.46, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 262.46, "end": 262.5, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "chiamato?", "start": 262.5, "end": 263.299, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 263.299, "end": 263.759, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "<PERSON><PERSON>,", "start": 263.759, "end": 264.299, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 264.299, "end": 264.299, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "quando", "start": 264.299, "end": 264.619, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 264.619, "end": 264.64, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "ti", "start": 264.64, "end": 264.78, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 264.78, "end": 264.78, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "chiamo", "start": 264.78, "end": 265.099, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 265.099, "end": 265.119, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "devi", "start": 265.119, "end": 265.319, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 265.319, "end": 265.36, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "co<PERSON>e", "start": 265.36, "end": 265.72, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 265.72, "end": 265.78, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "subito,", "start": 265.78, "end": 266.079, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 266.079, "end": 266.079, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "hai", "start": 266.079, "end": 266.179, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 266.179, "end": 266.199, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "capito?", "start": 266.199, "end": 266.679, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 266.679, "end": 267.259, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 267.259, "end": 267.599, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 267.599, "end": 267.599, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "quel", "start": 267.599, "end": 267.739, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 267.739, "end": 267.759, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 267.759, "end": 268.079, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 268.079, "end": 268.079, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "che", "start": 268.079, "end": 268.179, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 268.179, "end": 268.179, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "vi", "start": 268.179, "end": 268.259, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 268.259, "end": 268.28, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "è", "start": 268.28, "end": 268.3, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 268.3, "end": 268.319, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "caduto.", "start": 268.319, "end": 268.779, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 268.779, "end": 274.619, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Quando", "start": 274.619, "end": 274.94, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 274.94, "end": 274.959, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "imparerai", "start": 274.959, "end": 275.519, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 275.519, "end": 275.519, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "che", "start": 275.519, "end": 275.679, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 275.679, "end": 275.679, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "comando", "start": 275.679, "end": 276.099, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 276.099, "end": 276.14, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "io", "start": 276.14, "end": 276.279, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 276.279, "end": 276.28, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "ormai?", "start": 276.28, "end": 276.819, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 276.819, "end": 276.92, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Sono", "start": 276.92, "end": 277.219, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 277.219, "end": 277.36, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "io", "start": 277.36, "end": 277.499, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 277.499, "end": 277.5, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "la", "start": 277.5, "end": 277.599, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 277.599, "end": 277.659, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "padrona.", "start": 277.659, "end": 278.299, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 278.299, "end": 278.819, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 278.819, "end": 278.959, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 278.959, "end": 278.979, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "capirlo?", "start": 278.979, "end": 279.539, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 279.539, "end": 281.079, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Aspetta,", "start": 281.079, "end": 281.6, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 281.6, "end": 281.86, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "non", "start": 281.86, "end": 281.959, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 281.959, "end": 281.979, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "te", "start": 281.979, "end": 282.059, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 282.059, "end": 282.079, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "ne", "start": 282.079, "end": 282.159, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 282.159, "end": 282.159, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "andare.", "start": 282.159, "end": 282.539, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 282.539, "end": 283.599, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 283.599, "end": 283.859, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 283.859, "end": 283.879, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "un", "start": 283.879, "end": 283.939, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 283.939, "end": 283.979, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "fiammifero.", "start": 283.979, "end": 284.6, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}], "language_detected": null, "confidence": null, "processing_time": 24.154101848602295, "metadata": {"service": "elevenlabs", "mode": "free", "model": "scribe_v1", "raw_result": {"language_code": "ita", "language_probability": 0.9695907831192017, "text": "C'era anche la moglie con lui, e per tutto il tempo mi guardò in silenzio. La sera stessa la incontrai di nuovo. Era col marito? Era sola. Mi disse che era dalla mia parte, che le ero piaciuto perché mi ero opposto a suo marito e mi promise di aiutarli. E questo non ti meravigliò? Non ti fece pensare? Mi disse che si trovava lì per forza, che non era un posto per lei, che aveva tanto bisogno di amicizia. Con il Garcia a Palermo usò le stesse parole, la stessa tattica. Allora non sapevo nulla di lei. Però quell'incontro mi fece passare di mente il solito appuntamento con Maria. È pronto Rosario? Cosa c'è? Perché non parli? Pensi ancora a quel farabutto? Devi dimenticartelo, <PERSON><PERSON>, non fa per te. Anche in Garcia dice lo stesso di te a Maria. Ma io parlo per il tuo bene, <PERSON><PERSON>. Non voglio vederti piangere per uno che non lo merita. C'è stato forse qualcosa fra voi? Qualcosa di grave? No, no Rosario, no. Cosa fai qua? Come mai non sei venuto, <PERSON>? Ti ho aspettato al pozzo fino al tramonto. Non potevo, stasera non potevo. Devo parlarti. Vieni. Non posso più rimanere in quella casa, non posso. Anche mio padre è cambiato. Prima papà mi voleva bene, ma adesso è lei a istigare. Quella donna è sempre fra noi e mi odia Rosario, ci odia tutti. Ma non esagerare Maria. In fondo si tratta di una donna sola, in un'isola chiusa, ostile. Carla non è poi così malvagia come sembra. Se tu provassi a farle compagnia, a capirla. La chiami Carla adesso? È il suo nome, no? E come vuoi che la chiami? Tu l'hai vista e parlato con lei quando? Dopo il mercato delle spugne. Non potevo mandarla via e poi ci può essere utile. Utile? Ma se è stata lei a convincere mio padre a non cedere sul prezzo delle spugne e minacciare i pescatori. Papà era disposto ad accordarsi con te. Ma lei ha fatto così senza ragione, solo perché è cattiva. Ora capisco molte cose, Maria. Non ho mai avuto bisogno di te come in questo momento. Ti aiuterò come vorrai, caro. Ma che intendi fare? Ho un progetto. C'è un tale a Palermo che da tempo ha messo gli occhi sul mio battello. Domani sarà qui. Vuoi vendere il Porto Salvo? No, solo affittarlo per un anno. Mi serve del denaro per creare una cooperativa. Saremo soli contro tutti, ma se andrà bene, spazzeremo finalmente un po' di miseria da quest'isola. Mettemmo su la cooperativa. La chiamammo Cooperativa del Mare. Fu Avul a battezzarla così. Ci assicurò che quel nome era di buon augurio. Invece ha portato disgrazie a tutti, a lui per primo, anche se gli affari nei primi tempi andavano bene. Fu allora che Maria e suo padre partirono per Palermo, vero? Appunto, fu tutta una macchinazione di Carla. Decisi allora di andarla a trovare per chiarire la situazione. Soltanto per questo? Sì, anche se dopo accade il contrario. Margherita! Margherita! Margherita! Vo' sia chiamato? Senti, quando ti chiamo devi correre subito, hai capito? Raccogli quel coltello che vi è caduto. Quando imparerai che comando io ormai? Sono io la padrona. Vuoi capirlo? Aspetta, non te ne andare. Voglio un fiammifero.", "words": [{"text": "C'era", "start": 2.319, "end": 2.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.579, "end": 2.579, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "anche", "start": 2.579, "end": 2.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.779, "end": 2.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "la", "start": 2.779, "end": 2.879, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.879, "end": 2.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "moglie", "start": 2.919, "end": 3.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 3.239, "end": 3.24, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "con", "start": 3.24, "end": 3.399, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 3.399, "end": 3.439, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "lui,", "start": 3.439, "end": 3.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 3.779, "end": 4.4, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "e", "start": 4.4, "end": 4.519, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 4.519, "end": 4.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "per", "start": 4.539, "end": 4.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 4.659, "end": 4.679, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tutto", "start": 4.679, "end": 4.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 4.859, "end": 4.88, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "il", "start": 4.88, "end": 4.96, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 4.96, "end": 4.98, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tempo", "start": 4.98, "end": 5.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 5.219, "end": 5.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "mi", "start": 5.219, "end": 5.339, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 5.339, "end": 5.339, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 5.339, "end": 5.639, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 5.639, "end": 5.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "in", "start": 5.659, "end": 5.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 5.759, "end": 5.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "silenzio.", "start": 5.779, "end": 6.359, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 6.359, "end": 7.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "La", "start": 7.219, "end": 7.339, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 7.339, "end": 7.359, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sera", "start": 7.359, "end": 7.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 7.579, "end": 7.639, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "stessa", "start": 7.639, "end": 7.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 7.919, "end": 7.94, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "la", "start": 7.94, "end": 8.039, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 8.039, "end": 8.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "incontrai", "start": 8.059, "end": 8.519, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 8.519, "end": 8.519, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 8.519, "end": 8.639, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 8.639, "end": 8.639, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "nuovo.", "start": 8.639, "end": 9.079, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 9.079, "end": 9.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Era", "start": 9.599, "end": 9.819, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 9.819, "end": 9.84, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "col", "start": 9.84, "end": 9.98, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 9.98, "end": 10.0, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "marito?", "start": 10.0, "end": 10.459, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 10.459, "end": 10.92, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Era", "start": 10.92, "end": 11.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 11.199, "end": 11.259, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sola.", "start": 11.259, "end": 11.64, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 11.64, "end": 11.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>", "start": 11.979, "end": 12.079, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 12.079, "end": 12.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "disse", "start": 12.139, "end": 12.359, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 12.359, "end": 12.399, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 12.399, "end": 12.46, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 12.46, "end": 12.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "era", "start": 12.479, "end": 12.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 12.619, "end": 12.639, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "dalla", "start": 12.639, "end": 12.839, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 12.839, "end": 12.859, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "mia", "start": 12.859, "end": 13.059, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 13.059, "end": 13.079, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "parte,", "start": 13.079, "end": 13.499, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 13.499, "end": 13.92, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 13.92, "end": 14.059, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 14.059, "end": 14.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "le", "start": 14.059, "end": 14.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 14.199, "end": 14.199, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ero", "start": 14.199, "end": 14.379, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 14.379, "end": 14.399, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 14.399, "end": 14.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 14.819, "end": 14.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "perché", "start": 14.819, "end": 15.06, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 15.06, "end": 15.079, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "mi", "start": 15.079, "end": 15.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 15.159, "end": 15.179, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ero", "start": 15.179, "end": 15.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 15.319, "end": 15.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "opposto", "start": 15.319, "end": 15.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 15.619, "end": 15.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 15.619, "end": 15.679, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 15.679, "end": 15.679, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "suo", "start": 15.679, "end": 15.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 15.819, "end": 15.839, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "marito", "start": 15.839, "end": 16.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 16.219, "end": 16.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "e", "start": 16.559, "end": 16.68, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 16.68, "end": 16.68, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "mi", "start": 16.68, "end": 16.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 16.819, "end": 16.84, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "promise", "start": 16.84, "end": 17.2, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 17.2, "end": 17.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 17.219, "end": 17.279, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 17.279, "end": 17.279, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "aiutarli.", "start": 17.279, "end": 17.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 17.959, "end": 18.379, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "E", "start": 18.379, "end": 18.5, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 18.5, "end": 18.5, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "questo", "start": 18.5, "end": 18.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 18.799, "end": 18.84, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "non", "start": 18.84, "end": 18.92, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 18.92, "end": 18.959, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ti", "start": 18.959, "end": 19.039, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 19.039, "end": 19.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "meravigliò?", "start": 19.059, "end": 19.7, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 19.7, "end": 19.92, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Non", "start": 19.92, "end": 20.02, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 20.02, "end": 20.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ti", "start": 20.059, "end": 20.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 20.139, "end": 20.159, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "fece", "start": 20.159, "end": 20.399, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 20.399, "end": 20.439, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "pensare?", "start": 20.439, "end": 20.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 20.979, "end": 21.699, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>", "start": 21.699, "end": 21.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 21.859, "end": 21.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "disse", "start": 21.879, "end": 22.079, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 22.079, "end": 22.1, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 22.1, "end": 22.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 22.159, "end": 22.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "si", "start": 22.159, "end": 22.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 22.299, "end": 22.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "trovava", "start": 22.319, "end": 22.599, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 22.599, "end": 22.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "lì", "start": 22.619, "end": 22.739, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 22.739, "end": 22.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "per", "start": 22.739, "end": 22.879, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 22.879, "end": 22.92, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "forza,", "start": 22.92, "end": 23.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 23.479, "end": 23.92, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 23.92, "end": 24.04, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 24.04, "end": 24.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "non", "start": 24.059, "end": 24.179, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 24.179, "end": 24.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "era", "start": 24.219, "end": 24.399, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 24.399, "end": 24.42, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "un", "start": 24.42, "end": 24.519, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 24.519, "end": 24.519, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "posto", "start": 24.519, "end": 24.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 24.799, "end": 24.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "per", "start": 24.819, "end": 24.94, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 24.94, "end": 24.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "lei,", "start": 24.979, "end": 25.339, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 25.339, "end": 25.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 25.479, "end": 25.599, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 25.599, "end": 25.68, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "aveva", "start": 25.68, "end": 25.92, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 25.92, "end": 25.939, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tanto", "start": 25.939, "end": 26.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 26.199, "end": 26.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "bisogno", "start": 26.219, "end": 26.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 26.479, "end": 26.5, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 26.5, "end": 26.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 26.559, "end": 26.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "amicizia.", "start": 26.559, "end": 27.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 27.199, "end": 28.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Con", "start": 28.019, "end": 28.18, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 28.18, "end": 28.219, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "il", "start": 28.219, "end": 28.319, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 28.319, "end": 28.319, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>", "start": 28.319, "end": 28.739, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 28.739, "end": 28.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "a", "start": 28.799, "end": 28.819, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 28.819, "end": 28.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Palermo", "start": 28.819, "end": 29.179, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 29.179, "end": 29.179, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "usò", "start": 29.179, "end": 29.399, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 29.399, "end": 29.42, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "le", "start": 29.42, "end": 29.519, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 29.519, "end": 29.579, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "stesse", "start": 29.579, "end": 29.92, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 29.92, "end": 30.0, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "parole,", "start": 30.0, "end": 30.499, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 30.499, "end": 30.699, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "la", "start": 30.699, "end": 30.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 30.799, "end": 30.859, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "stessa", "start": 30.859, "end": 31.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 31.159, "end": 31.239, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "tattica.", "start": 31.239, "end": 31.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 31.799, "end": 32.159, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 32.159, "end": 32.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 32.559, "end": 32.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "non", "start": 32.559, "end": 32.7, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 32.7, "end": 32.719, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sapevo", "start": 32.719, "end": 33.04, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 33.04, "end": 33.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "nulla", "start": 33.059, "end": 33.24, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 33.24, "end": 33.279, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 33.279, "end": 33.38, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 33.38, "end": 33.38, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "lei.", "start": 33.38, "end": 33.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 33.759, "end": 34.36, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 34.36, "end": 34.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 34.659, "end": 34.7, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "quell'incontro", "start": 34.7, "end": 35.419, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 35.419, "end": 35.419, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "mi", "start": 35.419, "end": 35.54, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 35.54, "end": 35.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "fece", "start": 35.559, "end": 35.74, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 35.74, "end": 35.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "passare", "start": 35.779, "end": 36.04, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 36.04, "end": 36.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 36.059, "end": 36.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 36.159, "end": 36.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "mente", "start": 36.159, "end": 36.38, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 36.38, "end": 36.38, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "il", "start": 36.38, "end": 36.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 36.479, "end": 36.5, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "solito", "start": 36.5, "end": 36.739, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 36.739, "end": 36.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "appuntamento", "start": 36.739, "end": 37.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 37.299, "end": 37.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "con", "start": 37.319, "end": 37.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 37.459, "end": 37.5, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>.", "start": 37.5, "end": 37.94, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 37.94, "end": 39.759, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "È", "start": 39.759, "end": 39.939, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 39.939, "end": 39.939, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "pronto", "start": 39.939, "end": 40.279, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 40.279, "end": 40.299, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Rosario?", "start": 40.299, "end": 40.94, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 40.94, "end": 46.819, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Cosa", "start": 46.819, "end": 47.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 47.119, "end": 47.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "c'è?", "start": 47.159, "end": 47.419, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 47.419, "end": 47.439, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 47.439, "end": 47.72, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 47.72, "end": 47.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "non", "start": 47.739, "end": 47.88, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 47.88, "end": 47.899, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "parli?", "start": 47.899, "end": 48.339, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 48.339, "end": 50.2, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 50.2, "end": 50.5, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 50.5, "end": 50.5, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ancora", "start": 50.5, "end": 50.739, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 50.739, "end": 50.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 50.739, "end": 50.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 50.819, "end": 50.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "quel", "start": 50.819, "end": 50.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 50.979, "end": 50.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "farabutto?", "start": 50.979, "end": 51.54, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 51.54, "end": 53.34, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>", "start": 53.34, "end": 53.599, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 53.599, "end": 53.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "diment<PERSON><PERSON><PERSON>,", "start": 53.599, "end": 54.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 54.319, "end": 54.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Carmela,", "start": 54.319, "end": 54.68, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 54.68, "end": 54.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "non", "start": 54.699, "end": 54.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 54.799, "end": 54.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "fa", "start": 54.819, "end": 54.939, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 54.939, "end": 54.939, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "per", "start": 54.939, "end": 55.06, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 55.06, "end": 55.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "te.", "start": 55.139, "end": 55.339, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 55.339, "end": 56.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 56.539, "end": 56.819, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 56.819, "end": 56.84, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "in", "start": 56.84, "end": 56.9, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 56.9, "end": 56.939, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON>", "start": 56.939, "end": 57.379, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 57.379, "end": 57.399, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "dice", "start": 57.399, "end": 57.619, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 57.619, "end": 57.619, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "lo", "start": 57.619, "end": 57.72, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 57.72, "end": 57.779, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "stesso", "start": 57.779, "end": 58.079, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 58.079, "end": 58.099, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "di", "start": 58.099, "end": 58.219, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 58.219, "end": 58.219, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "te", "start": 58.219, "end": 58.379, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 58.379, "end": 58.379, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "a", "start": 58.379, "end": 58.479, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 58.479, "end": 58.479, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON>.", "start": 58.479, "end": 58.92, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 58.92, "end": 59.999, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Ma", "start": 59.999, "end": 60.14, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 60.14, "end": 60.16, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "io", "start": 60.16, "end": 60.339, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 60.339, "end": 60.359, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "parlo", "start": 60.359, "end": 60.54, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 60.54, "end": 60.54, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "per", "start": 60.54, "end": 60.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 60.659, "end": 60.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "il", "start": 60.699, "end": 60.739, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 60.739, "end": 60.759, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tuo", "start": 60.759, "end": 60.879, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 60.879, "end": 60.899, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "bene,", "start": 60.899, "end": 61.14, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 61.14, "end": 61.14, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Carmela.", "start": 61.14, "end": 61.639, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 61.639, "end": 63.039, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Non", "start": 63.039, "end": 63.179, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 63.179, "end": 63.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "voglio", "start": 63.219, "end": 63.4, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 63.4, "end": 63.4, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ve<PERSON><PERSON>", "start": 63.4, "end": 63.739, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 63.739, "end": 63.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 63.739, "end": 64.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 64.139, "end": 64.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "per", "start": 64.159, "end": 64.259, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 64.259, "end": 64.3, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "uno", "start": 64.3, "end": 64.439, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 64.439, "end": 64.439, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 64.439, "end": 64.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 64.559, "end": 64.579, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "non", "start": 64.579, "end": 64.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 64.699, "end": 64.719, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "lo", "start": 64.719, "end": 64.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 64.799, "end": 64.84, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "merita.", "start": 64.84, "end": 65.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 65.239, "end": 66.199, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "C'è", "start": 66.199, "end": 66.339, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 66.339, "end": 66.339, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "stato", "start": 66.339, "end": 66.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 66.559, "end": 66.579, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "forse", "start": 66.579, "end": 66.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 66.779, "end": 66.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "qualcosa", "start": 66.779, "end": 67.18, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 67.18, "end": 67.18, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "fra", "start": 67.18, "end": 67.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 67.319, "end": 67.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "voi?", "start": 67.319, "end": 67.639, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 67.639, "end": 68.42, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Qualcosa", "start": 68.42, "end": 68.84, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 68.84, "end": 68.859, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 68.859, "end": 68.939, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 68.939, "end": 68.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "grave?", "start": 68.959, "end": 69.36, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 69.36, "end": 69.439, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "No,", "start": 69.439, "end": 69.759, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 69.759, "end": 69.88, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "no", "start": 69.88, "end": 69.979, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 69.979, "end": 70.059, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Rosario,", "start": 70.059, "end": 70.539, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 70.539, "end": 70.559, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "no.", "start": 70.559, "end": 70.799, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 70.799, "end": 72.899, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Cosa", "start": 72.899, "end": 94.34, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 94.34, "end": 94.379, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "fai", "start": 94.379, "end": 94.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 94.619, "end": 94.639, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "qua?", "start": 94.639, "end": 94.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 94.819, "end": 95.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Come", "start": 95.699, "end": 95.879, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 95.879, "end": 95.92, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "mai", "start": 95.92, "end": 96.039, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 96.039, "end": 96.059, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "non", "start": 96.059, "end": 96.159, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 96.159, "end": 96.219, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "sei", "start": 96.219, "end": 96.34, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 96.34, "end": 96.359, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "venuto,", "start": 96.359, "end": 96.679, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 96.679, "end": 96.679, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Rosario?", "start": 96.679, "end": 97.2, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 97.2, "end": 97.76, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Ti", "start": 97.76, "end": 97.879, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 97.879, "end": 97.879, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "ho", "start": 97.879, "end": 97.96, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 97.96, "end": 97.96, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "aspettato", "start": 97.96, "end": 98.54, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 98.54, "end": 98.62, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "al", "start": 98.62, "end": 98.739, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 98.739, "end": 98.799, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "pozzo", "start": 98.799, "end": 99.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 99.1, "end": 99.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "fino", "start": 99.1, "end": 99.26, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 99.26, "end": 99.26, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "al", "start": 99.26, "end": 99.379, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 99.379, "end": 99.379, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "tramonto.", "start": 99.379, "end": 99.76, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 99.76, "end": 99.76, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Non", "start": 99.76, "end": 99.939, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 99.939, "end": 99.96, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "potevo,", "start": 99.96, "end": 100.28, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 100.28, "end": 100.359, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "stasera", "start": 100.359, "end": 100.7, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 100.7, "end": 100.719, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "non", "start": 100.719, "end": 100.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 100.819, "end": 100.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "potevo.", "start": 100.819, "end": 101.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 101.239, "end": 102.339, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Devo", "start": 102.339, "end": 102.579, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 102.579, "end": 102.639, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "parlar<PERSON>.", "start": 102.639, "end": 103.259, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 103.259, "end": 109.42, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Vieni.", "start": 109.42, "end": 109.88, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 109.88, "end": 120.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Non", "start": 120.479, "end": 120.679, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 120.679, "end": 120.719, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "posso", "start": 120.719, "end": 120.98, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 120.98, "end": 121.039, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "più", "start": 121.039, "end": 121.159, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 121.159, "end": 121.159, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "rimanere", "start": 121.159, "end": 121.539, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 121.539, "end": 121.539, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "in", "start": 121.539, "end": 121.6, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 121.6, "end": 121.619, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "quella", "start": 121.619, "end": 121.819, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 121.819, "end": 121.819, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "casa,", "start": 121.819, "end": 122.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 122.1, "end": 122.119, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "non", "start": 122.119, "end": 122.239, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 122.239, "end": 122.339, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "posso.", "start": 122.339, "end": 122.739, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 122.739, "end": 123.259, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 123.259, "end": 123.519, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 123.519, "end": 123.519, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "mio", "start": 123.519, "end": 123.659, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 123.659, "end": 123.719, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "padre", "start": 123.719, "end": 123.979, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 123.979, "end": 124.019, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "è", "start": 124.019, "end": 124.059, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 124.059, "end": 124.059, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "cambiato.", "start": 124.059, "end": 124.619, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 124.619, "end": 125.439, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Prima", "start": 125.439, "end": 125.759, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 125.759, "end": 125.78, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "papà", "start": 125.78, "end": 126.059, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 126.059, "end": 126.079, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "mi", "start": 126.079, "end": 126.179, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 126.179, "end": 126.18, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON>eva", "start": 126.18, "end": 126.519, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 126.519, "end": 126.559, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "bene,", "start": 126.559, "end": 126.92, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 126.92, "end": 127.46, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "ma", "start": 127.46, "end": 127.559, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 127.559, "end": 127.579, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "adesso", "start": 127.579, "end": 127.859, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 127.859, "end": 127.859, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "è", "start": 127.859, "end": 127.939, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 127.939, "end": 127.939, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "lei", "start": 127.939, "end": 128.119, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 128.119, "end": 128.18, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "a", "start": 128.18, "end": 128.239, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 128.239, "end": 128.239, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "istigare.", "start": 128.239, "end": 128.88, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 128.88, "end": 129.019, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 129.019, "end": 129.219, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 129.219, "end": 129.259, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "donna", "start": 129.259, "end": 129.46, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 129.46, "end": 129.519, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "è", "start": 129.519, "end": 129.539, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 129.539, "end": 129.539, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "sempre", "start": 129.539, "end": 129.86, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 129.86, "end": 129.899, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "fra", "start": 129.899, "end": 130.079, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 130.079, "end": 130.119, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "noi", "start": 130.119, "end": 130.379, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 130.379, "end": 130.839, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "e", "start": 130.839, "end": 130.92, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 130.92, "end": 130.96, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "mi", "start": 130.96, "end": 131.12, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 131.12, "end": 131.14, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "odia", "start": 131.14, "end": 131.46, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 131.46, "end": 131.479, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Rosario,", "start": 131.479, "end": 131.939, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 131.939, "end": 131.939, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "ci", "start": 131.939, "end": 132.039, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 132.039, "end": 132.059, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "odia", "start": 132.059, "end": 132.339, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 132.339, "end": 132.339, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "tutti.", "start": 132.339, "end": 132.659, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 132.659, "end": 132.659, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Ma", "start": 132.659, "end": 133.019, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 133.019, "end": 133.039, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "non", "start": 133.039, "end": 133.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 133.159, "end": 133.18, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "esagerare", "start": 133.18, "end": 133.739, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 133.739, "end": 133.759, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>.", "start": 133.759, "end": 134.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 134.199, "end": 134.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "In", "start": 134.479, "end": 134.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 134.559, "end": 134.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "fondo", "start": 134.559, "end": 134.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 134.799, "end": 134.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "si", "start": 134.799, "end": 134.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 134.899, "end": 134.939, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tratta", "start": 134.939, "end": 135.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 135.199, "end": 135.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 135.219, "end": 135.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 135.299, "end": 135.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "una", "start": 135.299, "end": 135.419, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 135.419, "end": 135.46, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "donna", "start": 135.46, "end": 135.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 135.699, "end": 135.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sola,", "start": 135.739, "end": 136.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 136.119, "end": 136.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "in", "start": 136.119, "end": 136.259, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 136.259, "end": 136.259, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "un'isola", "start": 136.259, "end": 136.639, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 136.639, "end": 136.679, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "chiusa,", "start": 136.679, "end": 137.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 137.139, "end": 137.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ostile.", "start": 137.159, "end": 137.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 137.759, "end": 138.679, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>", "start": 138.679, "end": 139.0, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 139.0, "end": 139.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "non", "start": 139.019, "end": 139.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 139.139, "end": 139.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "è", "start": 139.159, "end": 139.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 139.239, "end": 139.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "poi", "start": 139.239, "end": 139.439, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 139.439, "end": 139.439, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "così", "start": 139.439, "end": 139.639, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 139.639, "end": 139.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "malvagia", "start": 139.659, "end": 140.079, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 140.079, "end": 140.1, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "come", "start": 140.1, "end": 140.28, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 140.28, "end": 140.32, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sembra.", "start": 140.32, "end": 140.72, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 140.72, "end": 141.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Se", "start": 141.019, "end": 141.14, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 141.14, "end": 141.14, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tu", "start": 141.14, "end": 141.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 141.239, "end": 141.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 141.239, "end": 141.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 141.559, "end": 141.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 141.559, "end": 141.6, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 141.6, "end": 141.64, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "farle", "start": 141.64, "end": 141.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 141.899, "end": 141.899, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "compagnia,", "start": 141.899, "end": 142.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 142.579, "end": 142.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 142.619, "end": 142.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 142.699, "end": 142.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "capirla.", "start": 142.739, "end": 143.339, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 143.339, "end": 143.939, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "La", "start": 143.939, "end": 144.059, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 144.059, "end": 144.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "chiami", "start": 144.1, "end": 144.42, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 144.42, "end": 144.42, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON>", "start": 144.42, "end": 144.799, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 144.799, "end": 144.839, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "adesso?", "start": 144.839, "end": 145.359, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 145.359, "end": 145.82, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "È", "start": 145.82, "end": 145.92, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 145.92, "end": 145.92, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "il", "start": 145.92, "end": 146.019, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 146.019, "end": 146.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "suo", "start": 146.019, "end": 146.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 146.199, "end": 146.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "nome,", "start": 146.219, "end": 146.499, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 146.499, "end": 146.5, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "no?", "start": 146.5, "end": 146.7, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 146.7, "end": 146.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "E", "start": 146.799, "end": 146.879, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 146.879, "end": 146.899, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "come", "start": 146.899, "end": 147.099, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 147.099, "end": 147.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "vuoi", "start": 147.119, "end": 147.259, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 147.259, "end": 147.259, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 147.259, "end": 147.359, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 147.359, "end": 147.359, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "la", "start": 147.359, "end": 147.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 147.459, "end": 147.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "chiami?", "start": 147.479, "end": 147.88, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 147.88, "end": 148.14, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Tu", "start": 148.14, "end": 148.239, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 148.239, "end": 148.239, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "l'hai", "start": 148.239, "end": 148.399, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 148.399, "end": 148.42, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "vista", "start": 148.42, "end": 148.6, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 148.6, "end": 148.6, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "e", "start": 148.6, "end": 148.719, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 148.719, "end": 148.719, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "parlato", "start": 148.719, "end": 149.059, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 149.059, "end": 149.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "con", "start": 149.1, "end": 149.179, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 149.179, "end": 149.199, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "lei", "start": 149.199, "end": 149.399, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 149.399, "end": 149.46, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "quando?", "start": 149.46, "end": 149.959, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 149.959, "end": 151.019, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 151.019, "end": 152.82, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 152.82, "end": 152.839, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "il", "start": 152.839, "end": 152.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 152.899, "end": 152.92, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "mercato", "start": 152.92, "end": 153.28, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 153.28, "end": 153.28, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "delle", "start": 153.28, "end": 153.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 153.459, "end": 153.46, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "spugne.", "start": 153.46, "end": 153.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 153.899, "end": 154.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Non", "start": 154.119, "end": 154.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 154.219, "end": 154.259, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "pot<PERSON>", "start": 154.259, "end": 154.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 154.559, "end": 154.579, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "man<PERSON><PERSON>", "start": 154.579, "end": 154.939, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 154.939, "end": 154.96, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "via", "start": 154.96, "end": 155.1, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 155.1, "end": 155.14, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "e", "start": 155.14, "end": 155.22, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 155.22, "end": 155.259, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "poi", "start": 155.259, "end": 155.399, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 155.399, "end": 155.399, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ci", "start": 155.399, "end": 155.539, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 155.539, "end": 155.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 155.539, "end": 155.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 155.659, "end": 155.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "essere", "start": 155.659, "end": 155.96, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 155.96, "end": 155.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "utile.", "start": 155.979, "end": 156.38, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 156.38, "end": 156.759, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Utile?", "start": 156.759, "end": 157.28, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 157.28, "end": 157.659, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Ma", "start": 157.659, "end": 157.78, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 157.78, "end": 157.82, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "se", "start": 157.82, "end": 157.96, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 157.96, "end": 157.96, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "è", "start": 157.96, "end": 157.999, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 157.999, "end": 157.999, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "stata", "start": 157.999, "end": 158.279, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 158.279, "end": 158.28, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "lei", "start": 158.28, "end": 158.46, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 158.46, "end": 158.46, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "a", "start": 158.46, "end": 158.5, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 158.5, "end": 158.559, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "convincere", "start": 158.559, "end": 158.999, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 158.999, "end": 158.999, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "mio", "start": 158.999, "end": 159.139, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 159.139, "end": 159.199, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "padre", "start": 159.199, "end": 159.479, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 159.479, "end": 159.519, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "a", "start": 159.519, "end": 159.579, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 159.579, "end": 159.579, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "non", "start": 159.579, "end": 159.759, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 159.759, "end": 159.759, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "cedere", "start": 159.759, "end": 160.059, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 160.059, "end": 160.079, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "sul", "start": 160.079, "end": 160.239, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 160.239, "end": 160.259, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "prezzo", "start": 160.259, "end": 160.5, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 160.5, "end": 160.519, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "delle", "start": 160.519, "end": 160.659, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 160.659, "end": 160.679, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "spugne", "start": 160.679, "end": 160.999, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 160.999, "end": 160.999, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "e", "start": 160.999, "end": 161.079, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 161.079, "end": 161.079, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "min<PERSON><PERSON><PERSON>", "start": 161.079, "end": 161.42, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 161.42, "end": 161.42, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "i", "start": 161.42, "end": 161.479, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 161.479, "end": 161.499, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "pescatori.", "start": 161.499, "end": 162.039, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 162.039, "end": 162.559, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Papà", "start": 162.559, "end": 162.839, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 162.839, "end": 162.859, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "era", "start": 162.859, "end": 163.04, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 163.04, "end": 163.059, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "disposto", "start": 163.059, "end": 163.519, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 163.519, "end": 163.519, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "ad", "start": 163.519, "end": 163.639, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 163.639, "end": 163.64, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 163.64, "end": 164.159, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 164.159, "end": 164.219, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "con", "start": 164.219, "end": 164.339, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 164.339, "end": 164.439, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "te.", "start": 164.439, "end": 164.619, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 164.619, "end": 165.019, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Ma", "start": 165.019, "end": 165.159, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 165.159, "end": 165.159, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "lei", "start": 165.159, "end": 165.359, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 165.359, "end": 165.379, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "ha", "start": 165.379, "end": 165.479, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 165.479, "end": 165.479, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "fatto", "start": 165.479, "end": 165.699, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 165.699, "end": 165.699, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "così", "start": 165.699, "end": 165.919, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 165.919, "end": 165.96, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "senza", "start": 165.96, "end": 166.219, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 166.219, "end": 166.259, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "ragione,", "start": 166.259, "end": 166.7, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 166.7, "end": 166.759, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "solo", "start": 166.759, "end": 166.959, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 166.959, "end": 166.999, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "perché", "start": 166.999, "end": 167.239, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 167.239, "end": 167.319, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "è", "start": 167.319, "end": 167.339, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 167.339, "end": 167.339, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "cattiva.", "start": 167.339, "end": 167.959, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 167.959, "end": 169.879, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON>a", "start": 169.879, "end": 170.079, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 170.079, "end": 170.079, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "capisco", "start": 170.079, "end": 170.519, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 170.519, "end": 170.519, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "molte", "start": 170.519, "end": 170.839, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 170.839, "end": 170.859, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "cose,", "start": 170.859, "end": 171.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 171.139, "end": 171.14, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>.", "start": 171.14, "end": 171.539, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 171.539, "end": 172.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Non", "start": 172.159, "end": 172.279, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 172.279, "end": 172.28, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ho", "start": 172.28, "end": 172.359, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 172.359, "end": 172.379, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "mai", "start": 172.379, "end": 172.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 172.479, "end": 172.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "avuto", "start": 172.479, "end": 172.739, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 172.739, "end": 172.759, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "bisogno", "start": 172.759, "end": 173.039, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 173.039, "end": 173.079, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 173.079, "end": 173.179, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 173.179, "end": 173.179, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "te", "start": 173.179, "end": 173.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 173.299, "end": 173.339, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "come", "start": 173.339, "end": 173.499, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 173.499, "end": 173.499, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "in", "start": 173.499, "end": 173.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 173.579, "end": 173.579, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "questo", "start": 173.579, "end": 173.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 173.819, "end": 173.859, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "momento.", "start": 173.859, "end": 174.439, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 174.439, "end": 176.839, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Ti", "start": 176.839, "end": 176.979, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 176.979, "end": 177.019, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 177.019, "end": 177.42, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 177.42, "end": 177.439, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "come", "start": 177.439, "end": 177.639, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 177.639, "end": 177.639, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "vorrai,", "start": 177.639, "end": 178.159, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 178.159, "end": 178.179, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "caro.", "start": 178.179, "end": 178.599, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 178.599, "end": 179.859, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Ma", "start": 179.859, "end": 180.019, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.019, "end": 180.079, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "che", "start": 180.079, "end": 180.179, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.179, "end": 180.199, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "intendi", "start": 180.199, "end": 180.659, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.659, "end": 180.679, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "fare?", "start": 180.679, "end": 181.039, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 181.039, "end": 182.679, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON>", "start": 182.679, "end": 182.86, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 182.86, "end": 182.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "un", "start": 182.879, "end": 183.0, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 183.0, "end": 183.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "progetto.", "start": 183.019, "end": 183.599, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 183.599, "end": 183.759, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "C'è", "start": 183.759, "end": 183.92, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 183.92, "end": 183.92, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "un", "start": 183.92, "end": 184.019, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 184.019, "end": 184.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tale", "start": 184.019, "end": 184.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 184.219, "end": 184.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 184.219, "end": 184.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 184.299, "end": 184.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Palermo", "start": 184.299, "end": 184.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 184.699, "end": 184.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 184.699, "end": 184.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 184.799, "end": 184.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "da", "start": 184.819, "end": 184.92, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 184.92, "end": 184.939, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tempo", "start": 184.939, "end": 185.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 185.119, "end": 185.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ha", "start": 185.119, "end": 185.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 185.199, "end": 185.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "messo", "start": 185.219, "end": 185.42, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 185.42, "end": 185.42, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "gli", "start": 185.42, "end": 185.519, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 185.519, "end": 185.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "occhi", "start": 185.539, "end": 185.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 185.699, "end": 185.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sul", "start": 185.699, "end": 185.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 185.859, "end": 185.859, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "mio", "start": 185.859, "end": 186.019, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 186.019, "end": 186.039, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "battello.", "start": 186.039, "end": 186.539, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 186.539, "end": 186.839, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 186.839, "end": 187.179, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 187.179, "end": 187.179, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sarà", "start": 187.179, "end": 187.42, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 187.42, "end": 187.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "qui.", "start": 187.459, "end": 187.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 187.699, "end": 188.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 188.119, "end": 188.339, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 188.339, "end": 188.36, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "vendere", "start": 188.36, "end": 188.679, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 188.679, "end": 188.699, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "il", "start": 188.699, "end": 188.82, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 188.82, "end": 188.839, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Porto", "start": 188.839, "end": 189.099, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 189.099, "end": 189.159, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Salvo?", "start": 189.159, "end": 189.659, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 189.659, "end": 189.759, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "No,", "start": 189.759, "end": 190.04, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 190.04, "end": 190.099, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "solo", "start": 190.099, "end": 190.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 190.319, "end": 190.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a<PERSON><PERSON><PERSON><PERSON>", "start": 190.319, "end": 190.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 190.859, "end": 190.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "per", "start": 190.879, "end": 191.019, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 191.019, "end": 191.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "un", "start": 191.019, "end": 191.099, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 191.099, "end": 191.14, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "anno.", "start": 191.14, "end": 191.419, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 191.419, "end": 191.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>", "start": 191.699, "end": 191.78, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 191.78, "end": 191.839, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "serve", "start": 191.839, "end": 192.08, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 192.08, "end": 192.08, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "del", "start": 192.08, "end": 192.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 192.219, "end": 192.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "denaro", "start": 192.239, "end": 192.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 192.579, "end": 192.58, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "per", "start": 192.58, "end": 192.719, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 192.719, "end": 192.78, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "creare", "start": 192.78, "end": 193.099, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 193.099, "end": 193.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "una", "start": 193.119, "end": 193.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 193.239, "end": 193.259, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "cooperativa.", "start": 193.259, "end": 193.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 193.959, "end": 195.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Saremo", "start": 195.019, "end": 195.379, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 195.379, "end": 195.42, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "soli", "start": 195.42, "end": 195.679, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 195.679, "end": 195.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "contro", "start": 195.699, "end": 196.02, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 196.02, "end": 196.08, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tutti,", "start": 196.08, "end": 196.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 196.479, "end": 196.72, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ma", "start": 196.72, "end": 196.86, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 196.86, "end": 196.899, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "se", "start": 196.899, "end": 197.019, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 197.019, "end": 197.039, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 197.039, "end": 197.36, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 197.36, "end": 197.36, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "bene,", "start": 197.36, "end": 197.74, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 197.74, "end": 197.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "spazzere<PERSON>", "start": 197.979, "end": 198.519, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 198.519, "end": 198.519, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "finalmente", "start": 198.519, "end": 198.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 198.959, "end": 198.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "un", "start": 198.979, "end": 199.059, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 199.059, "end": 199.08, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "po'", "start": 199.08, "end": 199.179, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 199.179, "end": 199.199, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 199.199, "end": 199.28, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 199.28, "end": 199.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "miseria", "start": 199.299, "end": 199.64, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 199.64, "end": 199.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "da", "start": 199.659, "end": 199.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 199.759, "end": 199.759, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "quest'isola.", "start": 199.759, "end": 200.38, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 200.38, "end": 201.839, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Mettemmo", "start": 201.839, "end": 202.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 202.239, "end": 202.259, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "su", "start": 202.259, "end": 202.379, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 202.379, "end": 202.379, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "la", "start": 202.379, "end": 202.5, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 202.5, "end": 202.519, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "cooperativa.", "start": 202.519, "end": 203.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 203.219, "end": 205.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "La", "start": 205.059, "end": 205.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 205.199, "end": 205.22, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ch<PERSON><PERSON><PERSON>", "start": 205.22, "end": 205.639, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 205.639, "end": 205.86, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Cooperativa", "start": 205.86, "end": 206.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 206.479, "end": 206.519, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "del", "start": 206.519, "end": 206.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 206.659, "end": 206.72, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Mare.", "start": 206.72, "end": 207.1, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 207.1, "end": 208.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>", "start": 208.459, "end": 208.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 208.619, "end": 208.64, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Avul", "start": 208.64, "end": 208.92, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 208.92, "end": 208.92, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 208.92, "end": 209.0, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 209.0, "end": 209.0, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "battezza<PERSON><PERSON>", "start": 209.0, "end": 209.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 209.559, "end": 209.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "così.", "start": 209.559, "end": 209.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 209.899, "end": 210.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Ci", "start": 210.659, "end": 210.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 210.759, "end": 210.78, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "assicur<PERSON>", "start": 210.78, "end": 211.259, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 211.259, "end": 211.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 211.299, "end": 211.399, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 211.399, "end": 211.399, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "quel", "start": 211.399, "end": 211.599, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 211.599, "end": 211.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "nome", "start": 211.599, "end": 211.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 211.899, "end": 211.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "era", "start": 211.979, "end": 212.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 212.159, "end": 212.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 212.159, "end": 212.28, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 212.28, "end": 212.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "buon", "start": 212.299, "end": 212.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 212.479, "end": 212.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "augurio.", "start": 212.479, "end": 213.039, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 213.039, "end": 214.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Invece", "start": 214.059, "end": 214.539, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 214.539, "end": 214.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ha", "start": 214.539, "end": 214.72, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 214.72, "end": 214.72, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "portato", "start": 214.72, "end": 215.039, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 215.039, "end": 215.08, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "disgrazie", "start": 215.08, "end": 215.539, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 215.539, "end": 215.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 215.539, "end": 215.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 215.659, "end": 215.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tutti,", "start": 215.659, "end": 216.039, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 216.039, "end": 216.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 216.059, "end": 216.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 216.119, "end": 216.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "lui", "start": 216.119, "end": 216.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 216.299, "end": 216.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "per", "start": 216.299, "end": 216.44, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 216.44, "end": 216.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "primo,", "start": 216.459, "end": 216.82, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 216.82, "end": 217.099, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "anche", "start": 217.099, "end": 217.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 217.319, "end": 217.339, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "se", "start": 217.339, "end": 217.399, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 217.399, "end": 217.42, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "gli", "start": 217.42, "end": 217.499, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 217.499, "end": 217.519, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "affari", "start": 217.519, "end": 217.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 217.819, "end": 217.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "nei", "start": 217.819, "end": 217.939, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 217.939, "end": 217.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "primi", "start": 217.959, "end": 218.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 218.119, "end": 218.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tempi", "start": 218.159, "end": 218.379, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 218.379, "end": 218.399, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "and<PERSON><PERSON>", "start": 218.399, "end": 218.739, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 218.739, "end": 218.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "bene.", "start": 218.799, "end": 219.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 219.159, "end": 219.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>", "start": 219.739, "end": 219.899, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 219.899, "end": 219.899, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "allora", "start": 219.899, "end": 220.199, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 220.199, "end": 220.22, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "che", "start": 220.22, "end": 220.379, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 220.379, "end": 220.42, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>", "start": 220.42, "end": 220.759, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 220.759, "end": 220.78, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "e", "start": 220.78, "end": 220.88, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 220.88, "end": 220.939, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "suo", "start": 220.939, "end": 221.16, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 221.16, "end": 221.179, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "padre", "start": 221.179, "end": 221.42, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 221.42, "end": 221.42, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "partirono", "start": 221.42, "end": 221.86, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 221.86, "end": 221.86, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "per", "start": 221.86, "end": 221.959, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 221.959, "end": 222.019, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Palermo,", "start": 222.019, "end": 222.439, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 222.439, "end": 222.439, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "vero?", "start": 222.439, "end": 222.74, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 222.74, "end": 223.14, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "A<PERSON><PERSON><PERSON>,", "start": 223.14, "end": 223.58, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 223.58, "end": 223.64, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "fu", "start": 223.64, "end": 223.739, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 223.739, "end": 223.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tutta", "start": 223.739, "end": 223.939, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 223.939, "end": 223.939, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "una", "start": 223.939, "end": 224.059, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 224.059, "end": 224.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "macchinazione", "start": 224.059, "end": 224.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 224.619, "end": 224.64, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 224.64, "end": 224.739, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 224.739, "end": 224.759, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>.", "start": 224.759, "end": 225.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 225.159, "end": 225.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Decisi", "start": 225.879, "end": 226.259, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 226.259, "end": 226.259, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "allora", "start": 226.259, "end": 226.499, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 226.499, "end": 226.519, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 226.519, "end": 226.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 226.579, "end": 226.58, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 226.58, "end": 226.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 226.859, "end": 226.86, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 226.86, "end": 226.92, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 226.92, "end": 226.92, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "trovare", "start": 226.92, "end": 227.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 227.239, "end": 227.259, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "per", "start": 227.259, "end": 227.399, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 227.399, "end": 227.399, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "chiarire", "start": 227.399, "end": 227.72, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 227.72, "end": 227.72, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "la", "start": 227.72, "end": 227.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 227.819, "end": 227.839, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "situazione.", "start": 227.839, "end": 228.419, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 228.419, "end": 228.5, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Soltanto", "start": 228.5, "end": 229.019, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 229.019, "end": 229.039, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "per", "start": 229.039, "end": 229.16, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 229.16, "end": 229.239, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "questo?", "start": 229.239, "end": 229.619, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 229.619, "end": 230.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Sì,", "start": 230.799, "end": 231.08, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 231.08, "end": 231.099, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "anche", "start": 231.099, "end": 231.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 231.319, "end": 231.339, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "se", "start": 231.339, "end": 231.439, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 231.439, "end": 231.439, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "dopo", "start": 231.439, "end": 231.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 231.659, "end": 231.679, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "accade", "start": 231.679, "end": 231.939, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 231.939, "end": 231.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "il", "start": 231.959, "end": 232.039, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 232.039, "end": 232.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "contrario.", "start": 232.059, "end": 232.68, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 232.68, "end": 248.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Margheri<PERSON>!", "start": 248.299, "end": 249.119, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 249.119, "end": 253.379, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Margheri<PERSON>!", "start": 253.379, "end": 254.24, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 254.24, "end": 258.799, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Margheri<PERSON>!", "start": 258.799, "end": 259.6, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 259.6, "end": 261.92, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Vo'", "start": 261.92, "end": 262.119, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 262.119, "end": 262.119, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "sia", "start": 262.119, "end": 262.46, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 262.46, "end": 262.5, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "chiamato?", "start": 262.5, "end": 263.299, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 263.299, "end": 263.759, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "<PERSON><PERSON>,", "start": 263.759, "end": 264.299, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 264.299, "end": 264.299, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "quando", "start": 264.299, "end": 264.619, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 264.619, "end": 264.64, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "ti", "start": 264.64, "end": 264.78, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 264.78, "end": 264.78, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "chiamo", "start": 264.78, "end": 265.099, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 265.099, "end": 265.119, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "devi", "start": 265.119, "end": 265.319, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 265.319, "end": 265.36, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "co<PERSON>e", "start": 265.36, "end": 265.72, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 265.72, "end": 265.78, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "subito,", "start": 265.78, "end": 266.079, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 266.079, "end": 266.079, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "hai", "start": 266.079, "end": 266.179, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 266.179, "end": 266.199, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "capito?", "start": 266.199, "end": 266.679, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 266.679, "end": 267.259, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 267.259, "end": 267.599, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 267.599, "end": 267.599, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "quel", "start": 267.599, "end": 267.739, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 267.739, "end": 267.759, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 267.759, "end": 268.079, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 268.079, "end": 268.079, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "che", "start": 268.079, "end": 268.179, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 268.179, "end": 268.179, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "vi", "start": 268.179, "end": 268.259, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 268.259, "end": 268.28, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "è", "start": 268.28, "end": 268.3, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 268.3, "end": 268.319, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "caduto.", "start": 268.319, "end": 268.779, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 268.779, "end": 274.619, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Quando", "start": 274.619, "end": 274.94, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 274.94, "end": 274.959, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "imparerai", "start": 274.959, "end": 275.519, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 275.519, "end": 275.519, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "che", "start": 275.519, "end": 275.679, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 275.679, "end": 275.679, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "comando", "start": 275.679, "end": 276.099, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 276.099, "end": 276.14, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "io", "start": 276.14, "end": 276.279, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 276.279, "end": 276.28, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "ormai?", "start": 276.28, "end": 276.819, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 276.819, "end": 276.92, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Sono", "start": 276.92, "end": 277.219, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 277.219, "end": 277.36, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "io", "start": 277.36, "end": 277.499, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 277.499, "end": 277.5, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "la", "start": 277.5, "end": 277.599, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 277.599, "end": 277.659, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "padrona.", "start": 277.659, "end": 278.299, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 278.299, "end": 278.819, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 278.819, "end": 278.959, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 278.959, "end": 278.979, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "capirlo?", "start": 278.979, "end": 279.539, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 279.539, "end": 281.079, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Aspetta,", "start": 281.079, "end": 281.6, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 281.6, "end": 281.86, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "non", "start": 281.86, "end": 281.959, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 281.959, "end": 281.979, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "te", "start": 281.979, "end": 282.059, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 282.059, "end": 282.079, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "ne", "start": 282.079, "end": 282.159, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 282.159, "end": 282.159, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "andare.", "start": 282.159, "end": 282.539, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 282.539, "end": 283.599, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 283.599, "end": 283.859, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 283.859, "end": 283.879, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "un", "start": 283.879, "end": 283.939, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 283.939, "end": 283.979, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "fiammifero.", "start": 283.979, "end": 284.6, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}]}}, "created_at": 1754319606.0052722}