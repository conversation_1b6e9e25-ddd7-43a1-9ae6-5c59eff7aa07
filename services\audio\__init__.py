"""
音频处理服务模块（一体化版）

专门为WAV统一输出设计的一体化音频处理模块。

主要组件：
- AudioProcessor: 一体化处理器（集成分析、声道处理、转换）

核心功能：
- FFprobe声道检测
- 专业多声道降混处理
- PCM 16-bit WAV统一输出

要求：Python 3.8+, FFmpeg 4.0+
"""

# 核心处理组件导入（一体化处理器）
from .audio_processor import AudioProcessor

# 工具函数导入（简化版）
from .utils import (
    is_supported_audio_format,
    generate_output_path, format_file_size, format_duration,
    validate_file_path, ensure_directory_exists, get_file_info
)

# 数据模型导入（一体化版）
from .audio_processor import ProcessingResult
from .exceptions import AudioError, AudioExceptions
from .logger_mixin import AudioLoggerMixin

# 旧的异常体系已删除，使用统一异常体系

# 公共API导出列表（一体化版）
__all__ = [
    # 核心处理组件（一体化）
    'AudioProcessor',

    # 数据模型
    'ProcessingResult',
    'AudioError',
    'AudioExceptions',

    # 工具函数
    'is_supported_audio_format',
    'generate_output_path',
    'validate_file_path',
    'ensure_directory_exists',
    'format_file_size',
    'format_duration',
    'get_file_info',

    # 分离的异常和日志体系
    'AudioError',
    'AudioExceptions',
    'AudioLoggerMixin',
]
