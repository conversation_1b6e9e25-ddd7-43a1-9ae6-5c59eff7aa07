#!/usr/bin/env python3
"""
日志系统模块 - 从main_application.py中提取的日志相关功能
"""

import logging
import datetime
import flet as ft


class LogSystem:
    """日志系统 - 统一管理应用日志的收集、过滤和显示

    核心功能：
    - 全局日志处理器设置
    - 日志消息缓冲和过滤
    - DEBUG模式切换
    - 测试日志独立管理
    """

    def __init__(self, app_instance):
        """初始化日志系统，设置全局日志处理器和缓冲区"""
        self.app = app_instance  # 主应用实例的引用
        self.log_messages_buffer = []  # 日志消息缓冲区，存储所有消息
        self.global_ui_handler = None  # 全局UI日志处理器
        self._pending_log_messages = []  # 延迟的日志消息
        
        # UI组件引用
        self.log_area = None
        self.debug_log_switch = None
        self.test_log_areas = {}  # 测试日志区域
        self.test_log_cleared = {}  # 测试日志清空状态
        
        self.setup_global_log_handler()
    
    def setup_global_log_handler(self):
        """设置全局日志处理器，拦截所有系统日志并转发到UI显示"""
        class UILogHandler(logging.Handler):
            def __init__(self, log_system):
                super().__init__()
                self.log_system = log_system

            def emit(self, record):
                try:
                    msg = self.format(record)
                    level_name = record.levelname
                    is_debug = (record.levelno == logging.DEBUG)

                    # 特殊处理：将某些INFO级别的音频系统消息标记为DEBUG
                    if level_name == 'INFO':
                        # 检查是否是应该隐藏的音频系统初始化消息
                        debug_patterns = [
                            'AudioProcessor (simplified for 320k MP3) initialized',
                            'Available hardware acceleration',
                            '音频处理器初始化完成',
                            'AudioProcessor initialized'
                        ]

                        # 如果消息匹配任何DEBUG模式，将其标记为DEBUG
                        for pattern in debug_patterns:
                            if pattern in msg:
                                is_debug = True
                                break

                    # 根据日志级别添加不同的前缀
                    if level_name == 'DEBUG':
                        self.log_system.log_message(f"[DEBUG] {msg}", is_debug=True)
                    elif level_name == 'INFO':
                        self.log_system.log_message(msg, is_debug=is_debug)  # INFO级别不加前缀，但可能被标记为DEBUG
                    elif level_name == 'WARNING':
                        self.log_system.log_message(f"[WARNING] {msg}", is_debug=False)
                    elif level_name == 'ERROR':
                        self.log_system.log_message(f"[ERROR] {msg}", is_debug=False)
                    else:
                        self.log_system.log_message(f"[{level_name}] {msg}", is_debug=is_debug)
                except Exception as e:
                    # 记录日志处理异常，但不影响主流程
                    try:
                        logging.getLogger(__name__).error(f"日志处理异常: {e}")
                    except:
                        pass

        # 创建全局UI处理器
        self.global_ui_handler = UILogHandler(self)
        self.global_ui_handler.setLevel(logging.DEBUG)

        # 添加到所有系统相关的logger
        system_loggers = [
            # ASR系统
            'services.asr.parser',           # 解析器
            'services.asr.manager',          # 服务管理器
            'services.asr.elevenlabs',       # ElevenLabs服务
            'services.asr.assemblyai',       # AssemblyAI服务
            'services.asr.deepgram',         # Deepgram服务
            'services.asr.utils',            # ASR工具函数
            'services.asr',                  # ASR根logger

            # 音频系统（一体化）
            'services.audio.audio_processor',  # 一体化音频处理器
            'services.audio.utils',            # 音频工具函数
            'services.audio',                  # 音频根logger

            # 字幕系统
            'services.subtitle.llm_service', # 字幕LLM服务
            'services.subtitle',             # 字幕根logger

            # LLM系统
            'services.llm.api',              # LLM API服务
            'services.llm.multi_api',        # 多LLM服务
            'services.llm',                  # LLM根logger
        ]

        for logger_name in system_loggers:
            logger_instance = logging.getLogger(logger_name)
            logger_instance.setLevel(logging.DEBUG)

            # 防止日志向父logger传播，避免重复处理
            logger_instance.propagate = False

            # 添加UI处理器
            logger_instance.addHandler(self.global_ui_handler)
    
    def log_message(self, message: str, is_debug: bool = False):
        """添加日志消息到缓冲区并刷新显示

        Args:
            message: 日志消息文本内容
            is_debug: 是否为调试级别消息，影响显示过滤
        """
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        
        # 创建日志条目
        log_entry = {
            'timestamp': timestamp,
            'message': message,
            'is_debug': is_debug,
            'full_line': f"[{timestamp}] {message}"
        }

        # 添加到缓冲区
        self.log_messages_buffer.append(log_entry)

        # 限制缓冲区大小
        if len(self.log_messages_buffer) > 100000:
            self.log_messages_buffer = self.log_messages_buffer[-100000:]

        # 刷新显示
        self.refresh_log_display()

    def refresh_log_display(self):
        """刷新日志显示区域，根据DEBUG开关状态过滤显示内容"""
        if not self.log_area:
            # 如果UI还没初始化，暂存消息
            if not hasattr(self, '_pending_log_messages'):
                self._pending_log_messages = []
            # 只暂存非DEBUG消息
            for entry in self.log_messages_buffer:
                if not entry['is_debug']:
                    self._pending_log_messages.append(entry['message'])
            return

        # 根据DEBUG开关状态过滤消息
        show_debug = self.app.config.get('show_debug_logs', False)

        filtered_lines = []
        for entry in self.log_messages_buffer:
            if entry['is_debug'] and not show_debug:
                continue  # 跳过DEBUG消息
            filtered_lines.append(entry['full_line'])

        # 限制显示行数
        if len(filtered_lines) > 100000:
            filtered_lines = filtered_lines[-100000:]

        # 更新显示
        self.log_area.value = '\n'.join(filtered_lines)
        self.app.page.update()
    
    def process_pending_log_messages(self):
        """处理延迟的日志消息"""
        if hasattr(self, '_pending_log_messages'):
            for message in self._pending_log_messages:
                self.log_message(message, is_debug=False)  # 启动消息通常不是DEBUG级别
            del self._pending_log_messages
    
    def create_log_area(self, colors):
        """创建主日志区域"""
        # 日志区域
        self.log_area = ft.TextField(
            multiline=True,
            read_only=True,
            min_lines=8,

            expand=True,
            border_color="transparent",
            bgcolor=colors['input_bg'],
            color=colors.get('input_text_color', colors['text_color']),
            value=""
        )
        return self.log_area
    
    def create_debug_switch(self, on_change_callback):
        """创建DEBUG开关"""
        self.debug_log_switch = ft.Switch(
            value=self.app.config.get('show_debug_logs', False),
            on_change=on_change_callback,
            active_color="#2196F3",
            scale=0.8  # 稍微缩小开关尺寸
        )
        return self.debug_log_switch
    
    def log_test_message(self, config_id: str, message: str, clear_first: bool = False):
        """向指定LLM配置的测试日志区域添加消息

        特殊处理逻辑：
        - 检测新测试开始的关键词（"正在拉取"、"正在测试"）
        - 新测试开始时自动清空历史日志
        - 添加时间戳前缀
        - 支持强制清空模式

        Args:
            config_id: LLM配置标识符
            message: 日志消息内容
            clear_first: 是否强制清空后再添加
        """
        if hasattr(self, 'test_log_areas') and config_id in self.test_log_areas:
            log_area = self.test_log_areas[config_id]
            timestamp = datetime.datetime.now().strftime("%H:%M:%S")

            # 检查是否是新的测试开始（包含"正在拉取"或"正在测试"）
            is_new_test_start = ("[Multi-LLM] 正在拉取" in message or
                               "[Multi-LLM] 正在测试" in message)

            # 如果是新测试开始，强制清空并设置新内容
            if is_new_test_start or clear_first or log_area.value == "暂无测试日志":
                new_line = f"[{timestamp}] {message}"

                # 强制清空：先设为空，更新，再设置新值
                log_area.value = ""
                self.app.page.update()

                # 设置新的第一条消息
                log_area.value = new_line

                # 标记此配置的日志已被清空
                if hasattr(self, 'test_log_cleared'):
                    self.test_log_cleared[config_id] = True

                self.app.page.update()
                return

            # 否则追加到现有日志
            current = log_area.value or ""
            new_line = f"[{timestamp}] {message}"

            if current:
                log_area.value = f"{current}\n{new_line}"
            else:
                log_area.value = new_line

            # 限制日志长度
            lines = log_area.value.split('\n')
            if len(lines) > 200:
                log_area.value = '\n'.join(lines[-200:])

            # 只更新页面
            self.app.page.update()
    
    def clear_test_log(self, config_id: str):
        """清空指定配置的测试日志"""
        if hasattr(self, 'test_log_areas') and config_id in self.test_log_areas:
            log_area = self.test_log_areas[config_id]
            log_area.value = ""
            # 只更新页面，避免控件更新错误
            self.app.page.update()
