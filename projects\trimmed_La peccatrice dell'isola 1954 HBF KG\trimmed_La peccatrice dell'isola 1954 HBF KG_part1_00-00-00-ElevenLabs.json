{"success": true, "service": "elevenlabs", "transcription_id": "elevenlabs_1754319455", "text": "Brigadier<PERSON>? Aspettavamo col postale, commissario De Santis. Ho preferito il peschereccio. Si balla di più, ma si arriva prima e io soffro di mal in mare. L'unico rimedio è di evitare il mare. Il questore a Palermo mi ha detto che tu sai già chi è stato. Eh magari, è un affare imbrogliato. Tutti e quattro avrebbero avuto motivi per ucciderla. La conoscevi? E come si faceva a non vederla? Si metteva in mostra tutto il giorno. Era molto bella, vero? Eh, roba troppo di lusso per un'isola come questa. Dove posso trovare questo Pietro Ingarsia? Su alla rocca, dopo la chiesa a destra. Sta rintanato lassù, non può più vedere nessuno. <PERSON><PERSON> ben<PERSON>, <PERSON><PERSON><PERSON><PERSON>, ci vediamo più tardi. Vengo con voi, commissario, quello non ti piace? No, non c'è bisogno. Pensa piuttosto al mio bagaglio. V<PERSON> bene, provvedo subito. Grazie. Cosa volete? And<PERSON><PERSON>e via, non voglio parlare con nessuno. Voglio essere lasciato in pace. Non ti conviene sparare in Garcia. Venite ancora avanti e lo vedrete. Sono il commissario De Santis della Mobile di Palermo. Sono qui per indagare sulla morte di vostra moglie. Cosa volete che vi dica? Tutto. Conobbi Carla a Palermo. Dove? In un tabarè del porto. Ma non era come le altre, era una signora. Sembrava che si trovasse lì per forza, me lo diceva sempre. Non era posto per lei quello. Io andavo spesso a Palermo per affari. Che affari? Spugli. Ce ne sono tante qui intorno. Mi andava bene allora e di soldi ne facevo parecchi. La riempii di regali e mi sposò. E vostra figlia? Le scrissi una lettera per farle sapere che mi sposavo di nuovo. Poi uno deve avere una donna, capite? Non potevo rimanere vedovo per tutta la vita. E così, una mattina me la portai in casa. Era tanto bella.", "words": [{"text": "Brigadiere", "start": 143.759, "end": 144.22, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 144.22, "end": 144.279, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>?", "start": 144.279, "end": 144.939, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 144.939, "end": 147.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Aspettavamo", "start": 147.979, "end": 148.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 148.559, "end": 148.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "col", "start": 148.559, "end": 148.699, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 148.699, "end": 148.72, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "postale,", "start": 148.72, "end": 149.22, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 149.22, "end": 149.36, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "commissario", "start": 149.36, "end": 150.019, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 150.019, "end": 150.3, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "De", "start": 150.3, "end": 150.399, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 150.399, "end": 150.44, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Sant<PERSON>.", "start": 150.44, "end": 150.919, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 150.919, "end": 152.019, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>", "start": 152.019, "end": 152.179, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 152.179, "end": 152.179, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "preferito", "start": 152.179, "end": 152.679, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 152.679, "end": 152.679, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "il", "start": 152.679, "end": 152.799, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 152.799, "end": 152.819, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "pescher<PERSON><PERSON>.", "start": 152.819, "end": 153.519, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 153.519, "end": 153.779, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Si", "start": 153.779, "end": 153.94, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 153.94, "end": 153.94, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "balla", "start": 153.94, "end": 154.179, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 154.179, "end": 154.199, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "di", "start": 154.199, "end": 154.279, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 154.279, "end": 154.3, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "pi<PERSON>,", "start": 154.3, "end": 154.459, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 154.459, "end": 154.459, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "ma", "start": 154.459, "end": 154.599, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 154.599, "end": 154.599, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "si", "start": 154.599, "end": 154.699, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 154.699, "end": 154.72, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "arriva", "start": 154.72, "end": 154.959, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 154.959, "end": 154.959, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "prima", "start": 154.959, "end": 155.32, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 155.32, "end": 155.36, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "e", "start": 155.36, "end": 155.52, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 155.52, "end": 155.559, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "io", "start": 155.559, "end": 155.72, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 155.72, "end": 155.759, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "soffro", "start": 155.759, "end": 156.119, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 156.119, "end": 156.119, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "di", "start": 156.119, "end": 156.239, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 156.239, "end": 156.239, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "mal", "start": 156.239, "end": 156.399, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 156.399, "end": 156.399, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "in", "start": 156.399, "end": 156.44, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 156.44, "end": 156.519, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "mare.", "start": 156.519, "end": 156.879, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 156.879, "end": 157.3, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "L'unico", "start": 157.3, "end": 157.739, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 157.739, "end": 157.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "rimedio", "start": 157.739, "end": 158.119, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 158.119, "end": 158.179, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "è", "start": 158.179, "end": 158.219, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 158.219, "end": 158.22, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 158.22, "end": 158.279, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 158.279, "end": 158.279, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "evitare", "start": 158.279, "end": 158.659, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 158.659, "end": 158.679, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "il", "start": 158.679, "end": 158.759, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 158.759, "end": 158.8, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mare.", "start": 158.8, "end": 159.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 159.159, "end": 159.779, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Il", "start": 159.779, "end": 159.879, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 159.879, "end": 159.899, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "questore", "start": 159.899, "end": 160.379, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 160.379, "end": 160.379, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "a", "start": 160.379, "end": 160.479, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 160.479, "end": 160.479, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Palermo", "start": 160.479, "end": 160.879, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 160.879, "end": 160.879, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "mi", "start": 160.879, "end": 160.979, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 160.979, "end": 160.979, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "ha", "start": 160.979, "end": 161.039, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 161.039, "end": 161.059, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "detto", "start": 161.059, "end": 161.259, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 161.259, "end": 161.279, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "che", "start": 161.279, "end": 161.46, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 161.46, "end": 161.519, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "tu", "start": 161.519, "end": 161.66, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 161.66, "end": 161.699, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "sai", "start": 161.699, "end": 161.94, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 161.94, "end": 161.94, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "già", "start": 161.94, "end": 162.079, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 162.079, "end": 162.119, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "chi", "start": 162.119, "end": 162.259, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 162.259, "end": 162.279, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "è", "start": 162.279, "end": 162.36, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 162.36, "end": 162.399, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "stato.", "start": 162.399, "end": 162.759, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 162.759, "end": 162.759, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Eh", "start": 162.759, "end": 162.94, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 162.94, "end": 163.0, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "magari,", "start": 163.0, "end": 163.579, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 163.579, "end": 164.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "è", "start": 164.119, "end": 164.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 164.299, "end": 164.319, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "un", "start": 164.319, "end": 164.419, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 164.419, "end": 164.44, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "affare", "start": 164.44, "end": 164.819, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 164.819, "end": 164.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "imbrogliato.", "start": 164.819, "end": 165.519, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 165.519, "end": 165.519, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 165.519, "end": 165.779, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 165.779, "end": 165.779, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "e", "start": 165.779, "end": 165.86, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 165.86, "end": 165.879, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "quattro", "start": 165.879, "end": 166.179, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 166.179, "end": 166.22, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 166.22, "end": 166.539, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 166.539, "end": 166.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "avuto", "start": 166.559, "end": 166.8, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 166.8, "end": 166.86, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "motivi", "start": 166.86, "end": 167.259, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 167.259, "end": 167.259, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "per", "start": 167.259, "end": 167.419, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 167.419, "end": 167.44, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ucciderla.", "start": 167.44, "end": 168.04, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 168.04, "end": 168.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "La", "start": 168.819, "end": 168.919, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 168.919, "end": 168.94, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "con<PERSON><PERSON><PERSON>?", "start": 168.94, "end": 169.499, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 169.499, "end": 170.119, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "E", "start": 170.119, "end": 170.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 170.139, "end": 170.279, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "come", "start": 170.279, "end": 170.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 170.559, "end": 170.58, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "si", "start": 170.58, "end": 170.699, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 170.699, "end": 170.72, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "faceva", "start": 170.72, "end": 171.08, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 171.08, "end": 171.08, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "a", "start": 171.08, "end": 171.1, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 171.1, "end": 171.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "non", "start": 171.119, "end": 171.239, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 171.239, "end": 171.239, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "vederla?", "start": 171.239, "end": 171.779, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 171.779, "end": 171.879, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Si", "start": 171.879, "end": 171.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 171.979, "end": 172.019, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 172.019, "end": 172.339, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 172.339, "end": 172.339, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "in", "start": 172.339, "end": 172.439, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 172.439, "end": 172.459, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mostra", "start": 172.459, "end": 172.8, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 172.8, "end": 172.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "tutto", "start": 172.819, "end": 173.0, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 173.0, "end": 173.0, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "il", "start": 173.0, "end": 173.079, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 173.079, "end": 173.08, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "gior<PERSON>.", "start": 173.08, "end": 173.439, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 173.439, "end": 173.599, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Era", "start": 173.599, "end": 173.86, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 173.86, "end": 173.899, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "molto", "start": 173.899, "end": 174.159, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 174.159, "end": 174.22, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "bella,", "start": 174.22, "end": 174.519, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 174.519, "end": 174.519, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "vero?", "start": 174.519, "end": 174.799, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 174.799, "end": 174.86, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON>,", "start": 174.86, "end": 175.179, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 175.179, "end": 175.179, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "roba", "start": 175.179, "end": 175.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 175.479, "end": 175.5, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "troppo", "start": 175.5, "end": 175.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 175.799, "end": 175.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 175.819, "end": 175.879, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 175.879, "end": 175.899, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "lusso", "start": 175.899, "end": 176.179, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 176.179, "end": 176.22, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "per", "start": 176.22, "end": 176.339, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 176.339, "end": 176.339, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "un'isola", "start": 176.339, "end": 176.719, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 176.719, "end": 176.72, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "come", "start": 176.72, "end": 176.899, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 176.899, "end": 176.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "questa.", "start": 176.919, "end": 177.419, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 177.419, "end": 177.419, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Dove", "start": 177.419, "end": 178.38, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 178.38, "end": 178.399, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "posso", "start": 178.399, "end": 178.619, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 178.619, "end": 178.639, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "trovare", "start": 178.639, "end": 178.979, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 178.979, "end": 179.0, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "questo", "start": 179.0, "end": 179.32, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 179.32, "end": 179.419, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON>", "start": 179.419, "end": 179.719, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 179.719, "end": 179.779, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Ingarsia?", "start": 179.779, "end": 180.519, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.519, "end": 181.199, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Su", "start": 181.199, "end": 181.38, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 181.38, "end": 181.399, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "alla", "start": 181.399, "end": 181.619, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 181.619, "end": 181.619, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "rocca,", "start": 181.619, "end": 182.059, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 182.059, "end": 182.259, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "dopo", "start": 182.259, "end": 182.439, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 182.439, "end": 182.459, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "la", "start": 182.459, "end": 182.539, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 182.539, "end": 182.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "chiesa", "start": 182.559, "end": 182.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 182.799, "end": 182.8, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "a", "start": 182.8, "end": 182.9, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 182.9, "end": 182.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "destra.", "start": 182.919, "end": 183.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 183.299, "end": 183.699, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Sta", "start": 183.699, "end": 183.819, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 183.819, "end": 183.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "r<PERSON><PERSON><PERSON>", "start": 183.819, "end": 184.279, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 184.279, "end": 184.279, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "las<PERSON><PERSON>,", "start": 184.279, "end": 184.639, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 184.639, "end": 184.879, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "non", "start": 184.879, "end": 185.0, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 185.0, "end": 185.039, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 185.039, "end": 185.219, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 185.219, "end": 185.22, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "più", "start": 185.22, "end": 185.339, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 185.339, "end": 185.339, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "vedere", "start": 185.339, "end": 185.599, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 185.599, "end": 185.599, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "nessuno.", "start": 185.599, "end": 186.059, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 186.059, "end": 186.22, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Va", "start": 186.22, "end": 186.319, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 186.319, "end": 186.36, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "bene,", "start": 186.36, "end": 186.679, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 186.679, "end": 186.679, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>,", "start": 186.679, "end": 187.199, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 187.199, "end": 187.199, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "ci", "start": 187.199, "end": 187.319, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 187.319, "end": 187.339, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "vediamo", "start": 187.339, "end": 187.639, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 187.639, "end": 187.72, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "più", "start": 187.72, "end": 187.839, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 187.839, "end": 187.839, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "tardi.", "start": 187.839, "end": 188.199, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 188.199, "end": 188.199, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Vengo", "start": 188.199, "end": 188.439, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 188.439, "end": 188.459, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "con", "start": 188.459, "end": 188.579, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 188.579, "end": 188.58, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "voi,", "start": 188.58, "end": 188.759, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 188.759, "end": 188.759, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "commissario,", "start": 188.759, "end": 189.259, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 189.259, "end": 189.3, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "quello", "start": 189.3, "end": 189.459, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 189.459, "end": 189.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "non", "start": 189.479, "end": 189.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 189.559, "end": 189.58, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ti", "start": 189.58, "end": 189.699, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 189.699, "end": 189.699, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "piace?", "start": 189.699, "end": 190.079, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 190.079, "end": 190.099, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "No,", "start": 190.099, "end": 190.339, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 190.339, "end": 190.379, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "non", "start": 190.379, "end": 190.459, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 190.459, "end": 190.5, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "c'è", "start": 190.5, "end": 190.619, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 190.619, "end": 190.639, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "bisogno.", "start": 190.639, "end": 191.02, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 191.02, "end": 191.039, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Pensa", "start": 191.039, "end": 191.279, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 191.279, "end": 191.279, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "piu<PERSON><PERSON>", "start": 191.279, "end": 191.639, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 191.639, "end": 191.639, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "al", "start": 191.639, "end": 191.739, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 191.739, "end": 191.739, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "mio", "start": 191.739, "end": 191.879, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 191.879, "end": 191.899, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "bagaglio.", "start": 191.899, "end": 192.379, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 192.379, "end": 192.819, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Va", "start": 192.819, "end": 193.0, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 193.0, "end": 193.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "bene,", "start": 193.059, "end": 193.319, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 193.319, "end": 193.319, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "provvedo", "start": 193.319, "end": 193.699, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 193.699, "end": 193.72, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "subito.", "start": 193.72, "end": 194.219, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 194.219, "end": 194.399, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Grazie.", "start": 194.399, "end": 194.9, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 194.9, "end": 210.599, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Cosa", "start": 210.599, "end": 210.86, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 210.86, "end": 210.86, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "volete?", "start": 210.86, "end": 211.339, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 211.339, "end": 212.419, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 212.419, "end": 212.959, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 212.959, "end": 212.979, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "via,", "start": 212.979, "end": 213.299, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 213.299, "end": 213.419, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "non", "start": 213.419, "end": 213.539, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 213.539, "end": 213.58, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "voglio", "start": 213.58, "end": 213.779, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 213.779, "end": 213.819, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "parlare", "start": 213.819, "end": 214.119, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 214.119, "end": 214.139, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "con", "start": 214.139, "end": 214.239, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 214.239, "end": 214.259, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "nessuno.", "start": 214.259, "end": 214.72, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 214.72, "end": 215.36, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 215.36, "end": 215.579, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 215.579, "end": 215.639, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "essere", "start": 215.639, "end": 215.959, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 215.959, "end": 215.959, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "lasciato", "start": 215.959, "end": 216.299, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 216.299, "end": 216.299, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "in", "start": 216.299, "end": 216.419, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 216.419, "end": 216.44, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "pace.", "start": 216.44, "end": 216.859, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 216.859, "end": 217.639, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Non", "start": 217.639, "end": 217.799, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 217.799, "end": 217.86, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "ti", "start": 217.86, "end": 218.0, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 218.0, "end": 218.019, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "conviene", "start": 218.019, "end": 218.599, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 218.599, "end": 218.639, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "sparare", "start": 218.639, "end": 219.199, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 219.199, "end": 219.199, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "in", "start": 219.199, "end": 219.339, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 219.339, "end": 219.339, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON>.", "start": 219.339, "end": 219.979, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 219.979, "end": 220.36, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Venite", "start": 220.36, "end": 220.759, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 220.759, "end": 220.759, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "ancora", "start": 220.759, "end": 221.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 221.019, "end": 221.039, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "avanti", "start": 221.039, "end": 221.339, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 221.339, "end": 221.339, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "e", "start": 221.339, "end": 221.419, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 221.419, "end": 221.419, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "lo", "start": 221.419, "end": 221.499, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 221.499, "end": 221.539, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "vedrete.", "start": 221.539, "end": 222.059, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 222.059, "end": 227.299, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Sono", "start": 227.299, "end": 228.219, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 228.219, "end": 228.239, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "il", "start": 228.239, "end": 228.319, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 228.319, "end": 228.399, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "commissario", "start": 228.399, "end": 228.859, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 228.859, "end": 228.899, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "De", "start": 228.899, "end": 229.0, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 229.0, "end": 229.039, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 229.039, "end": 229.419, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 229.419, "end": 229.5, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "della", "start": 229.5, "end": 229.699, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 229.699, "end": 229.72, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Mobile", "start": 229.72, "end": 230.059, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 230.059, "end": 230.08, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "di", "start": 230.08, "end": 230.179, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 230.179, "end": 230.239, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Palermo.", "start": 230.239, "end": 230.779, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 230.779, "end": 231.58, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Sono", "start": 231.58, "end": 231.799, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 231.799, "end": 231.839, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "qui", "start": 231.839, "end": 232.0, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 232.0, "end": 232.019, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "per", "start": 232.019, "end": 232.139, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 232.139, "end": 232.179, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "indagare", "start": 232.179, "end": 232.679, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 232.679, "end": 232.72, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "sulla", "start": 232.72, "end": 232.919, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 232.919, "end": 232.959, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "morte", "start": 232.959, "end": 233.239, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 233.239, "end": 233.239, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "di", "start": 233.239, "end": 233.319, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 233.319, "end": 233.319, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "vostra", "start": 233.319, "end": 233.679, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 233.679, "end": 233.72, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "moglie.", "start": 233.72, "end": 234.199, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 234.199, "end": 234.339, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Cosa", "start": 234.339, "end": 234.619, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 234.619, "end": 234.619, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "volete", "start": 234.619, "end": 234.979, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 234.979, "end": 234.979, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "che", "start": 234.979, "end": 235.099, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 235.099, "end": 235.099, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "vi", "start": 235.099, "end": 235.22, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 235.22, "end": 235.239, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "dica?", "start": 235.239, "end": 235.639, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 235.639, "end": 236.08, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON>.", "start": 236.08, "end": 236.699, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 236.699, "end": 243.199, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 243.199, "end": 243.599, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 243.599, "end": 243.599, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON>", "start": 243.599, "end": 244.0, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 244.0, "end": 244.0, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "a", "start": 244.0, "end": 244.119, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 244.119, "end": 244.119, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Palermo.", "start": 244.119, "end": 244.799, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 244.799, "end": 245.519, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Dove?", "start": 245.519, "end": 245.999, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 245.999, "end": 248.139, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "In", "start": 248.139, "end": 248.319, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 248.319, "end": 248.36, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "un", "start": 248.36, "end": 248.419, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 248.419, "end": 248.439, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "tabarè", "start": 248.439, "end": 248.799, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 248.799, "end": 248.799, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "del", "start": 248.799, "end": 249.02, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 249.02, "end": 249.08, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "porto.", "start": 249.08, "end": 249.579, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 249.579, "end": 249.819, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Ma", "start": 249.819, "end": 249.939, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 249.939, "end": 249.939, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "non", "start": 249.939, "end": 250.099, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 250.099, "end": 250.139, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "era", "start": 250.139, "end": 250.36, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 250.36, "end": 250.379, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "come", "start": 250.379, "end": 250.579, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 250.579, "end": 250.599, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "le", "start": 250.599, "end": 250.699, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 250.699, "end": 250.72, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "altre,", "start": 250.72, "end": 251.059, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 251.059, "end": 251.099, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "era", "start": 251.099, "end": 251.279, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 251.279, "end": 251.299, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "una", "start": 251.299, "end": 251.44, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 251.44, "end": 251.5, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "signora.", "start": 251.5, "end": 252.039, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 252.039, "end": 252.739, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Semb<PERSON>va", "start": 252.739, "end": 253.22, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 253.22, "end": 253.239, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "che", "start": 253.239, "end": 253.339, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 253.339, "end": 253.36, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "si", "start": 253.36, "end": 253.459, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 253.459, "end": 253.5, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "trovasse", "start": 253.5, "end": 253.899, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 253.899, "end": 253.919, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "lì", "start": 253.919, "end": 254.059, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 254.059, "end": 254.059, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "per", "start": 254.059, "end": 254.22, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 254.22, "end": 254.279, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "forza,", "start": 254.279, "end": 254.739, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 254.739, "end": 254.839, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "me", "start": 254.839, "end": 254.979, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 254.979, "end": 255.0, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "lo", "start": 255.0, "end": 255.079, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 255.079, "end": 255.119, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "diceva", "start": 255.119, "end": 255.479, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 255.479, "end": 255.5, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "sempre.", "start": 255.5, "end": 256.039, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 256.039, "end": 256.54, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Non", "start": 256.54, "end": 256.679, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 256.679, "end": 256.699, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "era", "start": 256.699, "end": 256.879, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 256.879, "end": 256.88, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "posto", "start": 256.88, "end": 257.16, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 257.16, "end": 257.179, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "per", "start": 257.179, "end": 257.339, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 257.339, "end": 257.359, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "lei", "start": 257.359, "end": 257.6, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 257.6, "end": 257.619, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "quello.", "start": 257.619, "end": 258.119, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 258.119, "end": 258.739, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Io", "start": 258.739, "end": 258.899, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 258.899, "end": 258.919, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "andavo", "start": 258.919, "end": 259.259, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 259.259, "end": 259.299, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "spesso", "start": 259.299, "end": 259.6, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 259.6, "end": 259.6, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "a", "start": 259.6, "end": 259.679, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 259.679, "end": 259.699, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Palermo", "start": 259.699, "end": 260.119, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 260.119, "end": 260.139, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "per", "start": 260.139, "end": 260.279, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 260.279, "end": 260.279, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "affari.", "start": 260.279, "end": 260.82, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 260.82, "end": 261.179, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Che", "start": 261.179, "end": 261.319, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 261.319, "end": 261.339, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "affari?", "start": 261.339, "end": 261.839, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 261.839, "end": 262.059, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>.", "start": 262.059, "end": 262.619, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 262.619, "end": 263.279, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Ce", "start": 263.279, "end": 263.419, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 263.419, "end": 263.44, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "ne", "start": 263.44, "end": 263.559, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 263.559, "end": 263.579, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "sono", "start": 263.579, "end": 263.779, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 263.779, "end": 263.82, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "tante", "start": 263.82, "end": 264.099, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 264.099, "end": 264.16, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "qui", "start": 264.16, "end": 264.279, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 264.279, "end": 264.279, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "intorno.", "start": 264.279, "end": 264.799, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 264.799, "end": 265.88, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON>", "start": 265.88, "end": 266.0, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 266.0, "end": 266.04, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "andava", "start": 266.04, "end": 266.299, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 266.299, "end": 266.32, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "bene", "start": 266.32, "end": 266.539, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 266.539, "end": 266.559, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "allora", "start": 266.559, "end": 266.899, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 266.899, "end": 266.94, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "e", "start": 266.94, "end": 267.04, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 267.04, "end": 267.059, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "di", "start": 267.059, "end": 267.179, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 267.179, "end": 267.179, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "soldi", "start": 267.179, "end": 267.479, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 267.479, "end": 267.479, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "ne", "start": 267.479, "end": 267.6, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 267.6, "end": 267.619, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "facevo", "start": 267.619, "end": 267.959, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 267.959, "end": 267.959, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "par<PERSON><PERSON>.", "start": 267.959, "end": 268.48, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 268.48, "end": 269.279, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "La", "start": 269.279, "end": 269.399, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 269.399, "end": 269.399, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 269.399, "end": 269.799, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 269.799, "end": 269.82, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "di", "start": 269.82, "end": 269.919, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 269.919, "end": 269.919, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "regali", "start": 269.919, "end": 270.339, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 270.339, "end": 270.699, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "e", "start": 270.699, "end": 270.819, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 270.819, "end": 270.839, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "mi", "start": 270.839, "end": 270.939, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 270.939, "end": 270.94, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "sposò.", "start": 270.94, "end": 271.399, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 271.399, "end": 271.899, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "E", "start": 271.899, "end": 271.979, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 271.979, "end": 271.979, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "vostra", "start": 271.979, "end": 272.279, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 272.279, "end": 272.32, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "figlia?", "start": 272.32, "end": 272.659, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 272.659, "end": 273.38, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Le", "start": 273.38, "end": 273.619, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 273.619, "end": 273.66, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "scris<PERSON>", "start": 273.66, "end": 274.079, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 274.079, "end": 274.079, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "una", "start": 274.079, "end": 274.239, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 274.239, "end": 274.259, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "lettera", "start": 274.259, "end": 274.6, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 274.6, "end": 274.6, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "per", "start": 274.6, "end": 274.739, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 274.739, "end": 274.759, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "farle", "start": 274.759, "end": 274.999, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 274.999, "end": 275.0, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "sapere", "start": 275.0, "end": 275.359, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 275.359, "end": 275.359, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "che", "start": 275.359, "end": 275.48, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 275.48, "end": 275.5, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "mi", "start": 275.5, "end": 275.58, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 275.58, "end": 275.6, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "sposavo", "start": 275.6, "end": 275.999, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 275.999, "end": 276.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "di", "start": 276.019, "end": 276.099, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 276.099, "end": 276.119, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "nuovo.", "start": 276.119, "end": 276.499, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 276.499, "end": 277.16, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 277.16, "end": 277.339, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 277.339, "end": 277.5, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "uno", "start": 277.5, "end": 277.619, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 277.619, "end": 277.639, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "deve", "start": 277.639, "end": 277.799, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 277.799, "end": 277.82, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "avere", "start": 277.82, "end": 278.1, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 278.1, "end": 278.1, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "una", "start": 278.1, "end": 278.239, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 278.239, "end": 278.279, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "donna,", "start": 278.279, "end": 278.579, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 278.579, "end": 278.579, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "capite?", "start": 278.579, "end": 279.04, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 279.04, "end": 279.6, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Non", "start": 279.6, "end": 279.739, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 279.739, "end": 279.759, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "pot<PERSON>", "start": 279.759, "end": 280.059, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 280.059, "end": 280.1, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "rimanere", "start": 280.1, "end": 280.459, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 280.459, "end": 280.479, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "vedovo", "start": 280.479, "end": 280.819, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 280.819, "end": 280.82, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "per", "start": 280.82, "end": 280.979, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 280.979, "end": 281.0, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "tutta", "start": 281.0, "end": 281.199, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 281.199, "end": 281.22, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "la", "start": 281.22, "end": 281.299, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 281.299, "end": 281.32, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "vita.", "start": 281.32, "end": 281.659, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 281.659, "end": 282.359, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "E", "start": 282.359, "end": 282.5, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 282.5, "end": 282.519, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "così,", "start": 282.519, "end": 282.899, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 282.899, "end": 284.179, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "una", "start": 284.179, "end": 284.399, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 284.399, "end": 284.399, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "mattina", "start": 284.399, "end": 284.72, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 284.72, "end": 284.739, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "me", "start": 284.739, "end": 284.839, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 284.839, "end": 284.859, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "la", "start": 284.859, "end": 284.959, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 284.959, "end": 284.979, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "portai", "start": 284.979, "end": 285.259, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 285.259, "end": 285.279, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "in", "start": 285.279, "end": 285.399, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 285.399, "end": 285.419, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "casa.", "start": 285.419, "end": 285.76, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 285.76, "end": 286.739, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Era", "start": 286.739, "end": 287.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 287.019, "end": 287.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "tanto", "start": 287.019, "end": 287.48, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 287.48, "end": 287.519, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "bella.", "start": 287.519, "end": 287.959, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}], "language_detected": null, "confidence": null, "processing_time": 12.232315301895142, "metadata": {"service": "elevenlabs", "mode": "free", "model": "scribe_v1", "raw_result": {"language_code": "ita", "language_probability": 0.9952723979949951, "text": "Brigadier<PERSON>? Aspettavamo col postale, commissario De Santis. Ho preferito il peschereccio. Si balla di più, ma si arriva prima e io soffro di mal in mare. L'unico rimedio è di evitare il mare. Il questore a Palermo mi ha detto che tu sai già chi è stato. Eh magari, è un affare imbrogliato. Tutti e quattro avrebbero avuto motivi per ucciderla. La conoscevi? E come si faceva a non vederla? Si metteva in mostra tutto il giorno. Era molto bella, vero? Eh, roba troppo di lusso per un'isola come questa. Dove posso trovare questo Pietro Ingarsia? Su alla rocca, dopo la chiesa a destra. Sta rintanato lassù, non può più vedere nessuno. <PERSON><PERSON> ben<PERSON>, <PERSON><PERSON><PERSON><PERSON>, ci vediamo più tardi. Vengo con voi, commissario, quello non ti piace? No, non c'è bisogno. Pensa piuttosto al mio bagaglio. V<PERSON> bene, provvedo subito. Grazie. Cosa volete? And<PERSON><PERSON>e via, non voglio parlare con nessuno. Voglio essere lasciato in pace. Non ti conviene sparare in Garcia. Venite ancora avanti e lo vedrete. Sono il commissario De Santis della Mobile di Palermo. Sono qui per indagare sulla morte di vostra moglie. Cosa volete che vi dica? Tutto. Conobbi Carla a Palermo. Dove? In un tabarè del porto. Ma non era come le altre, era una signora. Sembrava che si trovasse lì per forza, me lo diceva sempre. Non era posto per lei quello. Io andavo spesso a Palermo per affari. Che affari? Spugli. Ce ne sono tante qui intorno. Mi andava bene allora e di soldi ne facevo parecchi. La riempii di regali e mi sposò. E vostra figlia? Le scrissi una lettera per farle sapere che mi sposavo di nuovo. Poi uno deve avere una donna, capite? Non potevo rimanere vedovo per tutta la vita. E così, una mattina me la portai in casa. Era tanto bella.", "words": [{"text": "Brigadiere", "start": 143.759, "end": 144.22, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 144.22, "end": 144.279, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>?", "start": 144.279, "end": 144.939, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 144.939, "end": 147.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Aspettavamo", "start": 147.979, "end": 148.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 148.559, "end": 148.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "col", "start": 148.559, "end": 148.699, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 148.699, "end": 148.72, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "postale,", "start": 148.72, "end": 149.22, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 149.22, "end": 149.36, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "commissario", "start": 149.36, "end": 150.019, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 150.019, "end": 150.3, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "De", "start": 150.3, "end": 150.399, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 150.399, "end": 150.44, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Sant<PERSON>.", "start": 150.44, "end": 150.919, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 150.919, "end": 152.019, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>", "start": 152.019, "end": 152.179, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 152.179, "end": 152.179, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "preferito", "start": 152.179, "end": 152.679, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 152.679, "end": 152.679, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "il", "start": 152.679, "end": 152.799, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 152.799, "end": 152.819, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "pescher<PERSON><PERSON>.", "start": 152.819, "end": 153.519, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 153.519, "end": 153.779, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Si", "start": 153.779, "end": 153.94, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 153.94, "end": 153.94, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "balla", "start": 153.94, "end": 154.179, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 154.179, "end": 154.199, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "di", "start": 154.199, "end": 154.279, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 154.279, "end": 154.3, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "pi<PERSON>,", "start": 154.3, "end": 154.459, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 154.459, "end": 154.459, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "ma", "start": 154.459, "end": 154.599, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 154.599, "end": 154.599, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "si", "start": 154.599, "end": 154.699, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 154.699, "end": 154.72, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "arriva", "start": 154.72, "end": 154.959, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 154.959, "end": 154.959, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "prima", "start": 154.959, "end": 155.32, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 155.32, "end": 155.36, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "e", "start": 155.36, "end": 155.52, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 155.52, "end": 155.559, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "io", "start": 155.559, "end": 155.72, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 155.72, "end": 155.759, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "soffro", "start": 155.759, "end": 156.119, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 156.119, "end": 156.119, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "di", "start": 156.119, "end": 156.239, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 156.239, "end": 156.239, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "mal", "start": 156.239, "end": 156.399, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 156.399, "end": 156.399, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "in", "start": 156.399, "end": 156.44, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 156.44, "end": 156.519, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "mare.", "start": 156.519, "end": 156.879, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 156.879, "end": 157.3, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "L'unico", "start": 157.3, "end": 157.739, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 157.739, "end": 157.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "rimedio", "start": 157.739, "end": 158.119, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 158.119, "end": 158.179, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "è", "start": 158.179, "end": 158.219, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 158.219, "end": 158.22, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 158.22, "end": 158.279, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 158.279, "end": 158.279, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "evitare", "start": 158.279, "end": 158.659, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 158.659, "end": 158.679, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "il", "start": 158.679, "end": 158.759, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 158.759, "end": 158.8, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mare.", "start": 158.8, "end": 159.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 159.159, "end": 159.779, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Il", "start": 159.779, "end": 159.879, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 159.879, "end": 159.899, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "questore", "start": 159.899, "end": 160.379, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 160.379, "end": 160.379, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "a", "start": 160.379, "end": 160.479, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 160.479, "end": 160.479, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Palermo", "start": 160.479, "end": 160.879, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 160.879, "end": 160.879, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "mi", "start": 160.879, "end": 160.979, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 160.979, "end": 160.979, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "ha", "start": 160.979, "end": 161.039, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 161.039, "end": 161.059, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "detto", "start": 161.059, "end": 161.259, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 161.259, "end": 161.279, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "che", "start": 161.279, "end": 161.46, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 161.46, "end": 161.519, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "tu", "start": 161.519, "end": 161.66, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 161.66, "end": 161.699, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "sai", "start": 161.699, "end": 161.94, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 161.94, "end": 161.94, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "già", "start": 161.94, "end": 162.079, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 162.079, "end": 162.119, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "chi", "start": 162.119, "end": 162.259, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 162.259, "end": 162.279, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "è", "start": 162.279, "end": 162.36, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 162.36, "end": 162.399, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "stato.", "start": 162.399, "end": 162.759, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 162.759, "end": 162.759, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Eh", "start": 162.759, "end": 162.94, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 162.94, "end": 163.0, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "magari,", "start": 163.0, "end": 163.579, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 163.579, "end": 164.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "è", "start": 164.119, "end": 164.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 164.299, "end": 164.319, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "un", "start": 164.319, "end": 164.419, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 164.419, "end": 164.44, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "affare", "start": 164.44, "end": 164.819, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 164.819, "end": 164.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "imbrogliato.", "start": 164.819, "end": 165.519, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 165.519, "end": 165.519, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 165.519, "end": 165.779, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 165.779, "end": 165.779, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "e", "start": 165.779, "end": 165.86, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 165.86, "end": 165.879, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "quattro", "start": 165.879, "end": 166.179, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 166.179, "end": 166.22, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 166.22, "end": 166.539, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 166.539, "end": 166.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "avuto", "start": 166.559, "end": 166.8, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 166.8, "end": 166.86, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "motivi", "start": 166.86, "end": 167.259, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 167.259, "end": 167.259, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "per", "start": 167.259, "end": 167.419, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 167.419, "end": 167.44, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ucciderla.", "start": 167.44, "end": 168.04, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 168.04, "end": 168.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "La", "start": 168.819, "end": 168.919, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 168.919, "end": 168.94, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "con<PERSON><PERSON><PERSON>?", "start": 168.94, "end": 169.499, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 169.499, "end": 170.119, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "E", "start": 170.119, "end": 170.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 170.139, "end": 170.279, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "come", "start": 170.279, "end": 170.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 170.559, "end": 170.58, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "si", "start": 170.58, "end": 170.699, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 170.699, "end": 170.72, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "faceva", "start": 170.72, "end": 171.08, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 171.08, "end": 171.08, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "a", "start": 171.08, "end": 171.1, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 171.1, "end": 171.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "non", "start": 171.119, "end": 171.239, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 171.239, "end": 171.239, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "vederla?", "start": 171.239, "end": 171.779, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 171.779, "end": 171.879, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Si", "start": 171.879, "end": 171.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 171.979, "end": 172.019, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 172.019, "end": 172.339, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 172.339, "end": 172.339, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "in", "start": 172.339, "end": 172.439, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 172.439, "end": 172.459, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mostra", "start": 172.459, "end": 172.8, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 172.8, "end": 172.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "tutto", "start": 172.819, "end": 173.0, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 173.0, "end": 173.0, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "il", "start": 173.0, "end": 173.079, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 173.079, "end": 173.08, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "gior<PERSON>.", "start": 173.08, "end": 173.439, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 173.439, "end": 173.599, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Era", "start": 173.599, "end": 173.86, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 173.86, "end": 173.899, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "molto", "start": 173.899, "end": 174.159, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 174.159, "end": 174.22, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "bella,", "start": 174.22, "end": 174.519, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 174.519, "end": 174.519, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "vero?", "start": 174.519, "end": 174.799, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 174.799, "end": 174.86, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON>,", "start": 174.86, "end": 175.179, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 175.179, "end": 175.179, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "roba", "start": 175.179, "end": 175.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 175.479, "end": 175.5, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "troppo", "start": 175.5, "end": 175.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 175.799, "end": 175.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 175.819, "end": 175.879, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 175.879, "end": 175.899, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "lusso", "start": 175.899, "end": 176.179, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 176.179, "end": 176.22, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "per", "start": 176.22, "end": 176.339, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 176.339, "end": 176.339, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "un'isola", "start": 176.339, "end": 176.719, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 176.719, "end": 176.72, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "come", "start": 176.72, "end": 176.899, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 176.899, "end": 176.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "questa.", "start": 176.919, "end": 177.419, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 177.419, "end": 177.419, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Dove", "start": 177.419, "end": 178.38, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 178.38, "end": 178.399, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "posso", "start": 178.399, "end": 178.619, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 178.619, "end": 178.639, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "trovare", "start": 178.639, "end": 178.979, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 178.979, "end": 179.0, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "questo", "start": 179.0, "end": 179.32, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 179.32, "end": 179.419, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON>", "start": 179.419, "end": 179.719, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 179.719, "end": 179.779, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Ingarsia?", "start": 179.779, "end": 180.519, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.519, "end": 181.199, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Su", "start": 181.199, "end": 181.38, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 181.38, "end": 181.399, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "alla", "start": 181.399, "end": 181.619, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 181.619, "end": 181.619, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "rocca,", "start": 181.619, "end": 182.059, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 182.059, "end": 182.259, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "dopo", "start": 182.259, "end": 182.439, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 182.439, "end": 182.459, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "la", "start": 182.459, "end": 182.539, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 182.539, "end": 182.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "chiesa", "start": 182.559, "end": 182.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 182.799, "end": 182.8, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "a", "start": 182.8, "end": 182.9, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 182.9, "end": 182.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "destra.", "start": 182.919, "end": 183.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 183.299, "end": 183.699, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Sta", "start": 183.699, "end": 183.819, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 183.819, "end": 183.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "r<PERSON><PERSON><PERSON>", "start": 183.819, "end": 184.279, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 184.279, "end": 184.279, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "las<PERSON><PERSON>,", "start": 184.279, "end": 184.639, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 184.639, "end": 184.879, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "non", "start": 184.879, "end": 185.0, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 185.0, "end": 185.039, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 185.039, "end": 185.219, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 185.219, "end": 185.22, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "più", "start": 185.22, "end": 185.339, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 185.339, "end": 185.339, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "vedere", "start": 185.339, "end": 185.599, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 185.599, "end": 185.599, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "nessuno.", "start": 185.599, "end": 186.059, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 186.059, "end": 186.22, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Va", "start": 186.22, "end": 186.319, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 186.319, "end": 186.36, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "bene,", "start": 186.36, "end": 186.679, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 186.679, "end": 186.679, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>,", "start": 186.679, "end": 187.199, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 187.199, "end": 187.199, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "ci", "start": 187.199, "end": 187.319, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 187.319, "end": 187.339, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "vediamo", "start": 187.339, "end": 187.639, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 187.639, "end": 187.72, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "più", "start": 187.72, "end": 187.839, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 187.839, "end": 187.839, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "tardi.", "start": 187.839, "end": 188.199, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 188.199, "end": 188.199, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Vengo", "start": 188.199, "end": 188.439, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 188.439, "end": 188.459, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "con", "start": 188.459, "end": 188.579, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 188.579, "end": 188.58, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "voi,", "start": 188.58, "end": 188.759, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 188.759, "end": 188.759, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "commissario,", "start": 188.759, "end": 189.259, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 189.259, "end": 189.3, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "quello", "start": 189.3, "end": 189.459, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 189.459, "end": 189.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "non", "start": 189.479, "end": 189.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 189.559, "end": 189.58, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ti", "start": 189.58, "end": 189.699, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 189.699, "end": 189.699, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "piace?", "start": 189.699, "end": 190.079, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 190.079, "end": 190.099, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "No,", "start": 190.099, "end": 190.339, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 190.339, "end": 190.379, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "non", "start": 190.379, "end": 190.459, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 190.459, "end": 190.5, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "c'è", "start": 190.5, "end": 190.619, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 190.619, "end": 190.639, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "bisogno.", "start": 190.639, "end": 191.02, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 191.02, "end": 191.039, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Pensa", "start": 191.039, "end": 191.279, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 191.279, "end": 191.279, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "piu<PERSON><PERSON>", "start": 191.279, "end": 191.639, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 191.639, "end": 191.639, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "al", "start": 191.639, "end": 191.739, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 191.739, "end": 191.739, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "mio", "start": 191.739, "end": 191.879, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 191.879, "end": 191.899, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "bagaglio.", "start": 191.899, "end": 192.379, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 192.379, "end": 192.819, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Va", "start": 192.819, "end": 193.0, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 193.0, "end": 193.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "bene,", "start": 193.059, "end": 193.319, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 193.319, "end": 193.319, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "provvedo", "start": 193.319, "end": 193.699, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 193.699, "end": 193.72, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "subito.", "start": 193.72, "end": 194.219, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 194.219, "end": 194.399, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Grazie.", "start": 194.399, "end": 194.9, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 194.9, "end": 210.599, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Cosa", "start": 210.599, "end": 210.86, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 210.86, "end": 210.86, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "volete?", "start": 210.86, "end": 211.339, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 211.339, "end": 212.419, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 212.419, "end": 212.959, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 212.959, "end": 212.979, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "via,", "start": 212.979, "end": 213.299, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 213.299, "end": 213.419, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "non", "start": 213.419, "end": 213.539, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 213.539, "end": 213.58, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "voglio", "start": 213.58, "end": 213.779, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 213.779, "end": 213.819, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "parlare", "start": 213.819, "end": 214.119, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 214.119, "end": 214.139, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "con", "start": 214.139, "end": 214.239, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 214.239, "end": 214.259, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "nessuno.", "start": 214.259, "end": 214.72, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 214.72, "end": 215.36, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 215.36, "end": 215.579, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 215.579, "end": 215.639, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "essere", "start": 215.639, "end": 215.959, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 215.959, "end": 215.959, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "lasciato", "start": 215.959, "end": 216.299, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 216.299, "end": 216.299, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "in", "start": 216.299, "end": 216.419, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 216.419, "end": 216.44, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "pace.", "start": 216.44, "end": 216.859, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 216.859, "end": 217.639, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Non", "start": 217.639, "end": 217.799, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 217.799, "end": 217.86, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "ti", "start": 217.86, "end": 218.0, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 218.0, "end": 218.019, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "conviene", "start": 218.019, "end": 218.599, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 218.599, "end": 218.639, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "sparare", "start": 218.639, "end": 219.199, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 219.199, "end": 219.199, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "in", "start": 219.199, "end": 219.339, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 219.339, "end": 219.339, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON>.", "start": 219.339, "end": 219.979, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 219.979, "end": 220.36, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Venite", "start": 220.36, "end": 220.759, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 220.759, "end": 220.759, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "ancora", "start": 220.759, "end": 221.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 221.019, "end": 221.039, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "avanti", "start": 221.039, "end": 221.339, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 221.339, "end": 221.339, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "e", "start": 221.339, "end": 221.419, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 221.419, "end": 221.419, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "lo", "start": 221.419, "end": 221.499, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 221.499, "end": 221.539, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "vedrete.", "start": 221.539, "end": 222.059, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 222.059, "end": 227.299, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Sono", "start": 227.299, "end": 228.219, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 228.219, "end": 228.239, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "il", "start": 228.239, "end": 228.319, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 228.319, "end": 228.399, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "commissario", "start": 228.399, "end": 228.859, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 228.859, "end": 228.899, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "De", "start": 228.899, "end": 229.0, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 229.0, "end": 229.039, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 229.039, "end": 229.419, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 229.419, "end": 229.5, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "della", "start": 229.5, "end": 229.699, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 229.699, "end": 229.72, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Mobile", "start": 229.72, "end": 230.059, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 230.059, "end": 230.08, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "di", "start": 230.08, "end": 230.179, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 230.179, "end": 230.239, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Palermo.", "start": 230.239, "end": 230.779, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 230.779, "end": 231.58, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Sono", "start": 231.58, "end": 231.799, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 231.799, "end": 231.839, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "qui", "start": 231.839, "end": 232.0, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 232.0, "end": 232.019, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "per", "start": 232.019, "end": 232.139, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 232.139, "end": 232.179, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "indagare", "start": 232.179, "end": 232.679, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 232.679, "end": 232.72, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "sulla", "start": 232.72, "end": 232.919, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 232.919, "end": 232.959, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "morte", "start": 232.959, "end": 233.239, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 233.239, "end": 233.239, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "di", "start": 233.239, "end": 233.319, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 233.319, "end": 233.319, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "vostra", "start": 233.319, "end": 233.679, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 233.679, "end": 233.72, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "moglie.", "start": 233.72, "end": 234.199, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 234.199, "end": 234.339, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Cosa", "start": 234.339, "end": 234.619, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 234.619, "end": 234.619, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "volete", "start": 234.619, "end": 234.979, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 234.979, "end": 234.979, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "che", "start": 234.979, "end": 235.099, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 235.099, "end": 235.099, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "vi", "start": 235.099, "end": 235.22, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 235.22, "end": 235.239, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "dica?", "start": 235.239, "end": 235.639, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 235.639, "end": 236.08, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON>.", "start": 236.08, "end": 236.699, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 236.699, "end": 243.199, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 243.199, "end": 243.599, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 243.599, "end": 243.599, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON>", "start": 243.599, "end": 244.0, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 244.0, "end": 244.0, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "a", "start": 244.0, "end": 244.119, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 244.119, "end": 244.119, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Palermo.", "start": 244.119, "end": 244.799, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 244.799, "end": 245.519, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Dove?", "start": 245.519, "end": 245.999, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 245.999, "end": 248.139, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "In", "start": 248.139, "end": 248.319, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 248.319, "end": 248.36, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "un", "start": 248.36, "end": 248.419, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 248.419, "end": 248.439, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "tabarè", "start": 248.439, "end": 248.799, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 248.799, "end": 248.799, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "del", "start": 248.799, "end": 249.02, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 249.02, "end": 249.08, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "porto.", "start": 249.08, "end": 249.579, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 249.579, "end": 249.819, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Ma", "start": 249.819, "end": 249.939, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 249.939, "end": 249.939, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "non", "start": 249.939, "end": 250.099, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 250.099, "end": 250.139, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "era", "start": 250.139, "end": 250.36, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 250.36, "end": 250.379, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "come", "start": 250.379, "end": 250.579, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 250.579, "end": 250.599, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "le", "start": 250.599, "end": 250.699, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 250.699, "end": 250.72, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "altre,", "start": 250.72, "end": 251.059, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 251.059, "end": 251.099, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "era", "start": 251.099, "end": 251.279, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 251.279, "end": 251.299, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "una", "start": 251.299, "end": 251.44, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 251.44, "end": 251.5, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "signora.", "start": 251.5, "end": 252.039, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 252.039, "end": 252.739, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Semb<PERSON>va", "start": 252.739, "end": 253.22, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 253.22, "end": 253.239, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "che", "start": 253.239, "end": 253.339, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 253.339, "end": 253.36, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "si", "start": 253.36, "end": 253.459, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 253.459, "end": 253.5, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "trovasse", "start": 253.5, "end": 253.899, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 253.899, "end": 253.919, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "lì", "start": 253.919, "end": 254.059, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 254.059, "end": 254.059, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "per", "start": 254.059, "end": 254.22, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 254.22, "end": 254.279, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "forza,", "start": 254.279, "end": 254.739, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 254.739, "end": 254.839, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "me", "start": 254.839, "end": 254.979, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 254.979, "end": 255.0, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "lo", "start": 255.0, "end": 255.079, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 255.079, "end": 255.119, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "diceva", "start": 255.119, "end": 255.479, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 255.479, "end": 255.5, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "sempre.", "start": 255.5, "end": 256.039, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 256.039, "end": 256.54, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Non", "start": 256.54, "end": 256.679, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 256.679, "end": 256.699, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "era", "start": 256.699, "end": 256.879, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 256.879, "end": 256.88, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "posto", "start": 256.88, "end": 257.16, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 257.16, "end": 257.179, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "per", "start": 257.179, "end": 257.339, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 257.339, "end": 257.359, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "lei", "start": 257.359, "end": 257.6, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 257.6, "end": 257.619, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "quello.", "start": 257.619, "end": 258.119, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 258.119, "end": 258.739, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Io", "start": 258.739, "end": 258.899, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 258.899, "end": 258.919, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "andavo", "start": 258.919, "end": 259.259, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 259.259, "end": 259.299, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "spesso", "start": 259.299, "end": 259.6, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 259.6, "end": 259.6, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "a", "start": 259.6, "end": 259.679, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 259.679, "end": 259.699, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Palermo", "start": 259.699, "end": 260.119, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 260.119, "end": 260.139, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "per", "start": 260.139, "end": 260.279, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 260.279, "end": 260.279, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "affari.", "start": 260.279, "end": 260.82, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 260.82, "end": 261.179, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Che", "start": 261.179, "end": 261.319, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 261.319, "end": 261.339, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "affari?", "start": 261.339, "end": 261.839, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 261.839, "end": 262.059, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>.", "start": 262.059, "end": 262.619, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 262.619, "end": 263.279, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Ce", "start": 263.279, "end": 263.419, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 263.419, "end": 263.44, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "ne", "start": 263.44, "end": 263.559, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 263.559, "end": 263.579, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "sono", "start": 263.579, "end": 263.779, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 263.779, "end": 263.82, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "tante", "start": 263.82, "end": 264.099, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 264.099, "end": 264.16, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "qui", "start": 264.16, "end": 264.279, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 264.279, "end": 264.279, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "intorno.", "start": 264.279, "end": 264.799, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 264.799, "end": 265.88, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON>", "start": 265.88, "end": 266.0, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 266.0, "end": 266.04, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "andava", "start": 266.04, "end": 266.299, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 266.299, "end": 266.32, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "bene", "start": 266.32, "end": 266.539, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 266.539, "end": 266.559, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "allora", "start": 266.559, "end": 266.899, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 266.899, "end": 266.94, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "e", "start": 266.94, "end": 267.04, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 267.04, "end": 267.059, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "di", "start": 267.059, "end": 267.179, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 267.179, "end": 267.179, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "soldi", "start": 267.179, "end": 267.479, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 267.479, "end": 267.479, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "ne", "start": 267.479, "end": 267.6, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 267.6, "end": 267.619, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "facevo", "start": 267.619, "end": 267.959, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 267.959, "end": 267.959, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "par<PERSON><PERSON>.", "start": 267.959, "end": 268.48, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 268.48, "end": 269.279, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "La", "start": 269.279, "end": 269.399, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 269.399, "end": 269.399, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 269.399, "end": 269.799, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 269.799, "end": 269.82, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "di", "start": 269.82, "end": 269.919, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 269.919, "end": 269.919, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "regali", "start": 269.919, "end": 270.339, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 270.339, "end": 270.699, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "e", "start": 270.699, "end": 270.819, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 270.819, "end": 270.839, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "mi", "start": 270.839, "end": 270.939, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 270.939, "end": 270.94, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "sposò.", "start": 270.94, "end": 271.399, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 271.399, "end": 271.899, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "E", "start": 271.899, "end": 271.979, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 271.979, "end": 271.979, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "vostra", "start": 271.979, "end": 272.279, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 272.279, "end": 272.32, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "figlia?", "start": 272.32, "end": 272.659, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 272.659, "end": 273.38, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Le", "start": 273.38, "end": 273.619, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 273.619, "end": 273.66, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "scris<PERSON>", "start": 273.66, "end": 274.079, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 274.079, "end": 274.079, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "una", "start": 274.079, "end": 274.239, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 274.239, "end": 274.259, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "lettera", "start": 274.259, "end": 274.6, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 274.6, "end": 274.6, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "per", "start": 274.6, "end": 274.739, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 274.739, "end": 274.759, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "farle", "start": 274.759, "end": 274.999, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 274.999, "end": 275.0, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "sapere", "start": 275.0, "end": 275.359, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 275.359, "end": 275.359, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "che", "start": 275.359, "end": 275.48, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 275.48, "end": 275.5, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "mi", "start": 275.5, "end": 275.58, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 275.58, "end": 275.6, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "sposavo", "start": 275.6, "end": 275.999, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 275.999, "end": 276.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "di", "start": 276.019, "end": 276.099, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 276.099, "end": 276.119, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "nuovo.", "start": 276.119, "end": 276.499, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 276.499, "end": 277.16, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 277.16, "end": 277.339, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 277.339, "end": 277.5, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "uno", "start": 277.5, "end": 277.619, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 277.619, "end": 277.639, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "deve", "start": 277.639, "end": 277.799, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 277.799, "end": 277.82, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "avere", "start": 277.82, "end": 278.1, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 278.1, "end": 278.1, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "una", "start": 278.1, "end": 278.239, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 278.239, "end": 278.279, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "donna,", "start": 278.279, "end": 278.579, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 278.579, "end": 278.579, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "capite?", "start": 278.579, "end": 279.04, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 279.04, "end": 279.6, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Non", "start": 279.6, "end": 279.739, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 279.739, "end": 279.759, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "pot<PERSON>", "start": 279.759, "end": 280.059, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 280.059, "end": 280.1, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "rimanere", "start": 280.1, "end": 280.459, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 280.459, "end": 280.479, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "vedovo", "start": 280.479, "end": 280.819, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 280.819, "end": 280.82, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "per", "start": 280.82, "end": 280.979, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 280.979, "end": 281.0, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "tutta", "start": 281.0, "end": 281.199, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 281.199, "end": 281.22, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "la", "start": 281.22, "end": 281.299, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 281.299, "end": 281.32, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "vita.", "start": 281.32, "end": 281.659, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 281.659, "end": 282.359, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "E", "start": 282.359, "end": 282.5, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 282.5, "end": 282.519, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "così,", "start": 282.519, "end": 282.899, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 282.899, "end": 284.179, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "una", "start": 284.179, "end": 284.399, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 284.399, "end": 284.399, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "mattina", "start": 284.399, "end": 284.72, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 284.72, "end": 284.739, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "me", "start": 284.739, "end": 284.839, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 284.839, "end": 284.859, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "la", "start": 284.859, "end": 284.959, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 284.959, "end": 284.979, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "portai", "start": 284.979, "end": 285.259, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 285.259, "end": 285.279, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "in", "start": 285.279, "end": 285.399, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 285.399, "end": 285.419, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "casa.", "start": 285.419, "end": 285.76, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 285.76, "end": 286.739, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Era", "start": 286.739, "end": 287.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 287.019, "end": 287.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "tanto", "start": 287.019, "end": 287.48, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 287.48, "end": 287.519, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "bella.", "start": 287.519, "end": 287.959, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}]}}, "created_at": 1754319467.6338477}