"""
EvaTrans 幻觉修复工具 - 核心模块

独立的幻觉修复工具核心模块，通过导入方式复用主程序的服务组件。
完全独立于主程序，不修改任何主程序代码。

主要组件：
- models: 数据模型定义
- services: 核心服务实现
- config: 独立配置管理
- utils: 工具函数
"""

from .models import SubtitleEntry, LongEntryRefineRequest, LongEntryRefineResult, AudioSegment
from .services import LongEntryRefinerService
from .config import RefineConfig
from .utils import calculate_entry_duration, parse_srt_time
from .exceptions import (
    RefineError, ConfigurationError, ServiceInitializationError,
    FileProcessingError, ASRServiceError, LLMServiceError,
    AudioProcessingError, SubtitleParsingError
)

__all__ = [
    'SubtitleEntry',
    'LongEntryRefineRequest',
    'LongEntryRefineResult',
    'AudioSegment',
    'LongEntryRefinerService',
    'RefineConfig',
    'calculate_entry_duration',
    'parse_srt_time',
    'RefineError',
    'ConfigurationError',
    'ServiceInitializationError',
    'FileProcessingError',
    'ASRServiceError',
    'LLMServiceError',
    'AudioProcessingError',
    'SubtitleParsingError'
]
