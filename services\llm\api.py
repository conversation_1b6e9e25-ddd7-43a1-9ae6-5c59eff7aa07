"""
统一LLM API服务

提供多API配置管理、自动故障转移、缓存支持等功能。
支持OpenAI和Gemini格式的API调用。
"""

import os
import json
import time
import requests
import logging
from typing import List, Optional, Dict, Any
from dataclasses import dataclass, asdict
from enum import Enum


from .exceptions import LLMError
from .logger_mixin import LLMLoggerMixin


class APIFormat(Enum):
    """API格式枚举"""
    OPENAI = "openai"
    GEMINI = "gemini"


@dataclass
class LLMConfig:
    """LLM API配置"""
    id: str
    name: str
    base_url: str
    model: str
    api_key: str
    format_type: APIFormat
    enabled: bool = True
    timeout: int = 180
    retry_count: int = 3
    temperature: float = 0.7
    request_interval: float = 10.0
    available_models: List[str] = None

    def __post_init__(self):
        if self.available_models is None:
            self.available_models = []


class LLMAPIService(LLMLoggerMixin):
    """统一LLM API服务

    提供多API配置管理、自动故障转移、缓存支持、错误处理等功能。
    支持OpenAI和Gemini格式的API调用。
    """
    
    def __init__(self, config_file_path: str = None):
        """初始化LLM API服务

        Args:
            config_file_path: 配置文件路径
        """
        super().__init__()

        self.config_file = config_file_path or self._get_default_config_file()
        self._config_data = None

        self.configs: List[LLMConfig] = []

        # 初始化时加载配置
        self._load_configs()

    def _get_default_config_file(self) -> str:
        """获取默认配置文件路径"""
        config_dir = os.path.join(os.path.expanduser("~"), ".evatrans_gui")
        return os.path.join(config_dir, "config.json")

    def _load_config(self) -> dict:
        """加载配置文件"""
        if self._config_data is None:
            try:
                if os.path.exists(self.config_file):
                    with open(self.config_file, 'r', encoding='utf-8') as f:
                        self._config_data = json.load(f)
                else:
                    self._config_data = {}
            except Exception as e:
                print(f"Warning: Failed to load config file: {e}")
                self._config_data = {}
        return self._config_data

    def get_config(self, key: str, default=None):
        """获取配置值"""
        config = self._load_config()
        return config.get(key, default)

    def log(self, message: str, signals_forwarder=None):
        """记录日志消息，支持信号转发（向后兼容）"""
        if signals_forwarder and hasattr(signals_forwarder, 'log_message') and hasattr(signals_forwarder.log_message, 'emit'):
            signals_forwarder.log_message.emit(f"[LLM API] {message}")
        else:
            self._log_info(f"[LLM API] {message}")
    
    def call_api_for_segmentation(
        self,
        text_to_segment: str,
        api_key: str = None,
        custom_api_base_url: str = None,
        custom_model_name: str = None,
        custom_temperature: float = None,
        target_language: str = None,
        signals_forwarder=None
    ) -> Optional[List[str]]:
        """调用LLM API进行文本分割

        Args:
            text_to_segment: 待分割文本
            api_key: API密钥（可选）
            custom_api_base_url: 自定义API基础URL
            custom_model_name: 自定义模型名称
            custom_temperature: 自定义温度参数
            target_language: 目标语言
            signals_forwarder: 信号转发器

        Returns:
            Optional[List[str]]: 分割后的文本列表，失败时返回None
        """
        try:
            # 验证输入参数
            if text_to_segment is None:
                raise ValueError("text_to_segment cannot be None")

            if not isinstance(text_to_segment, str):
                raise TypeError("text_to_segment must be a string")

            if api_key is not None and not isinstance(api_key, str):
                raise TypeError("api_key must be a string or None")

            # 根据参数选择调用方式
            if api_key or custom_api_base_url or custom_model_name:
                result = self._call_single_api(
                    api_key=api_key or "",
                    text_to_segment=text_to_segment,
                    custom_api_base_url_str=custom_api_base_url,
                    custom_model_name=custom_model_name,
                    custom_temperature=custom_temperature,
                    target_language=target_language,
                    signals_forwarder=signals_forwarder
                )
            else:
                result = self._call_multi_api(
                    text_to_segment=text_to_segment,
                    target_language=target_language,
                    signals_forwarder=signals_forwarder
                )

            return result
            
        except Exception as e:
            error_msg = f"LLM API调用异常: {e}"
            self.log(error_msg, signals_forwarder)
            raise LLMError(error_msg) from e
    


    def _load_configs(self):
        """从配置文件加载API配置"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                configs_data = data.get('llm_api_configs', [])
                self.configs = []

                if not configs_data:
                    # 配置文件中没有LLM配置，创建基础配置供UI使用
                    self._create_default_configs()
                    return

                for config_data in configs_data:
                    try:
                        config = LLMConfig(
                            id=config_data['id'],
                            name=config_data['name'],
                            base_url=config_data['base_url'],
                            model=config_data['model'],
                            api_key=config_data['api_key'],
                            format_type=APIFormat(config_data['format_type']) if isinstance(config_data['format_type'], str) else config_data['format_type'],
                            enabled=config_data.get('enabled', True),
                            timeout=config_data.get('timeout', 180),
                            retry_count=config_data.get('retry_count', 3),
                            temperature=config_data.get('temperature', 0.7),
                            request_interval=config_data.get('request_interval', 10.0),
                            available_models=config_data.get('available_models', config_data.get('models', []))
                        )
                        self.configs.append(config)
                    except (KeyError, ValueError) as e:
                        self.log(f"跳过无效配置 {config_data.get('name', 'Unknown')}: {e}")
                        import traceback
                        self.log(f"配置数据: {config_data}")
                        self.log(f"错误详情: {traceback.format_exc()}")
                        continue

                if not self.configs:
                    # 配置文件中没有有效的LLM配置，创建基础配置供UI使用
                    self._create_default_configs()
            else:
                # 配置文件不存在，创建基础配置供UI使用
                self._create_default_configs()

        except Exception as e:
            self._log_config_operation("加载", False, str(e))
            # 创建基础配置供UI使用
            self._create_default_configs()


    def _has_complete_config(self) -> bool:
        """检查是否已有完整的配置（避免覆盖config_manager的配置）"""
        try:
            if not os.path.exists(self.config_file):
                return False

            with open(self.config_file, 'r', encoding='utf-8') as f:
                data = json.load(f)

            configs_data = data.get('llm_api_configs', [])
            if not configs_data:
                return False

            # 检查是否有完整配置（有base_url和model的配置）
            for config_data in configs_data:
                if (config_data.get('base_url', '').strip() and
                    config_data.get('model', '').strip()):
                    return True  # 找到完整配置，不需要创建默认配置

            return False
        except:
            return False

    def _create_default_configs(self):
        """创建默认配置（智能版本 - 不覆盖完整配置）"""
        # 如果已有完整配置，不创建默认配置
        if self._has_complete_config():
            return

        self.configs = [
            LLMConfig(
                id="config_1",
                name="API1",
                base_url="",
                model="",
                api_key="",
                format_type=APIFormat.OPENAI,
                enabled=True,
                timeout=180,
                retry_count=3,
                temperature=0.01,
                request_interval=10.0,
                available_models=[]
            ),
            LLMConfig(
                id="config_2",
                name="API2",
                base_url="",
                model="",
                api_key="",
                format_type=APIFormat.OPENAI,
                enabled=True,
                timeout=180,
                retry_count=3,
                temperature=0.01,
                request_interval=10.0,
                available_models=[]
            ),
            LLMConfig(
                id="config_3",
                name="API3",
                base_url="",
                model="",
                api_key="",
                format_type=APIFormat.OPENAI,
                enabled=False,
                timeout=180,
                retry_count=3,
                temperature=0.02,
                request_interval=10.0,
                available_models=[]
            )
        ]
        # 不立即保存，让config_manager决定何时保存

    def save_configs(self):
        """保存配置到文件"""
        try:
            # 只有在有配置时才保存
            if not self.configs:
                return

            config_data = {}
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)

            config_data['llm_api_configs'] = [asdict(config) for config in self.configs]

            # 转换枚举为字符串
            for config_dict in config_data['llm_api_configs']:
                config_dict['format_type'] = config_dict['format_type'].value

            os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)

            self._log_debug(f"成功保存{len(self.configs)}个LLM配置")

        except Exception as e:
            self.log(f"保存LLM配置失败: {e}")

    def get_active_configs(self) -> List[LLMConfig]:
        """获取已启用的配置"""
        return [config for config in self.configs if config.enabled and config.api_key.strip()]

    def _call_multi_api(
        self,
        text_to_segment: str,
        target_language: str = None,
        signals_forwarder=None
    ) -> Optional[List[str]]:
        """调用所有开启的API"""
        configs = self.get_active_configs()

        if not configs:
            self.log("错误: 没有可用的API配置", signals_forwarder)
            return None

        for i, config in enumerate(configs):
            self.log(f"调用 {config.name} API...", signals_forwarder)

            result = self._call_single_api_config(
                config=config,
                text_to_segment=text_to_segment,
                target_language=target_language,
                signals_forwarder=signals_forwarder
            )

            if result:
                self.log(f"✅ {config.name} API调用成功", signals_forwarder)
                return result
            else:
                self.log(f"❌ {config.name} API调用失败", signals_forwarder)

        return None

    def _call_single_api_config(
        self,
        config: LLMConfig,
        text_to_segment: str,
        target_language: str = None,
        signals_forwarder=None
    ) -> Optional[List[str]]:
        """使用配置调用单个API"""
        return self._call_single_api(
            api_key=config.api_key,
            text_to_segment=text_to_segment,
            custom_api_base_url_str=config.base_url,
            custom_model_name=config.model,
            custom_temperature=config.temperature,
            target_language=target_language,
            signals_forwarder=signals_forwarder
        )

    def _call_single_api(
        self,
        api_key: str,
        text_to_segment: str,
        custom_api_base_url_str: Optional[str],
        custom_model_name: Optional[str],
        custom_temperature: Optional[float],
        target_language: Optional[str] = None,
        signals_forwarder=None
    ) -> Optional[List[str]]:
        """单个API调用实现"""
        try:
            if not self._is_running(signals_forwarder):
                self.log("API调用前任务已取消", signals_forwarder)
                return None

            target_url, effective_model = self._parse_api_url_and_model(
                custom_api_base_url_str,
                custom_model_name
            )
            api_format = APIFormat.GEMINI if "gemini" in (effective_model or "").lower() or (custom_api_base_url_str and "gemini" in custom_api_base_url_str.lower()) else APIFormat.OPENAI

            effective_temperature = custom_temperature
            system_prompt = self.get_config('user_llm_system_prompt')
            user_content = text_to_segment

            if api_format == APIFormat.GEMINI:
                headers = {"Content-Type": "application/json"}
                if "?" in target_url:
                    target_url += f"&key={api_key.strip()}"
                else:
                    target_url += f"?key={api_key.strip()}"
                request_body = {
                    "contents": [
                        {"role": "user", "parts": [{"text": user_content}]}
                    ],
                    "systemInstruction": {"parts": [{"text": system_prompt}]},
                    "generationConfig": {
                        "temperature": effective_temperature,
                        "maxOutputTokens": 48000
                    }
                }
            else:
                headers = {
                    "Authorization": f"Bearer {api_key.strip()}",
                    "Content-Type": "application/json"
                }
                request_body = {
                    "model": effective_model,
                    "messages": [
                        {"role": "system", "content": system_prompt},
                        {"role": "user", "content": user_content}
                    ],
                    "temperature": effective_temperature,
                    "max_tokens": 48000
                }

            self.log(f"调用API: {target_url}", signals_forwarder)
            response = requests.post(
                target_url,
                headers=headers,
                json=request_body,
                timeout=30
            )

            if response.status_code == 200:
                data = response.json()
                if api_format == APIFormat.GEMINI:
                    if "candidates" in data and len(data["candidates"]) > 0:
                        content = data["candidates"][0]["content"]["parts"][0]["text"]
                        lines = [line.strip() for line in content.split('\n') if line.strip()]
                        if lines:
                            self.log(f"分割成功: {len(lines)} 个片段", signals_forwarder)
                            return lines
                        else:
                            self.log("API返回空内容", signals_forwarder)
                            return None
                    else:
                        self.log("API响应格式错误", signals_forwarder)
                        return None
                else:
                    if "choices" in data and len(data["choices"]) > 0:
                        content = data["choices"][0]["message"]["content"]
                        lines = [line.strip() for line in content.split('\n') if line.strip()]
                        if lines:
                            self.log(f"分割成功: {len(lines)} 个片段", signals_forwarder)
                            return lines
                        else:
                            self.log("API返回空内容", signals_forwarder)
                            return None
                    else:
                        self.log("API响应格式错误", signals_forwarder)
                        return None
            else:
                self.log(f"API调用失败: {response.status_code} - {response.text}", signals_forwarder)
                return None

        except requests.exceptions.Timeout:
            self.log("API调用超时", signals_forwarder)
            return None
        except Exception as e:
            self.log(f"API调用异常: {e}", signals_forwarder)
            return None

    def _parse_api_url_and_model(
        self,
        input_base_url_str: Optional[str],
        input_model_name: Optional[str]
    ) -> tuple[str, str]:
        """解析API URL和模型名称"""
        effective_model = input_model_name if input_model_name else ""

        if not input_base_url_str:
            raise ValueError("API基础URL不能为空")

        raw_url = input_base_url_str.strip()

        if raw_url.endswith("#"):
            final_url = raw_url[:-1]
        elif "/v1" in raw_url or "/v2" in raw_url:
            if not raw_url.endswith('/'):
                raw_url += '/'
            if not raw_url.endswith('chat/completions'):
                final_url = raw_url + "chat/completions"
            else:
                final_url = raw_url
        elif raw_url.endswith('/'):
            final_url = raw_url + "v1/chat/completions"
        else:
            final_url = raw_url + "/v1/chat/completions"

        return final_url, effective_model



    def _is_running(self, signals_forwarder) -> bool:
        """检查任务是否仍在运行"""
        if signals_forwarder and hasattr(signals_forwarder, 'parent') and hasattr(signals_forwarder.parent(), 'is_running'):
            return signals_forwarder.parent().is_running
        return True

    def test_connection(
        self,
        api_key: str,
        custom_api_base_url_str: Optional[str],
        custom_model_name: Optional[str],
        custom_temperature: Optional[float],
        signals_forwarder=None
    ) -> tuple[bool, str]:
        """测试LLM连接"""
        try:
            target_url, effective_model = self._parse_api_url_and_model(
                custom_api_base_url_str, custom_model_name
            )

            headers = {
                "Authorization": f"Bearer {api_key.strip()}",
                "Content-Type": "application/json"
            }

            request_body = {
                "model": effective_model,
                "messages": [{"role": "user", "content": "Hello"}],
                "temperature": custom_temperature or 0.2,
                "max_tokens": 10
            }

            self.log(f"测试连接: {target_url}", signals_forwarder)

            response = requests.post(
                target_url,
                headers=headers,
                json=request_body,
                timeout=30
            )

            if response.status_code == 200:
                data = response.json()

                if ("choices" in data and isinstance(data["choices"], list) and
                    len(data["choices"]) > 0 and isinstance(data["choices"][0], dict) and
                    "message" in data["choices"][0] and isinstance(data["choices"][0]["message"], dict) and
                    "content" in data["choices"][0]["message"]):
                    return True, f"连接成功！模型 {effective_model} 在 {target_url} 返回了响应。"

                elif ("candidates" in data and isinstance(data["candidates"], list) and
                      len(data["candidates"]) > 0 and isinstance(data["candidates"][0], dict) and
                      data["candidates"][0].get("content", {}).get("parts", [{}]) and
                      isinstance(data["candidates"][0].get("content").get("parts"), list) and
                      len(data["candidates"][0].get("content").get("parts")) > 0 and
                      isinstance(data["candidates"][0].get("content").get("parts")[0], dict) and
                      data["candidates"][0].get("content").get("parts")[0].get("text") is not None):
                    return True, f"连接成功！模型 {effective_model} 在 {target_url} 返回了响应。"
                else:
                    return True, f"连接测试：收到HTTP 200响应，但响应内容格式未知或不完整。模型: {effective_model}, URL: {target_url}. 响应: {str(data)[:200]}"
            else:
                return False, f"连接失败: HTTP {response.status_code} - {response.text[:200]}"

        except requests.exceptions.Timeout:
            return False, f"连接超时 (30秒)。URL: {target_url}"
        except requests.exceptions.ConnectionError:
            return False, f"连接错误。请检查网络连接和URL: {target_url}"
        except Exception as e:
            return False, f"连接测试异常: {e}"

    def test_all_configs(self, signals_forwarder=None) -> Dict[str, tuple[bool, str]]:
        """测试所有配置的API"""
        results = {}

        for config in self.configs:
            if config.enabled and config.api_key.strip():
                self.log(f"测试 {config.name} 配置...", signals_forwarder)

                success, message = self.test_connection(
                    api_key=config.api_key,
                    custom_api_base_url_str=config.base_url,
                    custom_model_name=config.model,
                    custom_temperature=config.temperature,
                    signals_forwarder=signals_forwarder
                )

                results[config.id] = (success, message)
            else:
                results[config.id] = (False, "配置未启用或缺少API密钥")

        return results
