#!/usr/bin/env python3
"""
设置管理模块
负责所有设置页面的创建、管理和事件处理
"""

import flet as ft
from typing import Dict, Any
from services.llm import get_multi_llm_service, LLMConfig, APIFormat




class SettingsManager:
    """设置管理器 - 统一管理所有设置页面的创建、显示和交互

    核心功能：
    - ASR服务设置界面
    - LLM API设置界面
    - 目录配置界面
    - 设置数据验证和保存
    """

    def __init__(self, app):
        """初始化设置管理器，建立与主应用的连接

        Args:
            app: 主应用实例，提供配置和主题接口
        """
        self.app = app
        self.page = app.page

        # 设置相关状态
        self.current_settings_view = "asr"
        self.menu_items = {}
        self.settings_content = None

        # 设置字段引用
        self.project_path_field = None
        self.audio_file_path_field = None

        # ElevenLabs设置字段
        self.elevenlabs_mode_dropdown = None
        self.elevenlabs_api_key_field = None
        self.scribe_model_dropdown = None
        self.transcription_language_dropdown = None
        self.elevenlabs_audio_events_dropdown = None
        self.elevenlabs_api_settings_container = None

        # AssemblyAI设置字段
        self.assemblyai_api_key_field = None
        self.assemblyai_speech_model_dropdown = None
        self.assemblyai_language_dropdown = None

        # Deepgram设置字段
        self.deepgram_api_key_field = None
        self.deepgram_model_dropdown = None
        self.deepgram_language_dropdown = None

        # ASR服务开关
        self.elevenlabs_switch = None
        self.assemblyai_switch = None
        self.deepgram_switch = None

        # LLM相关字段
        self.api1_switch = None
        self.api2_switch = None
        self.api3_switch = None
        self.api1_interval_field = None
        self.api2_interval_field = None
        self.api3_interval_field = None
        self.model_selectors = {}

        # 调试模式状态
        self.debug_mode = getattr(app, 'debug_mode', False)

    def _sanitize_api_key(self, api_key: str) -> str:
        """清理API密钥格式，移除多余的空白字符

        Args:
            api_key: 用户输入的原始API密钥

        Returns:
            str: 清理后的API密钥字符串
        """
        if not api_key:
            return ""
        return api_key.strip()

    def create_settings_tab(self):
        """创建设置标签页，包含左侧菜单和右侧内容区域"""
        colors = self.app.theme_manager.get_theme_colors()

        # 设置菜单
        settings_menu = ft.Container(
            content=ft.Container(
                content=ft.Column([
                    self.create_settings_menu_item("asr", "ASR设置", True),
                    ft.Container(height=15),
                    self.create_settings_menu_item("llm", "LLM设置"),
                    ft.Container(height=15),
                    self.create_settings_menu_item("directory", "目录设置")
                ]),
                padding=25,
                bgcolor=colors['bg_color'],
                border_radius=12,
            ),
            width=280,
            bgcolor=colors['glass_bg'],
            border_radius=12,
            margin=20,
            border=ft.border.all(1, colors['border_color']),
            # 移除阴影和毛玻璃效果，保持苹果简约风格
        )

        # 设置内容区域 - 双层结构：外层微灰背景，内层实心背景
        self.settings_content = ft.Container(
            content=ft.Container(
                content=ft.Column([
                    self.create_asr_settings()
                ], scroll=ft.ScrollMode.AUTO, expand=True),
                padding=30,
                bgcolor=colors['bg_color'],  # 内层实心背景
                border_radius=12,
                expand=True,
            ),
            expand=True,
            bgcolor=colors['glass_bg'],  # 外层微灰背景
            border_radius=12,
            margin=ft.margin.only(top=20, bottom=20, right=20),
            border=ft.border.all(1, colors['border_color']),
            # 苹果简约风格，无阴影和毛玻璃效果
        )

        return ft.Container(
            content=ft.Row([
                settings_menu,
                ft.Container(width=20),
                self.settings_content
            ], expand=True),
            padding=20
        )

    def create_settings_menu_item(self, view_name: str, display_name: str, selected: bool = False):
        """创建设置菜单项 - 卡片式大按钮布局"""
        colors = self.app.theme_manager.get_theme_colors()

        # 图标映射
        icon_map = {
            'asr': "🎧",
            'llm': "✨",
            'directory': "📁"
        }

        # 创建图标
        icon_widget = ft.Text(
            icon_map.get(view_name, "⚙️"),
            size=24,
            color=colors['accent_color'] if selected else colors['secondary_text_color']
        )

        # 创建文本
        text_widget = ft.Text(
            display_name,
            size=18,
            weight="bold" if selected else "normal",
            color=colors['text_color']
        )

        # 创建内容行
        content_row = ft.Row([
            icon_widget,
            ft.Container(width=12),  # 图标和文字间距
            text_widget
        ], alignment=ft.MainAxisAlignment.START)

        # 创建大尺寸卡片容器
        container = ft.Container(
            content=content_row,
            padding=ft.padding.all(20),  # 增大padding
            border_radius=12,
            bgcolor=colors['menu_selected'] if selected else colors['menu_unselected'],
            border=ft.border.all(2, colors['accent_color']) if selected else ft.border.all(1, colors['border_color']),
            on_click=lambda e: self.switch_settings_view(view_name),
            ink=True,
            animate=200  # 添加动画
        )


        self.menu_items[view_name] = {
            'container': container,
            'text': text_widget,
            'icon': icon_widget
        }

        return container

    def switch_settings_view(self, view_name: str):
        """切换设置视图"""
        self.current_settings_view = view_name


        self.update_menu_selection()


        if view_name == "asr":
            content = self.create_asr_settings()
        elif view_name == "directory":
            content = self.create_directory_settings()
        elif view_name == "llm":
            content = self.create_llm_settings()
        elif view_name == "transcription":
            content = self.create_transcription_settings()
        else:
            content = ft.Text("未知设置页面")


        if self.settings_content and hasattr(self.settings_content, 'content') and hasattr(self.settings_content.content, 'content'):
            self.settings_content.content.content.controls = [content]
            self.app.page.update()

    def update_menu_selection(self):
        """更新菜单项的选中状态"""
        colors = self.app.theme_manager.get_theme_colors()

        for view_name, item in self.menu_items.items():
            selected = (view_name == self.current_settings_view)


            item['container'].bgcolor = colors['menu_selected'] if selected else colors['menu_unselected']


            item['container'].border = ft.border.all(2, colors['accent_color']) if selected else ft.border.all(1, colors['border_color'])


            item['text'].weight = "bold" if selected else "normal"
            item['text'].color = colors['text_color']


            if 'icon' in item:
                item['icon'].color = colors['accent_color'] if selected else colors['secondary_text_color']

    def create_directory_settings(self):
        """创建目录设置内容"""
        colors = self.app.theme_manager.get_theme_colors()

        # 项目目录设置
        if not hasattr(self, 'project_path_field') or self.project_path_field is None:
            self.project_path_field = ft.TextField(
                label="项目目录",
                value=self.app.config_manager.config.get('project_path', './projects'),
                read_only=False,  # 支持手动输入
                expand=True,
                on_change=self._on_project_path_change,  # 实时保存
                border_color=colors['input_border'],
                color=colors['text_color'],
                label_style=ft.TextStyle(color=colors['secondary_text_color']),
                bgcolor=colors['input_bg']
            )
        else:
            self.project_path_field.border_color = colors['input_border']
            self.project_path_field.color = colors['text_color']
            self.project_path_field.label_style = ft.TextStyle(color=colors['secondary_text_color'])
            self.project_path_field.bgcolor = colors['input_bg']
            self.project_path_field.value = self.app.config_manager.config.get('project_path', './projects')

        # 浏览目录按钮 - 雾霾蓝风格
        browse_project_btn = ft.ElevatedButton(
            "浏览目录",
            icon=ft.Icons.FOLDER_OPEN,
            bgcolor=colors['accent_color'],  # 雾霾蓝
            color="#FFFFFF",  # 白色文字
            on_click=self.app._browse_project_directory,
            style=ft.ButtonStyle(
                shape=ft.RoundedRectangleBorder(radius=8),
                elevation=0,
            )
        )

        # 字幕目录设置
        if not hasattr(self, 'subtitle_path_field') or self.subtitle_path_field is None:
            self.subtitle_path_field = ft.TextField(
                label="字幕目录",
                value=self.app.config_manager.config.get('subtitle_path', './subtitles'),
                read_only=False,  # 支持手动输入
                expand=True,
                on_change=self._on_subtitle_path_change,  # 实时保存
                border_color=colors['input_border'],
                color=colors['text_color'],
                label_style=ft.TextStyle(color=colors['secondary_text_color']),
                bgcolor=colors['input_bg']
            )
        else:
            self.subtitle_path_field.border_color = colors['input_border']
            self.subtitle_path_field.color = colors['text_color']
            self.subtitle_path_field.label_style = ft.TextStyle(color=colors['secondary_text_color'])
            self.subtitle_path_field.bgcolor = colors['input_bg']
            self.subtitle_path_field.value = self.app.config_manager.config.get('subtitle_path', './subtitles')

        # 浏览字幕目录按钮 - 雾霾蓝风格
        browse_subtitle_btn = ft.ElevatedButton(
            "浏览目录",
            icon=ft.Icons.FOLDER_OPEN,
            bgcolor=colors['accent_color'],  # 雾霾蓝
            color="#FFFFFF",  # 白色文字
            on_click=self.app._browse_subtitle_directory,
            style=ft.ButtonStyle(
                shape=ft.RoundedRectangleBorder(radius=8),
                elevation=0,
            )
        )



        return ft.Column([
            ft.Text("目录设置", size=18, weight="bold", color=colors['text_color']),
            ft.Container(height=20),

            ft.Row([
                self.project_path_field,
                browse_project_btn
            ]),
            ft.Container(height=20),

            ft.Row([
                self.subtitle_path_field,
                browse_subtitle_btn
            ])
        ])



    # 占位方法 - 这些方法将在后续添加
    def create_llm_settings(self):
        """创建多LLM管理界面"""
        colors = self.app.theme_manager.get_theme_colors()
        service = get_multi_llm_service()

        # 确保有3个配置
        if len(service.configs) == 0:
            service.llm_service._create_default_configs()

        # 初始化控件引用字典
        if not hasattr(self, 'model_selectors'):
            self.model_selectors = {}

        # 调试模式开关
        debug_switch = ft.Row([
            ft.Icon("bug_report", size=16, color=colors['text_color']),
            ft.Text("调试模式", size=14, color=colors['text_color'], weight="bold"),
            ft.Switch(
                value=self.debug_mode,
                on_change=self._on_debug_mode_change,
                active_color=colors['accent_color']  # 雾霾蓝
            ),
            ft.Text("(显示详细的API调试信息)", size=12, color=colors['secondary_text_color'])
        ], alignment=ft.MainAxisAlignment.START)

        debug_container = ft.Container(
            content=debug_switch,
            padding=ft.padding.symmetric(horizontal=20, vertical=10),
            bgcolor=colors['card_bg'],  # 使用实心卡片背景
            border_radius=8,
            border=ft.border.all(1, colors['border_color'])
        )

        # API1设置内容
        api1_content = self._create_single_api_config_inline(service.configs[0] if len(service.configs) > 0 else None, 0)

        # API2设置内容
        api2_content = self._create_single_api_config_inline(service.configs[1] if len(service.configs) > 1 else None, 1)

        # API3设置内容
        api3_content = self._create_single_api_config_inline(service.configs[2] if len(service.configs) > 2 else None, 2)

        return ft.Column([
            ft.Text("LLM设置", size=18, weight="bold", color=colors['text_color']),
            ft.Container(height=20),

            # LLM服务开关
            ft.Container(
                content=ft.Column([
                    ft.Text("LLM服务开关", size=14, weight="bold", color=colors['text_color']),
                    ft.Container(height=15),
                    self._create_llm_service_switches()
                ]),
                padding=20,
                bgcolor=colors['card_bg'],  # 使用实心卡片背景
                border_radius=8,
                border=ft.border.all(1, colors['border_color'])
            ),

            ft.Container(height=20),

            # 请求间隔时间设置
            ft.Container(
                content=ft.Column([
                    ft.Text("请求间隔时间", size=14, weight="bold", color=colors['text_color']),
                    ft.Container(height=15),
                    self._create_request_interval_settings()
                ]),
                padding=20,
                bgcolor=colors['card_bg'],
                border_radius=8,
                border=ft.border.all(1, colors['border_color'])
            ),

            ft.Container(height=20),

            # 调试模式开关
            debug_container,

            ft.Container(height=20),

            # API1设置区域
            ft.Container(
                content=ft.Column([
                    ft.Text("API1设置", size=14, weight="bold", color=colors['text_color']),
                    ft.Container(height=15),
                    api1_content,
                    ft.Container(height=15),
                    self._create_test_log_display(service.configs[0] if len(service.configs) > 0 else None, colors)
                ]),
                padding=20,
                bgcolor=colors['card_bg'],  # 使用实心卡片背景
                border_radius=8,
                border=ft.border.all(1, colors['border_color'])
            ),

            ft.Container(height=20),

            # API2设置区域
            ft.Container(
                content=ft.Column([
                    ft.Text("API2设置", size=14, weight="bold", color=colors['text_color']),
                    ft.Container(height=15),
                    api2_content,
                    ft.Container(height=15),
                    self._create_test_log_display(service.configs[1] if len(service.configs) > 1 else None, colors)
                ]),
                padding=20,
                bgcolor=colors['card_bg'],  # 使用实心卡片背景
                border_radius=8,
                border=ft.border.all(1, colors['border_color'])
            ),

            ft.Container(height=20),

            # API3设置区域
            ft.Container(
                content=ft.Column([
                    ft.Text("API3设置", size=14, weight="bold", color=colors['text_color']),
                    ft.Container(height=15),
                    api3_content,
                    ft.Container(height=15),
                    self._create_test_log_display(service.configs[2] if len(service.configs) > 2 else None, colors)
                ]),
                padding=20,
                bgcolor=colors['card_bg'],  # 使用实心卡片背景
                border_radius=8,
                border=ft.border.all(1, colors['border_color'])
            ),

            ft.Container(height=20),

            # 提示词设置区域
            ft.Container(
                content=ft.Column([
                    ft.Text("提示词设置", size=14, weight="bold", color=colors['text_color']),
                    ft.Container(height=15),
                    self._create_prompt_settings(colors)
                ]),
                padding=20,
                bgcolor=colors['card_bg'],  # 使用实心卡片背景
                border_radius=8,
                border=ft.border.all(1, colors['border_color'])
            ),
        ])

    def _on_debug_mode_change(self, e):
        """调试模式开关变更处理"""
        self.debug_mode = e.control.value
        self.app.debug_mode = self.debug_mode
        self.app.log_message(f"调试模式已{'开启' if self.debug_mode else '关闭'}")

    def create_asr_settings(self):
        """创建ASR服务设置界面

        包含内容：
        - ASR服务开关控制
        - ElevenLabs设置（内联显示）
        - AssemblyAI设置（内联显示）
        - Deepgram设置（内联显示）

        Returns:
            ft.Column: ASR设置界面组件
        """
        colors = self.app.theme_manager.get_theme_colors()

        # ElevenLabs设置内容（直接内联显示）
        elevenlabs_content = self.create_elevenlabs_settings_inline()

        # AssemblyAI设置内容（直接内联显示）
        assemblyai_content = self.create_assemblyai_settings_inline()

        return ft.Column([
            ft.Text("ASR设置", size=18, weight="bold", color=colors['text_color']),
            ft.Container(height=20),

            # ASR服务开关
            ft.Container(
                content=ft.Column([
                    ft.Text("ASR服务开关", size=14, weight="bold", color=colors['text_color']),
                    ft.Container(height=15),
                    self.create_asr_service_switches()
                ]),
                padding=20,
                bgcolor=colors['card_bg'],  # 使用实心卡片背景
                border_radius=8,
                border=ft.border.all(1, colors['border_color'])
            ),

            ft.Container(height=20),

            # ElevenLabs设置区域（内联显示）
            ft.Container(
                content=ft.Column([
                    ft.Text("ElevenLabs设置", size=14, weight="bold", color=colors['text_color']),
                    ft.Container(height=15),
                    elevenlabs_content
                ]),
                padding=20,
                bgcolor=colors['card_bg'],  # 使用实心卡片背景
                border_radius=8,
                border=ft.border.all(1, colors['border_color'])
            ),

            ft.Container(height=20),

            # AssemblyAI设置区域（内联显示）
            ft.Container(
                content=ft.Column([
                    ft.Text("AssemblyAI设置", size=14, weight="bold", color=colors['text_color']),
                    ft.Container(height=15),
                    assemblyai_content
                ]),
                padding=20,
                bgcolor=colors['card_bg'],  # 使用实心卡片背景
                border_radius=8,
                border=ft.border.all(1, colors['border_color'])
            ),

            ft.Container(height=20),

            # Deepgram设置区域（内联显示）
            ft.Container(
                content=ft.Column([
                    ft.Text("Deepgram设置", size=14, weight="bold", color=colors['text_color']),
                    ft.Container(height=15),
                    self._create_deepgram_settings_inline()
                ]),
                padding=20,
                bgcolor=colors['card_bg'],  # 使用实心卡片背景
                border_radius=8,
                border=ft.border.all(1, colors['border_color'])
            )
        ])

    def create_asr_service_switches(self):
        """创建ASR服务开关"""
        colors = self.app.theme_manager.get_theme_colors()

        # ElevenLabs开关
        self.elevenlabs_switch = ft.Switch(
            label="ElevenLabs",
            value=self.app.asr_service_switches.get('elevenlabs_enabled', True),
            on_change=self._on_asr_switch_change,
            label_style=ft.TextStyle(color=colors['secondary_text_color'])
        )

        # AssemblyAI开关
        self.assemblyai_switch = ft.Switch(
            label="AssemblyAI",
            value=self.app.asr_service_switches.get('assemblyai_enabled', True),
            on_change=self._on_asr_switch_change,
            label_style=ft.TextStyle(color=colors['secondary_text_color'])
        )

        # Deepgram开关
        self.deepgram_switch = ft.Switch(
            label="Deepgram",
            value=self.app.asr_service_switches.get('deepgram_enabled', True),
            on_change=self._on_asr_switch_change,
            label_style=ft.TextStyle(color=colors['secondary_text_color'])
        )

        return ft.Row([
            self.elevenlabs_switch,
            ft.Container(width=30),
            self.assemblyai_switch,
            ft.Container(width=30),
            self.deepgram_switch
        ])

    def _on_asr_switch_change(self, e):
        """ASR服务开关变更处理"""
        try:

            if hasattr(self, 'elevenlabs_switch') and self.elevenlabs_switch:
                self.app.asr_service_switches['elevenlabs_enabled'] = self.elevenlabs_switch.value
            if hasattr(self, 'assemblyai_switch') and self.assemblyai_switch:
                self.app.asr_service_switches['assemblyai_enabled'] = self.assemblyai_switch.value
            if hasattr(self, 'deepgram_switch') and self.deepgram_switch:
                self.app.asr_service_switches['deepgram_enabled'] = self.deepgram_switch.value

            self._save_asr_switch_settings()
            self.app.page.update()
        except Exception as ex:
            self.app.log_message(f"更新ASR服务开关时出错: {ex}")

    def _save_asr_switch_settings(self):
        """保存ASR服务开关设置到配置"""
        # 使用新的ASR服务配置保存方法
        self.app.config_manager.save_asr_switch_settings()



    def create_elevenlabs_settings_inline(self):
        """创建ElevenLabs ASR服务的详细设置界面

        包含设置项：
        - 使用模式（免费/API）
        - API密钥输入
        - 语言选择
        - 说话人数量
        - 音频事件标记
        - 模型选择
        - 说话人分离

        Returns:
            ft.Container: ElevenLabs设置容器组件
        """
        colors = self.app.theme_manager.get_theme_colors()

        # 模式选择
        self.elevenlabs_mode_dropdown = ft.Dropdown(
            label="使用模式",
            options=[
                ft.dropdown.Option("free", "免费模式"),
                ft.dropdown.Option("api", "API模式"),
            ],
            value=self.app.elevenlabs_settings.get('mode', "free"),
            width=150,
            on_change=self._on_elevenlabs_setting_change,
            border_color=colors['input_border'],
            color=colors['text_color'],
            bgcolor=colors['input_bg'],
            label_style=ft.TextStyle(color=colors['text_color'])
        )

        # API Key输入框
        self.elevenlabs_api_key_field = ft.TextField(
            label="ElevenLabs API Key",
            value=self.app.elevenlabs_settings.get('api_key', ""),
            password=True,
            can_reveal_password=True,
            expand=True,
            border_color=colors['input_border'],
            color=colors['text_color'],
            label_style=ft.TextStyle(color=colors['secondary_text_color']),
            bgcolor=colors['input_bg'],
            on_change=self._on_elevenlabs_setting_change
        )



        # 模型选择
        self.scribe_model_dropdown = ft.Dropdown(
            label="语音模型",
            options=[
                ft.dropdown.Option("scribe_v1", "Scribe v1"),
                ft.dropdown.Option("scribe_v1_experimental", "Scribe v1 Experimental"),
            ],
            value=self.app.elevenlabs_settings.get('model_id', "scribe_v1"),
            expand=1,  # 使用expand属性，与其他下拉框平均分配宽度
            on_change=self._on_elevenlabs_setting_change,
            border_color=colors['input_border'],
            color=colors['text_color'],
            bgcolor=colors['input_bg'],
            label_style=ft.TextStyle(color=colors['text_color'])
        )

        # 语言选择（WER<25%的语言，按使用人数排序）
        self.transcription_language_dropdown = ft.Dropdown(
            label="转录语言",
            options=[
                ft.dropdown.Option("auto", "自动检测"),
                # 按全球使用人数排序的WER<25%语言
                ft.dropdown.Option("cmn", "中文 (Mandarin Chinese)"),
                ft.dropdown.Option("en", "英语 (English)"),
                ft.dropdown.Option("hi", "印地语 (Hindi)"),
                ft.dropdown.Option("es", "西班牙语 (Spanish)"),
                ft.dropdown.Option("ar", "阿拉伯语 (Arabic)"),
                ft.dropdown.Option("ms", "马来语 (Malay)"),
                ft.dropdown.Option("fr", "法语 (French)"),
                ft.dropdown.Option("bn", "孟加拉语 (Bengali)"),
                ft.dropdown.Option("pt", "葡萄牙语 (Portuguese)"),
                ft.dropdown.Option("ru", "俄语 (Russian)"),
                ft.dropdown.Option("de", "德语 (German)"),
                ft.dropdown.Option("ja", "日语 (Japanese)"),
                ft.dropdown.Option("fas", "波斯语 (Persian)"),
                ft.dropdown.Option("vi", "越南语 (Vietnamese)"),
                ft.dropdown.Option("tr", "土耳其语 (Turkish)"),
                ft.dropdown.Option("kor", "韩语 (Korean)"),
                ft.dropdown.Option("it", "意大利语 (Italian)"),
                ft.dropdown.Option("tha", "泰语 (Thai)"),
                ft.dropdown.Option("pol", "波兰语 (Polish)"),
                ft.dropdown.Option("ukr", "乌克兰语 (Ukrainian)"),
                ft.dropdown.Option("nl", "荷兰语 (Dutch)"),
                ft.dropdown.Option("swa", "斯瓦希里语 (Swahili)"),
                ft.dropdown.Option("hau", "豪萨语 (Hausa)"),
                ft.dropdown.Option("tel", "泰卢固语 (Telugu)"),
                ft.dropdown.Option("mar", "马拉地语 (Marathi)"),
                ft.dropdown.Option("tam", "泰米尔语 (Tamil)"),
                ft.dropdown.Option("urd", "乌尔都语 (Urdu)"),
                ft.dropdown.Option("guj", "古吉拉特语 (Gujarati)"),
                ft.dropdown.Option("kan", "卡纳达语 (Kannada)"),
                ft.dropdown.Option("mal", "马拉雅拉姆语 (Malayalam)"),
                ft.dropdown.Option("ori", "奥里亚语 (Odia)"),
                ft.dropdown.Option("pan", "旁遮普语 (Punjabi)"),
                ft.dropdown.Option("asm", "阿萨姆语 (Assamese)"),
                ft.dropdown.Option("nep", "尼泊尔语 (Nepali)"),
                ft.dropdown.Option("snd", "信德语 (Sindhi)"),
                ft.dropdown.Option("fil", "菲律宾语 (Filipino)"),
                ft.dropdown.Option("hun", "匈牙利语 (Hungarian)"),
                ft.dropdown.Option("ces", "捷克语 (Czech)"),
                ft.dropdown.Option("ell", "希腊语 (Greek)"),
                ft.dropdown.Option("bul", "保加利亚语 (Bulgarian)"),
                ft.dropdown.Option("hrv", "克罗地亚语 (Croatian)"),
                ft.dropdown.Option("srp", "塞尔维亚语 (Serbian)"),
                ft.dropdown.Option("slk", "斯洛伐克语 (Slovak)"),
                ft.dropdown.Option("slv", "斯洛文尼亚语 (Slovenian)"),
                ft.dropdown.Option("bos", "波斯尼亚语 (Bosnian)"),
                ft.dropdown.Option("mkd", "马其顿语 (Macedonian)"),
                ft.dropdown.Option("lav", "拉脱维亚语 (Latvian)"),
                ft.dropdown.Option("lit", "立陶宛语 (Lithuanian)"),
                ft.dropdown.Option("est", "爱沙尼亚语 (Estonian)"),
                ft.dropdown.Option("fin", "芬兰语 (Finnish)"),
                ft.dropdown.Option("swe", "瑞典语 (Swedish)"),
                ft.dropdown.Option("nor", "挪威语 (Norwegian)"),
                ft.dropdown.Option("dan", "丹麦语 (Danish)"),
                ft.dropdown.Option("isl", "冰岛语 (Icelandic)"),
                ft.dropdown.Option("ron", "罗马尼亚语 (Romanian)"),
                ft.dropdown.Option("cat", "加泰罗尼亚语 (Catalan)"),
                ft.dropdown.Option("glg", "加利西亚语 (Galician)"),
                ft.dropdown.Option("ast", "阿斯图里亚斯语 (Asturian)"),
                ft.dropdown.Option("oci", "奥克语 (Occitan)"),
                ft.dropdown.Option("cym", "威尔士语 (Welsh)"),
                ft.dropdown.Option("heb", "希伯来语 (Hebrew)"),
                ft.dropdown.Option("kat", "格鲁吉亚语 (Georgian)"),
                ft.dropdown.Option("hye", "亚美尼亚语 (Armenian)"),
                ft.dropdown.Option("aze", "阿塞拜疆语 (Azerbaijani)"),
                ft.dropdown.Option("kaz", "哈萨克语 (Kazakh)"),
                ft.dropdown.Option("kir", "吉尔吉斯语 (Kyrgyz)"),
                ft.dropdown.Option("uzb", "乌兹别克语 (Uzbek)"),
                ft.dropdown.Option("tgk", "塔吉克语 (Tajik)"),
                ft.dropdown.Option("mon", "蒙古语 (Mongolian)"),
                ft.dropdown.Option("bel", "白俄罗斯语 (Belarusian)"),
                ft.dropdown.Option("yue", "粤语 (Cantonese)"),
                ft.dropdown.Option("ind", "印尼语 (Indonesian)"),
                ft.dropdown.Option("jav", "爪哇语 (Javanese)"),
                ft.dropdown.Option("ceb", "宿务语 (Cebuano)"),
                ft.dropdown.Option("mya", "缅甸语 (Burmese)"),
                ft.dropdown.Option("afr", "南非荷兰语 (Afrikaans)"),
                ft.dropdown.Option("mlt", "马耳他语 (Maltese)"),
                ft.dropdown.Option("mri", "毛利语 (Māori)"),
                ft.dropdown.Option("kea", "佛得角克里奥尔语 (Kabuverdianu)"),
                ft.dropdown.Option("lin", "林加拉语 (Lingala)")
            ],
            value=self.app.elevenlabs_settings.get('language', "auto"),
            expand=1,  # 使用expand属性，与其他下拉框平均分配宽度
            on_change=self._on_elevenlabs_setting_change,
            border_color=colors['input_border'],
            color=colors['text_color'],
            bgcolor=colors['input_bg'],
            label_style=ft.TextStyle(color=colors['text_color'])
        )



        # 音频事件标记（替换时间戳精度下拉框）
        self.elevenlabs_audio_events_dropdown = ft.Dropdown(
            label="音频事件标记",
            options=[
                ft.dropdown.Option("false", "关闭"),
                ft.dropdown.Option("true", "开启"),
            ],
            value="true" if self.app.elevenlabs_settings.get('tag_audio_events', True) else "false",
            expand=1,  # 使用expand属性，与其他下拉框平均分配宽度
            on_change=self._on_elevenlabs_setting_change,
            border_color=colors['input_border'],
            color=colors['text_color'],
            bgcolor=colors['input_bg'],
            label_style=ft.TextStyle(color=colors['text_color'])
        )

        # API Key设置容器
        self.elevenlabs_api_settings_container = ft.Column([
            ft.Row([
                self.elevenlabs_api_key_field
            ])
        ], visible=self.app.elevenlabs_settings.get('mode', "free") == 'api')

        return ft.Column([
            # 第一行：模式选择
            ft.Row([
                self.elevenlabs_mode_dropdown
            ]),
            ft.Container(height=10),

            # API Key设置（仅在API模式时显示）
            self.elevenlabs_api_settings_container,
            ft.Container(height=10),

            # 第二行：模型、语言和音频事件标记
            ft.Row([
                self.scribe_model_dropdown,
                ft.Container(width=15),
                self.transcription_language_dropdown,
                ft.Container(width=15),
                self.elevenlabs_audio_events_dropdown
            ]),
            ft.Container(height=10)
        ])

    def _on_elevenlabs_setting_change(self, e):
        """ElevenLabs设置变更处理"""
        try:
            # 更新设置
            if hasattr(self, 'elevenlabs_mode_dropdown') and self.elevenlabs_mode_dropdown:
                self.app.elevenlabs_settings['mode'] = self.elevenlabs_mode_dropdown.value

                # 根据模式显示/隐藏API Key设置
                if hasattr(self, 'elevenlabs_api_settings_container'):
                    self.elevenlabs_api_settings_container.visible = (self.elevenlabs_mode_dropdown.value == 'api')

            if hasattr(self, 'elevenlabs_api_key_field') and self.elevenlabs_api_key_field:
                self.app.elevenlabs_settings['api_key'] = self._sanitize_api_key(self.elevenlabs_api_key_field.value)



            if hasattr(self, 'scribe_model_dropdown') and self.scribe_model_dropdown:
                self.app.elevenlabs_settings['model_id'] = self.scribe_model_dropdown.value

            if hasattr(self, 'transcription_language_dropdown') and self.transcription_language_dropdown:
                self.app.elevenlabs_settings['language'] = self.transcription_language_dropdown.value

            if hasattr(self, 'elevenlabs_audio_events_dropdown') and self.elevenlabs_audio_events_dropdown:
                self.app.elevenlabs_settings['tag_audio_events'] = (self.elevenlabs_audio_events_dropdown.value == "true")

            # 时间戳精度已在ASR服务层面固定为词级时间戳，无需在UI中更新

            # 说话人数量固定为0（自动检测）
            self.app.elevenlabs_settings['num_speakers'] = 0
            # 说话人分离固定为True
            self.app.elevenlabs_settings['diarize'] = True

            # 保存到配置（使用新格式）
            self.app.config_manager.save_asr_service_settings()
            self.app.page.update()

            self.app.log_message(f"ElevenLabs设置已更新")
        except Exception as ex:
            self.app.log_message(f"更新ElevenLabs设置时出错: {ex}")

    def create_assemblyai_settings_inline(self):
        """创建内联的AssemblyAI设置内容"""
        colors = self.app.theme_manager.get_theme_colors()

        # API Key输入框
        self.assemblyai_api_key_field = ft.TextField(
            label="AssemblyAI API Key",
            value=self.app.assemblyai_settings.get('api_key', ''),
            password=True,
            can_reveal_password=True,
            expand=True,
            border_color=colors['input_border'],
            color=colors['text_color'],
            label_style=ft.TextStyle(color=colors['secondary_text_color']),
            bgcolor=colors['input_bg'],
            on_change=self._on_assemblyai_setting_change
        )



        # 模型选择
        self.assemblyai_speech_model_dropdown = ft.Dropdown(
            label="语音模型",
            options=[
                ft.dropdown.Option("best", "Best"),
                ft.dropdown.Option("universal", "Universal"),
                ft.dropdown.Option("nano", "Nano"),
            ],
            value=self.app.assemblyai_settings.get('speech_model', 'universal'),
            expand=1,  # 使用expand属性，与其他下拉框平均分配宽度
            on_change=self._on_assemblyai_setting_change,
            border_color=colors['input_border'],
            color=colors['text_color'],
            bgcolor=colors['input_bg'],
            label_style=ft.TextStyle(color=colors['text_color'])
        )

        # 语言选择
        self.assemblyai_language_dropdown = ft.Dropdown(
            label="转录语言",
            options=[
                ft.dropdown.Option("auto", "自动检测"),
                ft.dropdown.Option("en", "英语 (English)"),
                ft.dropdown.Option("zh", "中文 (Chinese)"),
                ft.dropdown.Option("es", "西班牙语 (Spanish)"),
                ft.dropdown.Option("fr", "法语 (French)"),
                ft.dropdown.Option("de", "德语 (German)"),
                ft.dropdown.Option("it", "意大利语 (Italian)"),
                ft.dropdown.Option("pt", "葡萄牙语 (Portuguese)"),
                ft.dropdown.Option("hi", "印地语 (Hindi)"),
                ft.dropdown.Option("ja", "日语 (Japanese)"),
                ft.dropdown.Option("ko", "韩语 (Korean)"),
                ft.dropdown.Option("nl", "荷兰语 (Dutch)"),
                ft.dropdown.Option("pl", "波兰语 (Polish)"),
                ft.dropdown.Option("ru", "俄语 (Russian)"),
                ft.dropdown.Option("tr", "土耳其语 (Turkish)"),
                ft.dropdown.Option("uk", "乌克兰语 (Ukrainian)"),
                ft.dropdown.Option("vi", "越南语 (Vietnamese)"),
                ft.dropdown.Option("ar", "阿拉伯语 (Arabic)"),
                ft.dropdown.Option("fi", "芬兰语 (Finnish)"),
                ft.dropdown.Option("el", "希腊语 (Greek)"),
                ft.dropdown.Option("he", "希伯来语 (Hebrew)")
            ],
            value=self.app.assemblyai_settings.get('language_code', 'auto'),
            expand=1,  # 使用expand属性，与其他下拉框平均分配宽度
            on_change=self._on_assemblyai_setting_change,
            border_color=colors['input_border'],
            color=colors['text_color'],
            bgcolor=colors['input_bg'],
            label_style=ft.TextStyle(color=colors['text_color'])
        )

        # 说话人数量
        self.assemblyai_speakers_dropdown = ft.Dropdown(
            label="说话人数量",
            options=[
                ft.dropdown.Option("0", "自动检测"),
                ft.dropdown.Option("1", "1个说话人"),
                ft.dropdown.Option("2", "2个说话人"),
                ft.dropdown.Option("3", "3个说话人"),
                ft.dropdown.Option("4", "4个说话人"),
                ft.dropdown.Option("5", "5个说话人"),
                ft.dropdown.Option("6", "6个说话人"),
                ft.dropdown.Option("7", "7个说话人"),
                ft.dropdown.Option("8", "8个说话人"),
                ft.dropdown.Option("9", "9个说话人"),
                ft.dropdown.Option("10", "10个说话人")
            ],
            value=str(self.app.assemblyai_settings.get('speakers_expected', 0)),
            expand=1,  # 使用expand属性，与其他下拉框平均分配宽度
            on_change=self._on_assemblyai_setting_change,
            border_color=colors['input_border'],
            color=colors['text_color'],
            bgcolor=colors['input_bg'],
            label_style=ft.TextStyle(color=colors['text_color'])
        )

        return ft.Column([
            # API Key设置
            ft.Row([self.assemblyai_api_key_field]),
            ft.Container(height=15),

            # 基本设置
            ft.Row([
                self.assemblyai_speech_model_dropdown,
                ft.Container(width=15),
                self.assemblyai_language_dropdown,
                ft.Container(width=15),
                self.assemblyai_speakers_dropdown
            ])
        ])

    def _on_assemblyai_setting_change(self, e):
        """AssemblyAI设置变更处理"""
        try:
            # 更新设置
            if hasattr(self, 'assemblyai_api_key_field') and self.assemblyai_api_key_field:
                self.app.assemblyai_settings['api_key'] = self._sanitize_api_key(self.assemblyai_api_key_field.value)



            if hasattr(self, 'assemblyai_speech_model_dropdown') and self.assemblyai_speech_model_dropdown:
                self.app.assemblyai_settings['speech_model'] = self.assemblyai_speech_model_dropdown.value

            if hasattr(self, 'assemblyai_language_dropdown') and self.assemblyai_language_dropdown:
                self.app.assemblyai_settings['language_code'] = self.assemblyai_language_dropdown.value

            if hasattr(self, 'assemblyai_speakers_dropdown') and self.assemblyai_speakers_dropdown:
                self.app.assemblyai_settings['speakers_expected'] = int(self.assemblyai_speakers_dropdown.value)

            # 说话人标签固定为True（启用说话人分离）
            self.app.assemblyai_settings['speaker_labels'] = True
            # 其他AssemblyAI设置固定值
            self.app.assemblyai_settings['language_detection'] = True
            self.app.assemblyai_settings['punctuate'] = True
            self.app.assemblyai_settings['format_text'] = True
            self.app.assemblyai_settings['disfluencies'] = False
            self.app.assemblyai_settings['sentiment_analysis'] = False
            self.app.assemblyai_settings['entity_detection'] = False
            self.app.assemblyai_settings['auto_highlights'] = False
            self.app.assemblyai_settings['auto_chapters'] = False
            self.app.assemblyai_settings['summarization'] = False

            # 保存到配置（使用新格式）
            self.app.config_manager.save_asr_service_settings()
            self.app.log_message(f"AssemblyAI设置已更新")
        except Exception as ex:
            self.app.log_message(f"更新AssemblyAI设置时出错: {ex}")

    def _create_deepgram_settings_inline(self):
        """创建内联的Deepgram设置内容"""
        colors = self.app.theme_manager.get_theme_colors()

        # API Key输入框
        self.deepgram_api_key_field = ft.TextField(
            label="Deepgram API Key",
            value=self.app.deepgram_settings.get('api_key', ''),
            password=True,
            can_reveal_password=True,
            expand=True,
            border_color=colors['input_border'],
            color=colors['text_color'],
            label_style=ft.TextStyle(color=colors['secondary_text_color']),
            bgcolor=colors['input_bg'],
            on_change=self._on_deepgram_setting_change
        )



        # 模型选择
        self.deepgram_model_dropdown = ft.Dropdown(
            label="语音模型",
            options=[
                ft.dropdown.Option("nova-3", "Nova-3"),
                ft.dropdown.Option("nova-2", "Nova-2"),
                ft.dropdown.Option("whisper", "Whisper"),
                ft.dropdown.Option("enhanced", "Enhanced"),
                ft.dropdown.Option("base", "Base"),
                ft.dropdown.Option("nova", "Nova"),
            ],
            value=self.app.deepgram_settings.get('model', 'nova-3'),
            expand=1,  # 使用expand属性，与其他ASR服务对齐
            on_change=self._on_deepgram_setting_change,
            border_color=colors['input_border'],
            color=colors['text_color'],
            bgcolor=colors['input_bg'],
            label_style=ft.TextStyle(color=colors['text_color'])
        )

        # 语言选择
        self.deepgram_language_dropdown = ft.Dropdown(
            label="转录语言",
            options=[
                ft.dropdown.Option("auto", "自动检测"),
                ft.dropdown.Option("en", "英语 (English)"),
                ft.dropdown.Option("zh", "中文 (Chinese)"),
                ft.dropdown.Option("es", "西班牙语 (Spanish)"),
                ft.dropdown.Option("fr", "法语 (French)"),
                ft.dropdown.Option("de", "德语 (German)"),
                ft.dropdown.Option("it", "意大利语 (Italian)"),
                ft.dropdown.Option("pt", "葡萄牙语 (Portuguese)"),
                ft.dropdown.Option("ru", "俄语 (Russian)"),
                ft.dropdown.Option("ja", "日语 (Japanese)"),
                ft.dropdown.Option("ko", "韩语 (Korean)"),
                ft.dropdown.Option("ar", "阿拉伯语 (Arabic)"),
                ft.dropdown.Option("hi", "印地语 (Hindi)"),
                ft.dropdown.Option("nl", "荷兰语 (Dutch)"),
                ft.dropdown.Option("sv", "瑞典语 (Swedish)"),
                ft.dropdown.Option("no", "挪威语 (Norwegian)"),
                ft.dropdown.Option("da", "丹麦语 (Danish)"),
                ft.dropdown.Option("fi", "芬兰语 (Finnish)"),
                ft.dropdown.Option("pl", "波兰语 (Polish)"),
                ft.dropdown.Option("tr", "土耳其语 (Turkish)"),
                ft.dropdown.Option("th", "泰语 (Thai)"),
                ft.dropdown.Option("vi", "越南语 (Vietnamese)"),
                ft.dropdown.Option("id", "印尼语 (Indonesian)"),
                ft.dropdown.Option("ms", "马来语 (Malay)"),
                ft.dropdown.Option("tl", "菲律宾语 (Filipino)"),
                ft.dropdown.Option("uk", "乌克兰语 (Ukrainian)")
            ],
            value=self.app.deepgram_settings.get('language', 'auto'),
            expand=1,  # 使用expand属性，与其他ASR服务对齐
            on_change=self._on_deepgram_setting_change,
            border_color=colors['input_border'],
            color=colors['text_color'],
            bgcolor=colors['input_bg'],
            label_style=ft.TextStyle(color=colors['text_color'])
        )

        return ft.Column([
            # API Key设置
            ft.Row([self.deepgram_api_key_field]),
            ft.Container(height=15),

            # 基本设置 - 与其他ASR服务对齐
            ft.Row([
                self.deepgram_model_dropdown,
                ft.Container(width=15),
                self.deepgram_language_dropdown,
                ft.Container(width=15),
                ft.Container(expand=1)  # 空容器占据第三个位置，与其他ASR服务对齐
            ])
        ])

    def _on_deepgram_setting_change(self, e):
        """Deepgram设置变更处理"""
        try:
            # 更新设置
            if hasattr(self, 'deepgram_api_key_field') and self.deepgram_api_key_field:
                self.app.deepgram_settings['api_key'] = self._sanitize_api_key(self.deepgram_api_key_field.value)



            if hasattr(self, 'deepgram_model_dropdown') and self.deepgram_model_dropdown:
                self.app.deepgram_settings['model'] = self.deepgram_model_dropdown.value

            if hasattr(self, 'deepgram_language_dropdown') and self.deepgram_language_dropdown:
                self.app.deepgram_settings['language'] = self.deepgram_language_dropdown.value

            # 说话人分离固定为True
            self.app.deepgram_settings['diarize'] = True
            # 其他Deepgram设置固定值
            self.app.deepgram_settings['punctuate'] = True
            self.app.deepgram_settings['utterances'] = True
            self.app.deepgram_settings['smart_format'] = True
            self.app.deepgram_settings['paragraphs'] = True
            self.app.deepgram_settings['detect_language'] = True

            self.save_deepgram_settings()
            self.app.log_message(f"Deepgram设置已更新")
        except Exception as ex:
            self.app.log_message(f"更新Deepgram设置时出错: {ex}")

    def _create_llm_service_switches(self):
        """创建LLM服务开关"""
        colors = self.app.theme_manager.get_theme_colors()

        # 获取LLM服务
        service = get_multi_llm_service()
        configs = service.configs

        # 确保有3个配置
        if len(configs) == 0:
            service.llm_service._create_default_configs()

        # API1开关
        self.api1_switch = ft.Switch(
            label="API1",
            value=service.configs[0].enabled if len(service.configs) > 0 else True,
            on_change=lambda e: self._on_llm_switch_change(0, e.control.value),
            label_style=ft.TextStyle(color=colors['secondary_text_color'])
        )

        # API2开关
        self.api2_switch = ft.Switch(
            label="API2",
            value=service.configs[1].enabled if len(service.configs) > 1 else True,
            on_change=lambda e: self._on_llm_switch_change(1, e.control.value),
            label_style=ft.TextStyle(color=colors['secondary_text_color'])
        )

        # API3开关
        self.api3_switch = ft.Switch(
            label="API3",
            value=service.configs[2].enabled if len(service.configs) > 2 else False,
            on_change=lambda e: self._on_llm_switch_change(2, e.control.value),
            label_style=ft.TextStyle(color=colors['secondary_text_color'])
        )

        return ft.Row([
            self.api1_switch,
            ft.Container(width=30),
            self.api2_switch,
            ft.Container(width=30),
            self.api3_switch
        ])

    def _create_request_interval_settings(self):
        """创建3个独立的请求间隔时间设置，与API开关对齐"""
        colors = self.app.theme_manager.get_theme_colors()

        # 获取LLM服务
        service = get_multi_llm_service()

        # API1请求间隔输入框
        api1_interval = service.configs[0].request_interval if len(service.configs) > 0 else 10.0
        self.api1_interval_field = ft.TextField(
            value=str(int(api1_interval)),
            width=85,
            border_color=colors['input_border'],
            color=colors['text_color'],
            bgcolor=colors['input_bg'],
            on_change=lambda e: self._on_request_interval_change(0, e.control.value)
        )

        # API2请求间隔输入框
        api2_interval = service.configs[1].request_interval if len(service.configs) > 1 else 10.0
        self.api2_interval_field = ft.TextField(
            value=str(int(api2_interval)),
            width=85,
            border_color=colors['input_border'],
            color=colors['text_color'],
            bgcolor=colors['input_bg'],
            on_change=lambda e: self._on_request_interval_change(1, e.control.value)
        )

        # API3请求间隔输入框
        api3_interval = service.configs[2].request_interval if len(service.configs) > 2 else 10.0
        self.api3_interval_field = ft.TextField(
            value=str(int(api3_interval)),
            width=85,
            border_color=colors['input_border'],
            color=colors['text_color'],
            bgcolor=colors['input_bg'],
            on_change=lambda e: self._on_request_interval_change(2, e.control.value)
        )

        # 创建与API开关对齐的布局
        return ft.Row([
            ft.Column([
                ft.Text("API1", color=colors['secondary_text_color']),
                self.api1_interval_field
            ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
            ft.Container(width=30),  # 与API开关的间距保持一致
            ft.Column([
                ft.Text("API2", color=colors['secondary_text_color']),
                self.api2_interval_field
            ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
            ft.Container(width=30),
            ft.Column([
                ft.Text("API3", color=colors['secondary_text_color']),
                self.api3_interval_field
            ], horizontal_alignment=ft.CrossAxisAlignment.CENTER)
        ])

    def _on_llm_switch_change(self, index: int, enabled: bool):
        """LLM开关变更处理"""
        try:
            service = get_multi_llm_service()
            configs = service.configs

            if len(service.configs) > index:
                config = service.configs[index]
                service.update_config_field(config.id, 'enabled', enabled)
                self.app.log_message(f"已{'启用' if enabled else '禁用'}API{index+1}")
        except Exception as ex:
            self.app.log_message(f"更新LLM开关时出错: {ex}")

    def _on_request_interval_change(self, api_index: int, value: str):
        """请求间隔时间变更处理"""
        try:
            # 验证输入值
            interval_value = float(value)

            # 范围验证
            if interval_value < 0.1:
                interval_value = 0.1
                display_value = "1"  # 显示整数
            elif interval_value > 60.0:
                interval_value = 60.0
                display_value = "60"  # 显示整数
            else:
                display_value = str(int(interval_value))  # 显示整数

            # 更新对应的输入框显示
            if api_index == 0 and self.api1_interval_field:
                self.api1_interval_field.value = display_value
            elif api_index == 1 and self.api2_interval_field:
                self.api2_interval_field.value = display_value
            elif api_index == 2 and self.api3_interval_field:
                self.api3_interval_field.value = display_value

            # 直接更新LLM服务配置（统一保存机制）
            service = get_multi_llm_service()
            if len(service.configs) > api_index:
                config = service.configs[api_index]
                service.update_config_field(config.id, 'request_interval', interval_value)
                # update_config_field内部已调用save_configs()

            # 4. 更新UI
            self.page.update()

            self.app.log_message(f"API{api_index + 1} 请求间隔时间已更新为 {int(interval_value)} 秒")

        except ValueError:
            # 输入无效时恢复原值
            service = get_multi_llm_service()
            if api_index < len(service.configs):
                current_interval = service.configs[api_index].request_interval
                display_value = str(int(current_interval))

                # 恢复对应输入框的值
                if api_index == 0 and self.api1_interval_field:
                    self.api1_interval_field.value = display_value
                elif api_index == 1 and self.api2_interval_field:
                    self.api2_interval_field.value = display_value
                elif api_index == 2 and self.api3_interval_field:
                    self.api3_interval_field.value = display_value

                self.app.log_message("请输入有效的数字")
                self.page.update()
        except Exception as ex:
            self.app.log_message(f"更新API{api_index + 1}请求间隔时间时出错: {ex}")

    def _create_single_api_config_inline(self, config: LLMConfig, index: int):
        """创建单个API配置内联面板"""
        colors = self.app.theme_manager.get_theme_colors()

        if config is None:
            # 如果配置不存在，创建基础配置
            service = get_multi_llm_service()
            if len(service.configs) <= index:
                service.llm_service._create_default_configs()
            config = service.configs[index] if len(service.configs) > index else None

        if config is None:
            return ft.Text("配置加载失败", color=colors['text_color'])

        return ft.Column([
            # API密钥 (独占一行)
            ft.TextField(
                label="API密钥",
                value=config.api_key,
                password=True,
                can_reveal_password=True,
                expand=True,
                border_color=colors['input_border'],
                color=colors['text_color'],
                bgcolor=colors['input_bg'],
                label_style=ft.TextStyle(color=colors['text_color']),
                on_change=lambda e, config_id=config.id: self._on_api_key_change(config_id, e.control.value)
            ),

            ft.Container(height=10),

            # API地址 + API格式 (7:3比例)
            ft.Row([
                ft.TextField(
                    label="API地址",
                    value=config.base_url,
                    expand=7,
                    border_color=colors['input_border'],
                    color=colors['text_color'],
                    bgcolor=colors['input_bg'],
                    label_style=ft.TextStyle(color=colors['text_color']),
                    on_change=lambda e: self._update_config_field(config.id, 'base_url', e.control.value)
                ),
                ft.Container(width=15),
                ft.Dropdown(
                    label="API格式",
                    options=[
                        ft.dropdown.Option("openai", "OpenAI"),
                        ft.dropdown.Option("gemini", "Gemini")
                    ],
                    value=config.format_type.value,
                    expand=3,
                    border_color=colors['input_border'],
                    color=colors['text_color'],
                    bgcolor=colors['input_bg'],
                    label_style=ft.TextStyle(color=colors['text_color']),
                    on_change=lambda e: self._update_config_field(config.id, 'format_type', e.control.value)
                )
            ]),

            ft.Container(height=10),

            # 模型名称 + 拉取模型按钮 (模型名称与API地址同宽，按钮与API格式同宽同位置)
            ft.Row([
                self._create_model_selector(config, colors),
                ft.Container(width=15),
                ft.ElevatedButton(
                    "拉取模型",
                    on_click=lambda e, config_id=config.id: self._fetch_models(config_id),
                    bgcolor=colors['accent_color'],  # 雾霾蓝
                    color="#FFFFFF",  # 白色文字
                    expand=3,  # 与API格式同宽
                    height=48  # 调整为48
                )
            ]),

            ft.Container(height=10),

            # 温度框 + 测试连接按钮
            ft.Row([
                self._create_temperature_container(config, colors),  # 使用专门的容器方法
                ft.Container(width=15),
                ft.ElevatedButton(
                    "测试连接",
                    on_click=lambda e, config_id=config.id: self._test_single_config(config_id),
                    bgcolor=colors['accent_color'],  # 雾霾蓝
                    color="#FFFFFF",  # 白色文字
                    expand=3,  # 与API格式同宽
                    height=48  # 调整为48
                )
            ])
        ])

    def _create_model_selector(self, config, colors):
        """创建模型选择器（下拉框或文本框）"""
        # 初始化模型选择器字典
        if not hasattr(self, 'model_selectors'):
            self.model_selectors = {}

        if config.available_models and len(config.available_models) > 0:
            # 有可用模型时显示下拉框
            options = [ft.dropdown.Option(model) for model in config.available_models]
            dropdown = ft.Dropdown(
                label="模型名称",
                options=options,
                value=config.model if config.model in config.available_models else None,
                expand=7,  # 与API地址同宽
                border_color=colors['input_border'],
                color=colors['text_color'],
                bgcolor=colors['input_bg'],
                label_style=ft.TextStyle(color=colors['text_color']),
                on_change=lambda e, config_id=config.id: self._update_config_field(config_id, 'model', e.control.value)
            )
            # 保存控件引用
            self.model_selectors[config.id] = dropdown
            return dropdown
        else:
            # 没有可用模型时显示文本框
            textfield = ft.TextField(
                label="模型名称",
                value=config.model,
                expand=7,  # 与API地址同宽
                border_color=colors['input_border'],
                color=colors['text_color'],
                bgcolor=colors['input_bg'],
                label_style=ft.TextStyle(color=colors['text_color']),
                on_change=lambda e, config_id=config.id: self._update_config_field(config_id, 'model', e.control.value)
            )
            # 保存控件引用
            self.model_selectors[config.id] = textfield
            return textfield

    def _create_temperature_container(self, config, colors):
        """创建温度容器，使用真正的TextField实现浮动标签"""
        # 创建滑块
        temperature_slider = ft.Slider(
            min=0.01,
            max=2.0,
            divisions=199,  # (2.0-0.01)/0.01 = 199步
            value=config.temperature,
            expand=True,  # 自适应容器宽度
            height=30,    # 滑块高度
            label="{value}",  # 使用{value}占位符显示当前数值，跟随滑块移动
            round=2,      # 设置小数位数为2位
            active_color=colors['accent_color'],  # 雾霾蓝
            inactive_color=colors['border_color'],
            thumb_color=colors['accent_color'],  # 雾霾蓝
            on_change=lambda e, config_id=config.id: self._update_config_temperature_slider(config_id, e.control.value)
        )

        # 使用真正的TextField，但将滑块作为suffix
        return ft.TextField(
            label="温度",  # 这会自动创建浮动标签效果
            value=" ",  # 给一个空格，让label浮动到边框线上
            expand=7,  # 与API地址同宽
            border_color=colors['input_border'],
            color=colors['text_color'],
            bgcolor=colors['input_bg'],
            read_only=True,  # 只读，防止用户输入
            dense=True,  # 使用紧凑模式
            content_padding=ft.padding.only(left=12, right=0, top=12, bottom=8),  # 修正：左边距+12，顶部+12（向上移动）
            # 调整标签样式，与模型名称字体大小一致
            label_style=ft.TextStyle(
                size=16,  # 增大字体，与模型名称标签一致
                color=colors['text_color']
            ),
            # 将滑块作为suffix放在TextField内部
            suffix=ft.Container(
                content=temperature_slider,
                expand=True,
                margin=ft.margin.only(left=-20, right=-10, top=2, bottom=-2)  # 微调：左-20右-10让滑块往左靠，上+2下-2保持垂直居中
            )
        )

    def _update_config_temperature_slider(self, config_id: str, temperature: float):
        """更新配置温度滑块"""
        try:
            service = get_multi_llm_service()
            service.update_config_field(config_id, 'temperature', temperature)
        except Exception as ex:
            self.app.log_message(f"更新温度设置时出错: {ex}")

    def _on_api_key_change(self, config_id: str, api_key: str):
        """API密钥变化处理"""
        try:
            service = get_multi_llm_service()
            sanitized_api_key = self._sanitize_api_key(api_key)
            service.update_config_field(config_id, 'api_key', sanitized_api_key)
            self.app.log_message(f"API密钥已更新: {config_id}")
        except Exception as ex:
            self.app.log_message(f"更新API密钥时出错: {ex}")

    def save_deepgram_settings(self):
        """保存Deepgram设置到配置"""
        # 使用新的ASR服务配置保存方法
        self.app.config_manager.save_deepgram_settings()



    # ========== LLM相关方法 ==========



    def _fetch_models(self, config_id: str):
        """拉取模型列表"""
        try:
            self.app.log_message(f"开始拉取模型列表...")

            service = get_multi_llm_service()
            config = service.get_config_by_id(config_id)

            if not config:
                self.app.log_message(f"[ERROR] 配置不存在: {config_id}")
                return

            # 基础验证
            if not config.api_key.strip():
                self.app.log_message(f"[ERROR] {config.name}: API密钥为空")
                return

            if not config.base_url.strip():
                self.app.log_message(f"[ERROR] {config.name}: API地址为空")
                return

            # 调用服务拉取模型
            success = service.fetch_models_for_config(config_id, self.app)

            if success:
                # 只更新模型下拉框，不刷新整个界面
                self.app.log_message(f"[OK] 正在更新模型列表...")
                self.update_model_dropdown(config_id)
                self.app.log_message(f"[OK] 模型列表更新完成")

        except Exception as ex:
            self.app.log_message(f"[ERROR] 拉取模型时出错: {ex}")
            import traceback
            traceback.print_exc()

    def update_model_dropdown(self, config_id: str):
        """更新指定配置的模型下拉框，而不刷新整个界面"""
        try:
            service = get_multi_llm_service()
            config = service.get_config_by_id(config_id)

            if not config:
                self.app.log_message(f"[ERROR] 配置不存在: {config_id}")
                return

            # 查找对应的模型选择器控件
            if hasattr(self, 'model_selectors') and config_id in self.model_selectors:
                model_selector = self.model_selectors[config_id]

                # 根据是否有可用模型来决定控件类型
                if config.available_models and len(config.available_models) > 0:
                    # 有可用模型时，更新下拉框选项
                    options = [ft.dropdown.Option(model) for model in config.available_models]

                    if isinstance(model_selector, ft.Dropdown):
                        # 如果已经是下拉框，只更新选项
                        model_selector.options = options
                        model_selector.value = config.model if config.model in config.available_models else None
                        self.app.page.update()
                        self.app.log_message(f"[OK] 已更新 {config.name} 的模型下拉框，包含 {len(config.available_models)} 个模型")
                    elif isinstance(model_selector, ft.TextField):
                        # 如果是文本框，转换为下拉框（但不刷新整个界面）
                        colors = self.app.theme_manager.get_theme_colors()
                        new_dropdown = ft.Dropdown(
                            label="模型名称",
                            options=options,
                            value=config.model if config.model in config.available_models else None,
                            expand=7,
                            border_color=colors['input_border'],
                            color=colors['text_color'],
                            bgcolor=colors['input_bg'],
                            on_change=lambda e, config_id=config.id: self._update_config_field(config_id, 'model', e.control.value)
                        )

                        # 尝试安全地替换控件
                        try:
                            # 找到包含模型选择器的行
                            parent_container = model_selector.parent
                            if parent_container and hasattr(parent_container, 'controls'):
                                for i, control in enumerate(parent_container.controls):
                                    if control == model_selector:
                                        parent_container.controls[i] = new_dropdown
                                        self.model_selectors[config_id] = new_dropdown
                                        self.app.page.update()
                                        self.app.log_message(f"[OK] {config.name} 已从文本框切换为下拉框，包含 {len(config.available_models)} 个模型")
                                        return
                        except Exception as replace_ex:
                            self.app.log_message(f"[WARN] 控件替换失败，将在下次界面刷新时生效: {replace_ex}")
                            # 备用方案：只更新字典引用
                            self.model_selectors[config_id] = new_dropdown
                            self.app.log_message(f"[OK] 已将 {config.name} 的模型输入框转换为下拉框")
                else:
                    self.app.log_message(f"[INFO] {config.name} 没有可用模型，保持文本输入框")

        except Exception as ex:
            self.app.log_message(f"[ERROR] 更新模型下拉框时出错: {ex}")

    def _update_config_field(self, config_id: str, field: str, value):
        """更新配置字段的通用方法"""
        try:
            service = get_multi_llm_service()
            service.update_config_field(config_id, field, value)
            self.app.log_message(f"已更新配置 {config_id} 的 {field}")
        except Exception as ex:
            self.app.log_message(f"更新配置字段时出错: {ex}")

    def _test_single_config(self, config_id: str):
        """测试单个配置连接"""
        try:
            service = get_multi_llm_service()
            config = service.get_config_by_id(config_id)

            if not config:
                self.app.log_message(f"[ERROR] 配置不存在: {config_id}")
                return

            # 基础验证
            if not config.api_key.strip():
                self.app.log_message(f"[ERROR] {config.name}: API密钥为空")
                return

            if not config.base_url.strip():
                self.app.log_message(f"[ERROR] {config.name}: API地址为空")
                return

            if not config.model.strip():
                self.app.log_message(f"[ERROR] {config.name}: 模型名称为空")
                return

            # 调用服务测试连接
            success = service.test_model_for_config(config_id, self.app)

            if success:
                self.app.log_message(f"[OK] {config.name} 连接测试成功")
            else:
                self.app.log_message(f"[ERROR] {config.name} 连接测试失败")

        except Exception as ex:
            self.app.log_message(f"[ERROR] 测试连接时出错: {ex}")
            import traceback
            traceback.print_exc()

    def _create_test_log_display(self, config, colors):
        """创建测试日志显示框"""
        if not config:
            return ft.Container()

        # 为每个配置创建独立的日志区域
        log_area = ft.TextField(
            multiline=True,
            read_only=True,
            min_lines=6,
            max_lines=8,
            expand=True,
            border_color="transparent",
            bgcolor=colors['input_bg'],
            color=colors['text_color'],
            value="暂无测试日志",
            text_size=11
        )

        # 将日志区域存储到配置对象中，以便后续更新
        if not hasattr(self.app, 'test_log_areas'):
            self.app.test_log_areas = {}
        if not hasattr(self.app, 'test_log_cleared'):
            self.app.test_log_cleared = {}
        self.app.test_log_areas[config.id] = log_area
        self.app.test_log_cleared[config.id] = False

        return ft.Container(
            content=ft.Column([
                ft.Text("测试日志", size=12, weight="bold", color=colors['text_color']),
                ft.Container(height=5),
                log_area
            ]),
            padding=10,
            bgcolor=colors['card_bg'],
            border_radius=6,
            border=ft.border.all(1, colors['border_color'])
        )

    def log_test_message(self, config_id: str, message: str, clear_first: bool = False):
        """添加测试日志消息到指定配置的日志区域"""
        if hasattr(self.app, 'test_log_areas') and config_id in self.app.test_log_areas:
            log_area = self.app.test_log_areas[config_id]
            import datetime
            timestamp = datetime.datetime.now().strftime("%H:%M:%S")

            # 检查是否是新的测试开始（包含"正在拉取"或"正在测试"）
            is_new_test_start = ("[Multi-LLM] 正在拉取" in message or
                               "[Multi-LLM] 正在测试" in message)

            # 如果是新测试开始，强制清空并设置新内容
            if is_new_test_start or clear_first or log_area.value == "暂无测试日志":
                new_line = f"[{timestamp}] {message}"

                # 强制清空：先设为空，更新，再设置新值
                log_area.value = ""
                self.app.page.update()

                # 设置新的第一条消息
                log_area.value = new_line

                # 标记此配置的日志已被清空
                if hasattr(self.app, 'test_log_cleared'):
                    self.app.test_log_cleared[config_id] = True

                self.app.page.update()
                return

            # 否则追加到现有日志
            current = log_area.value or ""
            new_line = f"[{timestamp}] {message}"

            if current:
                log_area.value = f"{current}\n{new_line}"
            else:
                log_area.value = new_line

            # 限制日志长度
            lines = log_area.value.split('\n')
            if len(lines) > 30:
                log_area.value = '\n'.join(lines[-30:])

            # 只更新页面
            self.app.page.update()

    def clear_test_log(self, config_id: str):
        """清空指定配置的测试日志"""
        if hasattr(self.app, 'test_log_areas') and config_id in self.app.test_log_areas:
            log_area = self.app.test_log_areas[config_id]
            log_area.value = ""
            # 只更新页面，避免控件更新错误
            self.app.page.update()

    # ========== 转录设置 ==========

    def create_transcription_settings(self):
        """创建转写设置内容（ElevenLabs）"""
        colors = self.app.theme_manager.get_theme_colors()

        # 音频文件路径
        self.audio_file_path_field = ft.TextField(
            label="音频文件路径",
            value=self.app.elevenlabs_settings.get('audio_file_path', '') if hasattr(self.app, 'elevenlabs_settings') else '',
            read_only=True,
            expand=True,
            border_color=colors['input_border'],
            color=colors['text_color'],
            label_style=ft.TextStyle(color=colors['secondary_text_color']),
            bgcolor=colors['input_bg']
        )

        # 浏览音频文件按钮 - 雾霾蓝风格
        audio_browse_btn = ft.ElevatedButton(
            "浏览音频",
            on_click=self.app._browse_audio_file if hasattr(self.app, '_browse_audio_file') else lambda e: None,
            bgcolor=colors['accent_color'],  # 雾霾蓝
            color="#FFFFFF",  # 白色文字
            style=ft.ButtonStyle(
                shape=ft.RoundedRectangleBorder(radius=8),
                elevation=0,
            )
        )

        # 转录语言选择
        self.transcription_language_dropdown = ft.Dropdown(
            label="转录语言",
            options=[
                ft.dropdown.Option("auto"),
                ft.dropdown.Option("zh"),
                ft.dropdown.Option("en"),
                ft.dropdown.Option("ja"),
                ft.dropdown.Option("ko"),
            ],
            value=self.app.elevenlabs_settings.get('language', 'auto') if hasattr(self.app, 'elevenlabs_settings') else 'auto',
            width=150,
            on_change=self._on_elevenlabs_setting_change,
            border_color=colors['input_border'],
            color=colors['text_color'],
            bgcolor=colors['input_bg'],
            label_style=ft.TextStyle(color=colors['text_color'])
        )

        # 说话人数
        self.num_speakers_field = ft.TextField(
            label="说话人数",
            value=str(self.app.elevenlabs_settings.get('num_speakers', 2) if hasattr(self.app, 'elevenlabs_settings') else 2),
            width=120,
            keyboard_type="number",
            helper_text="音频中的说话人数量",
            border_color=colors['input_border'],
            color=colors['text_color'],
            label_style=ft.TextStyle(color=colors['secondary_text_color']),
            bgcolor=colors['input_bg'],
            on_change=self._on_elevenlabs_setting_change
        )

        # 标记音频事件
        self.tag_audio_events_checkbox = ft.Checkbox(
            label="标记音频事件",
            value=self.app.elevenlabs_settings.get('tag_audio_events', True) if hasattr(self.app, 'elevenlabs_settings') else True,
            on_change=self._on_elevenlabs_setting_change,
            label_style=ft.TextStyle(color=colors['secondary_text_color'])
        )

        # 开始转录按钮（使用新ASR系统） - 雾霾蓝风格
        start_transcription_btn = ft.ElevatedButton(
            "开始转录",
            on_click=self.app._start_conversion if hasattr(self.app, '_start_conversion') else lambda e: None,  # 使用音频转换，转换完成后自动触发ASR转录
            bgcolor=colors['accent_color'],  # 雾霾蓝
            color="#FFFFFF",  # 白色文字
            style=ft.ButtonStyle(
                shape=ft.RoundedRectangleBorder(radius=8),
                elevation=0,
            )
        )

        return ft.Column([
            ft.Text("转录设置", size=18, weight="bold", color=colors['text_color']),
            ft.Container(height=20),

            ft.Text("音频文件", size=16, color=colors['secondary_text_color']),
            ft.Container(height=10),
            ft.Row([
                self.audio_file_path_field,
                audio_browse_btn
            ]),
            ft.Container(height=20),

            ft.Text("转录参数", size=16, color=colors['secondary_text_color']),
            ft.Container(height=10),
            ft.Row([
                self.transcription_language_dropdown,
                ft.Container(width=20),
                self.num_speakers_field
            ]),
            ft.Container(height=10),
            self.tag_audio_events_checkbox,
            ft.Container(height=20),

            start_transcription_btn,
            ft.Container(height=10),
            ft.Text("通过免费ElevenLabs服务将音频转换为JSON格式", size=12, color=colors['secondary_text_color'])
        ])

    # ========== 菜单更新方法 ==========



    def _close_error_dialog(self, error_dialog):
        """关闭错误对话框"""
        error_dialog.open = False
        self.page.dialog = self.dialog  # 恢复主对话框
        self.page.update()

    def _create_prompt_settings(self, colors):
        """创建提示词设置区域"""
        # 获取当前提示词设置
        current_prompt = self.app.config_manager.get_llm_system_prompt()

        # 提示词编辑器
        self.prompt_editor = ft.TextField(
            label="系统提示词",
            value=current_prompt,
            multiline=True,
            min_lines=8,
            max_lines=15,
            expand=True,
            border_color=colors['input_border'],
            color=colors['text_color'],
            bgcolor=colors['input_bg'],
            label_style=ft.TextStyle(color=colors['text_color']),
            on_change=self._on_prompt_change
        )

        return ft.Column([
            self.prompt_editor
        ])

    def _on_prompt_change(self, e):
        """提示词编辑器内容变化事件 - 自动保存"""
        try:
            if hasattr(self, 'prompt_editor') and self.prompt_editor:
                new_prompt = self.prompt_editor.value

                # 数据处理（对应API密钥的_sanitize_api_key）
                processed_prompt = new_prompt.strip() if new_prompt else ""

                # 实时保存到内存
                self.app.llm_settings["user_llm_system_prompt"] = processed_prompt

                # 更新配置并自动保存（对应API密钥的service.update_config_field）
                self.app.config_manager.set_llm_system_prompt(processed_prompt)
                self.app.config_manager.save_config()

                # 用户反馈（对应API密钥的日志消息）
                self.app.log_message("系统提示词已更新")

        except Exception as ex:
            # 错误处理（对应API密钥的错误处理）
            self.app.log_message(f"更新系统提示词时出错: {ex}")





    def _on_project_path_change(self, e):
        """项目目录路径改变时保存"""
        # 避免浏览时重复保存
        if not getattr(self, '_is_browsing_project', False):
            self._save_project_path_config()

    def _on_subtitle_path_change(self, e):
        """字幕目录路径改变时保存"""
        # 避免浏览时重复保存
        if not getattr(self, '_is_browsing_subtitle', False):
            self._save_subtitle_path_config()

    def _save_project_path_config(self):
        """保存项目目录配置"""
        try:
            project_path = self.project_path_field.value.strip()
            if project_path:  # 非空才保存
                self.app.config['project_path'] = project_path
                self.app.config_manager.save_config()
                self.app.log_message(f"已保存项目目录: {project_path}")
        except Exception as ex:
            self.app.log_message(f"保存项目目录失败: {ex}")

    def _save_subtitle_path_config(self):
        """保存字幕目录配置"""
        try:
            subtitle_path = self.subtitle_path_field.value.strip()
            if subtitle_path:  # 非空才保存
                self.app.config['subtitle_path'] = subtitle_path
                self.app.config_manager.save_config()
                self.app.log_message(f"已保存字幕目录: {subtitle_path}")
        except Exception as ex:
            self.app.log_message(f"保存字幕目录失败: {ex}")
