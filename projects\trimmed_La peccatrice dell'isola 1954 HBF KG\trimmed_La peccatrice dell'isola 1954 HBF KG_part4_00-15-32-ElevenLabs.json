{"success": true, "service": "elevenlabs", "transcription_id": "elevenlabs_1754319516", "text": "Io andai a Palermo, <PERSON><PERSON> si ammazzò e poi scoppiò l'inferno. Come se tutto fosse legato alla morte di quella ragazzetta che quando era viva pareva non ci fosse nemmeno fra noi. <PERSON>enite al sodo, l'ordine dei fatti devo metterlo io e non voi. Chi vi informò di quello che vostra moglie aveva fatto mentre voi eravate a Palermo? La stessa sera del funerale di Carmela, ora che mi ci fate pensare. È proprio strano. Abula era più ubriaco del solito. Lo spirito di male si è fermato in questa isola. Un innocente ha pagato per i peccati di tutti. Un innocente aver salvo scusa. È ubriaco, fede. <PERSON><PERSON>, è ubriaco, ma dice la verità. La colpa è stata tutta di quella mala femmina. Carmela è morta e io l'ho veduta corre verso la morte. Perché non l'ho fermata? Tu. Tu hai colpa di tutto. Il demonio è nel tuo animo. Tu sei... Le ho davvero ditorno. Io l'ho vista che correva piangendo e ho visto Francesco che baciarla per la cagna. Tua moglie ti ha mandato a Palermo con Maria perché voleva restare sola con Rosario e con tutti gli uomini dell'isola. Ma anche tu. Tu puoi farmi tacere, ma non puoi cancellare la verità. Sentivo come un fuoco dentro il petto, qualche cosa che non avevo provato mai. Gli avevo dato tutto. L'avevo sposata. L'avevo portata nella mia casa, accanto a mia figlia. E buttai a correre per raggiungerla. Quando l'incendio si spense era l'alba, ormai. Li trovamo tutti e due carbonizzati. Li portarono subito a Palermo. Credo che li abbiano sepolte laggiù. Non so più nulla. Non mi interessa. C'è una cosa che potrà interessarvi ancora, don Pietro Inghersia. Vostra moglie l'ucisa con una revolverata in testa. Proiettile calibro nove. Era morta prima che la baracca saltasse. E il negro non aveva armi. Rosario. Rosario. Potevate risparmiarvi di venire fin qui. Tanto da me non cavate niente. Non sono tipo da sparare in testa alle donne, io. Questo devo stabilirlo io. E vedi di calmare i bollenti spiriti, Rosario. O forse soltanto perché stai per sposare la figlia di Ingarsia, ti senti già padrone qui. Padroni non ce ne saranno più qui, e nemmeno padrone. Mia sorella e il mio miglior amico ci hanno lasciato la vita in questo affare. Perciò non dovete credere che mi faccia piacere ricordare. Stavo chiacchierando con Abulapoca quella mattina. Tu ritieni. Partiremo non appena ha attraccato il postale. Aiutatemi a levare la palanca. Guarda Rosario, è là come sembra. Già. E Carmela è con lei. Inseparabili, non c'è che dire. Dai su, molla. Tuo padre si fermerà a lungo stavolta? Non lo so, non parla mai dei suoi affari in casa. Però quando le barche prendono il mare e Rosario è alla pesca, io sono tranquilla. Anche se è lontano. Quando mio padre e tuo fratello sono a terra, invece, può sempre succedere qualcosa. Ma Rosario difende i pescatori. Ma ciascuno difende se stesso, dice mio padre. Ciao Carmela. Buongiorno signorina Maria. Quando il porto salvo è partito ed ho finito con i passeggeri del postale, fatti trovare al solito posto. Attento, Rosario ci guarda. E smettila di tremare, di cosa hai paura? Starai mare qualche settimana stavolta.", "words": [{"text": "Io", "start": 4.519, "end": 4.679, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 4.679, "end": 4.679, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "andai", "start": 4.679, "end": 4.94, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 4.94, "end": 4.94, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 4.94, "end": 5.019, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 5.019, "end": 5.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Palermo,", "start": 5.019, "end": 5.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 5.559, "end": 5.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Carmel<PERSON>", "start": 5.559, "end": 6.059, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 6.059, "end": 6.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "si", "start": 6.059, "end": 6.179, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 6.179, "end": 6.199, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ammazzò", "start": 6.199, "end": 6.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 6.619, "end": 7.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "e", "start": 7.019, "end": 7.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 7.139, "end": 7.199, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "poi", "start": 7.199, "end": 7.499, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 7.499, "end": 7.519, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "scopp<PERSON><PERSON>", "start": 7.519, "end": 7.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 7.899, "end": 7.899, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "l'inferno.", "start": 7.899, "end": 8.56, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 8.56, "end": 9.039, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Come", "start": 9.039, "end": 9.28, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 9.28, "end": 9.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "se", "start": 9.319, "end": 9.42, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 9.42, "end": 9.46, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tutto", "start": 9.46, "end": 9.679, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 9.679, "end": 9.679, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "fosse", "start": 9.679, "end": 9.94, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 9.94, "end": 9.96, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "legato", "start": 9.96, "end": 10.279, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 10.279, "end": 10.3, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "alla", "start": 10.3, "end": 10.44, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 10.44, "end": 10.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "morte", "start": 10.479, "end": 10.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 10.799, "end": 10.8, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 10.8, "end": 10.92, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 10.92, "end": 10.939, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "quella", "start": 10.939, "end": 11.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 11.159, "end": 11.179, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ragazzetta", "start": 11.179, "end": 11.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 11.779, "end": 12.079, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 12.079, "end": 12.22, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 12.22, "end": 12.22, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "quando", "start": 12.22, "end": 12.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 12.559, "end": 12.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "era", "start": 12.599, "end": 12.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 12.819, "end": 12.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "viva", "start": 12.819, "end": 13.14, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 13.14, "end": 13.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "pareva", "start": 13.659, "end": 14.039, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 14.039, "end": 14.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "non", "start": 14.059, "end": 14.179, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 14.179, "end": 14.179, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ci", "start": 14.179, "end": 14.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 14.319, "end": 14.34, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "fosse", "start": 14.34, "end": 14.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 14.579, "end": 14.579, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 14.579, "end": 14.96, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 14.96, "end": 14.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "fra", "start": 14.979, "end": 15.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 15.139, "end": 15.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "noi.", "start": 15.159, "end": 15.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 15.459, "end": 15.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Venite", "start": 15.659, "end": 15.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 15.979, "end": 16.0, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "al", "start": 16.0, "end": 16.119, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 16.119, "end": 16.18, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sodo,", "start": 16.18, "end": 16.62, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 16.62, "end": 16.719, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "l'ordine", "start": 16.719, "end": 17.119, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 17.119, "end": 17.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "dei", "start": 17.119, "end": 17.259, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 17.259, "end": 17.26, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "fatti", "start": 17.26, "end": 17.459, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 17.459, "end": 17.459, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "devo", "start": 17.459, "end": 17.639, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 17.639, "end": 17.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "metterlo", "start": 17.639, "end": 17.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 17.979, "end": 17.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "io", "start": 17.979, "end": 18.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 18.159, "end": 18.18, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "e", "start": 18.18, "end": 18.28, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 18.28, "end": 18.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "non", "start": 18.299, "end": 18.439, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 18.439, "end": 18.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "voi.", "start": 18.479, "end": 18.819, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 18.819, "end": 19.199, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>", "start": 19.199, "end": 19.319, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 19.319, "end": 19.359, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "vi", "start": 19.359, "end": 19.439, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 19.439, "end": 19.439, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "informò", "start": 19.439, "end": 19.899, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 19.899, "end": 19.899, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 19.899, "end": 20.019, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 20.019, "end": 20.02, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "quello", "start": 20.02, "end": 20.219, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 20.219, "end": 20.219, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "che", "start": 20.219, "end": 20.319, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 20.319, "end": 20.319, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "vostra", "start": 20.319, "end": 20.599, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 20.599, "end": 20.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "moglie", "start": 20.639, "end": 20.86, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 20.86, "end": 20.879, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "aveva", "start": 20.879, "end": 21.119, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 21.119, "end": 21.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "fatto", "start": 21.119, "end": 21.399, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 21.399, "end": 21.42, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mentre", "start": 21.42, "end": 21.679, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 21.679, "end": 21.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "voi", "start": 21.739, "end": 21.879, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 21.879, "end": 21.92, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "eravate", "start": 21.92, "end": 22.279, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 22.279, "end": 22.319, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "a", "start": 22.319, "end": 22.399, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 22.399, "end": 22.439, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Palermo?", "start": 22.439, "end": 22.879, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 22.879, "end": 23.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "La", "start": 23.059, "end": 23.259, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 23.259, "end": 23.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "stessa", "start": 23.299, "end": 23.599, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 23.599, "end": 23.639, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sera", "start": 23.639, "end": 23.879, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 23.879, "end": 23.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "del", "start": 23.879, "end": 24.039, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 24.039, "end": 24.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "funerale", "start": 24.059, "end": 24.5, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 24.5, "end": 24.5, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 24.5, "end": 24.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 24.619, "end": 24.639, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Carmela,", "start": 24.639, "end": 25.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 25.239, "end": 25.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ora", "start": 25.459, "end": 25.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 25.699, "end": 25.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 25.699, "end": 25.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 25.819, "end": 25.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "mi", "start": 25.819, "end": 25.939, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 25.939, "end": 25.939, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ci", "start": 25.939, "end": 26.099, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 26.099, "end": 26.099, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "fate", "start": 26.099, "end": 26.36, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 26.36, "end": 26.379, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "pensare.", "start": 26.379, "end": 26.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 26.979, "end": 28.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "È", "start": 28.059, "end": 28.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 28.219, "end": 28.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "proprio", "start": 28.219, "end": 28.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 28.619, "end": 28.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "strano.", "start": 28.619, "end": 29.079, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 29.079, "end": 29.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Abula", "start": 29.659, "end": 30.039, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 30.039, "end": 30.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "era", "start": 30.059, "end": 30.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 30.199, "end": 30.199, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "più", "start": 30.199, "end": 30.38, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 30.38, "end": 30.399, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ubriaco", "start": 30.399, "end": 30.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 30.819, "end": 30.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "del", "start": 30.819, "end": 30.96, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 30.96, "end": 31.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "solito.", "start": 31.019, "end": 31.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 31.479, "end": 34.34, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Lo", "start": 34.34, "end": 34.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 34.619, "end": 34.7, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "spirito", "start": 34.7, "end": 35.679, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 35.679, "end": 35.7, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 35.7, "end": 35.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 35.779, "end": 35.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "male", "start": 35.819, "end": 36.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 36.159, "end": 36.36, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "si", "start": 36.36, "end": 36.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 36.479, "end": 36.5, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "è", "start": 36.5, "end": 36.599, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 36.599, "end": 36.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "fermato", "start": 36.619, "end": 37.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 37.139, "end": 37.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "in", "start": 37.139, "end": 37.279, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 37.279, "end": 37.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "questa", "start": 37.299, "end": 37.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 37.659, "end": 37.719, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "isola.", "start": 37.719, "end": 38.179, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 38.179, "end": 40.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Un", "start": 40.919, "end": 41.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 41.119, "end": 41.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "innocente", "start": 41.139, "end": 41.88, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 41.88, "end": 41.899, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ha", "start": 41.899, "end": 42.019, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 42.019, "end": 42.02, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "pagato", "start": 42.02, "end": 42.5, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 42.5, "end": 42.7, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "per", "start": 42.7, "end": 42.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 42.899, "end": 42.899, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "i", "start": 42.899, "end": 42.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 42.979, "end": 42.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "pec<PERSON>i", "start": 42.979, "end": 43.6, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 43.6, "end": 43.639, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 43.639, "end": 43.76, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 43.76, "end": 43.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tutti.", "start": 43.819, "end": 44.419, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 44.419, "end": 45.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Un", "start": 45.659, "end": 45.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 45.819, "end": 45.84, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "innocente", "start": 45.84, "end": 46.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 46.459, "end": 46.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "aver", "start": 46.479, "end": 46.72, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 46.72, "end": 46.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "salvo", "start": 46.779, "end": 47.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 47.119, "end": 47.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "scusa.", "start": 47.319, "end": 47.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 47.799, "end": 47.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "È", "start": 47.799, "end": 47.819, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 47.819, "end": 47.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ubriaco,", "start": 47.819, "end": 48.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 48.139, "end": 48.139, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "fede.", "start": 48.139, "end": 48.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 48.159, "end": 48.159, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Sì,", "start": 48.159, "end": 48.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 48.159, "end": 48.159, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "è", "start": 48.159, "end": 48.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 48.159, "end": 48.159, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ubriaco,", "start": 48.159, "end": 48.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 48.159, "end": 48.159, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ma", "start": 48.159, "end": 48.259, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 48.259, "end": 48.259, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "dice", "start": 48.259, "end": 48.439, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 48.439, "end": 48.439, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "la", "start": 48.439, "end": 48.52, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 48.52, "end": 48.539, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "verità.", "start": 48.539, "end": 48.919, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 48.919, "end": 49.18, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "La", "start": 49.18, "end": 49.52, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 49.52, "end": 49.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "colpa", "start": 49.539, "end": 49.74, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 49.74, "end": 49.759, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "è", "start": 49.759, "end": 49.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 49.799, "end": 49.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "stata", "start": 49.799, "end": 49.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 49.979, "end": 49.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tutta", "start": 49.979, "end": 50.2, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 50.2, "end": 50.2, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 50.2, "end": 50.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 50.299, "end": 50.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "quella", "start": 50.299, "end": 50.5, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 50.5, "end": 50.639, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "mala", "start": 50.639, "end": 50.86, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 50.86, "end": 50.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "femmina.", "start": 50.919, "end": 51.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 51.479, "end": 57.36, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Carmel<PERSON>", "start": 57.36, "end": 58.04, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 58.04, "end": 58.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "è", "start": 58.119, "end": 58.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 58.139, "end": 58.279, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "morta", "start": 58.279, "end": 58.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 58.859, "end": 58.899, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "e", "start": 58.899, "end": 58.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 58.919, "end": 59.0, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "io", "start": 59.0, "end": 59.18, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 59.18, "end": 59.199, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "l'ho", "start": 59.199, "end": 59.399, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 59.399, "end": 59.399, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "veduta", "start": 59.399, "end": 59.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 59.919, "end": 59.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "corre", "start": 59.979, "end": 60.28, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 60.28, "end": 60.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "verso", "start": 60.319, "end": 60.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 60.859, "end": 60.859, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "la", "start": 60.859, "end": 61.04, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 61.04, "end": 61.079, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "morte.", "start": 61.079, "end": 62.179, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 62.179, "end": 62.999, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 62.999, "end": 63.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 63.579, "end": 63.579, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "non", "start": 63.579, "end": 63.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 63.859, "end": 63.859, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "l'ho", "start": 63.859, "end": 64.06, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 64.06, "end": 64.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "fermata?", "start": 64.119, "end": 64.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 64.779, "end": 65.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Tu.", "start": 65.739, "end": 66.12, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 66.12, "end": 67.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Tu", "start": 67.599, "end": 67.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 67.779, "end": 67.839, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "hai", "start": 67.839, "end": 67.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 67.959, "end": 67.98, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "colpa", "start": 67.98, "end": 68.32, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 68.32, "end": 68.32, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 68.32, "end": 68.44, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 68.44, "end": 68.499, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tutto.", "start": 68.499, "end": 68.96, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 68.96, "end": 70.06, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Il", "start": 70.06, "end": 70.279, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 70.279, "end": 70.32, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "demonio", "start": 70.32, "end": 70.9, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 70.9, "end": 70.9, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "è", "start": 70.9, "end": 71.02, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 71.02, "end": 71.02, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "nel", "start": 71.02, "end": 71.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 71.219, "end": 71.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tuo", "start": 71.319, "end": 71.5, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 71.5, "end": 71.52, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "animo.", "start": 71.52, "end": 72.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 72.159, "end": 72.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Tu", "start": 72.159, "end": 72.32, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 72.32, "end": 72.379, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sei...", "start": 72.379, "end": 72.679, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 72.679, "end": 72.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Le", "start": 72.699, "end": 72.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 72.799, "end": 72.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ho", "start": 72.879, "end": 73.099, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 73.099, "end": 73.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "da<PERSON><PERSON><PERSON>", "start": 73.119, "end": 73.419, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 73.419, "end": 73.44, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ditorno.", "start": 73.44, "end": 74.0, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 74.0, "end": 74.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Io", "start": 74.919, "end": 75.02, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 75.02, "end": 75.079, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "l'ho", "start": 75.079, "end": 75.179, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 75.179, "end": 75.179, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "vista", "start": 75.179, "end": 75.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 75.459, "end": 75.48, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 75.48, "end": 75.56, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 75.56, "end": 75.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "correva", "start": 75.619, "end": 76.099, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 76.099, "end": 76.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 76.119, "end": 76.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 76.699, "end": 76.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "e", "start": 76.919, "end": 76.98, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 76.98, "end": 76.98, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ho", "start": 76.98, "end": 77.08, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 77.08, "end": 77.099, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "visto", "start": 77.099, "end": 77.339, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 77.339, "end": 77.359, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>", "start": 77.359, "end": 77.939, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 77.939, "end": 77.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 77.959, "end": 78.08, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 78.08, "end": 78.08, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "bac<PERSON><PERSON>", "start": 78.08, "end": 78.419, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 78.419, "end": 78.439, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "per", "start": 78.439, "end": 78.56, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 78.56, "end": 78.56, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "la", "start": 78.56, "end": 78.639, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 78.639, "end": 78.66, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "cagna.", "start": 78.66, "end": 78.9, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 78.9, "end": 78.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 78.959, "end": 79.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 79.139, "end": 79.179, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "moglie", "start": 79.179, "end": 79.56, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 79.56, "end": 80.439, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ti", "start": 80.439, "end": 80.719, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 80.719, "end": 80.719, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ha", "start": 80.719, "end": 80.839, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 80.839, "end": 80.839, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "mandato", "start": 80.839, "end": 81.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 81.319, "end": 81.32, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 81.32, "end": 81.44, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 81.44, "end": 81.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Palermo", "start": 81.459, "end": 82.019, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 82.019, "end": 82.079, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "con", "start": 82.079, "end": 82.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 82.239, "end": 82.279, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>", "start": 82.279, "end": 82.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 82.619, "end": 82.679, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "perché", "start": 82.679, "end": 82.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 82.979, "end": 82.98, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>eva", "start": 82.98, "end": 83.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 83.299, "end": 83.32, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "restare", "start": 83.32, "end": 83.74, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 83.74, "end": 83.759, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sola", "start": 83.759, "end": 84.039, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 84.039, "end": 84.279, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "con", "start": 84.279, "end": 84.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 84.459, "end": 84.499, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Rosario", "start": 84.499, "end": 84.98, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 84.98, "end": 84.98, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "e", "start": 84.98, "end": 85.08, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 85.08, "end": 85.099, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "con", "start": 85.099, "end": 85.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 85.219, "end": 85.279, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tutti", "start": 85.279, "end": 85.48, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 85.48, "end": 85.519, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "gli", "start": 85.519, "end": 85.639, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 85.639, "end": 85.639, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 85.639, "end": 86.039, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 86.039, "end": 86.039, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "dell'isola.", "start": 86.039, "end": 86.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 86.759, "end": 87.279, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Ma", "start": 87.279, "end": 87.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 87.459, "end": 87.48, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "anche", "start": 87.48, "end": 87.679, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 87.679, "end": 87.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tu.", "start": 87.799, "end": 88.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 88.219, "end": 92.179, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Tu", "start": 92.179, "end": 92.359, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 92.359, "end": 92.42, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "puoi", "start": 92.42, "end": 92.64, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 92.64, "end": 92.679, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "farmi", "start": 92.679, "end": 93.06, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 93.06, "end": 93.08, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tacere,", "start": 93.08, "end": 93.66, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 93.66, "end": 94.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ma", "start": 94.019, "end": 94.14, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 94.14, "end": 94.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "non", "start": 94.159, "end": 94.28, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 94.28, "end": 94.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "puoi", "start": 94.319, "end": 94.48, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 94.48, "end": 94.519, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "cancellare", "start": 94.519, "end": 95.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 95.219, "end": 95.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "la", "start": 95.219, "end": 95.34, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 95.34, "end": 95.359, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "verità.", "start": 95.359, "end": 96.059, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 96.059, "end": 103.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Sentivo", "start": 103.459, "end": 103.92, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 103.92, "end": 103.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "come", "start": 103.979, "end": 104.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 104.199, "end": 104.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "un", "start": 104.219, "end": 104.359, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 104.359, "end": 104.379, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "fuoco", "start": 104.379, "end": 104.719, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 104.719, "end": 104.74, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "dentro", "start": 104.74, "end": 105.039, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 105.039, "end": 105.06, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "il", "start": 105.06, "end": 105.14, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 105.14, "end": 105.179, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "petto,", "start": 105.179, "end": 105.639, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 105.639, "end": 106.339, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "qualche", "start": 106.339, "end": 106.719, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 106.719, "end": 106.74, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "cosa", "start": 106.74, "end": 106.999, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 106.999, "end": 107.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 107.019, "end": 107.16, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 107.16, "end": 107.199, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "non", "start": 107.199, "end": 107.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 107.319, "end": 107.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "avevo", "start": 107.319, "end": 107.62, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 107.62, "end": 107.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "provato", "start": 107.659, "end": 108.06, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 108.06, "end": 108.08, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "mai.", "start": 108.08, "end": 108.419, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 108.419, "end": 113.42, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 113.42, "end": 113.56, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 113.56, "end": 113.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "avevo", "start": 113.599, "end": 113.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 113.859, "end": 113.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "dato", "start": 113.879, "end": 114.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 114.119, "end": 114.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tutto.", "start": 114.119, "end": 114.499, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 114.499, "end": 115.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "L'avevo", "start": 115.299, "end": 115.66, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 115.66, "end": 115.679, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sposata.", "start": 115.679, "end": 116.419, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 116.419, "end": 117.24, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "L'avevo", "start": 117.24, "end": 117.58, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 117.58, "end": 117.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "portata", "start": 117.619, "end": 118.08, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 118.08, "end": 118.099, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "nella", "start": 118.099, "end": 118.32, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 118.32, "end": 118.359, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "mia", "start": 118.359, "end": 118.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 118.559, "end": 118.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "casa,", "start": 118.599, "end": 119.059, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 119.059, "end": 120.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "accanto", "start": 120.539, "end": 120.999, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 120.999, "end": 120.999, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 120.999, "end": 121.06, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 121.06, "end": 121.06, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "mia", "start": 121.06, "end": 121.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 121.219, "end": 121.24, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "figlia.", "start": 121.24, "end": 121.66, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 121.66, "end": 129.339, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "E", "start": 129.339, "end": 129.439, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 129.439, "end": 129.439, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "buttai", "start": 129.439, "end": 129.78, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 129.78, "end": 129.78, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 129.78, "end": 129.839, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 129.839, "end": 129.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "co<PERSON>e", "start": 129.879, "end": 130.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 130.199, "end": 130.199, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "per", "start": 130.199, "end": 130.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 130.319, "end": 130.359, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "rag<PERSON><PERSON><PERSON><PERSON>.", "start": 130.359, "end": 131.079, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 131.079, "end": 140.679, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Quando", "start": 140.679, "end": 140.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 140.979, "end": 140.999, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "l'incendio", "start": 140.999, "end": 141.46, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 141.46, "end": 141.46, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "si", "start": 141.46, "end": 141.539, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 141.539, "end": 141.579, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "spense", "start": 141.579, "end": 141.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 141.959, "end": 141.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "era", "start": 141.979, "end": 142.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 142.119, "end": 142.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "l'alba,", "start": 142.119, "end": 142.499, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 142.499, "end": 142.499, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ormai.", "start": 142.499, "end": 143.06, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 143.06, "end": 143.519, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Li", "start": 143.519, "end": 143.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 143.659, "end": 143.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "trovamo", "start": 143.659, "end": 144.019, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 144.019, "end": 144.039, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tutti", "start": 144.039, "end": 144.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 144.219, "end": 144.24, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "e", "start": 144.24, "end": 144.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 144.319, "end": 144.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "due", "start": 144.319, "end": 144.519, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 144.519, "end": 144.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>.", "start": 144.539, "end": 145.359, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 145.359, "end": 146.259, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Li", "start": 146.259, "end": 146.399, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 146.399, "end": 146.399, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "portarono", "start": 146.399, "end": 146.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 146.899, "end": 146.92, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "subito", "start": 146.92, "end": 147.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 147.299, "end": 147.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 147.319, "end": 147.419, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 147.419, "end": 147.439, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Palermo.", "start": 147.439, "end": 147.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 147.979, "end": 148.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 148.879, "end": 149.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 149.239, "end": 149.259, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 149.259, "end": 149.359, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 149.359, "end": 149.359, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "li", "start": 149.359, "end": 149.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 149.479, "end": 149.499, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 149.499, "end": 149.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 149.759, "end": 149.78, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sepolte", "start": 149.78, "end": 150.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 150.219, "end": 150.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "laggi<PERSON>.", "start": 150.219, "end": 150.739, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 150.739, "end": 151.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Non", "start": 151.659, "end": 151.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 151.859, "end": 151.899, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "so", "start": 151.899, "end": 152.04, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 152.04, "end": 152.06, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "più", "start": 152.06, "end": 152.179, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 152.179, "end": 152.179, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "nulla.", "start": 152.179, "end": 152.519, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 152.519, "end": 153.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Non", "start": 153.159, "end": 153.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 153.299, "end": 153.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "mi", "start": 153.319, "end": 153.379, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 153.379, "end": 153.379, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "interessa.", "start": 153.379, "end": 153.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 153.859, "end": 153.859, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "C'è", "start": 153.859, "end": 154.379, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 154.379, "end": 154.399, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "una", "start": 154.399, "end": 154.639, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 154.639, "end": 154.659, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "cosa", "start": 154.659, "end": 154.899, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 154.899, "end": 154.939, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "che", "start": 154.939, "end": 155.019, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 155.019, "end": 155.039, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "potrà", "start": 155.039, "end": 155.399, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 155.399, "end": 155.399, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "interessar<PERSON>", "start": 155.399, "end": 155.999, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 155.999, "end": 155.999, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ancora,", "start": 155.999, "end": 156.379, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 156.379, "end": 156.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "don", "start": 156.379, "end": 156.539, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 156.539, "end": 156.579, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>", "start": 156.579, "end": 156.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 156.799, "end": 156.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Inghersia.", "start": 156.819, "end": 157.539, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 157.539, "end": 158.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Vostra", "start": 158.379, "end": 158.819, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 158.819, "end": 158.899, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "moglie", "start": 158.899, "end": 159.4, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 159.4, "end": 159.839, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "l'ucisa", "start": 159.839, "end": 160.499, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 160.499, "end": 160.539, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "con", "start": 160.539, "end": 160.699, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 160.699, "end": 160.719, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "una", "start": 160.719, "end": 160.86, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 160.86, "end": 160.899, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "revolverata", "start": 160.899, "end": 161.519, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 161.519, "end": 161.579, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "in", "start": 161.579, "end": 161.679, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 161.679, "end": 161.759, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "testa.", "start": 161.759, "end": 162.219, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 162.219, "end": 163.339, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Proiet<PERSON>", "start": 163.339, "end": 164.019, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 164.019, "end": 164.079, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "calibro", "start": 164.079, "end": 164.579, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 164.579, "end": 164.619, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "nove.", "start": 164.619, "end": 165.079, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 165.079, "end": 165.839, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Era", "start": 165.839, "end": 166.06, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 166.06, "end": 166.079, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "morta", "start": 166.079, "end": 166.459, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 166.459, "end": 166.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "prima", "start": 166.479, "end": 166.719, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 166.719, "end": 166.74, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "che", "start": 166.74, "end": 166.819, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 166.819, "end": 166.839, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "la", "start": 166.839, "end": 166.939, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 166.939, "end": 166.959, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "baracca", "start": 166.959, "end": 167.319, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 167.319, "end": 167.339, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "saltasse.", "start": 167.339, "end": 168.099, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 168.099, "end": 168.699, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "E", "start": 168.699, "end": 168.86, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 168.86, "end": 168.899, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "il", "start": 168.899, "end": 169.039, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 169.039, "end": 169.139, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "negro", "start": 169.139, "end": 169.599, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 169.599, "end": 169.719, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "non", "start": 169.719, "end": 169.879, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 169.879, "end": 169.92, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "aveva", "start": 169.92, "end": 170.319, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 170.319, "end": 170.359, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "armi.", "start": 170.359, "end": 170.859, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 170.859, "end": 185.279, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Rosario.", "start": 185.279, "end": 187.599, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 187.599, "end": 190.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Rosario.", "start": 190.539, "end": 191.24, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 191.24, "end": 217.079, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Potevate", "start": 217.079, "end": 217.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 217.859, "end": 217.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ris<PERSON><PERSON><PERSON><PERSON>", "start": 217.879, "end": 218.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 218.459, "end": 218.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 218.459, "end": 218.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 218.559, "end": 218.579, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "venire", "start": 218.579, "end": 218.86, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 218.86, "end": 218.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "fin", "start": 218.879, "end": 219.019, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 219.019, "end": 219.039, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "qui.", "start": 219.039, "end": 219.259, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 219.259, "end": 219.579, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 219.579, "end": 219.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 219.799, "end": 219.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "da", "start": 219.819, "end": 219.92, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 219.92, "end": 219.92, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "me", "start": 219.92, "end": 220.019, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 220.019, "end": 220.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "non", "start": 220.019, "end": 220.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 220.139, "end": 220.14, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "cavate", "start": 220.14, "end": 220.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 220.479, "end": 220.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "niente.", "start": 220.479, "end": 220.88, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 220.88, "end": 221.56, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Non", "start": 221.56, "end": 221.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 221.699, "end": 221.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sono", "start": 221.739, "end": 221.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 221.919, "end": 221.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tipo", "start": 221.959, "end": 222.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 222.159, "end": 222.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "da", "start": 222.159, "end": 222.259, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 222.259, "end": 222.259, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sparare", "start": 222.259, "end": 222.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 222.579, "end": 222.579, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "in", "start": 222.579, "end": 222.679, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 222.679, "end": 222.679, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "testa", "start": 222.679, "end": 222.92, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 222.92, "end": 222.92, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "alle", "start": 222.92, "end": 223.02, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 223.02, "end": 223.06, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "donne,", "start": 223.06, "end": 223.36, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 223.36, "end": 223.36, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "io.", "start": 223.36, "end": 223.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 223.579, "end": 224.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 224.739, "end": 224.999, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 224.999, "end": 225.039, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "devo", "start": 225.039, "end": 225.22, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 225.22, "end": 225.22, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "stabilirlo", "start": 225.22, "end": 225.8, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 225.8, "end": 225.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "io.", "start": 225.819, "end": 226.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 226.139, "end": 226.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "E", "start": 226.799, "end": 226.899, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 226.899, "end": 226.899, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "vedi", "start": 226.899, "end": 227.079, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 227.079, "end": 227.079, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 227.079, "end": 227.179, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 227.179, "end": 227.179, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "calmare", "start": 227.179, "end": 227.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 227.479, "end": 227.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "i", "start": 227.479, "end": 227.56, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 227.56, "end": 227.56, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "boll<PERSON>i", "start": 227.56, "end": 227.939, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 227.939, "end": 228.0, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "spiriti,", "start": 228.0, "end": 228.519, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 228.519, "end": 228.519, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Rosario.", "start": 228.519, "end": 229.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 229.139, "end": 229.619, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "O", "start": 229.619, "end": 229.72, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 229.72, "end": 229.759, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "forse", "start": 229.759, "end": 230.019, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 230.019, "end": 230.039, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "soltanto", "start": 230.039, "end": 230.44, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 230.44, "end": 230.459, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "perché", "start": 230.459, "end": 230.719, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 230.719, "end": 230.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "stai", "start": 230.739, "end": 230.94, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 230.94, "end": 230.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "per", "start": 230.979, "end": 231.119, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 231.119, "end": 231.14, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sposare", "start": 231.14, "end": 231.539, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 231.539, "end": 231.539, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "la", "start": 231.539, "end": 231.639, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 231.639, "end": 231.679, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "figlia", "start": 231.679, "end": 231.879, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 231.879, "end": 231.879, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 231.879, "end": 231.94, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 231.94, "end": 231.959, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Ingarsia,", "start": 231.959, "end": 232.539, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 232.539, "end": 232.539, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ti", "start": 232.539, "end": 232.639, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 232.639, "end": 232.64, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "senti", "start": 232.64, "end": 232.92, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 232.92, "end": 232.92, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "già", "start": 232.92, "end": 233.039, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 233.039, "end": 233.099, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "padrone", "start": 233.099, "end": 233.639, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 233.639, "end": 233.699, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "qui.", "start": 233.699, "end": 233.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 233.979, "end": 234.239, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 234.239, "end": 234.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 234.659, "end": 234.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "non", "start": 234.659, "end": 234.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 234.779, "end": 234.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ce", "start": 234.799, "end": 234.879, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 234.879, "end": 234.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ne", "start": 234.879, "end": 234.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 234.959, "end": 234.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "saranno", "start": 234.959, "end": 235.28, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 235.28, "end": 235.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "più", "start": 235.319, "end": 235.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 235.479, "end": 235.5, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "qui,", "start": 235.5, "end": 235.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 235.699, "end": 235.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "e", "start": 235.699, "end": 235.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 235.759, "end": 235.759, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 235.759, "end": 236.079, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 236.079, "end": 236.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "padrone.", "start": 236.119, "end": 236.66, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 236.66, "end": 237.42, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>", "start": 237.42, "end": 237.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 237.579, "end": 237.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sorella", "start": 237.619, "end": 238.0, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 238.0, "end": 238.0, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "e", "start": 238.0, "end": 238.079, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 238.079, "end": 238.079, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "il", "start": 238.079, "end": 238.179, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 238.179, "end": 238.179, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "mio", "start": 238.179, "end": 238.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 238.299, "end": 238.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "miglior", "start": 238.319, "end": 238.539, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 238.539, "end": 238.56, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "amico", "start": 238.56, "end": 238.839, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 238.839, "end": 238.86, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ci", "start": 238.86, "end": 238.939, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 238.939, "end": 238.939, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "hanno", "start": 238.939, "end": 239.079, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 239.079, "end": 239.099, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "lasciato", "start": 239.099, "end": 239.399, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 239.399, "end": 239.42, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "la", "start": 239.42, "end": 239.5, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 239.5, "end": 239.519, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "vita", "start": 239.519, "end": 239.719, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 239.719, "end": 239.719, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "in", "start": 239.719, "end": 239.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 239.799, "end": 239.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "questo", "start": 239.819, "end": 239.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 239.979, "end": 239.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "affare.", "start": 239.979, "end": 240.419, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 240.419, "end": 241.399, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 241.399, "end": 241.679, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 241.679, "end": 241.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "non", "start": 241.699, "end": 241.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 241.799, "end": 241.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "dovete", "start": 241.819, "end": 242.06, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 242.06, "end": 242.06, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "credere", "start": 242.06, "end": 242.359, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 242.359, "end": 242.36, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 242.36, "end": 242.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 242.459, "end": 242.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "mi", "start": 242.459, "end": 242.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 242.579, "end": 242.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "faccia", "start": 242.599, "end": 242.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 242.779, "end": 242.78, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "piacere", "start": 242.78, "end": 243.14, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 243.14, "end": 243.14, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ricordare.", "start": 243.14, "end": 243.719, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 243.719, "end": 248.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Stavo", "start": 248.879, "end": 249.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 249.159, "end": 249.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "chiac<PERSON><PERSON><PERSON>", "start": 249.219, "end": 249.719, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 249.719, "end": 249.759, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "con", "start": 249.759, "end": 249.88, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 249.88, "end": 249.939, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Abulapoca", "start": 249.939, "end": 250.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 250.699, "end": 250.839, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "quella", "start": 250.839, "end": 251.079, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 251.079, "end": 251.079, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "mattina.", "start": 251.079, "end": 251.839, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 251.839, "end": 252.839, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Tu", "start": 252.839, "end": 252.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 252.979, "end": 252.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ritieni.", "start": 252.979, "end": 253.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 253.559, "end": 257.439, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Partiremo", "start": 257.439, "end": 258.039, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 258.039, "end": 258.079, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "non", "start": 258.079, "end": 258.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 258.199, "end": 258.199, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "appena", "start": 258.199, "end": 258.399, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 258.399, "end": 258.399, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ha", "start": 258.399, "end": 258.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 258.459, "end": 258.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "attraccato", "start": 258.459, "end": 258.78, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 258.78, "end": 258.78, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "il", "start": 258.78, "end": 258.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 258.859, "end": 258.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "postale.", "start": 258.879, "end": 259.38, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 259.38, "end": 260.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 260.459, "end": 260.92, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 260.92, "end": 260.92, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 260.92, "end": 260.939, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 260.939, "end": 260.939, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "levare", "start": 260.939, "end": 261.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 261.159, "end": 261.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "la", "start": 261.159, "end": 261.259, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 261.259, "end": 261.28, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "palanca.", "start": 261.28, "end": 261.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 261.759, "end": 267.92, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Guarda", "start": 267.92, "end": 268.28, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 268.28, "end": 268.28, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Rosario,", "start": 268.28, "end": 268.88, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 268.88, "end": 269.06, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "è", "start": 269.06, "end": 269.179, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 269.179, "end": 269.179, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "là", "start": 269.179, "end": 269.339, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 269.339, "end": 269.339, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "come", "start": 269.339, "end": 269.58, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 269.58, "end": 269.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sembra.", "start": 269.619, "end": 269.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 269.979, "end": 270.939, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Già.", "start": 270.939, "end": 271.64, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 271.64, "end": 271.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "E", "start": 271.879, "end": 271.999, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 271.999, "end": 272.06, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Carmel<PERSON>", "start": 272.06, "end": 272.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 272.459, "end": 272.519, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "è", "start": 272.519, "end": 272.539, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 272.539, "end": 272.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "con", "start": 272.539, "end": 272.739, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 272.739, "end": 272.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "lei.", "start": 272.739, "end": 273.179, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 273.179, "end": 273.56, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Inseparabili,", "start": 273.56, "end": 274.399, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 274.399, "end": 274.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "non", "start": 274.539, "end": 274.639, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 274.639, "end": 274.679, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "c'è", "start": 274.679, "end": 274.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 274.819, "end": 274.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 274.819, "end": 274.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 274.959, "end": 274.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "dire.", "start": 274.959, "end": 275.399, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 275.399, "end": 278.899, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Dai", "start": 278.899, "end": 279.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 279.979, "end": 280.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "su,", "start": 280.019, "end": 280.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 280.319, "end": 280.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "molla.", "start": 280.459, "end": 284.499, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 284.499, "end": 286.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 286.699, "end": 286.92, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 286.92, "end": 286.959, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "padre", "start": 286.959, "end": 287.219, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 287.219, "end": 287.219, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "si", "start": 287.219, "end": 287.339, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 287.339, "end": 287.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "fermerà", "start": 287.379, "end": 287.699, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 287.699, "end": 287.699, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "a", "start": 287.699, "end": 287.78, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 287.78, "end": 287.78, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "lungo", "start": 287.78, "end": 288.019, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 288.019, "end": 288.06, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "stavolta?", "start": 288.06, "end": 288.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 288.559, "end": 288.699, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Non", "start": 288.699, "end": 289.099, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 289.099, "end": 289.119, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "lo", "start": 289.119, "end": 289.259, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 289.259, "end": 289.28, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "so,", "start": 289.28, "end": 289.479, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 289.479, "end": 289.519, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "non", "start": 289.519, "end": 289.619, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 289.619, "end": 289.699, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "parla", "start": 289.699, "end": 289.939, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 289.939, "end": 289.959, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "mai", "start": 289.959, "end": 290.14, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 290.14, "end": 290.159, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "dei", "start": 290.159, "end": 290.299, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 290.299, "end": 290.359, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "suoi", "start": 290.359, "end": 290.56, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 290.56, "end": 290.579, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "affari", "start": 290.579, "end": 291.039, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 291.039, "end": 291.039, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "in", "start": 291.039, "end": 291.179, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 291.179, "end": 291.199, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "casa.", "start": 291.199, "end": 291.659, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 291.659, "end": 292.159, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 292.159, "end": 292.42, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 292.42, "end": 292.459, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "quando", "start": 292.459, "end": 292.699, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 292.699, "end": 292.719, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "le", "start": 292.719, "end": 292.819, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 292.819, "end": 292.839, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "barche", "start": 292.839, "end": 293.159, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 293.159, "end": 293.159, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "prendono", "start": 293.159, "end": 293.519, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 293.519, "end": 293.539, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "il", "start": 293.539, "end": 293.599, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 293.599, "end": 293.659, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "mare", "start": 293.659, "end": 293.859, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 293.859, "end": 293.92, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "e", "start": 293.92, "end": 293.94, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 293.94, "end": 293.959, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Rosario", "start": 293.959, "end": 294.359, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 294.359, "end": 294.379, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "è", "start": 294.379, "end": 294.399, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 294.399, "end": 294.42, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "alla", "start": 294.42, "end": 294.539, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 294.539, "end": 294.599, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "pesca,", "start": 294.599, "end": 294.979, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 294.979, "end": 295.099, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "io", "start": 295.099, "end": 295.22, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 295.22, "end": 295.239, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "sono", "start": 295.239, "end": 295.439, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 295.439, "end": 295.499, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "tranquilla.", "start": 295.499, "end": 296.119, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 296.119, "end": 297.06, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 297.06, "end": 297.299, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 297.299, "end": 297.319, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "se", "start": 297.319, "end": 297.419, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 297.419, "end": 297.439, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "è", "start": 297.439, "end": 297.499, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 297.499, "end": 297.499, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "lontano.", "start": 297.499, "end": 298.039, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 298.039, "end": 298.92, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Quando", "start": 298.92, "end": 299.139, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 299.139, "end": 299.159, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "mio", "start": 299.159, "end": 299.279, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 299.279, "end": 299.339, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "padre", "start": 299.339, "end": 299.559, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 299.559, "end": 299.56, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "e", "start": 299.56, "end": 299.64, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 299.64, "end": 299.659, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "tuo", "start": 299.659, "end": 299.759, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 299.759, "end": 299.78, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "fratello", "start": 299.78, "end": 300.159, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 300.159, "end": 300.199, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "sono", "start": 300.199, "end": 300.379, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 300.379, "end": 300.379, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "a", "start": 300.379, "end": 300.499, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 300.499, "end": 300.499, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "terra,", "start": 300.499, "end": 300.759, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 300.759, "end": 300.759, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "invece,", "start": 300.759, "end": 301.199, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 301.199, "end": 301.319, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 301.319, "end": 301.479, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 301.479, "end": 301.499, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "sempre", "start": 301.499, "end": 301.759, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 301.759, "end": 301.78, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "succedere", "start": 301.78, "end": 302.259, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 302.259, "end": 302.259, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "qualcosa.", "start": 302.259, "end": 302.859, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 302.859, "end": 302.999, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Ma", "start": 302.999, "end": 303.199, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 303.199, "end": 303.219, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Rosario", "start": 303.219, "end": 303.639, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 303.639, "end": 303.64, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "difende", "start": 303.64, "end": 303.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 303.979, "end": 303.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "i", "start": 303.979, "end": 304.039, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 304.039, "end": 304.039, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "pescatori.", "start": 304.039, "end": 304.679, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 304.679, "end": 304.679, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Ma", "start": 304.679, "end": 304.819, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 304.819, "end": 304.839, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 304.839, "end": 305.199, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 305.199, "end": 305.199, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "difende", "start": 305.199, "end": 305.499, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 305.499, "end": 305.539, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "se", "start": 305.539, "end": 305.679, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 305.679, "end": 305.699, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "stesso,", "start": 305.699, "end": 306.039, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 306.039, "end": 306.039, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "dice", "start": 306.039, "end": 306.219, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 306.219, "end": 306.259, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "mio", "start": 306.259, "end": 306.379, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 306.379, "end": 306.439, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "padre.", "start": 306.439, "end": 306.579, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 306.579, "end": 306.579, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Ciao", "start": 306.579, "end": 306.8, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 306.8, "end": 306.839, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Carmela.", "start": 306.839, "end": 307.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 307.299, "end": 308.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "start": 308.159, "end": 308.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 308.559, "end": 308.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "signorina", "start": 308.599, "end": 308.939, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 308.939, "end": 308.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>.", "start": 308.959, "end": 309.28, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 309.28, "end": 311.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Quando", "start": 311.479, "end": 311.679, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 311.679, "end": 311.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "il", "start": 311.699, "end": 311.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 311.759, "end": 311.78, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "porto", "start": 311.78, "end": 311.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 311.979, "end": 312.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "salvo", "start": 312.019, "end": 312.259, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 312.259, "end": 312.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "è", "start": 312.299, "end": 312.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 312.319, "end": 312.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "partito", "start": 312.319, "end": 312.679, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 312.679, "end": 312.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ed", "start": 312.699, "end": 312.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 312.779, "end": 312.78, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ho", "start": 312.78, "end": 312.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 312.859, "end": 312.859, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "finito", "start": 312.859, "end": 313.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 313.119, "end": 313.14, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "con", "start": 313.14, "end": 313.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 313.239, "end": 313.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "i", "start": 313.239, "end": 313.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 313.319, "end": 313.339, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 313.339, "end": 313.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 313.759, "end": 313.78, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "del", "start": 313.78, "end": 313.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 313.899, "end": 313.92, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "postale,", "start": 313.92, "end": 314.339, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 314.339, "end": 314.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "fatti", "start": 314.619, "end": 314.86, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 314.86, "end": 314.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "trovare", "start": 314.879, "end": 315.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 315.139, "end": 315.14, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "al", "start": 315.14, "end": 315.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 315.219, "end": 315.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "solito", "start": 315.219, "end": 315.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 315.459, "end": 315.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "posto.", "start": 315.479, "end": 315.66, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 315.66, "end": 315.66, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Attento,", "start": 315.66, "end": 316.359, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 316.359, "end": 316.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Rosario", "start": 316.739, "end": 317.119, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 317.119, "end": 317.179, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ci", "start": 317.179, "end": 317.28, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 317.28, "end": 317.319, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "guarda.", "start": 317.319, "end": 317.82, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 317.82, "end": 318.679, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "E", "start": 318.679, "end": 318.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 318.799, "end": 318.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "smettila", "start": 318.799, "end": 319.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 319.239, "end": 319.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 319.239, "end": 319.339, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 319.339, "end": 319.339, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tremare,", "start": 319.339, "end": 319.739, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 319.739, "end": 319.78, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 319.78, "end": 319.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 319.859, "end": 319.88, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "cosa", "start": 319.88, "end": 320.019, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 320.019, "end": 320.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "hai", "start": 320.019, "end": 320.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 320.119, "end": 320.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "paura?", "start": 320.139, "end": 320.519, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 320.519, "end": 321.5, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Starai", "start": 321.5, "end": 321.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 321.779, "end": 321.78, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "mare", "start": 321.78, "end": 321.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 321.959, "end": 321.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "qualche", "start": 321.979, "end": 322.2, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 322.2, "end": 322.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 322.239, "end": 322.679, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 322.679, "end": 322.679, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "stavolta.", "start": 322.679, "end": 322.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}], "language_detected": null, "confidence": null, "processing_time": 18.339267253875732, "metadata": {"service": "elevenlabs", "mode": "free", "model": "scribe_v1", "raw_result": {"language_code": "ita", "language_probability": 0.9950636625289917, "text": "Io andai a Palermo, <PERSON><PERSON> si ammazzò e poi scoppiò l'inferno. Come se tutto fosse legato alla morte di quella ragazzetta che quando era viva pareva non ci fosse nemmeno fra noi. <PERSON>enite al sodo, l'ordine dei fatti devo metterlo io e non voi. Chi vi informò di quello che vostra moglie aveva fatto mentre voi eravate a Palermo? La stessa sera del funerale di Carmela, ora che mi ci fate pensare. È proprio strano. Abula era più ubriaco del solito. Lo spirito di male si è fermato in questa isola. Un innocente ha pagato per i peccati di tutti. Un innocente aver salvo scusa. È ubriaco, fede. <PERSON><PERSON>, è ubriaco, ma dice la verità. La colpa è stata tutta di quella mala femmina. Carmela è morta e io l'ho veduta corre verso la morte. Perché non l'ho fermata? Tu. Tu hai colpa di tutto. Il demonio è nel tuo animo. Tu sei... Le ho davvero ditorno. Io l'ho vista che correva piangendo e ho visto Francesco che baciarla per la cagna. Tua moglie ti ha mandato a Palermo con Maria perché voleva restare sola con Rosario e con tutti gli uomini dell'isola. Ma anche tu. Tu puoi farmi tacere, ma non puoi cancellare la verità. Sentivo come un fuoco dentro il petto, qualche cosa che non avevo provato mai. Gli avevo dato tutto. L'avevo sposata. L'avevo portata nella mia casa, accanto a mia figlia. E buttai a correre per raggiungerla. Quando l'incendio si spense era l'alba, ormai. Li trovamo tutti e due carbonizzati. Li portarono subito a Palermo. Credo che li abbiano sepolte laggiù. Non so più nulla. Non mi interessa. C'è una cosa che potrà interessarvi ancora, don Pietro Inghersia. Vostra moglie l'ucisa con una revolverata in testa. Proiettile calibro nove. Era morta prima che la baracca saltasse. E il negro non aveva armi. Rosario. Rosario. Potevate risparmiarvi di venire fin qui. Tanto da me non cavate niente. Non sono tipo da sparare in testa alle donne, io. Questo devo stabilirlo io. E vedi di calmare i bollenti spiriti, Rosario. O forse soltanto perché stai per sposare la figlia di Ingarsia, ti senti già padrone qui. Padroni non ce ne saranno più qui, e nemmeno padrone. Mia sorella e il mio miglior amico ci hanno lasciato la vita in questo affare. Perciò non dovete credere che mi faccia piacere ricordare. Stavo chiacchierando con Abulapoca quella mattina. Tu ritieni. Partiremo non appena ha attraccato il postale. Aiutatemi a levare la palanca. Guarda Rosario, è là come sembra. Già. E Carmela è con lei. Inseparabili, non c'è che dire. Dai su, molla. Tuo padre si fermerà a lungo stavolta? Non lo so, non parla mai dei suoi affari in casa. Però quando le barche prendono il mare e Rosario è alla pesca, io sono tranquilla. Anche se è lontano. Quando mio padre e tuo fratello sono a terra, invece, può sempre succedere qualcosa. Ma Rosario difende i pescatori. Ma ciascuno difende se stesso, dice mio padre. Ciao Carmela. Buongiorno signorina Maria. Quando il porto salvo è partito ed ho finito con i passeggeri del postale, fatti trovare al solito posto. Attento, Rosario ci guarda. E smettila di tremare, di cosa hai paura? Starai mare qualche settimana stavolta.", "words": [{"text": "Io", "start": 4.519, "end": 4.679, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 4.679, "end": 4.679, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "andai", "start": 4.679, "end": 4.94, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 4.94, "end": 4.94, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 4.94, "end": 5.019, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 5.019, "end": 5.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Palermo,", "start": 5.019, "end": 5.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 5.559, "end": 5.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Carmel<PERSON>", "start": 5.559, "end": 6.059, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 6.059, "end": 6.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "si", "start": 6.059, "end": 6.179, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 6.179, "end": 6.199, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ammazzò", "start": 6.199, "end": 6.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 6.619, "end": 7.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "e", "start": 7.019, "end": 7.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 7.139, "end": 7.199, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "poi", "start": 7.199, "end": 7.499, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 7.499, "end": 7.519, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "scopp<PERSON><PERSON>", "start": 7.519, "end": 7.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 7.899, "end": 7.899, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "l'inferno.", "start": 7.899, "end": 8.56, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 8.56, "end": 9.039, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Come", "start": 9.039, "end": 9.28, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 9.28, "end": 9.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "se", "start": 9.319, "end": 9.42, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 9.42, "end": 9.46, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tutto", "start": 9.46, "end": 9.679, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 9.679, "end": 9.679, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "fosse", "start": 9.679, "end": 9.94, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 9.94, "end": 9.96, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "legato", "start": 9.96, "end": 10.279, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 10.279, "end": 10.3, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "alla", "start": 10.3, "end": 10.44, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 10.44, "end": 10.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "morte", "start": 10.479, "end": 10.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 10.799, "end": 10.8, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 10.8, "end": 10.92, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 10.92, "end": 10.939, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "quella", "start": 10.939, "end": 11.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 11.159, "end": 11.179, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ragazzetta", "start": 11.179, "end": 11.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 11.779, "end": 12.079, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 12.079, "end": 12.22, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 12.22, "end": 12.22, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "quando", "start": 12.22, "end": 12.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 12.559, "end": 12.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "era", "start": 12.599, "end": 12.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 12.819, "end": 12.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "viva", "start": 12.819, "end": 13.14, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 13.14, "end": 13.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "pareva", "start": 13.659, "end": 14.039, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 14.039, "end": 14.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "non", "start": 14.059, "end": 14.179, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 14.179, "end": 14.179, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ci", "start": 14.179, "end": 14.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 14.319, "end": 14.34, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "fosse", "start": 14.34, "end": 14.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 14.579, "end": 14.579, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 14.579, "end": 14.96, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 14.96, "end": 14.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "fra", "start": 14.979, "end": 15.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 15.139, "end": 15.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "noi.", "start": 15.159, "end": 15.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 15.459, "end": 15.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Venite", "start": 15.659, "end": 15.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 15.979, "end": 16.0, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "al", "start": 16.0, "end": 16.119, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 16.119, "end": 16.18, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sodo,", "start": 16.18, "end": 16.62, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 16.62, "end": 16.719, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "l'ordine", "start": 16.719, "end": 17.119, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 17.119, "end": 17.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "dei", "start": 17.119, "end": 17.259, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 17.259, "end": 17.26, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "fatti", "start": 17.26, "end": 17.459, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 17.459, "end": 17.459, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "devo", "start": 17.459, "end": 17.639, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 17.639, "end": 17.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "metterlo", "start": 17.639, "end": 17.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 17.979, "end": 17.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "io", "start": 17.979, "end": 18.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 18.159, "end": 18.18, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "e", "start": 18.18, "end": 18.28, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 18.28, "end": 18.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "non", "start": 18.299, "end": 18.439, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 18.439, "end": 18.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "voi.", "start": 18.479, "end": 18.819, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 18.819, "end": 19.199, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>", "start": 19.199, "end": 19.319, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 19.319, "end": 19.359, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "vi", "start": 19.359, "end": 19.439, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 19.439, "end": 19.439, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "informò", "start": 19.439, "end": 19.899, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 19.899, "end": 19.899, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 19.899, "end": 20.019, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 20.019, "end": 20.02, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "quello", "start": 20.02, "end": 20.219, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 20.219, "end": 20.219, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "che", "start": 20.219, "end": 20.319, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 20.319, "end": 20.319, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "vostra", "start": 20.319, "end": 20.599, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 20.599, "end": 20.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "moglie", "start": 20.639, "end": 20.86, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 20.86, "end": 20.879, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "aveva", "start": 20.879, "end": 21.119, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 21.119, "end": 21.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "fatto", "start": 21.119, "end": 21.399, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 21.399, "end": 21.42, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mentre", "start": 21.42, "end": 21.679, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 21.679, "end": 21.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "voi", "start": 21.739, "end": 21.879, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 21.879, "end": 21.92, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "eravate", "start": 21.92, "end": 22.279, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 22.279, "end": 22.319, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "a", "start": 22.319, "end": 22.399, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 22.399, "end": 22.439, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Palermo?", "start": 22.439, "end": 22.879, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 22.879, "end": 23.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "La", "start": 23.059, "end": 23.259, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 23.259, "end": 23.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "stessa", "start": 23.299, "end": 23.599, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 23.599, "end": 23.639, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sera", "start": 23.639, "end": 23.879, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 23.879, "end": 23.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "del", "start": 23.879, "end": 24.039, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 24.039, "end": 24.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "funerale", "start": 24.059, "end": 24.5, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 24.5, "end": 24.5, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 24.5, "end": 24.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 24.619, "end": 24.639, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Carmela,", "start": 24.639, "end": 25.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 25.239, "end": 25.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ora", "start": 25.459, "end": 25.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 25.699, "end": 25.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 25.699, "end": 25.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 25.819, "end": 25.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "mi", "start": 25.819, "end": 25.939, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 25.939, "end": 25.939, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ci", "start": 25.939, "end": 26.099, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 26.099, "end": 26.099, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "fate", "start": 26.099, "end": 26.36, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 26.36, "end": 26.379, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "pensare.", "start": 26.379, "end": 26.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 26.979, "end": 28.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "È", "start": 28.059, "end": 28.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 28.219, "end": 28.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "proprio", "start": 28.219, "end": 28.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 28.619, "end": 28.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "strano.", "start": 28.619, "end": 29.079, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 29.079, "end": 29.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Abula", "start": 29.659, "end": 30.039, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 30.039, "end": 30.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "era", "start": 30.059, "end": 30.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 30.199, "end": 30.199, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "più", "start": 30.199, "end": 30.38, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 30.38, "end": 30.399, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ubriaco", "start": 30.399, "end": 30.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 30.819, "end": 30.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "del", "start": 30.819, "end": 30.96, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 30.96, "end": 31.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "solito.", "start": 31.019, "end": 31.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 31.479, "end": 34.34, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Lo", "start": 34.34, "end": 34.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 34.619, "end": 34.7, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "spirito", "start": 34.7, "end": 35.679, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 35.679, "end": 35.7, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 35.7, "end": 35.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 35.779, "end": 35.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "male", "start": 35.819, "end": 36.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 36.159, "end": 36.36, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "si", "start": 36.36, "end": 36.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 36.479, "end": 36.5, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "è", "start": 36.5, "end": 36.599, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 36.599, "end": 36.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "fermato", "start": 36.619, "end": 37.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 37.139, "end": 37.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "in", "start": 37.139, "end": 37.279, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 37.279, "end": 37.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "questa", "start": 37.299, "end": 37.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 37.659, "end": 37.719, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "isola.", "start": 37.719, "end": 38.179, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 38.179, "end": 40.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Un", "start": 40.919, "end": 41.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 41.119, "end": 41.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "innocente", "start": 41.139, "end": 41.88, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 41.88, "end": 41.899, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ha", "start": 41.899, "end": 42.019, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 42.019, "end": 42.02, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "pagato", "start": 42.02, "end": 42.5, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 42.5, "end": 42.7, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "per", "start": 42.7, "end": 42.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 42.899, "end": 42.899, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "i", "start": 42.899, "end": 42.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 42.979, "end": 42.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "pec<PERSON>i", "start": 42.979, "end": 43.6, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 43.6, "end": 43.639, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 43.639, "end": 43.76, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 43.76, "end": 43.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tutti.", "start": 43.819, "end": 44.419, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 44.419, "end": 45.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Un", "start": 45.659, "end": 45.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 45.819, "end": 45.84, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "innocente", "start": 45.84, "end": 46.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 46.459, "end": 46.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "aver", "start": 46.479, "end": 46.72, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 46.72, "end": 46.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "salvo", "start": 46.779, "end": 47.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 47.119, "end": 47.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "scusa.", "start": 47.319, "end": 47.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 47.799, "end": 47.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "È", "start": 47.799, "end": 47.819, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 47.819, "end": 47.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ubriaco,", "start": 47.819, "end": 48.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 48.139, "end": 48.139, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "fede.", "start": 48.139, "end": 48.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 48.159, "end": 48.159, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Sì,", "start": 48.159, "end": 48.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 48.159, "end": 48.159, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "è", "start": 48.159, "end": 48.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 48.159, "end": 48.159, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ubriaco,", "start": 48.159, "end": 48.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 48.159, "end": 48.159, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ma", "start": 48.159, "end": 48.259, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 48.259, "end": 48.259, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "dice", "start": 48.259, "end": 48.439, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 48.439, "end": 48.439, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "la", "start": 48.439, "end": 48.52, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 48.52, "end": 48.539, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "verità.", "start": 48.539, "end": 48.919, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 48.919, "end": 49.18, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "La", "start": 49.18, "end": 49.52, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 49.52, "end": 49.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "colpa", "start": 49.539, "end": 49.74, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 49.74, "end": 49.759, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "è", "start": 49.759, "end": 49.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 49.799, "end": 49.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "stata", "start": 49.799, "end": 49.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 49.979, "end": 49.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tutta", "start": 49.979, "end": 50.2, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 50.2, "end": 50.2, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 50.2, "end": 50.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 50.299, "end": 50.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "quella", "start": 50.299, "end": 50.5, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 50.5, "end": 50.639, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "mala", "start": 50.639, "end": 50.86, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 50.86, "end": 50.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "femmina.", "start": 50.919, "end": 51.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 51.479, "end": 57.36, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Carmel<PERSON>", "start": 57.36, "end": 58.04, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 58.04, "end": 58.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "è", "start": 58.119, "end": 58.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 58.139, "end": 58.279, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "morta", "start": 58.279, "end": 58.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 58.859, "end": 58.899, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "e", "start": 58.899, "end": 58.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 58.919, "end": 59.0, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "io", "start": 59.0, "end": 59.18, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 59.18, "end": 59.199, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "l'ho", "start": 59.199, "end": 59.399, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 59.399, "end": 59.399, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "veduta", "start": 59.399, "end": 59.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 59.919, "end": 59.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "corre", "start": 59.979, "end": 60.28, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 60.28, "end": 60.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "verso", "start": 60.319, "end": 60.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 60.859, "end": 60.859, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "la", "start": 60.859, "end": 61.04, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 61.04, "end": 61.079, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "morte.", "start": 61.079, "end": 62.179, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 62.179, "end": 62.999, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 62.999, "end": 63.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 63.579, "end": 63.579, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "non", "start": 63.579, "end": 63.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 63.859, "end": 63.859, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "l'ho", "start": 63.859, "end": 64.06, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 64.06, "end": 64.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "fermata?", "start": 64.119, "end": 64.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 64.779, "end": 65.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Tu.", "start": 65.739, "end": 66.12, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 66.12, "end": 67.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Tu", "start": 67.599, "end": 67.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 67.779, "end": 67.839, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "hai", "start": 67.839, "end": 67.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 67.959, "end": 67.98, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "colpa", "start": 67.98, "end": 68.32, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 68.32, "end": 68.32, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 68.32, "end": 68.44, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 68.44, "end": 68.499, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tutto.", "start": 68.499, "end": 68.96, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 68.96, "end": 70.06, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Il", "start": 70.06, "end": 70.279, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 70.279, "end": 70.32, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "demonio", "start": 70.32, "end": 70.9, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 70.9, "end": 70.9, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "è", "start": 70.9, "end": 71.02, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 71.02, "end": 71.02, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "nel", "start": 71.02, "end": 71.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 71.219, "end": 71.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tuo", "start": 71.319, "end": 71.5, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 71.5, "end": 71.52, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "animo.", "start": 71.52, "end": 72.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 72.159, "end": 72.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Tu", "start": 72.159, "end": 72.32, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 72.32, "end": 72.379, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sei...", "start": 72.379, "end": 72.679, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 72.679, "end": 72.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Le", "start": 72.699, "end": 72.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 72.799, "end": 72.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ho", "start": 72.879, "end": 73.099, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 73.099, "end": 73.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "da<PERSON><PERSON><PERSON>", "start": 73.119, "end": 73.419, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 73.419, "end": 73.44, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ditorno.", "start": 73.44, "end": 74.0, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 74.0, "end": 74.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Io", "start": 74.919, "end": 75.02, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 75.02, "end": 75.079, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "l'ho", "start": 75.079, "end": 75.179, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 75.179, "end": 75.179, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "vista", "start": 75.179, "end": 75.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 75.459, "end": 75.48, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 75.48, "end": 75.56, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 75.56, "end": 75.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "correva", "start": 75.619, "end": 76.099, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 76.099, "end": 76.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 76.119, "end": 76.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 76.699, "end": 76.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "e", "start": 76.919, "end": 76.98, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 76.98, "end": 76.98, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ho", "start": 76.98, "end": 77.08, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 77.08, "end": 77.099, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "visto", "start": 77.099, "end": 77.339, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 77.339, "end": 77.359, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>", "start": 77.359, "end": 77.939, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 77.939, "end": 77.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 77.959, "end": 78.08, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 78.08, "end": 78.08, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "bac<PERSON><PERSON>", "start": 78.08, "end": 78.419, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 78.419, "end": 78.439, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "per", "start": 78.439, "end": 78.56, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 78.56, "end": 78.56, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "la", "start": 78.56, "end": 78.639, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 78.639, "end": 78.66, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "cagna.", "start": 78.66, "end": 78.9, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 78.9, "end": 78.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 78.959, "end": 79.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 79.139, "end": 79.179, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "moglie", "start": 79.179, "end": 79.56, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 79.56, "end": 80.439, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ti", "start": 80.439, "end": 80.719, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 80.719, "end": 80.719, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ha", "start": 80.719, "end": 80.839, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 80.839, "end": 80.839, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "mandato", "start": 80.839, "end": 81.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 81.319, "end": 81.32, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 81.32, "end": 81.44, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 81.44, "end": 81.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Palermo", "start": 81.459, "end": 82.019, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 82.019, "end": 82.079, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "con", "start": 82.079, "end": 82.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 82.239, "end": 82.279, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>", "start": 82.279, "end": 82.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 82.619, "end": 82.679, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "perché", "start": 82.679, "end": 82.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 82.979, "end": 82.98, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>eva", "start": 82.98, "end": 83.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 83.299, "end": 83.32, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "restare", "start": 83.32, "end": 83.74, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 83.74, "end": 83.759, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sola", "start": 83.759, "end": 84.039, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 84.039, "end": 84.279, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "con", "start": 84.279, "end": 84.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 84.459, "end": 84.499, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Rosario", "start": 84.499, "end": 84.98, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 84.98, "end": 84.98, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "e", "start": 84.98, "end": 85.08, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 85.08, "end": 85.099, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "con", "start": 85.099, "end": 85.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 85.219, "end": 85.279, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tutti", "start": 85.279, "end": 85.48, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 85.48, "end": 85.519, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "gli", "start": 85.519, "end": 85.639, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 85.639, "end": 85.639, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 85.639, "end": 86.039, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 86.039, "end": 86.039, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "dell'isola.", "start": 86.039, "end": 86.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 86.759, "end": 87.279, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Ma", "start": 87.279, "end": 87.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 87.459, "end": 87.48, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "anche", "start": 87.48, "end": 87.679, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 87.679, "end": 87.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tu.", "start": 87.799, "end": 88.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 88.219, "end": 92.179, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Tu", "start": 92.179, "end": 92.359, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 92.359, "end": 92.42, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "puoi", "start": 92.42, "end": 92.64, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 92.64, "end": 92.679, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "farmi", "start": 92.679, "end": 93.06, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 93.06, "end": 93.08, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tacere,", "start": 93.08, "end": 93.66, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 93.66, "end": 94.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ma", "start": 94.019, "end": 94.14, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 94.14, "end": 94.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "non", "start": 94.159, "end": 94.28, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 94.28, "end": 94.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "puoi", "start": 94.319, "end": 94.48, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 94.48, "end": 94.519, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "cancellare", "start": 94.519, "end": 95.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 95.219, "end": 95.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "la", "start": 95.219, "end": 95.34, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 95.34, "end": 95.359, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "verità.", "start": 95.359, "end": 96.059, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 96.059, "end": 103.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Sentivo", "start": 103.459, "end": 103.92, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 103.92, "end": 103.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "come", "start": 103.979, "end": 104.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 104.199, "end": 104.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "un", "start": 104.219, "end": 104.359, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 104.359, "end": 104.379, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "fuoco", "start": 104.379, "end": 104.719, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 104.719, "end": 104.74, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "dentro", "start": 104.74, "end": 105.039, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 105.039, "end": 105.06, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "il", "start": 105.06, "end": 105.14, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 105.14, "end": 105.179, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "petto,", "start": 105.179, "end": 105.639, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 105.639, "end": 106.339, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "qualche", "start": 106.339, "end": 106.719, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 106.719, "end": 106.74, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "cosa", "start": 106.74, "end": 106.999, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 106.999, "end": 107.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 107.019, "end": 107.16, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 107.16, "end": 107.199, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "non", "start": 107.199, "end": 107.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 107.319, "end": 107.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "avevo", "start": 107.319, "end": 107.62, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 107.62, "end": 107.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "provato", "start": 107.659, "end": 108.06, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 108.06, "end": 108.08, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "mai.", "start": 108.08, "end": 108.419, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 108.419, "end": 113.42, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 113.42, "end": 113.56, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 113.56, "end": 113.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "avevo", "start": 113.599, "end": 113.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 113.859, "end": 113.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "dato", "start": 113.879, "end": 114.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 114.119, "end": 114.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tutto.", "start": 114.119, "end": 114.499, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 114.499, "end": 115.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "L'avevo", "start": 115.299, "end": 115.66, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 115.66, "end": 115.679, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sposata.", "start": 115.679, "end": 116.419, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 116.419, "end": 117.24, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "L'avevo", "start": 117.24, "end": 117.58, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 117.58, "end": 117.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "portata", "start": 117.619, "end": 118.08, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 118.08, "end": 118.099, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "nella", "start": 118.099, "end": 118.32, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 118.32, "end": 118.359, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "mia", "start": 118.359, "end": 118.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 118.559, "end": 118.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "casa,", "start": 118.599, "end": 119.059, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 119.059, "end": 120.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "accanto", "start": 120.539, "end": 120.999, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 120.999, "end": 120.999, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 120.999, "end": 121.06, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 121.06, "end": 121.06, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "mia", "start": 121.06, "end": 121.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 121.219, "end": 121.24, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "figlia.", "start": 121.24, "end": 121.66, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 121.66, "end": 129.339, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "E", "start": 129.339, "end": 129.439, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 129.439, "end": 129.439, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "buttai", "start": 129.439, "end": 129.78, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 129.78, "end": 129.78, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 129.78, "end": 129.839, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 129.839, "end": 129.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "co<PERSON>e", "start": 129.879, "end": 130.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 130.199, "end": 130.199, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "per", "start": 130.199, "end": 130.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 130.319, "end": 130.359, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "rag<PERSON><PERSON><PERSON><PERSON>.", "start": 130.359, "end": 131.079, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 131.079, "end": 140.679, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Quando", "start": 140.679, "end": 140.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 140.979, "end": 140.999, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "l'incendio", "start": 140.999, "end": 141.46, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 141.46, "end": 141.46, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "si", "start": 141.46, "end": 141.539, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 141.539, "end": 141.579, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "spense", "start": 141.579, "end": 141.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 141.959, "end": 141.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "era", "start": 141.979, "end": 142.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 142.119, "end": 142.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "l'alba,", "start": 142.119, "end": 142.499, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 142.499, "end": 142.499, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ormai.", "start": 142.499, "end": 143.06, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 143.06, "end": 143.519, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Li", "start": 143.519, "end": 143.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 143.659, "end": 143.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "trovamo", "start": 143.659, "end": 144.019, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 144.019, "end": 144.039, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tutti", "start": 144.039, "end": 144.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 144.219, "end": 144.24, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "e", "start": 144.24, "end": 144.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 144.319, "end": 144.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "due", "start": 144.319, "end": 144.519, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 144.519, "end": 144.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>.", "start": 144.539, "end": 145.359, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 145.359, "end": 146.259, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Li", "start": 146.259, "end": 146.399, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 146.399, "end": 146.399, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "portarono", "start": 146.399, "end": 146.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 146.899, "end": 146.92, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "subito", "start": 146.92, "end": 147.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 147.299, "end": 147.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 147.319, "end": 147.419, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 147.419, "end": 147.439, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Palermo.", "start": 147.439, "end": 147.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 147.979, "end": 148.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 148.879, "end": 149.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 149.239, "end": 149.259, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 149.259, "end": 149.359, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 149.359, "end": 149.359, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "li", "start": 149.359, "end": 149.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 149.479, "end": 149.499, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 149.499, "end": 149.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 149.759, "end": 149.78, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sepolte", "start": 149.78, "end": 150.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 150.219, "end": 150.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "laggi<PERSON>.", "start": 150.219, "end": 150.739, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 150.739, "end": 151.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Non", "start": 151.659, "end": 151.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 151.859, "end": 151.899, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "so", "start": 151.899, "end": 152.04, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 152.04, "end": 152.06, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "più", "start": 152.06, "end": 152.179, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 152.179, "end": 152.179, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "nulla.", "start": 152.179, "end": 152.519, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 152.519, "end": 153.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Non", "start": 153.159, "end": 153.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 153.299, "end": 153.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "mi", "start": 153.319, "end": 153.379, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 153.379, "end": 153.379, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "interessa.", "start": 153.379, "end": 153.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 153.859, "end": 153.859, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "C'è", "start": 153.859, "end": 154.379, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 154.379, "end": 154.399, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "una", "start": 154.399, "end": 154.639, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 154.639, "end": 154.659, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "cosa", "start": 154.659, "end": 154.899, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 154.899, "end": 154.939, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "che", "start": 154.939, "end": 155.019, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 155.019, "end": 155.039, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "potrà", "start": 155.039, "end": 155.399, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 155.399, "end": 155.399, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "interessar<PERSON>", "start": 155.399, "end": 155.999, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 155.999, "end": 155.999, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ancora,", "start": 155.999, "end": 156.379, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 156.379, "end": 156.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "don", "start": 156.379, "end": 156.539, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 156.539, "end": 156.579, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>", "start": 156.579, "end": 156.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 156.799, "end": 156.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Inghersia.", "start": 156.819, "end": 157.539, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 157.539, "end": 158.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Vostra", "start": 158.379, "end": 158.819, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 158.819, "end": 158.899, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "moglie", "start": 158.899, "end": 159.4, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 159.4, "end": 159.839, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "l'ucisa", "start": 159.839, "end": 160.499, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 160.499, "end": 160.539, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "con", "start": 160.539, "end": 160.699, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 160.699, "end": 160.719, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "una", "start": 160.719, "end": 160.86, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 160.86, "end": 160.899, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "revolverata", "start": 160.899, "end": 161.519, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 161.519, "end": 161.579, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "in", "start": 161.579, "end": 161.679, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 161.679, "end": 161.759, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "testa.", "start": 161.759, "end": 162.219, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 162.219, "end": 163.339, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Proiet<PERSON>", "start": 163.339, "end": 164.019, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 164.019, "end": 164.079, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "calibro", "start": 164.079, "end": 164.579, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 164.579, "end": 164.619, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "nove.", "start": 164.619, "end": 165.079, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 165.079, "end": 165.839, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Era", "start": 165.839, "end": 166.06, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 166.06, "end": 166.079, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "morta", "start": 166.079, "end": 166.459, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 166.459, "end": 166.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "prima", "start": 166.479, "end": 166.719, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 166.719, "end": 166.74, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "che", "start": 166.74, "end": 166.819, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 166.819, "end": 166.839, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "la", "start": 166.839, "end": 166.939, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 166.939, "end": 166.959, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "baracca", "start": 166.959, "end": 167.319, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 167.319, "end": 167.339, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "saltasse.", "start": 167.339, "end": 168.099, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 168.099, "end": 168.699, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "E", "start": 168.699, "end": 168.86, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 168.86, "end": 168.899, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "il", "start": 168.899, "end": 169.039, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 169.039, "end": 169.139, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "negro", "start": 169.139, "end": 169.599, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 169.599, "end": 169.719, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "non", "start": 169.719, "end": 169.879, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 169.879, "end": 169.92, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "aveva", "start": 169.92, "end": 170.319, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 170.319, "end": 170.359, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "armi.", "start": 170.359, "end": 170.859, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 170.859, "end": 185.279, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Rosario.", "start": 185.279, "end": 187.599, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 187.599, "end": 190.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Rosario.", "start": 190.539, "end": 191.24, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 191.24, "end": 217.079, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Potevate", "start": 217.079, "end": 217.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 217.859, "end": 217.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ris<PERSON><PERSON><PERSON><PERSON>", "start": 217.879, "end": 218.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 218.459, "end": 218.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 218.459, "end": 218.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 218.559, "end": 218.579, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "venire", "start": 218.579, "end": 218.86, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 218.86, "end": 218.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "fin", "start": 218.879, "end": 219.019, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 219.019, "end": 219.039, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "qui.", "start": 219.039, "end": 219.259, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 219.259, "end": 219.579, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 219.579, "end": 219.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 219.799, "end": 219.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "da", "start": 219.819, "end": 219.92, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 219.92, "end": 219.92, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "me", "start": 219.92, "end": 220.019, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 220.019, "end": 220.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "non", "start": 220.019, "end": 220.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 220.139, "end": 220.14, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "cavate", "start": 220.14, "end": 220.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 220.479, "end": 220.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "niente.", "start": 220.479, "end": 220.88, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 220.88, "end": 221.56, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Non", "start": 221.56, "end": 221.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 221.699, "end": 221.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sono", "start": 221.739, "end": 221.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 221.919, "end": 221.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tipo", "start": 221.959, "end": 222.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 222.159, "end": 222.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "da", "start": 222.159, "end": 222.259, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 222.259, "end": 222.259, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sparare", "start": 222.259, "end": 222.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 222.579, "end": 222.579, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "in", "start": 222.579, "end": 222.679, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 222.679, "end": 222.679, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "testa", "start": 222.679, "end": 222.92, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 222.92, "end": 222.92, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "alle", "start": 222.92, "end": 223.02, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 223.02, "end": 223.06, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "donne,", "start": 223.06, "end": 223.36, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 223.36, "end": 223.36, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "io.", "start": 223.36, "end": 223.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 223.579, "end": 224.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 224.739, "end": 224.999, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 224.999, "end": 225.039, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "devo", "start": 225.039, "end": 225.22, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 225.22, "end": 225.22, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "stabilirlo", "start": 225.22, "end": 225.8, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 225.8, "end": 225.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "io.", "start": 225.819, "end": 226.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 226.139, "end": 226.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "E", "start": 226.799, "end": 226.899, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 226.899, "end": 226.899, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "vedi", "start": 226.899, "end": 227.079, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 227.079, "end": 227.079, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 227.079, "end": 227.179, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 227.179, "end": 227.179, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "calmare", "start": 227.179, "end": 227.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 227.479, "end": 227.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "i", "start": 227.479, "end": 227.56, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 227.56, "end": 227.56, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "boll<PERSON>i", "start": 227.56, "end": 227.939, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 227.939, "end": 228.0, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "spiriti,", "start": 228.0, "end": 228.519, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 228.519, "end": 228.519, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Rosario.", "start": 228.519, "end": 229.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 229.139, "end": 229.619, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "O", "start": 229.619, "end": 229.72, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 229.72, "end": 229.759, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "forse", "start": 229.759, "end": 230.019, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 230.019, "end": 230.039, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "soltanto", "start": 230.039, "end": 230.44, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 230.44, "end": 230.459, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "perché", "start": 230.459, "end": 230.719, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 230.719, "end": 230.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "stai", "start": 230.739, "end": 230.94, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 230.94, "end": 230.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "per", "start": 230.979, "end": 231.119, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 231.119, "end": 231.14, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sposare", "start": 231.14, "end": 231.539, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 231.539, "end": 231.539, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "la", "start": 231.539, "end": 231.639, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 231.639, "end": 231.679, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "figlia", "start": 231.679, "end": 231.879, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 231.879, "end": 231.879, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 231.879, "end": 231.94, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 231.94, "end": 231.959, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Ingarsia,", "start": 231.959, "end": 232.539, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 232.539, "end": 232.539, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ti", "start": 232.539, "end": 232.639, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 232.639, "end": 232.64, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "senti", "start": 232.64, "end": 232.92, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 232.92, "end": 232.92, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "già", "start": 232.92, "end": 233.039, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 233.039, "end": 233.099, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "padrone", "start": 233.099, "end": 233.639, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 233.639, "end": 233.699, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "qui.", "start": 233.699, "end": 233.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 233.979, "end": 234.239, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 234.239, "end": 234.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 234.659, "end": 234.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "non", "start": 234.659, "end": 234.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 234.779, "end": 234.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ce", "start": 234.799, "end": 234.879, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 234.879, "end": 234.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ne", "start": 234.879, "end": 234.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 234.959, "end": 234.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "saranno", "start": 234.959, "end": 235.28, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 235.28, "end": 235.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "più", "start": 235.319, "end": 235.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 235.479, "end": 235.5, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "qui,", "start": 235.5, "end": 235.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 235.699, "end": 235.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "e", "start": 235.699, "end": 235.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 235.759, "end": 235.759, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 235.759, "end": 236.079, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 236.079, "end": 236.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "padrone.", "start": 236.119, "end": 236.66, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 236.66, "end": 237.42, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>", "start": 237.42, "end": 237.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 237.579, "end": 237.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sorella", "start": 237.619, "end": 238.0, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 238.0, "end": 238.0, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "e", "start": 238.0, "end": 238.079, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 238.079, "end": 238.079, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "il", "start": 238.079, "end": 238.179, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 238.179, "end": 238.179, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "mio", "start": 238.179, "end": 238.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 238.299, "end": 238.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "miglior", "start": 238.319, "end": 238.539, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 238.539, "end": 238.56, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "amico", "start": 238.56, "end": 238.839, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 238.839, "end": 238.86, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ci", "start": 238.86, "end": 238.939, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 238.939, "end": 238.939, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "hanno", "start": 238.939, "end": 239.079, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 239.079, "end": 239.099, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "lasciato", "start": 239.099, "end": 239.399, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 239.399, "end": 239.42, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "la", "start": 239.42, "end": 239.5, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 239.5, "end": 239.519, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "vita", "start": 239.519, "end": 239.719, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 239.719, "end": 239.719, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "in", "start": 239.719, "end": 239.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 239.799, "end": 239.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "questo", "start": 239.819, "end": 239.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 239.979, "end": 239.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "affare.", "start": 239.979, "end": 240.419, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 240.419, "end": 241.399, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 241.399, "end": 241.679, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 241.679, "end": 241.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "non", "start": 241.699, "end": 241.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 241.799, "end": 241.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "dovete", "start": 241.819, "end": 242.06, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 242.06, "end": 242.06, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "credere", "start": 242.06, "end": 242.359, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 242.359, "end": 242.36, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 242.36, "end": 242.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 242.459, "end": 242.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "mi", "start": 242.459, "end": 242.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 242.579, "end": 242.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "faccia", "start": 242.599, "end": 242.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 242.779, "end": 242.78, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "piacere", "start": 242.78, "end": 243.14, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 243.14, "end": 243.14, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ricordare.", "start": 243.14, "end": 243.719, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 243.719, "end": 248.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Stavo", "start": 248.879, "end": 249.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 249.159, "end": 249.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "chiac<PERSON><PERSON><PERSON>", "start": 249.219, "end": 249.719, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 249.719, "end": 249.759, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "con", "start": 249.759, "end": 249.88, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 249.88, "end": 249.939, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Abulapoca", "start": 249.939, "end": 250.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 250.699, "end": 250.839, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "quella", "start": 250.839, "end": 251.079, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 251.079, "end": 251.079, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "mattina.", "start": 251.079, "end": 251.839, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 251.839, "end": 252.839, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Tu", "start": 252.839, "end": 252.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 252.979, "end": 252.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ritieni.", "start": 252.979, "end": 253.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 253.559, "end": 257.439, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Partiremo", "start": 257.439, "end": 258.039, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 258.039, "end": 258.079, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "non", "start": 258.079, "end": 258.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 258.199, "end": 258.199, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "appena", "start": 258.199, "end": 258.399, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 258.399, "end": 258.399, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ha", "start": 258.399, "end": 258.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 258.459, "end": 258.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "attraccato", "start": 258.459, "end": 258.78, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 258.78, "end": 258.78, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "il", "start": 258.78, "end": 258.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 258.859, "end": 258.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "postale.", "start": 258.879, "end": 259.38, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 259.38, "end": 260.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 260.459, "end": 260.92, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 260.92, "end": 260.92, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 260.92, "end": 260.939, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 260.939, "end": 260.939, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "levare", "start": 260.939, "end": 261.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 261.159, "end": 261.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "la", "start": 261.159, "end": 261.259, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 261.259, "end": 261.28, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "palanca.", "start": 261.28, "end": 261.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 261.759, "end": 267.92, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Guarda", "start": 267.92, "end": 268.28, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 268.28, "end": 268.28, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Rosario,", "start": 268.28, "end": 268.88, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 268.88, "end": 269.06, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "è", "start": 269.06, "end": 269.179, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 269.179, "end": 269.179, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "là", "start": 269.179, "end": 269.339, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 269.339, "end": 269.339, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "come", "start": 269.339, "end": 269.58, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 269.58, "end": 269.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sembra.", "start": 269.619, "end": 269.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 269.979, "end": 270.939, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Già.", "start": 270.939, "end": 271.64, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 271.64, "end": 271.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "E", "start": 271.879, "end": 271.999, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 271.999, "end": 272.06, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Carmel<PERSON>", "start": 272.06, "end": 272.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 272.459, "end": 272.519, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "è", "start": 272.519, "end": 272.539, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 272.539, "end": 272.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "con", "start": 272.539, "end": 272.739, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 272.739, "end": 272.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "lei.", "start": 272.739, "end": 273.179, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 273.179, "end": 273.56, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Inseparabili,", "start": 273.56, "end": 274.399, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 274.399, "end": 274.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "non", "start": 274.539, "end": 274.639, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 274.639, "end": 274.679, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "c'è", "start": 274.679, "end": 274.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 274.819, "end": 274.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 274.819, "end": 274.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 274.959, "end": 274.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "dire.", "start": 274.959, "end": 275.399, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 275.399, "end": 278.899, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Dai", "start": 278.899, "end": 279.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 279.979, "end": 280.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "su,", "start": 280.019, "end": 280.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 280.319, "end": 280.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "molla.", "start": 280.459, "end": 284.499, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 284.499, "end": 286.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 286.699, "end": 286.92, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 286.92, "end": 286.959, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "padre", "start": 286.959, "end": 287.219, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 287.219, "end": 287.219, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "si", "start": 287.219, "end": 287.339, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 287.339, "end": 287.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "fermerà", "start": 287.379, "end": 287.699, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 287.699, "end": 287.699, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "a", "start": 287.699, "end": 287.78, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 287.78, "end": 287.78, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "lungo", "start": 287.78, "end": 288.019, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 288.019, "end": 288.06, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "stavolta?", "start": 288.06, "end": 288.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 288.559, "end": 288.699, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Non", "start": 288.699, "end": 289.099, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 289.099, "end": 289.119, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "lo", "start": 289.119, "end": 289.259, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 289.259, "end": 289.28, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "so,", "start": 289.28, "end": 289.479, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 289.479, "end": 289.519, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "non", "start": 289.519, "end": 289.619, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 289.619, "end": 289.699, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "parla", "start": 289.699, "end": 289.939, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 289.939, "end": 289.959, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "mai", "start": 289.959, "end": 290.14, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 290.14, "end": 290.159, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "dei", "start": 290.159, "end": 290.299, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 290.299, "end": 290.359, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "suoi", "start": 290.359, "end": 290.56, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 290.56, "end": 290.579, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "affari", "start": 290.579, "end": 291.039, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 291.039, "end": 291.039, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "in", "start": 291.039, "end": 291.179, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 291.179, "end": 291.199, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "casa.", "start": 291.199, "end": 291.659, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 291.659, "end": 292.159, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 292.159, "end": 292.42, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 292.42, "end": 292.459, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "quando", "start": 292.459, "end": 292.699, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 292.699, "end": 292.719, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "le", "start": 292.719, "end": 292.819, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 292.819, "end": 292.839, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "barche", "start": 292.839, "end": 293.159, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 293.159, "end": 293.159, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "prendono", "start": 293.159, "end": 293.519, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 293.519, "end": 293.539, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "il", "start": 293.539, "end": 293.599, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 293.599, "end": 293.659, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "mare", "start": 293.659, "end": 293.859, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 293.859, "end": 293.92, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "e", "start": 293.92, "end": 293.94, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 293.94, "end": 293.959, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Rosario", "start": 293.959, "end": 294.359, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 294.359, "end": 294.379, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "è", "start": 294.379, "end": 294.399, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 294.399, "end": 294.42, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "alla", "start": 294.42, "end": 294.539, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 294.539, "end": 294.599, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "pesca,", "start": 294.599, "end": 294.979, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 294.979, "end": 295.099, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "io", "start": 295.099, "end": 295.22, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 295.22, "end": 295.239, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "sono", "start": 295.239, "end": 295.439, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 295.439, "end": 295.499, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "tranquilla.", "start": 295.499, "end": 296.119, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 296.119, "end": 297.06, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 297.06, "end": 297.299, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 297.299, "end": 297.319, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "se", "start": 297.319, "end": 297.419, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 297.419, "end": 297.439, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "è", "start": 297.439, "end": 297.499, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 297.499, "end": 297.499, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "lontano.", "start": 297.499, "end": 298.039, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 298.039, "end": 298.92, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Quando", "start": 298.92, "end": 299.139, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 299.139, "end": 299.159, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "mio", "start": 299.159, "end": 299.279, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 299.279, "end": 299.339, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "padre", "start": 299.339, "end": 299.559, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 299.559, "end": 299.56, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "e", "start": 299.56, "end": 299.64, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 299.64, "end": 299.659, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "tuo", "start": 299.659, "end": 299.759, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 299.759, "end": 299.78, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "fratello", "start": 299.78, "end": 300.159, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 300.159, "end": 300.199, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "sono", "start": 300.199, "end": 300.379, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 300.379, "end": 300.379, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "a", "start": 300.379, "end": 300.499, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 300.499, "end": 300.499, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "terra,", "start": 300.499, "end": 300.759, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 300.759, "end": 300.759, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "invece,", "start": 300.759, "end": 301.199, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 301.199, "end": 301.319, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 301.319, "end": 301.479, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 301.479, "end": 301.499, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "sempre", "start": 301.499, "end": 301.759, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 301.759, "end": 301.78, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "succedere", "start": 301.78, "end": 302.259, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 302.259, "end": 302.259, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "qualcosa.", "start": 302.259, "end": 302.859, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 302.859, "end": 302.999, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Ma", "start": 302.999, "end": 303.199, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 303.199, "end": 303.219, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Rosario", "start": 303.219, "end": 303.639, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 303.639, "end": 303.64, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "difende", "start": 303.64, "end": 303.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 303.979, "end": 303.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "i", "start": 303.979, "end": 304.039, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 304.039, "end": 304.039, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "pescatori.", "start": 304.039, "end": 304.679, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 304.679, "end": 304.679, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Ma", "start": 304.679, "end": 304.819, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 304.819, "end": 304.839, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 304.839, "end": 305.199, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 305.199, "end": 305.199, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "difende", "start": 305.199, "end": 305.499, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 305.499, "end": 305.539, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "se", "start": 305.539, "end": 305.679, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 305.679, "end": 305.699, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "stesso,", "start": 305.699, "end": 306.039, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 306.039, "end": 306.039, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "dice", "start": 306.039, "end": 306.219, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 306.219, "end": 306.259, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "mio", "start": 306.259, "end": 306.379, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 306.379, "end": 306.439, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "padre.", "start": 306.439, "end": 306.579, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 306.579, "end": 306.579, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Ciao", "start": 306.579, "end": 306.8, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 306.8, "end": 306.839, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Carmela.", "start": 306.839, "end": 307.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 307.299, "end": 308.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "start": 308.159, "end": 308.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 308.559, "end": 308.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "signorina", "start": 308.599, "end": 308.939, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 308.939, "end": 308.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>.", "start": 308.959, "end": 309.28, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 309.28, "end": 311.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Quando", "start": 311.479, "end": 311.679, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 311.679, "end": 311.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "il", "start": 311.699, "end": 311.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 311.759, "end": 311.78, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "porto", "start": 311.78, "end": 311.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 311.979, "end": 312.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "salvo", "start": 312.019, "end": 312.259, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 312.259, "end": 312.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "è", "start": 312.299, "end": 312.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 312.319, "end": 312.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "partito", "start": 312.319, "end": 312.679, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 312.679, "end": 312.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ed", "start": 312.699, "end": 312.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 312.779, "end": 312.78, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ho", "start": 312.78, "end": 312.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 312.859, "end": 312.859, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "finito", "start": 312.859, "end": 313.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 313.119, "end": 313.14, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "con", "start": 313.14, "end": 313.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 313.239, "end": 313.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "i", "start": 313.239, "end": 313.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 313.319, "end": 313.339, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 313.339, "end": 313.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 313.759, "end": 313.78, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "del", "start": 313.78, "end": 313.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 313.899, "end": 313.92, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "postale,", "start": 313.92, "end": 314.339, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 314.339, "end": 314.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "fatti", "start": 314.619, "end": 314.86, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 314.86, "end": 314.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "trovare", "start": 314.879, "end": 315.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 315.139, "end": 315.14, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "al", "start": 315.14, "end": 315.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 315.219, "end": 315.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "solito", "start": 315.219, "end": 315.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 315.459, "end": 315.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "posto.", "start": 315.479, "end": 315.66, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 315.66, "end": 315.66, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Attento,", "start": 315.66, "end": 316.359, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 316.359, "end": 316.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Rosario", "start": 316.739, "end": 317.119, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 317.119, "end": 317.179, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ci", "start": 317.179, "end": 317.28, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 317.28, "end": 317.319, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "guarda.", "start": 317.319, "end": 317.82, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 317.82, "end": 318.679, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "E", "start": 318.679, "end": 318.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 318.799, "end": 318.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "smettila", "start": 318.799, "end": 319.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 319.239, "end": 319.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 319.239, "end": 319.339, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 319.339, "end": 319.339, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tremare,", "start": 319.339, "end": 319.739, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 319.739, "end": 319.78, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 319.78, "end": 319.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 319.859, "end": 319.88, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "cosa", "start": 319.88, "end": 320.019, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 320.019, "end": 320.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "hai", "start": 320.019, "end": 320.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 320.119, "end": 320.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "paura?", "start": 320.139, "end": 320.519, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 320.519, "end": 321.5, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Starai", "start": 321.5, "end": 321.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 321.779, "end": 321.78, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "mare", "start": 321.78, "end": 321.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 321.959, "end": 321.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "qualche", "start": 321.979, "end": 322.2, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 322.2, "end": 322.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 322.239, "end": 322.679, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 322.679, "end": 322.679, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "stavolta.", "start": 322.679, "end": 322.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}]}}, "created_at": 1754319534.8739688}