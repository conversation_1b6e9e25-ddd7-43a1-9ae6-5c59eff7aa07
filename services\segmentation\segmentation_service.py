"""
音频分割服务

基于静音检测的智能音频分割功能，支持长音频文件的精确分段处理。

核心功能：
- 智能静音检测和分析
- 动态分割点计算
- 精确音频切割
- 分段文件管理
"""

import os
import subprocess
import math
import time
import re
from dataclasses import dataclass, field
from datetime import timedelta
from typing import List, Optional

# 导入音频处理系统的数据模型
from services.audio.audio_processor import ProcessingResult
from services.audio.logger_mixin import AudioLoggerMixin
from services.audio.exceptions import AudioError


# ============================================================================
# 异常定义
# ============================================================================

class SegmentationError(AudioError):
    """音频分割异常"""
    pass


# ============================================================================
# 数据模型
# ============================================================================

@dataclass
class SegmentationResult:
    """音频分割操作结果数据类

    包含分割操作的完整结果信息，包括成功状态、输出文件、
    分割点信息和处理时间等。
    """
    success: bool = False
    input_path: str = ""
    output_dir: str = ""
    segment_files: List[str] = field(default_factory=list)
    cut_points: List[float] = field(default_factory=list)
    processing_time_seconds: float = 0.0
    error_message: str = ""


# ============================================================================
# 主服务类
# ============================================================================

class AudioSegmentationService(AudioLoggerMixin):
    """音频分割服务 - 基于静音检测的智能音频分段处理

    核心功能：
    - 静音区段检测和分析
    - 动态分割点计算算法
    - 精确音频文件切割
    - 分段结果验证和优化
    """
    
    # 配置参数 - 对应code.py中的配置常量
    INTERVAL = 5 * 60              # 目标分割间隔（秒）
    INITIAL_SEARCH_RADIUS = 60      # 初始搜索半径（秒）
    MAX_SEARCH_RADIUS = 10 * 60     # 最大搜索半径（秒）
    RADIUS_INCREMENT = 60           # 每次扩大搜索半径的增量（秒）
    NOISE_DB = -30                  # 静音检测的音量阈值 (dB)
    MIN_SILENCE_DURATION = 1.0      # 寻找静音的最低时长要求（秒）
    MP3_BITRATE = "320k"            # 输出MP3的比特率 (CBR)
    
    def __init__(self):
        """初始化音频分割服务，设置默认配置参数"""
        super().__init__()
        self._log_info("音频分割服务初始化完成")
    
    def segment_from_processing_result(self, processing_result: ProcessingResult,
                                     output_dir: str) -> SegmentationResult:
        """从音频处理结果执行分割操作

        Args:
            processing_result: 音频处理器的处理结果
            output_dir: 分段文件输出目录

        Returns:
            SegmentationResult: 分割操作结果
        """
        if not processing_result.success:
            raise SegmentationError(
                "音频处理失败，无法进行分割",
                file_path=processing_result.input_path,
                technical_details=processing_result.error_message
            )
        
        if not processing_result.output_path:
            raise SegmentationError(
                "音频处理结果缺少输出文件路径",
                file_path=processing_result.input_path
            )
        
        return self.segment_audio(processing_result.output_path, output_dir)
    
    def segment_audio(self, input_path: str, output_dir: str) -> SegmentationResult:
        """执行音频文件分割的主要处理流程

        处理步骤：
        1. 获取音频时长
        2. 检测静音区段
        3. 计算最优分割点
        4. 执行精确切割
        5. 验证分割结果

        Args:
            input_path: 输入音频文件路径
            output_dir: 分段文件输出目录

        Returns:
            SegmentationResult: 分割操作结果和分段文件信息
        """
        start_time = time.time()
        
        try:
            # 基础验证
            if not os.path.exists(input_path):
                raise SegmentationError(f"输入文件不存在: {input_path}", file_path=input_path)
            
            if not os.path.exists(output_dir):
                os.makedirs(output_dir, exist_ok=True)
            
            self._log_info(f"开始音频分割: {os.path.basename(input_path)} -> {output_dir}")
            
            # 获取音频时长
            duration = self.get_media_duration(input_path)
            if duration is None:
                raise SegmentationError("无法获取音频文件时长", file_path=input_path)
            
            # 静音检测
            all_silences = self.detect_silences(input_path)
            if not all_silences:
                raise SegmentationError("未检测到任何静音，无法进行分割", file_path=input_path)
            
            # 计算切割点 - 修复算法漏洞
            self._log_info("开始计算分割点...")
            final_cut_points = [0]

            # 修复：计算所有可能的间隔点，不限制search_end_point
            target_times = []
            current_target = self.INTERVAL
            while current_target < duration:
                target_times.append(current_target)
                current_target += self.INTERVAL

            self._log_info(f"目标切点时间: {target_times} (总时长: {duration:.2f}s)")

            for target_time in target_times:
                cut_point = self.find_best_cut_point_dynamically(float(target_time), all_silences)
                if cut_point:
                    final_cut_points.append(cut_point)

            final_cut_points.append(duration)
            final_cut_points = sorted(list(set(final_cut_points)))
            final_cut_points = [p for i, p in enumerate(final_cut_points)
                              if i == 0 or p - final_cut_points[i-1] > self.MIN_SILENCE_DURATION]

            # 验证和修复最后分段长度
            final_cut_points = self._validate_and_fix_last_segment(final_cut_points, all_silences, duration)
            
            self._log_info(f"最终确定的分割时间点列表（秒）: {[round(p, 2) for p in final_cut_points]}")
            
            if len(final_cut_points) <= 2:
                raise SegmentationError("未找到有效的分割点，或分割后的片段过短", file_path=input_path)
            
            # 执行分割
            segment_files = self.split_audio_precisely(input_path, final_cut_points)
            
            # 构建结果
            processing_time = time.time() - start_time
            self._log_info(f"分割完成: {len(segment_files)}个分段 (耗时: {processing_time:.2f}s)")
            
            return SegmentationResult(
                success=True,
                input_path=input_path,
                output_dir=output_dir,
                segment_files=segment_files,
                cut_points=final_cut_points,
                processing_time_seconds=processing_time
            )
            
        except SegmentationError:
            raise
        except Exception as e:
            processing_time = time.time() - start_time
            error_msg = f"分割过程中发生未知错误: {str(e)}"
            self._log_error(error_msg)
            
            return SegmentationResult(
                success=False,
                input_path=input_path,
                output_dir=output_dir,
                processing_time_seconds=processing_time,
                error_message=error_msg
            )

    # ========================================================================
    # 以下函数完全照搬code.py，保持原有逻辑不变
    # ========================================================================

    def get_media_duration(self, filepath):
        """获取媒体文件总时长

        使用FFprobe工具获取音频/视频文件的精确时长。

        Args:
            filepath: 媒体文件路径

        Returns:
            float: 文件时长（秒），失败返回None
        """
        command = [
            'ffprobe', '-v', 'error', '-show_entries',
            'format=duration', '-of', 'default=noprint_wrappers=1:nokey=1', filepath
        ]
        try:
            result = subprocess.run(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=False, check=True)
            # 手动解码stdout，避免编码问题
            stdout_text = result.stdout.decode('utf-8', errors='ignore') if result.stdout else ""
            return float(stdout_text.strip())
        except (subprocess.CalledProcessError, FileNotFoundError):
            self._log_error("无法获取媒体文件时长。请确保ffprobe已安装并在PATH中。")
            return None

    def detect_silences(self, filepath):
        """检测音频文件中的静音区段

        使用FFmpeg的silencedetect滤镜检测符合阈值要求的静音区段。

        Args:
            filepath: 音频文件路径

        Returns:
            List[Tuple[float, float]]: 静音区段列表，每个元组为(开始时间, 结束时间)
        """
        self._log_info("开始检测静音... (这可能需要一些时间)")
        command = [
            'ffmpeg', '-i', filepath, '-af',
            f"silencedetect=n={self.NOISE_DB}dB:d={self.MIN_SILENCE_DURATION}",
            '-f', 'null', '-'
        ]
        result = subprocess.run(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=False)

        # 手动解码stderr，避免编码问题
        stderr_text = result.stderr.decode('utf-8', errors='ignore') if result.stderr else ""

        start_matches = re.findall(r'silence_start: (\d+\.?\d*)', stderr_text)
        end_matches = re.findall(r'silence_end: (\d+\.?\d*)', stderr_text)

        if len(start_matches) != len(end_matches):
            if len(start_matches) == len(end_matches) + 1:
                duration = self.get_media_duration(filepath)
                if duration: end_matches.append(str(duration))
            else:
                self._log_warning("检测到的静音开始和结束数量不匹配。")
                return []

        silences = []
        for start, end in zip(start_matches, end_matches):
            start_time = float(start)
            end_time = float(end)
            duration = end_time - start_time
            silences.append({'start': start_time, 'end': end_time, 'duration': duration})

        self._log_info(f"检测到 {len(silences)} 个持续时间 >= {self.MIN_SILENCE_DURATION}秒 的静音区段。")
        return silences

    def find_best_cut_point_dynamically(self, target_time, silences):
        """动态搜索最佳分割点

        从目标时间点开始，逐步扩大搜索半径，寻找最适合的静音区段作为分割点。

        Args:
            target_time: 目标分割时间点
            silences: 静音区段列表

        Returns:
            float: 最佳分割点时间，未找到返回None
        """
        current_radius = self.INITIAL_SEARCH_RADIUS
        while current_radius <= self.MAX_SEARCH_RADIUS:
            self._log_debug(f"尝试在 {target_time}s ± {current_radius}s 窗口内寻找切点...")

            search_min = target_time - current_radius
            search_max = target_time + current_radius

            candidate_silences = [
                s for s in silences
                if max(s['start'], search_min) < min(s['end'], search_max)
            ]

            if candidate_silences:
                best_silence = max(candidate_silences, key=lambda s: s['duration'])
                cut_point = math.ceil(best_silence['start'])

                if cut_point >= best_silence['end'] and best_silence['end'] > best_silence['start']:
                     cut_point = math.floor(best_silence['end'])

                if cut_point <= best_silence['start']:
                    cut_point = round((best_silence['start'] + best_silence['end']) / 2)

                self._log_debug(f"成功! 在该窗口找到最长静音 (开始: {best_silence['start']:.2f}s, 持续: {best_silence['duration']:.2f}s)。")
                self._log_debug(f"最终切点确定为: {cut_point}s")
                return cut_point

            self._log_debug(f"未找到。扩大搜索半径...")
            current_radius += self.RADIUS_INCREMENT

        self._log_warning(f"搜索半径已达到最大值 {self.MAX_SEARCH_RADIUS}s，仍未找到切点。将放弃此次分割。")
        return None

    def _validate_and_fix_last_segment(self, cut_points, silences, duration):
        """验证和修复最后分段长度

        Args:
            cut_points: 当前分割点列表
            silences: 静音区段列表
            duration: 音频总时长

        Returns:
            List[float]: 修复后的分割点列表
        """
        if len(cut_points) < 2:
            return cut_points

        # 计算最后一段的时长
        last_segment_duration = cut_points[-1] - cut_points[-2]
        max_allowed_duration = self.INTERVAL * 1.5  # 允许最后一段最多30分钟

        self._log_info(f"最后分段时长: {last_segment_duration/60:.2f}分钟 (阈值: {max_allowed_duration/60:.1f}分钟)")

        if last_segment_duration > max_allowed_duration:
            self._log_warning(f"最后分段时长 {last_segment_duration/60:.1f}分钟 超过阈值，尝试额外分割")

            # 在最后一段中寻找额外的切点
            last_start = cut_points[-2]
            last_end = cut_points[-1]

            # 尝试在最后一段的中间位置附近寻找切点
            middle_target = last_start + self.INTERVAL

            # 确保有足够的空间进行分割（至少留60秒给最后一小段）
            if middle_target < last_end - 60:
                self._log_info(f"尝试在 {middle_target}s 附近寻找额外切点...")
                additional_cut = self.find_best_cut_point_dynamically(middle_target, silences)

                if additional_cut and additional_cut > last_start + 60 and additional_cut < last_end - 60:
                    self._log_info(f"找到额外切点: {additional_cut}s")
                    cut_points.insert(-1, additional_cut)
                    cut_points = sorted(cut_points)

                    # 重新验证分段长度
                    new_segment1_duration = additional_cut - last_start
                    new_segment2_duration = last_end - additional_cut
                    self._log_info(f"分割后: 分段1={new_segment1_duration/60:.2f}分钟, 分段2={new_segment2_duration/60:.2f}分钟")
                else:
                    self._log_warning("未找到合适的额外切点，保持原有分割")
            else:
                self._log_warning("最后分段空间不足，无法进行额外分割")
        else:
            self._log_info("最后分段时长在合理范围内")

        return cut_points

    def split_audio_precisely(self, filepath, cut_points):
        """根据分割点列表精确切割音频文件

        使用FFmpeg进行精确的音频分割，输出为高质量MP3格式。

        Args:
            filepath: 输入音频文件路径
            cut_points: 分割时间点列表

        Returns:
            List[str]: 生成的分段文件路径列表
        """
        self._log_info("开始精确分割音频并转码为 320k CBR MP3...")

        base_name, _ = os.path.splitext(os.path.basename(filepath))
        output_dir = os.path.dirname(filepath) or '.'
        segment_files = []

        for i in range(len(cut_points) - 1):
            start_time = cut_points[i]
            end_time = cut_points[i+1]

            part_num = i + 1
            start_timedelta = timedelta(seconds=int(start_time))
            time_tag = f"{start_timedelta.seconds // 3600:02d}-{(start_timedelta.seconds // 60) % 60:02d}-{start_timedelta.seconds % 60:02d}"

            output_filename = os.path.join(output_dir, f"{base_name}_part{part_num}_{time_tag}.mp3")
            segment_files.append(output_filename)

            self._log_debug(f"正在生成分段 {part_num}: 从 {start_time}s 到 {end_time}s")
            self._log_debug(f"输出文件: {output_filename}")

            command = [
                'ffmpeg', '-i', filepath, '-ss', str(start_time), '-to', str(end_time),
                '-c:a', 'libmp3lame', '-b:a', self.MP3_BITRATE, '-y', output_filename
            ]

            try:
                subprocess.run(command, check=True, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL, text=False)
            except subprocess.CalledProcessError:
                self._log_error(f"分割分段 {part_num} 时出错。请检查FFmpeg是否正确安装。")

        self._log_info("所有分段处理完成！")
        return segment_files
