"""
多LLM API管理服务

提供优先级管理、负载均衡等高级多API管理功能。
基于LLMAPIService构建的高级管理层。
"""

import os
import json
import time
import logging
from typing import List, Optional, Dict, Any, Callable
from dataclasses import dataclass, asdict

from .api import LLMAPIService, LLMConfig, APIFormat

from .exceptions import LLMError
from .logger_mixin import LLMLoggerMixin


class MultiLLMService(LLMLoggerMixin):
    """多LLM API管理服务 - 提供统一的多API调用和管理接口

    核心功能：
    - 多API配置管理
    - 优先级调用策略
    - 故障转移机制
    - API健康状态监控
    - 负载均衡支持
    """

    def __init__(self, config_file_path: str = None):
        """初始化多LLM服务管理器

        加载配置文件并初始化底层LLM API服务

        Args:
            config_file_path: 配置文件路径，默认使用用户目录下的配置
        """
        super().__init__()

        config_dir = os.path.join(os.path.expanduser("~"), ".evatrans_gui")
        self.config_file = config_file_path or os.path.join(config_dir, "config.json")

        self.llm_service = LLMAPIService(config_file_path)

    def call_api(
        self,
        text_to_segment: str,
        signals_forwarder=None,
        **kwargs
    ) -> Optional[List[str]]:
        """调用多LLM API进行文本分割

        使用配置的优先级顺序调用可用的LLM API，实现故障转移。

        Args:
            text_to_segment: 需要分割的完整文本内容
            signals_forwarder: 信号转发器，用于状态回调
            **kwargs: 传递给底层API的额外参数

        Returns:
            Optional[List[str]]: 分割后的文本片段列表，所有API失败时返回None
        """
        return self.llm_service._call_multi_api(
            text_to_segment=text_to_segment,
            signals_forwarder=signals_forwarder
        )

    def get_api_status(self) -> Dict[str, Dict[str, Any]]:
        """获取所有配置的LLM API的实时状态信息

        Returns:
            Dict[str, Dict[str, Any]]: API状态字典，键为API标识，值为状态信息
        """
        status_info = {}

        for config in self.llm_service.configs:
            # 实时查询状态，不使用缓存
            status_info[config.id] = {
                'name': config.name,
                'enabled': config.enabled,
                'is_available': False,  # 默认为不可用，需要实时测试
                'error_message': "需要测试以获取状态"
            }

        return status_info
    
    def test_all_configs(self, signals_forwarder=None) -> Dict[str, tuple[bool, str]]:
        """测试所有配置的API"""
        return self.llm_service.test_all_configs(signals_forwarder)



    def get_config_by_id(self, config_id: str) -> Optional[LLMConfig]:
        """根据ID获取配置"""
        for config in self.llm_service.configs:
            if config.id == config_id:
                return config
        return None

    def update_config(self, config: LLMConfig):
        """更新配置"""
        for i, existing_config in enumerate(self.llm_service.configs):
            if existing_config.id == config.id:
                self.llm_service.configs[i] = config
                self.llm_service.save_configs()
                break

    def update_config_field(self, config_id: str, field_name: str, value):
        """更新配置的特定字段"""
        config = self.get_config_by_id(config_id)
        if config:
            if field_name == 'enabled':
                config.enabled = bool(value)
            elif field_name == 'api_key':
                config.api_key = str(value)
            elif field_name == 'base_url':
                config.base_url = str(value)
            elif field_name == 'model':
                config.model = str(value)
            elif field_name == 'temperature':
                config.temperature = float(value)
            elif field_name == 'max_tokens':
                config.max_tokens = int(value)
            elif field_name == 'timeout':
                config.timeout = int(value)
            elif field_name == 'format_type':
                if isinstance(value, str):
                    config.format_type = APIFormat(value)
                else:
                    config.format_type = value
            elif field_name == 'available_models':
                config.available_models = list(value) if value else []
            elif field_name == 'request_interval':
                config.request_interval = float(value)

            self.llm_service.save_configs()

    def save_configs(self):
        """保存配置到文件"""
        self.llm_service.save_configs()

    def fetch_models_for_config(self, config_id: str, signals_forwarder=None) -> bool:
        """为指定配置拉取模型列表"""
        from .utils import LLMAPIUtils

        config = self.get_config_by_id(config_id)
        if not config:
            self._log_test(config_id, f"配置不存在: {config_id}", signals_forwarder)
            return False

        if not config.api_key.strip():
            self._log_test(config_id, f"{config.name}: API密钥为空", signals_forwarder)
            return False

        if not config.base_url.strip():
            self._log_test(config_id, f"{config.name}: API地址为空", signals_forwarder)
            return False

        try:
            self._log_test(config_id, f"[Multi-LLM] 正在拉取 {config.name} 的模型列表...", signals_forwarder)

            def test_logger(message):
                self._log_test(config_id, message, signals_forwarder)

            debug_enabled = getattr(signals_forwarder, 'debug_mode', False) if signals_forwarder else False

            success, models, error_msg = LLMAPIUtils.fetch_models(
                config.base_url,
                config.api_key,
                config.format_type,
                config.timeout,
                logger=test_logger,
                debug=debug_enabled
            )

            if success:
                config.available_models = models
                self.llm_service.save_configs()
                self._log_test(config_id, f"[Multi-LLM] ✅ {config.name}: 成功获取 {len(models)} 个模型", signals_forwarder)
                return True
            else:
                self._log_test(config_id, f"[Multi-LLM] ❌ {config.name}: {error_msg}", signals_forwarder)
                return False

        except Exception as e:
            self._log_test(config_id, f"[Multi-LLM] ❌ {config.name}: 拉取模型时出错 - {str(e)}", signals_forwarder)
            return False

    def test_model_for_config(self, config_id: str, signals_forwarder=None) -> bool:
        """测试指定配置的模型连接"""
        from .utils import LLMAPIUtils

        config = self.get_config_by_id(config_id)
        if not config:
            self._log_test(config_id, f"配置不存在: {config_id}", signals_forwarder)
            return False

        if not config.api_key.strip():
            self._log_test(config_id, f"{config.name}: API密钥为空", signals_forwarder)
            return False

        if not config.base_url.strip():
            self._log_test(config_id, f"{config.name}: API地址为空", signals_forwarder)
            return False

        if not config.model.strip():
            self._log_test(config_id, f"{config.name}: 模型名称为空", signals_forwarder)
            return False

        try:
            self._log_test(config_id, f"[Multi-LLM] 正在测试 {config.name} 的模型连接...", signals_forwarder)

            def test_logger(message):
                self._log_test(config_id, message, signals_forwarder)

            debug_enabled = getattr(signals_forwarder, 'debug_mode', False) if signals_forwarder else False

            result = LLMAPIUtils.test_model_connection(
                config.base_url,
                config.api_key,
                config.format_type,
                config.model,
                config.timeout,
                config.temperature,
                logger=test_logger,
                debug=debug_enabled
            )

            # 不再使用缓存，直接处理测试结果

            if result.status == "success":
                self._log_test(config_id, f"[Multi-LLM] ✅ {config.name}: 测试成功 ({result.response_time}s)", signals_forwarder)
                return True
            elif result.status == "inconsistent":
                self._log_test(config_id, f"[Multi-LLM] ⚠️ {config.name}: 模型不一致 - {result.error_message} ({result.response_time}s)", signals_forwarder)
                return False
            elif result.status == "timeout":
                self._log_test(config_id, f"[Multi-LLM] ⏰ {config.name}: {result.error_message}", signals_forwarder)
                return False
            else:
                self._log_test(config_id, f"[Multi-LLM] ❌ {config.name}: {result.error_message}", signals_forwarder)
                return False

        except Exception as e:
            self._log_test(config_id, f"[Multi-LLM] ❌ {config.name}: 测试连接时出错 - {str(e)}", signals_forwarder)
            return False

    @property
    def configs(self) -> List[LLMConfig]:
        """获取所有配置"""
        return self.llm_service.configs

    # status_cache属性已删除，不再使用缓存

    def _log_test(self, config_id: str, message: str, signals_forwarder=None, clear_first: bool = False):
        """记录测试日志到指定配置的测试日志区域"""
        if signals_forwarder and hasattr(signals_forwarder, 'log_test_message'):
            signals_forwarder.log_test_message(config_id, message, clear_first)
        else:
            print(f"[Test-{config_id}] {message}")

    def _clear_test_log(self, config_id: str, signals_forwarder=None):
        """清空指定配置的测试日志"""
        if signals_forwarder and hasattr(signals_forwarder, 'clear_test_log'):
            signals_forwarder.clear_test_log(config_id)
        else:
            print(f"[Test-{config_id}] 清空日志")
_multi_llm_service = None

def get_multi_llm_service() -> MultiLLMService:
    """获取全局多LLM服务实例"""
    global _multi_llm_service
    if _multi_llm_service is None:
        _multi_llm_service = MultiLLMService()
    return _multi_llm_service
