"""
音频系统异常体系

精简的异常定义，专注于异常分类和用户友好信息。
不包含日志功能，保持异常的纯净性。
"""

import os


class AudioError(Exception):
    """音频处理基础异常类
    
    纯异常定义，不包含日志功能。
    """
    
    def __init__(self, user_message: str, file_path: str = None, 
                 technical_details: str = None):
        """
        Args:
            user_message: 用户友好的错误信息
            file_path: 相关文件路径
            technical_details: 技术细节（用于调试）
        """
        self.user_message = user_message
        self.file_path = file_path
        self.technical_details = technical_details
        
        super().__init__(user_message)


class AudioInputError(AudioError):
    """输入相关错误（文件、格式、权限）"""
    pass


class AudioSystemError(AudioError):
    """系统环境错误（FFmpeg、资源、路径）"""
    pass


class AudioProcessError(AudioError):
    """处理执行错误（分析、转换、中断）"""
    pass


class AudioExceptions:
    """音频异常工厂类
    
    提供语义化的异常创建方法。
    """
    
    @staticmethod
    def input_file_not_found(file_path: str):
        return AudioInputError(
            user_message=f"找不到音频文件: {os.path.basename(file_path)}",
            file_path=file_path,
            technical_details="FileNotFoundError"
        )
    
    @staticmethod
    def input_file_invalid(file_path: str, reason: str = None):
        details = f"格式检查失败: {reason}" if reason else "格式无效"
        return AudioInputError(
            user_message=f"音频文件格式无效: {os.path.basename(file_path)}",
            file_path=file_path,
            technical_details=details
        )
    
    @staticmethod
    def input_file_corrupted(file_path: str):
        return AudioInputError(
            user_message=f"音频文件已损坏: {os.path.basename(file_path)}",
            file_path=file_path,
            technical_details="JSON解析失败或音频流无效"
        )
    
    @staticmethod
    def system_ffmpeg_missing():
        return AudioSystemError(
            user_message="FFmpeg未安装，请安装后重试",
            technical_details="FFmpeg/FFprobe not found in PATH"
        )
    
    @staticmethod
    def system_no_space(path: str = None):
        return AudioSystemError(
            user_message="磁盘空间不足，请清理后重试",
            file_path=path,
            technical_details="Disk full or no space left"
        )
    
    @staticmethod
    def system_no_permission(path: str):
        return AudioSystemError(
            user_message=f"文件权限不足: {os.path.basename(path)}",
            file_path=path,
            technical_details="PermissionError"
        )
    
    @staticmethod
    def process_analysis_failed(file_path: str, reason: str = None):
        return AudioProcessError(
            user_message=f"音频分析失败: {os.path.basename(file_path)}",
            file_path=file_path,
            technical_details=f"FFprobe failed: {reason}" if reason else "FFprobe failed"
        )
    
    @staticmethod
    def process_conversion_failed(file_path: str, reason: str = None):
        return AudioProcessError(
            user_message=f"音频转换失败: {os.path.basename(file_path)}",
            file_path=file_path,
            technical_details=f"FFmpeg failed: {reason}" if reason else "FFmpeg failed"
        )
    
    @staticmethod
    def process_timeout(file_path: str, timeout_seconds: int):
        return AudioProcessError(
            user_message=f"处理超时: {os.path.basename(file_path)}",
            file_path=file_path,
            technical_details=f"Timeout after {timeout_seconds}s"
        )
