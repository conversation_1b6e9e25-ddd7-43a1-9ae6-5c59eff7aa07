#!/usr/bin/env python3
"""
EvaTrans 幻觉修复工具 (Hallucination Fixer)
使用现代化界面，专业解决ASR幻觉问题
"""

import os
import sys
import time
from pathlib import Path

# 添加当前目录到路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from core.services import LongEntryRefinerService
from core.models import LongEntryRefineRequest
from core.config import RefineConfig


def print_banner():
    """打印现代化程序横幅"""
    print()
    print("╔══════════════════════════════════════════════════════════════════════════════╗")
    print("║                          🔧 EvaTrans 幻觉修复工具                           ║")
    print("║                                                                              ║")
    print("║  专业解决ASR幻觉问题 • 智能处理长条目字幕 • 提升转录质量                     ║")
    print("╚══════════════════════════════════════════════════════════════════════════════╝")
    print()


def print_success(msg):
    """打印成功信息"""
    print(f"✅ {msg}")


def print_error(msg):
    """打印错误信息"""
    print(f"❌ {msg}")


def print_info(msg):
    """打印信息"""
    print(f"ℹ️  {msg}")


def print_warning(msg):
    """打印警告"""
    print(f"⚠️  {msg}")


def get_task_input():
    """现代化任务输入界面"""
    print("╭─ 📋 任务选择 ────────────────────────────────────────────────────────────────╮")
    print("│                                                                              │")
    print("│  请输入需要处理的任务名称：                                                   │")
    print("│                                                                              │")
    print("╰──────────────────────────────────────────────────────────────────────────────╯")
    print()

    while True:
        try:
            task_name = input("🎯 任务名称: ").strip()
            if task_name:
                return task_name
            print("⚠️  名称不能为空，请重新输入")
        except KeyboardInterrupt:
            print("\n\n👋 程序退出")
            return None


def display_validation_results(task_name, task_folder, subtitle_files, audio_files):
    """显示环境验证结果"""
    print("╭─ 🔍 环境验证 ────────────────────────────────────────────────────────────────╮")
    print("│                                                                              │")
    print(f"│  📁 任务名称: {task_name:<58} │")
    print(f"│  📂 工作目录: {task_folder:<58} │")
    print("│                                                                              │")

    if subtitle_files:
        print(f"│  📄 字幕文件: {len(subtitle_files)}个{' ' * (58 - len(str(len(subtitle_files))))}│")
        for file in subtitle_files[:3]:  # 最多显示3个
            print(f"│     • {file:<66} │")
        if len(subtitle_files) > 3:
            print(f"│     ... 还有 {len(subtitle_files)-3} 个文件{' ' * (50 - len(str(len(subtitle_files)-3)))}│")
    else:
        print("│  ❌ 未找到字幕文件                                                           │")

    print("│                                                                              │")

    if audio_files:
        print(f"│  🎵 音频文件: {len(audio_files)}个{' ' * (58 - len(str(len(audio_files))))}│")
        for file in audio_files[:2]:  # 最多显示2个
            print(f"│     • {file:<66} │")
        if len(audio_files) > 2:
            print(f"│     ... 还有 {len(audio_files)-2} 个文件{' ' * (50 - len(str(len(audio_files)-2)))}│")
    else:
        print("│  ❌ 未找到音频文件                                                           │")

    print("│                                                                              │")
    print("╰──────────────────────────────────────────────────────────────────────────────╯")
    print()


def validate_task_environment(task_name: str):
    """验证任务环境"""
    # 检测当前是否在hallucination_fix子目录中
    current_dir = os.getcwd()
    if current_dir.endswith('hallucination_fix') or 'hallucination_fix' in os.path.basename(current_dir):
        project_path = '../projects'
    else:
        project_path = './projects'
    
    task_folder = os.path.join(project_path, task_name)
    task_folder = os.path.normpath(task_folder)
    
    if not os.path.exists(task_folder):
        return False, task_folder, [], []
    
    # 扫描字幕文件和音频文件
    subtitle_files = []
    audio_files = []

    try:
        for file in os.listdir(task_folder):
            if file.endswith('.srt') and '-' in file and not file.endswith('_fixed.srt'):
                # 只处理合并后的字幕文件，不处理分段文件
                if not '_part' in file:
                    subtitle_files.append(file)
            elif file.endswith('.wav'):
                # 完整音频一定是WAV格式，优先查找WAV文件
                audio_files.append(file)
    except Exception as e:
        print_error(f"扫描文件失败: {e}")
        return False, task_folder, [], []
    
    return True, task_folder, subtitle_files, audio_files


def display_completion_summary(total_processed, total_fixed, processing_time):
    """显示完成摘要"""
    print("╭─ 🎉 处理完成 ────────────────────────────────────────────────────────────────╮")
    print("│                                                                              │")
    print(f"│  ✅ 处理文件: {total_processed} 个{' ' * (58 - len(str(total_processed)))}│")
    print(f"│  🔧 修复条目: {total_fixed} 个{' ' * (58 - len(str(total_fixed)))}│")
    print(f"│  ⏱️  处理时间: {processing_time:.1f} 秒{' ' * (54 - len(f'{processing_time:.1f}'))}│")
    print("│                                                                              │")
    print("│  📁 输出文件已保存到任务文件夹                                                │")
    print("│  📊 详细报告已生成                                                           │")
    print("│                                                                              │")
    print("╰──────────────────────────────────────────────────────────────────────────────╯")
    print()
    print("🎯 按回车键退出...")


def wait_for_key():
    """等待按键退出"""
    try:
        input()
    except KeyboardInterrupt:
        print("\n👋 程序退出")


def main():
    """主入口函数"""
    print_banner()

    # 使用现代化任务输入界面
    task_name = get_task_input()
    if task_name is None:
        return
    
    # 验证任务环境
    is_valid, task_folder, subtitle_files, audio_files = validate_task_environment(task_name)

    # 显示验证结果
    display_validation_results(task_name, task_folder, subtitle_files, audio_files)

    if not is_valid:
        print_error(f"任务文件夹不存在: {task_folder}")
        wait_for_key()
        return

    if not subtitle_files:
        print_error("未找到合并后的字幕文件")
        print("   请确保任务已完成，字幕文件格式为: filename-ASR-LLM.srt")
        wait_for_key()
        return

    if not audio_files:
        print_error("未找到音频文件")
        wait_for_key()
        return
    
    try:
        # 加载配置
        print("正在加载配置...")
        try:
            config = RefineConfig.load_from_main_config()
            print_success("已加载主程序配置")
        except Exception as e:
            print_warning(f"加载主程序配置失败，使用环境变量配置: {e}")
            config = RefineConfig.load_from_env()
        
        # 创建服务
        print("正在初始化服务...")
        service = LongEntryRefinerService(config)
        print_success("服务初始化完成")
        
        # 处理每个字幕文件
        total_processed = 0
        total_refined = 0
        start_time = time.time()  # 记录开始时间
        
        for i, subtitle_file in enumerate(subtitle_files, 1):
            print(f"\n[{i}/{len(subtitle_files)}] 处理文件: {subtitle_file}")

            # 创建请求
            request = LongEntryRefineRequest(
                task_folder=task_folder,
                subtitle_file=subtitle_file,
                audio_file=audio_files[0] if audio_files else "",
                threshold_seconds=15.0,
                test_mode=False
            )
            
            # 处理请求
            start_time = time.time()
            result = service.process_request(request)
            processing_time = time.time() - start_time
            
            if result.success:
                total_processed += result.processed_entries
                total_refined += result.refined_entries
            else:
                print_error(f"处理失败: {result.error_message}")
        
        # 计算总处理时间
        total_time = time.time() - start_time

        # 显示完成摘要
        display_completion_summary(len(subtitle_files), total_refined, total_time)
        
    except KeyboardInterrupt:
        print_warning("\n用户中断操作")
    except Exception as e:
        print_error(f"处理出错: {e}")
        import traceback
        traceback.print_exc()
    
    wait_for_key()


if __name__ == "__main__":
    main()
