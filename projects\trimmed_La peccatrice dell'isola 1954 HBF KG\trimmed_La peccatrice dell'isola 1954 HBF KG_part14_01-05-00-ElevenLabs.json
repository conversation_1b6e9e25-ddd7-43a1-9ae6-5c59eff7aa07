{"success": true, "service": "elevenlabs", "transcription_id": "elevenlabs_1754319765", "text": "Oh, ti feci chist'occhi di stidda. Oh, ti feci st'occhi s'avvedra. Come cori tu si origineggia. E ti dia io canto accussì. E cantando ti parlo d'amori. Cu' paroli ditati du' cori. Io domani ti manco lo sciuri. A lo sciuri tuo a dire di sì. E con Maria lo sanno, finché l'hanno, spusi a mia, so gelosi quando canto, per chi sanno che io canto per te. Per chi sanno che io canto per te. Francisco, devo parlare. Tu non mi ami più, lo so. Anche tu come gli altri. Sei qui per lei. Quella donna vi ha stregato. Vattene, non fare storie. Quando mi andrà di vederti te lo dirò. Vattene. O ti feci chist'occhi di stidda, o ti-- Carmela ne soffriva. E a me dispiaceva perché in fondo le volevo bene. Ma era troppo buona, <PERSON><PERSON>, e io mi stancavo di vederla sempre così sottomessa. Per questo suo fratello non voleva che ci vedesse. Una sera... O ti feci chist'occhi di stiddra, o ti-- Carmela ne soffriva. E a me dispiaceva, perché in fondo le volevo bene. Ma era troppo buona, Carmela, e io mi stancavo di vederla sempre così sottomessa. Per questo suo fratello non voleva che ci vedesse. Una sera... Sei ancora in collera con me Francesco? In collera? E perché? Per quello che ho detto alla villa di don Pietro, ma ero disperata. Se mio fratello sapesse, se indovinasse tutto quello che c'è stato tra di noi. Ti sei lasciata scappare niente con Rosario? Mai, nemmeno una parola. Brava. Fai come ti dico e non avrai da lamentarti. In fondo Rosario è sempre inchiodato alle tavole del suo battello e noi abbiamo avuto tutto il tempo per rimanere insieme senza paura. Qualcuno in paese può accorgersi? Possono vederti entrare o seguirti? Sono troppo furbo io. Con me i guai non ne accadono. E poi anche Rosario non deve fare la voce grossa. Lui fila con Maria. Mica stupido tuo fratello. Fa il generoso coi pescatori e intanto cerca di infilare una scarpa in casa di Ingarzia. Maria significa quattrini. E tu dovresti saperne qualcosa. Io? Sei sempre con lei, no? È tua amica e frequenti la sua casa. È la moglie di Ingarzia, la forestiera. Cosa dice? Di che? Bah, di tutto, di Maria, dell'isola, di quello che succede qui. L'hai mai sentita parlare di me, per esempio? Ecco quello che vuoi, farti notare da quella donna. Ma no. Ma che dici? Per questo mi hai trattata così, ma non lo sopporto più, non posso. Su, non fare tragedie adesso. Lo sai bene che scherzo, no? Mmm?", "words": [{"text": "Oh,", "start": 4.44, "end": 5.02, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 5.02, "end": 5.079, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ti", "start": 5.079, "end": 5.279, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 5.279, "end": 5.42, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "feci", "start": 5.42, "end": 6.879, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 6.879, "end": 6.94, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "chist'occhi", "start": 6.94, "end": 8.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 8.239, "end": 8.5, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 8.5, "end": 8.68, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 8.68, "end": 8.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "stidda.", "start": 8.739, "end": 10.279, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 10.279, "end": 11.34, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Oh,", "start": 11.34, "end": 11.679, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 11.679, "end": 11.679, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ti", "start": 11.679, "end": 11.839, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 11.839, "end": 11.88, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "feci", "start": 11.88, "end": 12.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 12.899, "end": 13.38, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "st'occhi", "start": 13.38, "end": 14.8, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 14.8, "end": 14.8, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "s'av<PERSON>ra.", "start": 14.8, "end": 16.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 16.819, "end": 17.68, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Come", "start": 17.68, "end": 18.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 18.479, "end": 18.52, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "cori", "start": 18.52, "end": 19.879, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 19.879, "end": 19.899, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tu", "start": 19.899, "end": 20.12, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 20.12, "end": 20.18, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "si", "start": 20.18, "end": 20.5, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 20.5, "end": 21.0, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "origineggia.", "start": 21.0, "end": 23.38, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 23.38, "end": 24.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "E", "start": 24.239, "end": 24.6, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 24.6, "end": 24.84, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ti", "start": 24.84, "end": 25.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 25.199, "end": 25.26, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "dia", "start": 25.26, "end": 26.42, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 26.42, "end": 26.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "io", "start": 26.619, "end": 27.039, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 27.039, "end": 27.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "canto", "start": 27.139, "end": 28.079, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 28.079, "end": 28.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "acc<PERSON><PERSON>.", "start": 28.139, "end": 29.679, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 29.679, "end": 31.719, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "E", "start": 31.719, "end": 32.04, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 32.04, "end": 32.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "cantando", "start": 32.159, "end": 33.68, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 33.68, "end": 34.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ti", "start": 34.059, "end": 34.259, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 34.259, "end": 34.259, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "parlo", "start": 34.259, "end": 35.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 35.119, "end": 35.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "d'amori.", "start": 35.239, "end": 37.259, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 37.259, "end": 38.02, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>u'", "start": 38.02, "end": 38.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 38.299, "end": 38.84, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "paroli", "start": 38.84, "end": 39.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 39.859, "end": 40.5, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ditati", "start": 40.5, "end": 41.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 41.779, "end": 42.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "du'", "start": 42.319, "end": 42.54, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 42.54, "end": 43.419, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "cori.", "start": 43.419, "end": 44.24, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 44.24, "end": 44.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Io", "start": 44.879, "end": 45.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 45.699, "end": 45.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "domani", "start": 45.739, "end": 46.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 46.659, "end": 47.34, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ti", "start": 47.34, "end": 47.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 47.559, "end": 47.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "manco", "start": 47.599, "end": 48.34, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 48.34, "end": 48.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "lo", "start": 48.879, "end": 49.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 49.159, "end": 49.379, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sciuri.", "start": 49.379, "end": 50.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 50.579, "end": 51.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "A", "start": 51.979, "end": 52.54, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 52.54, "end": 52.579, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "lo", "start": 52.579, "end": 52.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 52.859, "end": 52.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sci<PERSON>", "start": 52.959, "end": 53.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 53.959, "end": 54.399, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tuo", "start": 54.399, "end": 54.7, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 54.7, "end": 54.84, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 54.84, "end": 55.02, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 55.02, "end": 55.039, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "dire", "start": 55.039, "end": 56.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 56.219, "end": 56.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 56.699, "end": 57.079, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 57.079, "end": 57.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sì.", "start": 57.479, "end": 58.1, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 58.1, "end": 60.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "E", "start": 60.319, "end": 61.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 61.159, "end": 61.199, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "con", "start": 61.199, "end": 61.439, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 61.439, "end": 61.52, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>", "start": 61.52, "end": 62.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 62.759, "end": 63.52, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "lo", "start": 63.52, "end": 64.04, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 64.04, "end": 64.099, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sanno,", "start": 64.099, "end": 65.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 65.319, "end": 65.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "finch<PERSON>", "start": 65.779, "end": 66.4, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 66.4, "end": 66.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "l'hanno,", "start": 66.459, "end": 67.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 67.459, "end": 68.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "spusi", "start": 68.119, "end": 69.22, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 69.22, "end": 69.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 69.239, "end": 69.379, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 69.379, "end": 69.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "mia,", "start": 69.599, "end": 70.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 70.759, "end": 71.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "so", "start": 71.459, "end": 71.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 71.699, "end": 71.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 71.879, "end": 73.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 73.479, "end": 74.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "quando", "start": 74.559, "end": 75.399, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 75.399, "end": 75.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "canto,", "start": 75.619, "end": 76.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 76.299, "end": 77.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "per", "start": 77.779, "end": 78.379, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 78.379, "end": 78.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "chi", "start": 78.559, "end": 78.94, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 78.94, "end": 79.04, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sanno", "start": 79.04, "end": 80.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 80.159, "end": 80.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 80.879, "end": 81.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 81.219, "end": 81.22, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "io", "start": 81.22, "end": 81.34, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 81.34, "end": 81.439, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "canto", "start": 81.439, "end": 82.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 82.459, "end": 82.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "per", "start": 82.459, "end": 83.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 83.579, "end": 83.579, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "te.", "start": 83.579, "end": 92.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 92.799, "end": 93.339, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Per", "start": 93.339, "end": 94.079, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 94.079, "end": 94.18, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "chi", "start": 94.18, "end": 94.48, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 94.48, "end": 94.839, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sanno", "start": 94.839, "end": 95.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 95.459, "end": 95.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 95.739, "end": 95.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 95.759, "end": 95.759, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "io", "start": 95.759, "end": 95.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 95.759, "end": 95.759, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "canto", "start": 95.759, "end": 95.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 95.759, "end": 95.759, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "per", "start": 95.759, "end": 95.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 95.759, "end": 95.759, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "te.", "start": 95.759, "end": 95.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 95.759, "end": 95.759, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Francisco,", "start": 95.759, "end": 96.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 96.299, "end": 96.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "devo", "start": 96.299, "end": 96.5, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 96.5, "end": 96.5, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "parlare.", "start": 96.5, "end": 96.839, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 96.839, "end": 99.139, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Tu", "start": 99.139, "end": 99.239, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 99.239, "end": 99.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "non", "start": 99.299, "end": 99.419, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 99.419, "end": 99.419, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mi", "start": 99.419, "end": 99.54, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 99.54, "end": 99.54, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ami", "start": 99.54, "end": 99.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 99.799, "end": 99.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "pi<PERSON>,", "start": 99.819, "end": 100.019, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 100.019, "end": 100.04, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "lo", "start": 100.04, "end": 100.199, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 100.199, "end": 100.22, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "so.", "start": 100.22, "end": 100.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 100.559, "end": 101.079, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 101.079, "end": 101.36, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 101.36, "end": 101.419, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "tu", "start": 101.419, "end": 101.519, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 101.519, "end": 101.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "come", "start": 101.559, "end": 101.719, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 101.719, "end": 101.759, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "gli", "start": 101.759, "end": 101.86, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 101.86, "end": 101.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "altri.", "start": 101.919, "end": 102.339, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 102.339, "end": 102.699, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 102.699, "end": 102.9, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 102.9, "end": 102.959, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "qui", "start": 102.959, "end": 103.099, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 103.099, "end": 103.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "per", "start": 103.119, "end": 103.26, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 103.26, "end": 103.319, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "lei.", "start": 103.319, "end": 103.699, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 103.699, "end": 103.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 103.979, "end": 104.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 104.159, "end": 104.18, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "donna", "start": 104.18, "end": 104.379, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 104.379, "end": 104.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "vi", "start": 104.379, "end": 104.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 104.479, "end": 104.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ha", "start": 104.479, "end": 104.519, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 104.519, "end": 104.519, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "stregato.", "start": 104.519, "end": 104.879, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 104.879, "end": 104.879, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>,", "start": 104.879, "end": 105.279, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 105.279, "end": 105.279, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "non", "start": 105.279, "end": 105.359, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 105.359, "end": 105.399, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "fare", "start": 105.399, "end": 105.559, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 105.559, "end": 105.579, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "storie.", "start": 105.579, "end": 106.019, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 106.019, "end": 106.18, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Quando", "start": 106.18, "end": 106.379, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 106.379, "end": 106.379, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "mi", "start": 106.379, "end": 106.459, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 106.459, "end": 106.459, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 106.459, "end": 106.639, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 106.639, "end": 106.639, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "di", "start": 106.639, "end": 106.72, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 106.72, "end": 106.72, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "ve<PERSON><PERSON>", "start": 106.72, "end": 107.059, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 107.059, "end": 107.059, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "te", "start": 107.059, "end": 107.139, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 107.139, "end": 107.159, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "lo", "start": 107.159, "end": 107.239, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 107.239, "end": 107.239, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "dirò.", "start": 107.239, "end": 107.559, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 107.559, "end": 107.739, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Vattene.", "start": 107.739, "end": 108.239, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 108.239, "end": 149.959, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "O", "start": 149.959, "end": 150.339, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 150.339, "end": 150.44, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ti", "start": 150.44, "end": 150.679, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 150.679, "end": 150.759, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "feci", "start": 150.759, "end": 152.439, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 152.439, "end": 152.679, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "chist'occhi", "start": 152.679, "end": 154.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 154.199, "end": 154.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 154.459, "end": 154.72, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 154.72, "end": 154.8, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "stidda,", "start": 154.8, "end": 156.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 156.579, "end": 157.3, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "o", "start": 157.3, "end": 157.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 157.699, "end": 157.94, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ti--", "start": 157.94, "end": 159.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 159.199, "end": 166.58, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Carmel<PERSON>", "start": 166.58, "end": 167.02, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 167.02, "end": 167.039, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "ne", "start": 167.039, "end": 167.16, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 167.16, "end": 167.16, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "soffriva.", "start": 167.16, "end": 167.839, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 167.839, "end": 169.279, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "E", "start": 169.279, "end": 169.399, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 169.399, "end": 169.399, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "a", "start": 169.399, "end": 169.439, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 169.439, "end": 169.459, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "me", "start": 169.459, "end": 169.58, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 169.58, "end": 169.58, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 169.58, "end": 170.199, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 170.199, "end": 170.199, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "perché", "start": 170.199, "end": 170.419, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 170.419, "end": 170.419, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "in", "start": 170.419, "end": 170.519, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 170.519, "end": 170.539, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "fondo", "start": 170.539, "end": 170.739, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 170.739, "end": 170.759, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "le", "start": 170.759, "end": 170.839, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 170.839, "end": 170.86, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "volevo", "start": 170.86, "end": 171.119, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 171.119, "end": 171.119, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "bene.", "start": 171.119, "end": 171.619, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 171.619, "end": 174.239, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Ma", "start": 174.239, "end": 174.339, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 174.339, "end": 174.36, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "era", "start": 174.36, "end": 174.5, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 174.5, "end": 174.5, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "troppo", "start": 174.5, "end": 174.779, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 174.779, "end": 174.819, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "buona,", "start": 174.819, "end": 175.159, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 175.159, "end": 175.179, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Carmela,", "start": 175.179, "end": 175.639, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 175.639, "end": 175.639, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "e", "start": 175.639, "end": 175.66, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 175.66, "end": 175.679, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "io", "start": 175.679, "end": 175.779, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 175.779, "end": 175.819, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "mi", "start": 175.819, "end": 175.879, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 175.879, "end": 175.94, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "stancavo", "start": 175.94, "end": 176.44, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 176.44, "end": 176.44, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "di", "start": 176.44, "end": 176.519, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 176.519, "end": 176.519, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "vederla", "start": 176.519, "end": 176.86, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 176.86, "end": 176.86, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "sempre", "start": 176.86, "end": 177.119, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 177.119, "end": 177.16, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "così", "start": 177.16, "end": 177.319, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 177.319, "end": 177.319, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "sottomessa.", "start": 177.319, "end": 177.999, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 177.999, "end": 179.059, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Per", "start": 179.059, "end": 179.22, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 179.22, "end": 179.239, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "questo", "start": 179.239, "end": 179.5, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 179.5, "end": 179.5, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "suo", "start": 179.5, "end": 179.639, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 179.639, "end": 179.659, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "fratello", "start": 179.659, "end": 180.0, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.0, "end": 180.019, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "non", "start": 180.019, "end": 180.08, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.08, "end": 180.08, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON>eva", "start": 180.08, "end": 180.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "che", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "ci", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "vedesse.", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Una", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "sera...", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "O", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ti", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "feci", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "chist'occhi", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "stiddra,", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "o", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ti--", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Carmel<PERSON>", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "ne", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "soffriva.", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "E", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "a", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "me", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>,", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "perché", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "in", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "fondo", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "le", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "volevo", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "bene.", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Ma", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "era", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "troppo", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "buona,", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Carmela,", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "e", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "io", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "mi", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "stancavo", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "di", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "vederla", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "sempre", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "così", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "sottomessa.", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Per", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "questo", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "suo", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "fratello", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "non", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.119, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON>eva", "start": 180.119, "end": 180.339, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.339, "end": 180.339, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "che", "start": 180.339, "end": 180.44, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.44, "end": 180.479, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "ci", "start": 180.479, "end": 180.559, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.559, "end": 180.58, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "vedesse.", "start": 180.58, "end": 181.019, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 181.019, "end": 182.319, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Una", "start": 182.319, "end": 182.5, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 182.5, "end": 182.559, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "sera...", "start": 182.559, "end": 183.2, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 183.2, "end": 199.639, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 199.639, "end": 200.679, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 200.679, "end": 200.679, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ancora", "start": 200.679, "end": 200.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 200.979, "end": 200.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "in", "start": 200.979, "end": 201.079, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 201.079, "end": 201.099, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "collera", "start": 201.099, "end": 201.459, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 201.459, "end": 201.5, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "con", "start": 201.5, "end": 201.599, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 201.599, "end": 201.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "me", "start": 201.639, "end": 201.759, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 201.759, "end": 201.779, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>?", "start": 201.779, "end": 202.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 202.559, "end": 203.22, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "In", "start": 203.22, "end": 203.359, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 203.359, "end": 203.379, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "collera?", "start": 203.379, "end": 203.839, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 203.839, "end": 204.279, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "E", "start": 204.279, "end": 204.379, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 204.379, "end": 204.379, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "perché?", "start": 204.379, "end": 204.82, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 204.82, "end": 206.339, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Per", "start": 206.339, "end": 206.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 206.479, "end": 206.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "quello", "start": 206.559, "end": 206.759, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 206.759, "end": 206.779, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "che", "start": 206.779, "end": 206.839, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 206.839, "end": 206.839, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ho", "start": 206.839, "end": 206.919, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 206.919, "end": 206.94, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "detto", "start": 206.94, "end": 207.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 207.139, "end": 207.139, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "alla", "start": 207.139, "end": 207.24, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 207.24, "end": 207.279, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "villa", "start": 207.279, "end": 207.5, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 207.5, "end": 207.519, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 207.519, "end": 207.599, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 207.599, "end": 207.599, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "don", "start": 207.599, "end": 207.779, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 207.779, "end": 207.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>,", "start": 207.819, "end": 208.379, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 208.379, "end": 208.759, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ma", "start": 208.759, "end": 208.879, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 208.879, "end": 208.899, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ero", "start": 208.899, "end": 209.059, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 209.059, "end": 209.099, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "disperata.", "start": 209.099, "end": 209.919, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 209.919, "end": 211.08, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Se", "start": 211.08, "end": 211.219, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 211.219, "end": 211.259, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mio", "start": 211.259, "end": 211.419, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 211.419, "end": 211.459, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "fratello", "start": 211.459, "end": 211.86, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 211.86, "end": 211.879, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sapes<PERSON>,", "start": 211.879, "end": 212.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 212.559, "end": 213.399, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "se", "start": 213.399, "end": 213.539, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 213.539, "end": 213.539, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "indovinasse", "start": 213.539, "end": 214.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 214.299, "end": 214.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "tutto", "start": 214.639, "end": 214.919, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 214.919, "end": 214.959, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "quello", "start": 214.959, "end": 215.199, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 215.199, "end": 215.239, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "che", "start": 215.239, "end": 215.319, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 215.319, "end": 215.339, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "c'è", "start": 215.339, "end": 215.5, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 215.5, "end": 215.5, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "stato", "start": 215.5, "end": 215.839, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 215.839, "end": 215.879, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "tra", "start": 215.879, "end": 216.039, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 216.039, "end": 216.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 216.059, "end": 216.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 216.139, "end": 216.179, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "noi.", "start": 216.179, "end": 216.579, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 216.579, "end": 223.599, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Ti", "start": 223.599, "end": 223.699, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 223.699, "end": 223.699, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "sei", "start": 223.699, "end": 223.819, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 223.819, "end": 223.839, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "lasciata", "start": 223.839, "end": 224.099, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 224.099, "end": 224.119, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "scappare", "start": 224.119, "end": 224.439, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 224.439, "end": 224.459, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "niente", "start": 224.459, "end": 224.719, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 224.719, "end": 224.72, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "con", "start": 224.72, "end": 224.839, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 224.839, "end": 224.839, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Rosario?", "start": 224.839, "end": 225.299, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 225.299, "end": 225.299, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON>,", "start": 225.299, "end": 225.959, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 225.959, "end": 225.959, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 225.959, "end": 226.279, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 226.279, "end": 226.279, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "una", "start": 226.279, "end": 226.379, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 226.379, "end": 226.44, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "parola.", "start": 226.44, "end": 226.879, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 226.879, "end": 226.879, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Brava.", "start": 226.879, "end": 228.139, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 228.139, "end": 228.559, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Fai", "start": 228.559, "end": 228.739, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 228.739, "end": 228.759, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "come", "start": 228.759, "end": 228.899, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 228.899, "end": 228.94, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "ti", "start": 228.94, "end": 229.019, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 229.019, "end": 229.039, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "dico", "start": 229.039, "end": 229.259, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 229.259, "end": 229.279, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "e", "start": 229.279, "end": 229.299, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 229.299, "end": 229.299, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "non", "start": 229.299, "end": 229.419, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 229.419, "end": 229.419, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "avrai", "start": 229.419, "end": 229.599, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 229.599, "end": 229.599, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "da", "start": 229.599, "end": 229.679, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 229.679, "end": 229.679, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "lamentarti.", "start": 229.679, "end": 230.319, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 230.319, "end": 230.679, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "In", "start": 230.679, "end": 230.8, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 230.8, "end": 230.819, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "fondo", "start": 230.819, "end": 231.099, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 231.099, "end": 231.099, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Rosario", "start": 231.099, "end": 231.479, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 231.479, "end": 231.539, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "è", "start": 231.539, "end": 231.559, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 231.559, "end": 231.559, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "sempre", "start": 231.559, "end": 231.859, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 231.859, "end": 231.86, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "inchiodato", "start": 231.86, "end": 232.419, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 232.419, "end": 232.419, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "alle", "start": 232.419, "end": 232.559, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 232.559, "end": 232.58, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "tavole", "start": 232.58, "end": 232.839, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 232.839, "end": 232.839, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "del", "start": 232.839, "end": 232.939, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 232.939, "end": 232.959, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "suo", "start": 232.959, "end": 233.079, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 233.079, "end": 233.119, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "battel<PERSON>", "start": 233.119, "end": 233.539, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 233.539, "end": 234.419, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "e", "start": 234.419, "end": 234.519, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 234.519, "end": 234.519, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "noi", "start": 234.519, "end": 234.639, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 234.639, "end": 234.659, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "abbiamo", "start": 234.659, "end": 234.86, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 234.86, "end": 234.86, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "avuto", "start": 234.86, "end": 235.119, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 235.119, "end": 235.119, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "tutto", "start": 235.119, "end": 235.299, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 235.299, "end": 235.299, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "il", "start": 235.299, "end": 235.38, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 235.38, "end": 235.439, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "tempo", "start": 235.439, "end": 235.679, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 235.679, "end": 235.679, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "per", "start": 235.679, "end": 235.779, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 235.779, "end": 235.799, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "rimanere", "start": 235.799, "end": 236.099, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 236.099, "end": 236.119, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "insieme", "start": 236.119, "end": 236.439, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 236.439, "end": 236.459, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "senza", "start": 236.459, "end": 236.699, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 236.699, "end": 236.739, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "paura.", "start": 236.739, "end": 237.08, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 237.08, "end": 237.08, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Qualcu<PERSON>", "start": 237.08, "end": 237.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 237.799, "end": 237.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "in", "start": 237.799, "end": 237.899, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 237.899, "end": 237.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "paese", "start": 237.919, "end": 238.259, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 238.259, "end": 238.259, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 238.259, "end": 238.379, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 238.379, "end": 238.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "accorgersi?", "start": 238.379, "end": 239.199, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 239.199, "end": 239.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>sson<PERSON>", "start": 239.379, "end": 239.739, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 239.739, "end": 239.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ve<PERSON><PERSON>", "start": 239.739, "end": 240.099, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 240.099, "end": 240.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "entrare", "start": 240.119, "end": 240.539, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 240.539, "end": 240.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "o", "start": 240.559, "end": 240.639, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 240.639, "end": 240.699, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "seguirti?", "start": 240.699, "end": 241.419, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 241.419, "end": 241.679, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Sono", "start": 241.679, "end": 242.739, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 242.739, "end": 242.739, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "troppo", "start": 242.739, "end": 243.059, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 243.059, "end": 243.099, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "furbo", "start": 243.099, "end": 243.439, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 243.439, "end": 243.5, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "io.", "start": 243.5, "end": 243.82, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 243.82, "end": 245.58, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Con", "start": 245.58, "end": 245.779, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 245.779, "end": 245.819, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "me", "start": 245.819, "end": 246.0, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 246.0, "end": 246.0, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "i", "start": 246.0, "end": 246.019, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 246.019, "end": 246.019, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "guai", "start": 246.019, "end": 246.299, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 246.299, "end": 246.299, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "non", "start": 246.299, "end": 246.419, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 246.419, "end": 246.419, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "ne", "start": 246.419, "end": 246.479, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 246.479, "end": 246.5, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "accadono.", "start": 246.5, "end": 246.979, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 246.979, "end": 247.179, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "E", "start": 247.179, "end": 247.299, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 247.299, "end": 247.319, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "poi", "start": 247.319, "end": 247.5, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 247.5, "end": 247.519, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "anche", "start": 247.519, "end": 247.719, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 247.719, "end": 247.72, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Rosario", "start": 247.72, "end": 248.079, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 248.079, "end": 248.08, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "non", "start": 248.08, "end": 248.179, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 248.179, "end": 248.199, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "deve", "start": 248.199, "end": 248.36, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 248.36, "end": 248.419, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "fare", "start": 248.419, "end": 248.58, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 248.58, "end": 248.58, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "la", "start": 248.58, "end": 248.679, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 248.679, "end": 248.699, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "voce", "start": 248.699, "end": 248.879, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 248.879, "end": 248.919, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "grossa.", "start": 248.919, "end": 249.36, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 249.36, "end": 249.939, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 249.939, "end": 250.119, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 250.119, "end": 250.159, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "fila", "start": 250.159, "end": 250.379, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 250.379, "end": 250.379, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "con", "start": 250.379, "end": 250.499, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 250.499, "end": 250.5, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON>.", "start": 250.5, "end": 250.94, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 250.94, "end": 251.459, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Mica", "start": 251.459, "end": 251.72, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 251.72, "end": 251.739, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "stupido", "start": 251.739, "end": 252.139, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 252.139, "end": 252.159, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "tuo", "start": 252.159, "end": 252.299, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 252.299, "end": 252.339, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "fratello.", "start": 252.339, "end": 252.759, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 252.759, "end": 252.899, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Fa", "start": 252.899, "end": 253.0, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 253.0, "end": 253.0, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "il", "start": 253.0, "end": 253.08, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 253.08, "end": 253.08, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "generoso", "start": 253.08, "end": 253.439, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 253.439, "end": 253.439, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "coi", "start": 253.439, "end": 253.579, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 253.579, "end": 253.58, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "pescatori", "start": 253.58, "end": 254.059, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 254.059, "end": 254.08, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "e", "start": 254.08, "end": 254.099, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 254.099, "end": 254.099, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "intanto", "start": 254.099, "end": 254.399, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 254.399, "end": 254.419, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "cerca", "start": 254.419, "end": 254.659, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 254.659, "end": 254.659, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "di", "start": 254.659, "end": 254.72, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 254.72, "end": 254.72, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "infilare", "start": 254.72, "end": 255.039, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 255.039, "end": 255.039, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "una", "start": 255.039, "end": 255.139, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 255.139, "end": 255.179, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "scarpa", "start": 255.179, "end": 255.5, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 255.5, "end": 255.5, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "in", "start": 255.5, "end": 255.599, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 255.599, "end": 255.619, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "casa", "start": 255.619, "end": 255.819, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 255.819, "end": 255.819, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "di", "start": 255.819, "end": 255.899, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 255.899, "end": 255.899, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Ingarzia.", "start": 255.899, "end": 256.5, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 256.5, "end": 257.54, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON>", "start": 257.54, "end": 258.0, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 258.0, "end": 258.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "significa", "start": 258.1, "end": 258.479, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 258.479, "end": 258.5, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "quattrini.", "start": 258.5, "end": 259.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 259.1, "end": 260.259, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "E", "start": 260.259, "end": 260.359, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 260.359, "end": 260.38, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "tu", "start": 260.38, "end": 260.479, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 260.479, "end": 260.5, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 260.5, "end": 260.819, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 260.819, "end": 260.82, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "saperne", "start": 260.82, "end": 261.16, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 261.16, "end": 261.16, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "qualcosa.", "start": 261.16, "end": 261.739, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 261.739, "end": 261.759, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Io?", "start": 261.759, "end": 262.179, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 262.179, "end": 262.94, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 262.94, "end": 263.219, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 263.219, "end": 263.22, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "sempre", "start": 263.22, "end": 263.499, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 263.499, "end": 263.5, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "con", "start": 263.5, "end": 263.64, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 263.64, "end": 263.66, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "lei,", "start": 263.66, "end": 263.839, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 263.839, "end": 263.859, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "no?", "start": 263.859, "end": 264.059, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 264.059, "end": 264.44, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "È", "start": 264.44, "end": 264.519, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 264.519, "end": 264.54, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "tua", "start": 264.54, "end": 264.699, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 264.699, "end": 264.699, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "amica", "start": 264.699, "end": 264.959, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 264.959, "end": 264.959, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "e", "start": 264.959, "end": 265.059, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 265.059, "end": 265.059, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON>i", "start": 265.059, "end": 265.38, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 265.38, "end": 265.38, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "la", "start": 265.38, "end": 265.479, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 265.479, "end": 265.479, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "sua", "start": 265.479, "end": 265.619, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 265.619, "end": 265.639, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "casa.", "start": 265.639, "end": 266.019, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 266.019, "end": 268.839, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "È", "start": 268.839, "end": 268.919, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 268.919, "end": 268.919, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "la", "start": 268.919, "end": 269.019, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 269.019, "end": 269.04, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "moglie", "start": 269.04, "end": 269.22, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 269.22, "end": 269.22, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "di", "start": 269.22, "end": 269.279, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 269.279, "end": 269.279, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Ingarzia,", "start": 269.279, "end": 269.799, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 269.799, "end": 269.799, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "la", "start": 269.799, "end": 269.88, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 269.88, "end": 269.899, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "forestiera.", "start": 269.899, "end": 270.42, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 270.42, "end": 270.579, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Cosa", "start": 270.579, "end": 270.779, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 270.779, "end": 270.82, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "dice?", "start": 270.82, "end": 271.14, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 271.14, "end": 271.979, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Di", "start": 271.979, "end": 272.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 272.139, "end": 272.139, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "che?", "start": 272.139, "end": 272.439, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 272.439, "end": 273.44, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Bah,", "start": 273.44, "end": 273.699, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 273.699, "end": 274.019, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "di", "start": 274.019, "end": 274.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 274.1, "end": 274.139, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "tutto,", "start": 274.139, "end": 274.44, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 274.44, "end": 274.44, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "di", "start": 274.44, "end": 274.499, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 274.499, "end": 274.54, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON>,", "start": 274.54, "end": 274.88, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 274.88, "end": 274.88, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "dell'isola,", "start": 274.88, "end": 275.359, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 275.359, "end": 275.359, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "di", "start": 275.359, "end": 275.419, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 275.419, "end": 275.419, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "quello", "start": 275.419, "end": 275.6, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 275.6, "end": 275.6, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "che", "start": 275.6, "end": 275.719, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 275.719, "end": 275.739, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "succede", "start": 275.739, "end": 276.019, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 276.019, "end": 276.079, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "qui.", "start": 276.079, "end": 276.399, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 276.399, "end": 276.5, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "L'hai", "start": 276.5, "end": 276.64, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 276.64, "end": 276.66, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "mai", "start": 276.66, "end": 276.779, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 276.779, "end": 276.779, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "sentita", "start": 276.779, "end": 277.079, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 277.079, "end": 277.079, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "parlare", "start": 277.079, "end": 277.399, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 277.399, "end": 277.419, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "di", "start": 277.419, "end": 277.499, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 277.499, "end": 277.5, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "me,", "start": 277.5, "end": 277.619, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 277.619, "end": 277.619, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "per", "start": 277.619, "end": 277.739, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 277.739, "end": 277.759, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "esempio?", "start": 277.759, "end": 278.2, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 278.2, "end": 278.419, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Ecco", "start": 278.419, "end": 278.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 278.799, "end": 278.82, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "quello", "start": 278.82, "end": 278.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 278.979, "end": 278.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "che", "start": 278.979, "end": 279.079, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 279.079, "end": 279.139, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "vuoi,", "start": 279.139, "end": 279.459, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 279.459, "end": 279.459, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>ti", "start": 279.459, "end": 279.72, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 279.72, "end": 279.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "notare", "start": 279.739, "end": 280.019, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 280.019, "end": 280.04, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "da", "start": 280.04, "end": 280.1, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 280.1, "end": 280.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "quella", "start": 280.119, "end": 280.279, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 280.279, "end": 280.339, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "donna.", "start": 280.339, "end": 280.719, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 280.719, "end": 281.1, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Ma", "start": 281.1, "end": 281.179, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 281.179, "end": 281.179, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "no.", "start": 281.179, "end": 281.479, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 281.479, "end": 282.019, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Ma", "start": 282.019, "end": 282.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 282.1, "end": 282.139, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "che", "start": 282.139, "end": 282.319, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 282.319, "end": 282.32, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "dici?", "start": 282.32, "end": 282.719, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 282.719, "end": 282.959, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Per", "start": 282.959, "end": 283.079, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 283.079, "end": 283.16, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "questo", "start": 283.16, "end": 283.379, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 283.379, "end": 283.419, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mi", "start": 283.419, "end": 283.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 283.479, "end": 283.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "hai", "start": 283.479, "end": 283.579, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 283.579, "end": 283.6, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "trattata", "start": 283.6, "end": 283.919, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 283.919, "end": 283.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "così,", "start": 283.919, "end": 284.16, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 284.16, "end": 284.16, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ma", "start": 284.16, "end": 284.239, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 284.239, "end": 284.239, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "non", "start": 284.239, "end": 284.339, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 284.339, "end": 284.359, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "lo", "start": 284.359, "end": 284.459, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 284.459, "end": 284.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sopporto", "start": 284.479, "end": 284.859, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 284.859, "end": 284.94, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "pi<PERSON>,", "start": 284.94, "end": 285.1, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 285.1, "end": 285.1, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "non", "start": 285.1, "end": 285.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 285.299, "end": 285.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "posso.", "start": 285.299, "end": 285.82, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 285.82, "end": 285.959, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>,", "start": 285.959, "end": 286.439, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 286.439, "end": 286.459, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "non", "start": 286.459, "end": 286.639, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 286.639, "end": 286.66, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "fare", "start": 286.66, "end": 286.859, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 286.859, "end": 286.88, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "tragedie", "start": 286.88, "end": 287.419, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 287.419, "end": 287.479, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "adesso.", "start": 287.479, "end": 288.059, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 288.059, "end": 288.82, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Lo", "start": 288.82, "end": 288.959, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 288.959, "end": 288.979, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "sai", "start": 288.979, "end": 289.139, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 289.139, "end": 289.16, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "bene", "start": 289.16, "end": 289.379, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 289.379, "end": 289.38, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "che", "start": 289.38, "end": 289.479, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 289.479, "end": 289.519, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "scherzo,", "start": 289.519, "end": 289.939, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 289.939, "end": 289.979, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "no?", "start": 289.979, "end": 290.179, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 290.179, "end": 291.739, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Mmm?", "start": 291.739, "end": 291.98, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}], "language_detected": null, "confidence": null, "processing_time": 21.41948676109314, "metadata": {"service": "elevenlabs", "mode": "free", "model": "scribe_v1", "raw_result": {"language_code": "ita", "language_probability": 0.9880049824714661, "text": "Oh, ti feci chist'occhi di stidda. Oh, ti feci st'occhi s'avvedra. Come cori tu si origineggia. E ti dia io canto accussì. E cantando ti parlo d'amori. Cu' paroli ditati du' cori. Io domani ti manco lo sciuri. A lo sciuri tuo a dire di sì. E con Maria lo sanno, finché l'hanno, spusi a mia, so gelosi quando canto, per chi sanno che io canto per te. Per chi sanno che io canto per te. Francisco, devo parlare. Tu non mi ami più, lo so. Anche tu come gli altri. Sei qui per lei. Quella donna vi ha stregato. Vattene, non fare storie. Quando mi andrà di vederti te lo dirò. Vattene. O ti feci chist'occhi di stidda, o ti-- Carmela ne soffriva. E a me dispiaceva perché in fondo le volevo bene. Ma era troppo buona, <PERSON><PERSON>, e io mi stancavo di vederla sempre così sottomessa. Per questo suo fratello non voleva che ci vedesse. Una sera... O ti feci chist'occhi di stiddra, o ti-- Carmela ne soffriva. E a me dispiaceva, perché in fondo le volevo bene. Ma era troppo buona, Carmela, e io mi stancavo di vederla sempre così sottomessa. Per questo suo fratello non voleva che ci vedesse. Una sera... Sei ancora in collera con me Francesco? In collera? E perché? Per quello che ho detto alla villa di don Pietro, ma ero disperata. Se mio fratello sapesse, se indovinasse tutto quello che c'è stato tra di noi. Ti sei lasciata scappare niente con Rosario? Mai, nemmeno una parola. Brava. Fai come ti dico e non avrai da lamentarti. In fondo Rosario è sempre inchiodato alle tavole del suo battello e noi abbiamo avuto tutto il tempo per rimanere insieme senza paura. Qualcuno in paese può accorgersi? Possono vederti entrare o seguirti? Sono troppo furbo io. Con me i guai non ne accadono. E poi anche Rosario non deve fare la voce grossa. Lui fila con Maria. Mica stupido tuo fratello. Fa il generoso coi pescatori e intanto cerca di infilare una scarpa in casa di Ingarzia. Maria significa quattrini. E tu dovresti saperne qualcosa. Io? Sei sempre con lei, no? È tua amica e frequenti la sua casa. È la moglie di Ingarzia, la forestiera. Cosa dice? Di che? Bah, di tutto, di Maria, dell'isola, di quello che succede qui. L'hai mai sentita parlare di me, per esempio? Ecco quello che vuoi, farti notare da quella donna. Ma no. Ma che dici? Per questo mi hai trattata così, ma non lo sopporto più, non posso. Su, non fare tragedie adesso. Lo sai bene che scherzo, no? Mmm?", "words": [{"text": "Oh,", "start": 4.44, "end": 5.02, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 5.02, "end": 5.079, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ti", "start": 5.079, "end": 5.279, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 5.279, "end": 5.42, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "feci", "start": 5.42, "end": 6.879, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 6.879, "end": 6.94, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "chist'occhi", "start": 6.94, "end": 8.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 8.239, "end": 8.5, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 8.5, "end": 8.68, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 8.68, "end": 8.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "stidda.", "start": 8.739, "end": 10.279, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 10.279, "end": 11.34, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Oh,", "start": 11.34, "end": 11.679, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 11.679, "end": 11.679, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ti", "start": 11.679, "end": 11.839, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 11.839, "end": 11.88, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "feci", "start": 11.88, "end": 12.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 12.899, "end": 13.38, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "st'occhi", "start": 13.38, "end": 14.8, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 14.8, "end": 14.8, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "s'av<PERSON>ra.", "start": 14.8, "end": 16.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 16.819, "end": 17.68, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Come", "start": 17.68, "end": 18.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 18.479, "end": 18.52, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "cori", "start": 18.52, "end": 19.879, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 19.879, "end": 19.899, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tu", "start": 19.899, "end": 20.12, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 20.12, "end": 20.18, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "si", "start": 20.18, "end": 20.5, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 20.5, "end": 21.0, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "origineggia.", "start": 21.0, "end": 23.38, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 23.38, "end": 24.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "E", "start": 24.239, "end": 24.6, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 24.6, "end": 24.84, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ti", "start": 24.84, "end": 25.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 25.199, "end": 25.26, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "dia", "start": 25.26, "end": 26.42, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 26.42, "end": 26.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "io", "start": 26.619, "end": 27.039, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 27.039, "end": 27.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "canto", "start": 27.139, "end": 28.079, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 28.079, "end": 28.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "acc<PERSON><PERSON>.", "start": 28.139, "end": 29.679, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 29.679, "end": 31.719, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "E", "start": 31.719, "end": 32.04, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 32.04, "end": 32.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "cantando", "start": 32.159, "end": 33.68, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 33.68, "end": 34.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ti", "start": 34.059, "end": 34.259, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 34.259, "end": 34.259, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "parlo", "start": 34.259, "end": 35.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 35.119, "end": 35.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "d'amori.", "start": 35.239, "end": 37.259, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 37.259, "end": 38.02, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>u'", "start": 38.02, "end": 38.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 38.299, "end": 38.84, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "paroli", "start": 38.84, "end": 39.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 39.859, "end": 40.5, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ditati", "start": 40.5, "end": 41.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 41.779, "end": 42.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "du'", "start": 42.319, "end": 42.54, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 42.54, "end": 43.419, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "cori.", "start": 43.419, "end": 44.24, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 44.24, "end": 44.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Io", "start": 44.879, "end": 45.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 45.699, "end": 45.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "domani", "start": 45.739, "end": 46.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 46.659, "end": 47.34, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ti", "start": 47.34, "end": 47.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 47.559, "end": 47.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "manco", "start": 47.599, "end": 48.34, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 48.34, "end": 48.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "lo", "start": 48.879, "end": 49.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 49.159, "end": 49.379, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sciuri.", "start": 49.379, "end": 50.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 50.579, "end": 51.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "A", "start": 51.979, "end": 52.54, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 52.54, "end": 52.579, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "lo", "start": 52.579, "end": 52.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 52.859, "end": 52.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sci<PERSON>", "start": 52.959, "end": 53.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 53.959, "end": 54.399, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tuo", "start": 54.399, "end": 54.7, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 54.7, "end": 54.84, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 54.84, "end": 55.02, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 55.02, "end": 55.039, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "dire", "start": 55.039, "end": 56.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 56.219, "end": 56.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 56.699, "end": 57.079, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 57.079, "end": 57.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sì.", "start": 57.479, "end": 58.1, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 58.1, "end": 60.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "E", "start": 60.319, "end": 61.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 61.159, "end": 61.199, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "con", "start": 61.199, "end": 61.439, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 61.439, "end": 61.52, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>", "start": 61.52, "end": 62.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 62.759, "end": 63.52, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "lo", "start": 63.52, "end": 64.04, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 64.04, "end": 64.099, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sanno,", "start": 64.099, "end": 65.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 65.319, "end": 65.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "finch<PERSON>", "start": 65.779, "end": 66.4, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 66.4, "end": 66.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "l'hanno,", "start": 66.459, "end": 67.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 67.459, "end": 68.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "spusi", "start": 68.119, "end": 69.22, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 69.22, "end": 69.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 69.239, "end": 69.379, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 69.379, "end": 69.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "mia,", "start": 69.599, "end": 70.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 70.759, "end": 71.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "so", "start": 71.459, "end": 71.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 71.699, "end": 71.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 71.879, "end": 73.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 73.479, "end": 74.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "quando", "start": 74.559, "end": 75.399, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 75.399, "end": 75.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "canto,", "start": 75.619, "end": 76.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 76.299, "end": 77.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "per", "start": 77.779, "end": 78.379, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 78.379, "end": 78.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "chi", "start": 78.559, "end": 78.94, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 78.94, "end": 79.04, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sanno", "start": 79.04, "end": 80.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 80.159, "end": 80.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 80.879, "end": 81.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 81.219, "end": 81.22, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "io", "start": 81.22, "end": 81.34, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 81.34, "end": 81.439, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "canto", "start": 81.439, "end": 82.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 82.459, "end": 82.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "per", "start": 82.459, "end": 83.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 83.579, "end": 83.579, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "te.", "start": 83.579, "end": 92.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 92.799, "end": 93.339, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Per", "start": 93.339, "end": 94.079, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 94.079, "end": 94.18, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "chi", "start": 94.18, "end": 94.48, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 94.48, "end": 94.839, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sanno", "start": 94.839, "end": 95.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 95.459, "end": 95.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 95.739, "end": 95.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 95.759, "end": 95.759, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "io", "start": 95.759, "end": 95.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 95.759, "end": 95.759, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "canto", "start": 95.759, "end": 95.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 95.759, "end": 95.759, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "per", "start": 95.759, "end": 95.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 95.759, "end": 95.759, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "te.", "start": 95.759, "end": 95.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 95.759, "end": 95.759, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Francisco,", "start": 95.759, "end": 96.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 96.299, "end": 96.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "devo", "start": 96.299, "end": 96.5, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 96.5, "end": 96.5, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "parlare.", "start": 96.5, "end": 96.839, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 96.839, "end": 99.139, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Tu", "start": 99.139, "end": 99.239, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 99.239, "end": 99.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "non", "start": 99.299, "end": 99.419, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 99.419, "end": 99.419, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mi", "start": 99.419, "end": 99.54, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 99.54, "end": 99.54, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ami", "start": 99.54, "end": 99.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 99.799, "end": 99.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "pi<PERSON>,", "start": 99.819, "end": 100.019, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 100.019, "end": 100.04, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "lo", "start": 100.04, "end": 100.199, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 100.199, "end": 100.22, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "so.", "start": 100.22, "end": 100.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 100.559, "end": 101.079, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 101.079, "end": 101.36, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 101.36, "end": 101.419, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "tu", "start": 101.419, "end": 101.519, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 101.519, "end": 101.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "come", "start": 101.559, "end": 101.719, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 101.719, "end": 101.759, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "gli", "start": 101.759, "end": 101.86, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 101.86, "end": 101.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "altri.", "start": 101.919, "end": 102.339, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 102.339, "end": 102.699, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 102.699, "end": 102.9, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 102.9, "end": 102.959, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "qui", "start": 102.959, "end": 103.099, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 103.099, "end": 103.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "per", "start": 103.119, "end": 103.26, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 103.26, "end": 103.319, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "lei.", "start": 103.319, "end": 103.699, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 103.699, "end": 103.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 103.979, "end": 104.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 104.159, "end": 104.18, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "donna", "start": 104.18, "end": 104.379, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 104.379, "end": 104.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "vi", "start": 104.379, "end": 104.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 104.479, "end": 104.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ha", "start": 104.479, "end": 104.519, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 104.519, "end": 104.519, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "stregato.", "start": 104.519, "end": 104.879, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 104.879, "end": 104.879, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>,", "start": 104.879, "end": 105.279, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 105.279, "end": 105.279, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "non", "start": 105.279, "end": 105.359, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 105.359, "end": 105.399, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "fare", "start": 105.399, "end": 105.559, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 105.559, "end": 105.579, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "storie.", "start": 105.579, "end": 106.019, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 106.019, "end": 106.18, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Quando", "start": 106.18, "end": 106.379, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 106.379, "end": 106.379, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "mi", "start": 106.379, "end": 106.459, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 106.459, "end": 106.459, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 106.459, "end": 106.639, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 106.639, "end": 106.639, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "di", "start": 106.639, "end": 106.72, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 106.72, "end": 106.72, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "ve<PERSON><PERSON>", "start": 106.72, "end": 107.059, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 107.059, "end": 107.059, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "te", "start": 107.059, "end": 107.139, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 107.139, "end": 107.159, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "lo", "start": 107.159, "end": 107.239, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 107.239, "end": 107.239, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "dirò.", "start": 107.239, "end": 107.559, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 107.559, "end": 107.739, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Vattene.", "start": 107.739, "end": 108.239, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 108.239, "end": 149.959, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "O", "start": 149.959, "end": 150.339, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 150.339, "end": 150.44, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ti", "start": 150.44, "end": 150.679, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 150.679, "end": 150.759, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "feci", "start": 150.759, "end": 152.439, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 152.439, "end": 152.679, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "chist'occhi", "start": 152.679, "end": 154.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 154.199, "end": 154.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 154.459, "end": 154.72, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 154.72, "end": 154.8, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "stidda,", "start": 154.8, "end": 156.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 156.579, "end": 157.3, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "o", "start": 157.3, "end": 157.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 157.699, "end": 157.94, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ti--", "start": 157.94, "end": 159.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 159.199, "end": 166.58, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Carmel<PERSON>", "start": 166.58, "end": 167.02, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 167.02, "end": 167.039, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "ne", "start": 167.039, "end": 167.16, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 167.16, "end": 167.16, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "soffriva.", "start": 167.16, "end": 167.839, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 167.839, "end": 169.279, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "E", "start": 169.279, "end": 169.399, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 169.399, "end": 169.399, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "a", "start": 169.399, "end": 169.439, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 169.439, "end": 169.459, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "me", "start": 169.459, "end": 169.58, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 169.58, "end": 169.58, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 169.58, "end": 170.199, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 170.199, "end": 170.199, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "perché", "start": 170.199, "end": 170.419, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 170.419, "end": 170.419, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "in", "start": 170.419, "end": 170.519, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 170.519, "end": 170.539, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "fondo", "start": 170.539, "end": 170.739, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 170.739, "end": 170.759, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "le", "start": 170.759, "end": 170.839, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 170.839, "end": 170.86, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "volevo", "start": 170.86, "end": 171.119, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 171.119, "end": 171.119, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "bene.", "start": 171.119, "end": 171.619, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 171.619, "end": 174.239, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Ma", "start": 174.239, "end": 174.339, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 174.339, "end": 174.36, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "era", "start": 174.36, "end": 174.5, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 174.5, "end": 174.5, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "troppo", "start": 174.5, "end": 174.779, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 174.779, "end": 174.819, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "buona,", "start": 174.819, "end": 175.159, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 175.159, "end": 175.179, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Carmela,", "start": 175.179, "end": 175.639, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 175.639, "end": 175.639, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "e", "start": 175.639, "end": 175.66, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 175.66, "end": 175.679, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "io", "start": 175.679, "end": 175.779, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 175.779, "end": 175.819, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "mi", "start": 175.819, "end": 175.879, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 175.879, "end": 175.94, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "stancavo", "start": 175.94, "end": 176.44, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 176.44, "end": 176.44, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "di", "start": 176.44, "end": 176.519, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 176.519, "end": 176.519, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "vederla", "start": 176.519, "end": 176.86, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 176.86, "end": 176.86, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "sempre", "start": 176.86, "end": 177.119, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 177.119, "end": 177.16, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "così", "start": 177.16, "end": 177.319, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 177.319, "end": 177.319, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "sottomessa.", "start": 177.319, "end": 177.999, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 177.999, "end": 179.059, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Per", "start": 179.059, "end": 179.22, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 179.22, "end": 179.239, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "questo", "start": 179.239, "end": 179.5, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 179.5, "end": 179.5, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "suo", "start": 179.5, "end": 179.639, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 179.639, "end": 179.659, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "fratello", "start": 179.659, "end": 180.0, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.0, "end": 180.019, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "non", "start": 180.019, "end": 180.08, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.08, "end": 180.08, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON>eva", "start": 180.08, "end": 180.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "che", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "ci", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "vedesse.", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Una", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "sera...", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "O", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ti", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "feci", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "chist'occhi", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "stiddra,", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "o", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ti--", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Carmel<PERSON>", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "ne", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "soffriva.", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "E", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "a", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "me", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>,", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "perché", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "in", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "fondo", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "le", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "volevo", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "bene.", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Ma", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "era", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "troppo", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "buona,", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Carmela,", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "e", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "io", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "mi", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "stancavo", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "di", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "vederla", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "sempre", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "così", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "sottomessa.", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Per", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "questo", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "suo", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "fratello", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "non", "start": 180.1, "end": 180.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.1, "end": 180.119, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON>eva", "start": 180.119, "end": 180.339, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.339, "end": 180.339, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "che", "start": 180.339, "end": 180.44, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.44, "end": 180.479, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "ci", "start": 180.479, "end": 180.559, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 180.559, "end": 180.58, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "vedesse.", "start": 180.58, "end": 181.019, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 181.019, "end": 182.319, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Una", "start": 182.319, "end": 182.5, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 182.5, "end": 182.559, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "sera...", "start": 182.559, "end": 183.2, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 183.2, "end": 199.639, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 199.639, "end": 200.679, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 200.679, "end": 200.679, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ancora", "start": 200.679, "end": 200.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 200.979, "end": 200.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "in", "start": 200.979, "end": 201.079, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 201.079, "end": 201.099, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "collera", "start": 201.099, "end": 201.459, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 201.459, "end": 201.5, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "con", "start": 201.5, "end": 201.599, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 201.599, "end": 201.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "me", "start": 201.639, "end": 201.759, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 201.759, "end": 201.779, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>?", "start": 201.779, "end": 202.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 202.559, "end": 203.22, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "In", "start": 203.22, "end": 203.359, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 203.359, "end": 203.379, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "collera?", "start": 203.379, "end": 203.839, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 203.839, "end": 204.279, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "E", "start": 204.279, "end": 204.379, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 204.379, "end": 204.379, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "perché?", "start": 204.379, "end": 204.82, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 204.82, "end": 206.339, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Per", "start": 206.339, "end": 206.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 206.479, "end": 206.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "quello", "start": 206.559, "end": 206.759, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 206.759, "end": 206.779, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "che", "start": 206.779, "end": 206.839, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 206.839, "end": 206.839, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ho", "start": 206.839, "end": 206.919, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 206.919, "end": 206.94, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "detto", "start": 206.94, "end": 207.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 207.139, "end": 207.139, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "alla", "start": 207.139, "end": 207.24, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 207.24, "end": 207.279, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "villa", "start": 207.279, "end": 207.5, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 207.5, "end": 207.519, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 207.519, "end": 207.599, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 207.599, "end": 207.599, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "don", "start": 207.599, "end": 207.779, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 207.779, "end": 207.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>,", "start": 207.819, "end": 208.379, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 208.379, "end": 208.759, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ma", "start": 208.759, "end": 208.879, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 208.879, "end": 208.899, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ero", "start": 208.899, "end": 209.059, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 209.059, "end": 209.099, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "disperata.", "start": 209.099, "end": 209.919, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 209.919, "end": 211.08, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Se", "start": 211.08, "end": 211.219, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 211.219, "end": 211.259, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mio", "start": 211.259, "end": 211.419, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 211.419, "end": 211.459, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "fratello", "start": 211.459, "end": 211.86, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 211.86, "end": 211.879, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sapes<PERSON>,", "start": 211.879, "end": 212.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 212.559, "end": 213.399, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "se", "start": 213.399, "end": 213.539, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 213.539, "end": 213.539, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "indovinasse", "start": 213.539, "end": 214.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 214.299, "end": 214.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "tutto", "start": 214.639, "end": 214.919, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 214.919, "end": 214.959, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "quello", "start": 214.959, "end": 215.199, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 215.199, "end": 215.239, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "che", "start": 215.239, "end": 215.319, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 215.319, "end": 215.339, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "c'è", "start": 215.339, "end": 215.5, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 215.5, "end": 215.5, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "stato", "start": 215.5, "end": 215.839, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 215.839, "end": 215.879, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "tra", "start": 215.879, "end": 216.039, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 216.039, "end": 216.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 216.059, "end": 216.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 216.139, "end": 216.179, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "noi.", "start": 216.179, "end": 216.579, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 216.579, "end": 223.599, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Ti", "start": 223.599, "end": 223.699, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 223.699, "end": 223.699, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "sei", "start": 223.699, "end": 223.819, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 223.819, "end": 223.839, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "lasciata", "start": 223.839, "end": 224.099, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 224.099, "end": 224.119, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "scappare", "start": 224.119, "end": 224.439, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 224.439, "end": 224.459, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "niente", "start": 224.459, "end": 224.719, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 224.719, "end": 224.72, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "con", "start": 224.72, "end": 224.839, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 224.839, "end": 224.839, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Rosario?", "start": 224.839, "end": 225.299, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 225.299, "end": 225.299, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON>,", "start": 225.299, "end": 225.959, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 225.959, "end": 225.959, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 225.959, "end": 226.279, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 226.279, "end": 226.279, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "una", "start": 226.279, "end": 226.379, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 226.379, "end": 226.44, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "parola.", "start": 226.44, "end": 226.879, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 226.879, "end": 226.879, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Brava.", "start": 226.879, "end": 228.139, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 228.139, "end": 228.559, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Fai", "start": 228.559, "end": 228.739, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 228.739, "end": 228.759, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "come", "start": 228.759, "end": 228.899, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 228.899, "end": 228.94, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "ti", "start": 228.94, "end": 229.019, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 229.019, "end": 229.039, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "dico", "start": 229.039, "end": 229.259, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 229.259, "end": 229.279, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "e", "start": 229.279, "end": 229.299, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 229.299, "end": 229.299, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "non", "start": 229.299, "end": 229.419, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 229.419, "end": 229.419, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "avrai", "start": 229.419, "end": 229.599, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 229.599, "end": 229.599, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "da", "start": 229.599, "end": 229.679, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 229.679, "end": 229.679, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "lamentarti.", "start": 229.679, "end": 230.319, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 230.319, "end": 230.679, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "In", "start": 230.679, "end": 230.8, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 230.8, "end": 230.819, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "fondo", "start": 230.819, "end": 231.099, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 231.099, "end": 231.099, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Rosario", "start": 231.099, "end": 231.479, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 231.479, "end": 231.539, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "è", "start": 231.539, "end": 231.559, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 231.559, "end": 231.559, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "sempre", "start": 231.559, "end": 231.859, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 231.859, "end": 231.86, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "inchiodato", "start": 231.86, "end": 232.419, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 232.419, "end": 232.419, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "alle", "start": 232.419, "end": 232.559, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 232.559, "end": 232.58, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "tavole", "start": 232.58, "end": 232.839, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 232.839, "end": 232.839, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "del", "start": 232.839, "end": 232.939, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 232.939, "end": 232.959, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "suo", "start": 232.959, "end": 233.079, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 233.079, "end": 233.119, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "battel<PERSON>", "start": 233.119, "end": 233.539, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 233.539, "end": 234.419, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "e", "start": 234.419, "end": 234.519, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 234.519, "end": 234.519, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "noi", "start": 234.519, "end": 234.639, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 234.639, "end": 234.659, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "abbiamo", "start": 234.659, "end": 234.86, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 234.86, "end": 234.86, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "avuto", "start": 234.86, "end": 235.119, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 235.119, "end": 235.119, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "tutto", "start": 235.119, "end": 235.299, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 235.299, "end": 235.299, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "il", "start": 235.299, "end": 235.38, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 235.38, "end": 235.439, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "tempo", "start": 235.439, "end": 235.679, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 235.679, "end": 235.679, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "per", "start": 235.679, "end": 235.779, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 235.779, "end": 235.799, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "rimanere", "start": 235.799, "end": 236.099, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 236.099, "end": 236.119, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "insieme", "start": 236.119, "end": 236.439, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 236.439, "end": 236.459, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "senza", "start": 236.459, "end": 236.699, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 236.699, "end": 236.739, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "paura.", "start": 236.739, "end": 237.08, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 237.08, "end": 237.08, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Qualcu<PERSON>", "start": 237.08, "end": 237.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 237.799, "end": 237.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "in", "start": 237.799, "end": 237.899, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 237.899, "end": 237.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "paese", "start": 237.919, "end": 238.259, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 238.259, "end": 238.259, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 238.259, "end": 238.379, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 238.379, "end": 238.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "accorgersi?", "start": 238.379, "end": 239.199, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 239.199, "end": 239.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>sson<PERSON>", "start": 239.379, "end": 239.739, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 239.739, "end": 239.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ve<PERSON><PERSON>", "start": 239.739, "end": 240.099, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 240.099, "end": 240.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "entrare", "start": 240.119, "end": 240.539, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 240.539, "end": 240.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "o", "start": 240.559, "end": 240.639, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 240.639, "end": 240.699, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "seguirti?", "start": 240.699, "end": 241.419, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 241.419, "end": 241.679, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Sono", "start": 241.679, "end": 242.739, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 242.739, "end": 242.739, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "troppo", "start": 242.739, "end": 243.059, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 243.059, "end": 243.099, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "furbo", "start": 243.099, "end": 243.439, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 243.439, "end": 243.5, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "io.", "start": 243.5, "end": 243.82, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 243.82, "end": 245.58, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Con", "start": 245.58, "end": 245.779, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 245.779, "end": 245.819, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "me", "start": 245.819, "end": 246.0, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 246.0, "end": 246.0, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "i", "start": 246.0, "end": 246.019, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 246.019, "end": 246.019, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "guai", "start": 246.019, "end": 246.299, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 246.299, "end": 246.299, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "non", "start": 246.299, "end": 246.419, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 246.419, "end": 246.419, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "ne", "start": 246.419, "end": 246.479, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 246.479, "end": 246.5, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "accadono.", "start": 246.5, "end": 246.979, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 246.979, "end": 247.179, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "E", "start": 247.179, "end": 247.299, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 247.299, "end": 247.319, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "poi", "start": 247.319, "end": 247.5, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 247.5, "end": 247.519, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "anche", "start": 247.519, "end": 247.719, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 247.719, "end": 247.72, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Rosario", "start": 247.72, "end": 248.079, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 248.079, "end": 248.08, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "non", "start": 248.08, "end": 248.179, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 248.179, "end": 248.199, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "deve", "start": 248.199, "end": 248.36, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 248.36, "end": 248.419, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "fare", "start": 248.419, "end": 248.58, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 248.58, "end": 248.58, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "la", "start": 248.58, "end": 248.679, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 248.679, "end": 248.699, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "voce", "start": 248.699, "end": 248.879, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 248.879, "end": 248.919, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "grossa.", "start": 248.919, "end": 249.36, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 249.36, "end": 249.939, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 249.939, "end": 250.119, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 250.119, "end": 250.159, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "fila", "start": 250.159, "end": 250.379, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 250.379, "end": 250.379, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "con", "start": 250.379, "end": 250.499, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 250.499, "end": 250.5, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON>.", "start": 250.5, "end": 250.94, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 250.94, "end": 251.459, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Mica", "start": 251.459, "end": 251.72, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 251.72, "end": 251.739, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "stupido", "start": 251.739, "end": 252.139, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 252.139, "end": 252.159, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "tuo", "start": 252.159, "end": 252.299, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 252.299, "end": 252.339, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "fratello.", "start": 252.339, "end": 252.759, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 252.759, "end": 252.899, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Fa", "start": 252.899, "end": 253.0, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 253.0, "end": 253.0, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "il", "start": 253.0, "end": 253.08, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 253.08, "end": 253.08, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "generoso", "start": 253.08, "end": 253.439, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 253.439, "end": 253.439, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "coi", "start": 253.439, "end": 253.579, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 253.579, "end": 253.58, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "pescatori", "start": 253.58, "end": 254.059, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 254.059, "end": 254.08, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "e", "start": 254.08, "end": 254.099, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 254.099, "end": 254.099, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "intanto", "start": 254.099, "end": 254.399, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 254.399, "end": 254.419, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "cerca", "start": 254.419, "end": 254.659, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 254.659, "end": 254.659, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "di", "start": 254.659, "end": 254.72, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 254.72, "end": 254.72, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "infilare", "start": 254.72, "end": 255.039, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 255.039, "end": 255.039, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "una", "start": 255.039, "end": 255.139, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 255.139, "end": 255.179, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "scarpa", "start": 255.179, "end": 255.5, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 255.5, "end": 255.5, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "in", "start": 255.5, "end": 255.599, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 255.599, "end": 255.619, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "casa", "start": 255.619, "end": 255.819, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 255.819, "end": 255.819, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "di", "start": 255.819, "end": 255.899, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 255.899, "end": 255.899, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Ingarzia.", "start": 255.899, "end": 256.5, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 256.5, "end": 257.54, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON>", "start": 257.54, "end": 258.0, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 258.0, "end": 258.1, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "significa", "start": 258.1, "end": 258.479, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 258.479, "end": 258.5, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "quattrini.", "start": 258.5, "end": 259.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 259.1, "end": 260.259, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "E", "start": 260.259, "end": 260.359, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 260.359, "end": 260.38, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "tu", "start": 260.38, "end": 260.479, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 260.479, "end": 260.5, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 260.5, "end": 260.819, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 260.819, "end": 260.82, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "saperne", "start": 260.82, "end": 261.16, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 261.16, "end": 261.16, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "qualcosa.", "start": 261.16, "end": 261.739, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 261.739, "end": 261.759, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Io?", "start": 261.759, "end": 262.179, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 262.179, "end": 262.94, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 262.94, "end": 263.219, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 263.219, "end": 263.22, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "sempre", "start": 263.22, "end": 263.499, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 263.499, "end": 263.5, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "con", "start": 263.5, "end": 263.64, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 263.64, "end": 263.66, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "lei,", "start": 263.66, "end": 263.839, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 263.839, "end": 263.859, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "no?", "start": 263.859, "end": 264.059, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 264.059, "end": 264.44, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "È", "start": 264.44, "end": 264.519, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 264.519, "end": 264.54, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "tua", "start": 264.54, "end": 264.699, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 264.699, "end": 264.699, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "amica", "start": 264.699, "end": 264.959, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 264.959, "end": 264.959, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "e", "start": 264.959, "end": 265.059, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 265.059, "end": 265.059, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON>i", "start": 265.059, "end": 265.38, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 265.38, "end": 265.38, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "la", "start": 265.38, "end": 265.479, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 265.479, "end": 265.479, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "sua", "start": 265.479, "end": 265.619, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 265.619, "end": 265.639, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "casa.", "start": 265.639, "end": 266.019, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 266.019, "end": 268.839, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "È", "start": 268.839, "end": 268.919, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 268.919, "end": 268.919, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "la", "start": 268.919, "end": 269.019, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 269.019, "end": 269.04, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "moglie", "start": 269.04, "end": 269.22, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 269.22, "end": 269.22, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "di", "start": 269.22, "end": 269.279, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 269.279, "end": 269.279, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Ingarzia,", "start": 269.279, "end": 269.799, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 269.799, "end": 269.799, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "la", "start": 269.799, "end": 269.88, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 269.88, "end": 269.899, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "forestiera.", "start": 269.899, "end": 270.42, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 270.42, "end": 270.579, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Cosa", "start": 270.579, "end": 270.779, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 270.779, "end": 270.82, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "dice?", "start": 270.82, "end": 271.14, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 271.14, "end": 271.979, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Di", "start": 271.979, "end": 272.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 272.139, "end": 272.139, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "che?", "start": 272.139, "end": 272.439, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 272.439, "end": 273.44, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Bah,", "start": 273.44, "end": 273.699, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 273.699, "end": 274.019, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "di", "start": 274.019, "end": 274.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 274.1, "end": 274.139, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "tutto,", "start": 274.139, "end": 274.44, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 274.44, "end": 274.44, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "di", "start": 274.44, "end": 274.499, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 274.499, "end": 274.54, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "<PERSON>,", "start": 274.54, "end": 274.88, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 274.88, "end": 274.88, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "dell'isola,", "start": 274.88, "end": 275.359, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 275.359, "end": 275.359, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "di", "start": 275.359, "end": 275.419, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 275.419, "end": 275.419, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "quello", "start": 275.419, "end": 275.6, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 275.6, "end": 275.6, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "che", "start": 275.6, "end": 275.719, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 275.719, "end": 275.739, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "succede", "start": 275.739, "end": 276.019, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 276.019, "end": 276.079, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "qui.", "start": 276.079, "end": 276.399, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 276.399, "end": 276.5, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "L'hai", "start": 276.5, "end": 276.64, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 276.64, "end": 276.66, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "mai", "start": 276.66, "end": 276.779, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 276.779, "end": 276.779, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "sentita", "start": 276.779, "end": 277.079, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 277.079, "end": 277.079, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "parlare", "start": 277.079, "end": 277.399, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 277.399, "end": 277.419, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "di", "start": 277.419, "end": 277.499, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 277.499, "end": 277.5, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "me,", "start": 277.5, "end": 277.619, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 277.619, "end": 277.619, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "per", "start": 277.619, "end": 277.739, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 277.739, "end": 277.759, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "esempio?", "start": 277.759, "end": 278.2, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 278.2, "end": 278.419, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Ecco", "start": 278.419, "end": 278.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 278.799, "end": 278.82, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "quello", "start": 278.82, "end": 278.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 278.979, "end": 278.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "che", "start": 278.979, "end": 279.079, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 279.079, "end": 279.139, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "vuoi,", "start": 279.139, "end": 279.459, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 279.459, "end": 279.459, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>ti", "start": 279.459, "end": 279.72, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 279.72, "end": 279.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "notare", "start": 279.739, "end": 280.019, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 280.019, "end": 280.04, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "da", "start": 280.04, "end": 280.1, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 280.1, "end": 280.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "quella", "start": 280.119, "end": 280.279, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 280.279, "end": 280.339, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "donna.", "start": 280.339, "end": 280.719, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 280.719, "end": 281.1, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Ma", "start": 281.1, "end": 281.179, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 281.179, "end": 281.179, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "no.", "start": 281.179, "end": 281.479, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 281.479, "end": 282.019, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Ma", "start": 282.019, "end": 282.1, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 282.1, "end": 282.139, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "che", "start": 282.139, "end": 282.319, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 282.319, "end": 282.32, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "dici?", "start": 282.32, "end": 282.719, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 282.719, "end": 282.959, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Per", "start": 282.959, "end": 283.079, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 283.079, "end": 283.16, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "questo", "start": 283.16, "end": 283.379, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 283.379, "end": 283.419, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mi", "start": 283.419, "end": 283.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 283.479, "end": 283.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "hai", "start": 283.479, "end": 283.579, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 283.579, "end": 283.6, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "trattata", "start": 283.6, "end": 283.919, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 283.919, "end": 283.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "così,", "start": 283.919, "end": 284.16, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 284.16, "end": 284.16, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ma", "start": 284.16, "end": 284.239, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 284.239, "end": 284.239, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "non", "start": 284.239, "end": 284.339, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 284.339, "end": 284.359, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "lo", "start": 284.359, "end": 284.459, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 284.459, "end": 284.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sopporto", "start": 284.479, "end": 284.859, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 284.859, "end": 284.94, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "pi<PERSON>,", "start": 284.94, "end": 285.1, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 285.1, "end": 285.1, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "non", "start": 285.1, "end": 285.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 285.299, "end": 285.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "posso.", "start": 285.299, "end": 285.82, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 285.82, "end": 285.959, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>,", "start": 285.959, "end": 286.439, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 286.439, "end": 286.459, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "non", "start": 286.459, "end": 286.639, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 286.639, "end": 286.66, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "fare", "start": 286.66, "end": 286.859, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 286.859, "end": 286.88, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "tragedie", "start": 286.88, "end": 287.419, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 287.419, "end": 287.479, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "adesso.", "start": 287.479, "end": 288.059, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 288.059, "end": 288.82, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Lo", "start": 288.82, "end": 288.959, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 288.959, "end": 288.979, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "sai", "start": 288.979, "end": 289.139, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 289.139, "end": 289.16, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "bene", "start": 289.16, "end": 289.379, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 289.379, "end": 289.38, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "che", "start": 289.38, "end": 289.479, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 289.479, "end": 289.519, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "scherzo,", "start": 289.519, "end": 289.939, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 289.939, "end": 289.979, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "no?", "start": 289.979, "end": 290.179, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 290.179, "end": 291.739, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Mmm?", "start": 291.739, "end": 291.98, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}]}}, "created_at": 1754319786.8049362}