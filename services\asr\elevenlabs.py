"""
ElevenLabs语音识别服务
支持API模式和免费模式转录
"""

import os
import json
import time
import random
import requests
from typing import Dict, Any, List, Optional
from io import BytesIO
from dataclasses import dataclass, field

from .base.interface import ASRServiceInterface, ASRRequest, ASRResponse, ASRStatus
from .base.models import ASRServiceConfig, TimestampedWord
from .base.exceptions import ASRException, ASRConfigurationError

# API配置
ELEVENLABS_STT_API_URL = "https://api.elevenlabs.io/v1/speech-to-text"
ELEVENLABS_STT_PARAMS = {"allow_unauthenticated": "1"}
DEFAULT_STT_MODEL_ID = "scribe_v1"

# 常量定义
VALID_MODES = {"free", "api"}
VALID_MODEL_IDS = {"scribe_v1", "scribe_v1_experimental"}
VALID_GRANULARITIES = {"none", "word", "character"}

# User-Agent池
DEFAULT_USER_AGENTS = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:126.0) Gecko/20100101 Firefox/126.0"
]

# Accept-Language池
DEFAULT_ACCEPT_LANGUAGES = [
    "en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7",
    "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7",
    "en-GB,en;q=0.9,en-US;q=0.8,de;q=0.7"
]


class ElevenLabsConfig(ASRServiceConfig):
    """ElevenLabs配置类"""

    def __init__(self, service_name: str = "elevenlabs", api_key: str = "", **kwargs):
        # 防御性清理UI字段
        kwargs = self._clean_ui_fields(kwargs)

        # ElevenLabs特定属性设置
        self.mode = kwargs.pop('mode', 'api')
        self.model_id = kwargs.pop('model_id', 'scribe_v1')
        self.timestamps_granularity = 'word'  # 固定为词级时间戳
        self.tag_audio_events = kwargs.pop('tag_audio_events', False)
        self.num_speakers = kwargs.pop('num_speakers', 0)

        # 父类初始化
        super().__init__(service_name=service_name, api_key=api_key, **kwargs)
    
    def validate(self) -> tuple[bool, List[str]]:
        """配置验证"""
        errors = []

        # 基础验证（不调用基类，避免强制API key验证）
        if not self.service_name.strip():
            errors.append("服务名称不能为空")

        if self.timeout <= 0:
            errors.append("超时时间必须大于0秒")

        if self.max_retries < 0:
            errors.append("重试次数不能为负数")

        # ElevenLabs特定验证规则
        if self.mode not in VALID_MODES:
            errors.append(f"无效的模式: {self.mode}，支持: {', '.join(VALID_MODES)}")

        # 只有API模式才要求API密钥
        if self.mode == "api" and not self.api_key.strip():
            errors.append("API模式需要API密钥")

        if self.model_id not in VALID_MODEL_IDS:
            errors.append(f"不支持的模型ID: {self.model_id}")

        if self.timestamps_granularity not in VALID_GRANULARITIES:
            errors.append(f"无效的时间戳粒度: {self.timestamps_granularity}")

        if not 0 <= self.num_speakers <= 32:
            errors.append("说话人数量必须是0-32之间的整数")

        return len(errors) == 0, errors


class ElevenLabsASRService(ASRServiceInterface):
    """ElevenLabs ASR服务"""
    
    def __init__(self, config: Dict[str, Any]):
        """初始化服务

        Args:
            config: 服务配置
        """
        # 配置对象创建
        if isinstance(config, dict):
            self.config = ElevenLabsConfig(**config)
        else:
            self.config = config
        
        # 配置验证
        is_valid, errors = self.config.validate()
        if not is_valid:
            raise ASRConfigurationError(f"ElevenLabs配置无效: {'; '.join(errors)}", "elevenlabs")

    @property
    def service_name(self) -> str:
        return "elevenlabs"
    

    
    def transcribe(self, request: ASRRequest) -> ASRResponse:
        """转录执行"""
        start_time = time.time()
        transcription_id = f"elevenlabs_{int(start_time)}"

        # 根据模式选择转录方法
        if self.config.mode == "api":
            result_data = self._transcribe_with_api(request)
        else:
            result_data = self._transcribe_with_free_mode(request)

        if result_data:
            # 结果解析
            return self._parse_transcription_result(result_data, transcription_id, start_time)
        else:
            raise ASRException("转录失败，未返回有效结果", "elevenlabs", "PROCESSING_ERROR")
    
    def _transcribe_with_api(self, request: ASRRequest) -> Optional[Dict[str, Any]]:
        """API模式转录"""
        # 重试逻辑（与AssemblyAI/Deepgram保持一致）
        max_retries = self.config.max_retries
        last_exception = None

        for attempt in range(max_retries + 1):
            try:
                if attempt > 0:
                    wait_time = 5 * attempt  # 5, 10, 15, 20, 25秒（与其他SDK一致）
                    print(f"[ElevenLabs] API模式重试第{attempt}次，等待{wait_time}秒...")
                    time.sleep(wait_time)

                print(f"[ElevenLabs] API模式转录尝试 {attempt + 1}/{max_retries + 1}")

                # 导入ElevenLabs SDK
                from elevenlabs.client import ElevenLabs

                # 客户端初始化
                client = ElevenLabs(api_key=self.config.api_key)

                # 音频文件读取（每次重试都重新读取）
                with open(request.audio_file_path, 'rb') as f:
                    audio_data = BytesIO(f.read())

                # 参数准备
                transcription_params = {
                    'file': audio_data,
                    'model_id': self.config.model_id,
                    'diarize': request.enable_diarization,
                    'tag_audio_events': self.config.tag_audio_events,
                    'timestamps_granularity': self.config.timestamps_granularity
                }

                # 语言设置
                if request.language and request.language != 'auto':
                    transcription_params['language_code'] = request.language

                # 说话人数量设置
                if request.num_speakers and request.num_speakers > 0:
                    transcription_params['num_speakers'] = request.num_speakers
                elif self.config.num_speakers > 0:
                    transcription_params['num_speakers'] = self.config.num_speakers

                # API调用
                result = client.speech_to_text.convert(**transcription_params)

                # 结果转换为字典
                if hasattr(result, 'model_dump'):
                    result_data = result.model_dump()
                elif hasattr(result, 'dict'):
                    result_data = result.dict()
                elif hasattr(result, '__dict__'):
                    result_data = result.__dict__
                else:
                    result_data = {"raw_result": str(result)}

                print(f"[ElevenLabs] API模式转录成功 (尝试 {attempt + 1})")
                return result_data

            except Exception as e:
                last_exception = e
                print(f"[ElevenLabs] API模式调用异常 (尝试 {attempt + 1}/{max_retries + 1}): {str(e)}")
                if attempt == max_retries:
                    # 包装为ASR异常（与其他SDK一致）
                    raise ASRException(f"API模式调用失败: {str(e)}", "elevenlabs", "SDK_ERROR") from e

        # 理论上不会到达这里
        if last_exception:
            raise ASRException(f"重试耗尽: {str(last_exception)}", "elevenlabs", "RETRY_EXHAUSTED") from last_exception
        else:
            raise ASRException("API模式转录重试耗尽", "elevenlabs", "RETRY_EXHAUSTED")
    
    def _transcribe_with_free_mode(self, request: ASRRequest) -> Optional[Dict[str, Any]]:
        """免费模式转录"""
        # 请求头准备
        headers = {
            "accept": "*/*",
            "accept-encoding": "gzip, deflate, br, zstd",
            "accept-language": random.choice(DEFAULT_ACCEPT_LANGUAGES),
            "origin": "https://elevenlabs.io",
            "referer": "https://elevenlabs.io/",
            "user-agent": random.choice(DEFAULT_USER_AGENTS),
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-site",
        }

        # 请求数据准备
        payload_data = {
            "model_id": self.config.model_id,
            "tag_audio_events": self.config.tag_audio_events,
            "diarize": request.enable_diarization
        }

        # 语言设置
        if request.language and request.language.lower() != "auto":
            payload_data["language_code"] = request.language

        # 说话人数量
        num_speakers = request.num_speakers or self.config.num_speakers
        if num_speakers and 1 <= num_speakers <= 32:
            payload_data["num_speakers"] = num_speakers

        # 文件信息准备
        file_extension = os.path.splitext(request.audio_file_path)[1].lower()
        mime_type_map = {
            ".mp3": "audio/mpeg", ".wav": "audio/wav", ".flac": "audio/flac",
            ".m4a": "audio/mp4", ".ogg": "audio/ogg", ".opus": "audio/opus",
            ".aac": "audio/aac", ".webm": "audio/webm"
        }
        mime_type = mime_type_map.get(file_extension, 'application/octet-stream')

        # 重试逻辑（参考LLM服务的重试机制）
        max_retries = self.config.max_retries
        last_exception = None

        for attempt in range(max_retries + 1):
            try:
                if attempt > 0:
                    wait_time = (2 ** (attempt - 1)) * 2  # 2, 4, 8, 16秒
                    print(f"[ElevenLabs] 免费模式重试第{attempt}次，等待{wait_time}秒...")
                    time.sleep(wait_time)

                print(f"[ElevenLabs] 免费模式转录尝试 {attempt + 1}/{max_retries + 1}")

                # 每次重试都重新打开文件
                with open(request.audio_file_path, 'rb') as f_audio:
                    files_data = {"file": (os.path.basename(request.audio_file_path), f_audio, mime_type)}

                    # 同步请求执行
                    response = requests.post(
                        ELEVENLABS_STT_API_URL,
                        params=ELEVENLABS_STT_PARAMS,
                        headers=headers,
                        data=payload_data,
                        files=files_data,
                        timeout=self.config.timeout
                    )

                    response.raise_for_status()
                    print(f"[ElevenLabs] 免费模式转录成功 (尝试 {attempt + 1})")
                    return response.json()

            except Exception as e:
                last_exception = e
                print(f"[ElevenLabs] 免费模式调用异常 (尝试 {attempt + 1}/{max_retries + 1}): {str(e)}")
                if attempt == max_retries:
                    raise

        # 理论上不会到达这里
        raise last_exception
    
    def _parse_transcription_result(self, result_data: Dict[str, Any], transcription_id: str, start_time: float) -> ASRResponse:
        """转录结果解析"""
        # 文本和词汇信息提取
        full_text = ""
        words = []

        # 多格式结果解析
        if 'text' in result_data:
            full_text = result_data['text']
        elif 'transcript' in result_data:
            full_text = result_data['transcript']

        if 'words' in result_data and isinstance(result_data['words'], list):
            words = result_data['words']

        # 语言检测
        language_detected = result_data.get('detected_language') or result_data.get('language')

        # 置信度计算
        confidence = result_data.get('confidence')

        processing_time = time.time() - start_time

        return ASRResponse(
            transcription_id=transcription_id,
            status=ASRStatus.COMPLETED,
            full_text=full_text,
            words=words,
            language_detected=language_detected,
            confidence=confidence,
            processing_time=processing_time,
            metadata={
                "service": "elevenlabs",
                "mode": self.config.mode,
                "model": self.config.model_id,
                "raw_result": result_data
            }
        )