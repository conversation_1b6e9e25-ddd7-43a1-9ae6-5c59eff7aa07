{"service": "elevenlabs", "full_text": "La trovarono morta. <PERSON><PERSON><PERSON> potuto attendere Rosario e raccontargli tutto prima di fare una cosa simile, ma era troppo buona e non ebbe il coraggio di farmi del male. Vin, lo sai che quanto mi hai detto è più che sufficiente per farti arrestare? E che cosa posso farci ormai? Tutto è andato storto. Era destino. Tutto doveva ricadere su di me. La morte di Carmela poteva portarmi solo sve-- ", "words": [{"word": "La", "start_time": 35.84, "end_time": 35.979, "confidence": 0.0, "speaker_id": "speaker_0"}, {"word": "trovarono", "start_time": 36.0, "end_time": 36.479, "confidence": 0.0, "speaker_id": "speaker_0"}, {"word": "morta", "start_time": 36.479, "end_time": 36.999, "confidence": 0.0, "speaker_id": "speaker_0"}, {"word": ".", "start_time": 36.999, "end_time": 36.999, "confidence": 0.0, "speaker_id": "speaker_0"}, {"word": "<PERSON><PERSON><PERSON>", "start_time": 39.2, "end_time": 39.599, "confidence": 0.0, "speaker_id": "speaker_0"}, {"word": "potuto", "start_time": 39.599, "end_time": 39.899, "confidence": 0.0, "speaker_id": "speaker_0"}, {"word": "attendere", "start_time": 39.899, "end_time": 40.279, "confidence": 0.0, "speaker_id": "speaker_0"}, {"word": "Rosario", "start_time": 40.319, "end_time": 40.84, "confidence": 0.0, "speaker_id": "speaker_0"}, {"word": "e", "start_time": 41.739, "end_time": 41.839, "confidence": 0.0, "speaker_id": "speaker_0"}, {"word": "raccontar<PERSON>", "start_time": 41.86, "end_time": 42.42, "confidence": 0.0, "speaker_id": "speaker_0"}, {"word": "tutto", "start_time": 42.479, "end_time": 42.74, "confidence": 0.0, "speaker_id": "speaker_0"}, {"word": "prima", "start_time": 42.759, "end_time": 42.959, "confidence": 0.0, "speaker_id": "speaker_0"}, {"word": "di", "start_time": 42.979, "end_time": 43.04, "confidence": 0.0, "speaker_id": "speaker_0"}, {"word": "fare", "start_time": 43.079, "end_time": 43.22, "confidence": 0.0, "speaker_id": "speaker_0"}, {"word": "una", "start_time": 43.239, "end_time": 43.36, "confidence": 0.0, "speaker_id": "speaker_0"}, {"word": "cosa", "start_time": 43.36, "end_time": 43.58, "confidence": 0.0, "speaker_id": "speaker_0"}, {"word": "simile", "start_time": 43.619, "end_time": 44.079, "confidence": 0.0, "speaker_id": "speaker_0"}, {"word": ",", "start_time": 44.079, "end_time": 44.079, "confidence": 0.0, "speaker_id": "speaker_0"}, {"word": "ma", "start_time": 45.039, "end_time": 45.159, "confidence": 0.0, "speaker_id": "speaker_0"}, {"word": "era", "start_time": 45.18, "end_time": 45.34, "confidence": 0.0, "speaker_id": "speaker_0"}, {"word": "troppo", "start_time": 45.36, "end_time": 45.659, "confidence": 0.0, "speaker_id": "speaker_0"}, {"word": "buona", "start_time": 45.7, "end_time": 46.059, "confidence": 0.0, "speaker_id": "speaker_0"}, {"word": "e", "start_time": 46.099, "end_time": 46.199, "confidence": 0.0, "speaker_id": "speaker_0"}, {"word": "non", "start_time": 46.219, "end_time": 46.319, "confidence": 0.0, "speaker_id": "speaker_0"}, {"word": "ebbe", "start_time": 46.34, "end_time": 46.52, "confidence": 0.0, "speaker_id": "speaker_0"}, {"word": "il", "start_time": 46.52, "end_time": 46.619, "confidence": 0.0, "speaker_id": "speaker_0"}, {"word": "coraggio", "start_time": 46.619, "end_time": 47.0, "confidence": 0.0, "speaker_id": "speaker_0"}, {"word": "di", "start_time": 47.02, "end_time": 47.1, "confidence": 0.0, "speaker_id": "speaker_0"}, {"word": "farmi", "start_time": 47.119, "end_time": 47.38, "confidence": 0.0, "speaker_id": "speaker_0"}, {"word": "del", "start_time": 47.399, "end_time": 47.5, "confidence": 0.0, "speaker_id": "speaker_0"}, {"word": "male", "start_time": 47.559, "end_time": 47.979, "confidence": 0.0, "speaker_id": "speaker_0"}, {"word": ".", "start_time": 47.979, "end_time": 47.979, "confidence": 0.0, "speaker_id": "speaker_0"}, {"word": "Vin", "start_time": 48.399, "end_time": 48.78, "confidence": 0.0, "speaker_id": "speaker_1"}, {"word": ",", "start_time": 48.78, "end_time": 48.78, "confidence": 0.0, "speaker_id": "speaker_1"}, {"word": "lo", "start_time": 49.5, "end_time": 49.599, "confidence": 0.0, "speaker_id": "speaker_1"}, {"word": "sai", "start_time": 49.619, "end_time": 49.839, "confidence": 0.0, "speaker_id": "speaker_1"}, {"word": "che", "start_time": 49.84, "end_time": 49.959, "confidence": 0.0, "speaker_id": "speaker_1"}, {"word": "quanto", "start_time": 49.979, "end_time": 50.219, "confidence": 0.0, "speaker_id": "speaker_1"}, {"word": "mi", "start_time": 50.219, "end_time": 50.319, "confidence": 0.0, "speaker_id": "speaker_1"}, {"word": "hai", "start_time": 50.319, "end_time": 50.439, "confidence": 0.0, "speaker_id": "speaker_1"}, {"word": "detto", "start_time": 50.459, "end_time": 50.68, "confidence": 0.0, "speaker_id": "speaker_1"}, {"word": "è", "start_time": 50.68, "end_time": 50.779, "confidence": 0.0, "speaker_id": "speaker_1"}, {"word": "più", "start_time": 50.779, "end_time": 50.899, "confidence": 0.0, "speaker_id": "speaker_1"}, {"word": "che", "start_time": 50.899, "end_time": 51.02, "confidence": 0.0, "speaker_id": "speaker_1"}, {"word": "sufficiente", "start_time": 51.02, "end_time": 51.559, "confidence": 0.0, "speaker_id": "speaker_1"}, {"word": "per", "start_time": 51.559, "end_time": 51.719, "confidence": 0.0, "speaker_id": "speaker_1"}, {"word": "<PERSON>ti", "start_time": 51.739, "end_time": 51.979, "confidence": 0.0, "speaker_id": "speaker_1"}, {"word": "arrestare", "start_time": 52.0, "end_time": 52.639, "confidence": 0.0, "speaker_id": "speaker_1"}, {"word": "?", "start_time": 52.639, "end_time": 52.639, "confidence": 0.0, "speaker_id": "speaker_1"}, {"word": "E", "start_time": 52.639, "end_time": 52.739, "confidence": 0.0, "speaker_id": "speaker_0"}, {"word": "che", "start_time": 52.739, "end_time": 52.86, "confidence": 0.0, "speaker_id": "speaker_0"}, {"word": "cosa", "start_time": 52.919, "end_time": 53.119, "confidence": 0.0, "speaker_id": "speaker_0"}, {"word": "posso", "start_time": 53.119, "end_time": 53.34, "confidence": 0.0, "speaker_id": "speaker_0"}, {"word": "farci", "start_time": 53.399, "end_time": 53.68, "confidence": 0.0, "speaker_id": "speaker_0"}, {"word": "or<PERSON>i", "start_time": 53.68, "end_time": 54.159, "confidence": 0.0, "speaker_id": "speaker_0"}, {"word": "?", "start_time": 54.159, "end_time": 54.159, "confidence": 0.0, "speaker_id": "speaker_0"}, {"word": "<PERSON><PERSON>", "start_time": 54.579, "end_time": 54.84, "confidence": 0.0, "speaker_id": "speaker_0"}, {"word": "è", "start_time": 54.86, "end_time": 54.899, "confidence": 0.0, "speaker_id": "speaker_0"}, {"word": "andato", "start_time": 54.899, "end_time": 55.18, "confidence": 0.0, "speaker_id": "speaker_0"}, {"word": "storto", "start_time": 55.18, "end_time": 55.759, "confidence": 0.0, "speaker_id": "speaker_0"}, {"word": ".", "start_time": 55.759, "end_time": 55.759, "confidence": 0.0, "speaker_id": "speaker_0"}, {"word": "Era", "start_time": 56.479, "end_time": 56.68, "confidence": 0.0, "speaker_id": "speaker_0"}, {"word": "destino", "start_time": 56.699, "end_time": 57.239, "confidence": 0.0, "speaker_id": "speaker_0"}, {"word": ".", "start_time": 57.239, "end_time": 57.239, "confidence": 0.0, "speaker_id": "speaker_0"}, {"word": "<PERSON><PERSON>", "start_time": 57.979, "end_time": 58.34, "confidence": 0.0, "speaker_id": "speaker_0"}, {"word": "<PERSON><PERSON>", "start_time": 58.359, "end_time": 58.659, "confidence": 0.0, "speaker_id": "speaker_0"}, {"word": "ricadere", "start_time": 58.659, "end_time": 59.079, "confidence": 0.0, "speaker_id": "speaker_0"}, {"word": "su", "start_time": 59.099, "end_time": 59.2, "confidence": 0.0, "speaker_id": "speaker_0"}, {"word": "di", "start_time": 59.219, "end_time": 59.319, "confidence": 0.0, "speaker_id": "speaker_0"}, {"word": "me", "start_time": 59.34, "end_time": 59.58, "confidence": 0.0, "speaker_id": "speaker_0"}, {"word": ".", "start_time": 59.58, "end_time": 59.58, "confidence": 0.0, "speaker_id": "speaker_0"}, {"word": "La", "start_time": 60.559, "end_time": 60.68, "confidence": 0.0, "speaker_id": "speaker_0"}, {"word": "morte", "start_time": 60.699, "end_time": 61.0, "confidence": 0.0, "speaker_id": "speaker_0"}, {"word": "di", "start_time": 61.02, "end_time": 61.099, "confidence": 0.0, "speaker_id": "speaker_0"}, {"word": "Carmel<PERSON>", "start_time": 61.139, "end_time": 61.599, "confidence": 0.0, "speaker_id": "speaker_0"}, {"word": "<PERSON><PERSON>", "start_time": 61.619, "end_time": 61.919, "confidence": 0.0, "speaker_id": "speaker_0"}, {"word": "portarmi", "start_time": 61.919, "end_time": 62.42, "confidence": 0.0, "speaker_id": "speaker_0"}, {"word": "solo", "start_time": 62.439, "end_time": 62.659, "confidence": 0.0, "speaker_id": "speaker_0"}, {"word": "sve--", "start_time": 62.68, "end_time": 63.059, "confidence": 0.0, "speaker_id": "speaker_0"}], "language": "unknown", "confidence": 0.0, "speaker_count": 2, "word_count": 79, "parsed_at": 1754319815.963285}