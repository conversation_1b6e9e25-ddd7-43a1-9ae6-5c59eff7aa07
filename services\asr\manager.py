"""
ASR服务工厂模块

提供统一的ASR服务实例创建和管理功能，支持多种ASR服务提供商。
"""

from typing import Dict, List, Any
from .base.interface import ASRServiceInterface
from .base.exceptions import ASRConfigurationError


def create_asr_service(service_name: str, config: Dict[str, Any]) -> ASRServiceInterface:
    """创建指定类型的ASR服务实例

    根据服务名称动态导入并创建对应的ASR服务实例。
    支持的服务：AssemblyAI、Deepgram、ElevenLabs

    Args:
        service_name: ASR服务名称 (assemblyai/deepgram/elevenlabs)
        config: 服务配置字典，包含API密钥等参数

    Returns:
        ASRServiceInterface: 创建的ASR服务实例

    Raises:
        ASRConfigurationError: 服务名称不存在或配置错误
    """
    if service_name == "assemblyai":
        from .assemblyai import AssemblyAIASRService
        return AssemblyAIASRService(config)
    elif service_name == "deepgram":
        from .deepgram import DeepgramASRService
        return DeepgramASRService(config)
    elif service_name == "elevenlabs":
        from .elevenlabs import ElevenLabsASRService
        return ElevenLabsASRService(config)
    else:
        available = ["assemblyai", "deepgram", "elevenlabs"]
        raise ASRConfigurationError(
            f"未知ASR服务: {service_name}，可用: {', '.join(available)}",
            service_name
        )
