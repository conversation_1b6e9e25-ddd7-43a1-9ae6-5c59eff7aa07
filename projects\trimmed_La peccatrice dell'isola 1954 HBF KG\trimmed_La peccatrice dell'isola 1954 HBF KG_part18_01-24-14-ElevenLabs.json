{"success": true, "service": "elevenlabs", "transcription_id": "elevenlabs_1754319841", "text": "Lo seppelliranno vicino a <PERSON>a. È meglio che sia finita così per tutti. <PERSON><PERSON>, <PERSON><PERSON>, speriam<PERSON> <PERSON> r<PERSON><PERSON><PERSON> presto, eh. <PERSON><PERSON> a Palermo. <PERSON><PERSON><PERSON>, dottore, sa<PERSON><PERSON> ora e poi un viaggio. <PERSON><PERSON>ie, arrived<PERSON>ci. Commissario, volevamo salutarla. Ciao ragazzi. Addio. E speriamo che il mare non faccia brutti stia- <PERSON><PERSON> tranquillo, è una tavola. È tardi, dobbiamo partire. I confetti mandatemeli alla questura centrale.", "words": [{"text": "Lo", "start": 15.219, "end": 15.359, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 15.359, "end": 15.359, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "start": 15.359, "end": 16.079, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 16.079, "end": 16.079, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "vicino", "start": 16.079, "end": 16.359, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 16.359, "end": 16.359, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 16.359, "end": 16.419, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 16.419, "end": 16.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Carmela.", "start": 16.479, "end": 17.02, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 17.02, "end": 17.26, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "È", "start": 17.26, "end": 17.399, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 17.399, "end": 17.42, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "meglio", "start": 17.42, "end": 17.659, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 17.659, "end": 17.659, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "che", "start": 17.659, "end": 17.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 17.799, "end": 17.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sia", "start": 17.799, "end": 18.039, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 18.039, "end": 18.039, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "finita", "start": 18.039, "end": 18.379, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 18.379, "end": 18.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "così", "start": 18.379, "end": 18.62, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 18.62, "end": 18.76, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "per", "start": 18.76, "end": 18.999, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 18.999, "end": 19.0, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "tutti.", "start": 19.0, "end": 19.42, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 19.42, "end": 20.399, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON>,", "start": 20.399, "end": 20.659, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 20.659, "end": 20.659, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "caro", "start": 20.659, "end": 20.879, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 20.879, "end": 20.879, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>,", "start": 20.879, "end": 21.44, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 21.44, "end": 21.539, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "speriamo", "start": 21.539, "end": 22.019, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 22.019, "end": 22.079, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 22.079, "end": 22.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 22.159, "end": 22.159, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 22.159, "end": 22.68, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 22.68, "end": 22.699, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "presto,", "start": 22.699, "end": 23.079, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 23.079, "end": 23.079, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "eh.", "start": 23.079, "end": 23.219, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 23.219, "end": 23.6, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 23.6, "end": 23.859, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 23.859, "end": 23.899, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "a", "start": 23.899, "end": 23.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 23.979, "end": 23.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Palermo.", "start": 23.979, "end": 24.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 24.479, "end": 24.699, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Ma<PERSON>i,", "start": 24.699, "end": 25.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 25.159, "end": 25.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "dottore,", "start": 25.159, "end": 25.539, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 25.539, "end": 25.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>re<PERSON>", "start": 25.539, "end": 25.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 25.899, "end": 25.92, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ora", "start": 25.92, "end": 26.059, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 26.059, "end": 26.099, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "e", "start": 26.099, "end": 26.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 26.119, "end": 26.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "poi", "start": 26.119, "end": 26.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 26.239, "end": 26.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "un", "start": 26.239, "end": 26.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 26.299, "end": 26.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "viaggio.", "start": 26.299, "end": 26.78, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 26.78, "end": 26.92, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>,", "start": 26.92, "end": 27.36, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 27.36, "end": 27.439, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>.", "start": 27.439, "end": 27.959, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 27.959, "end": 29.279, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>mmissa<PERSON>,", "start": 29.279, "end": 29.719, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 29.719, "end": 29.719, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "voleva<PERSON>", "start": 29.719, "end": 30.099, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 30.099, "end": 30.099, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "salutarla.", "start": 30.099, "end": 30.679, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 30.679, "end": 30.819, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Ciao", "start": 30.819, "end": 31.119, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 31.119, "end": 31.179, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "ragazzi.", "start": 31.179, "end": 31.679, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 31.679, "end": 31.679, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Addio.", "start": 31.679, "end": 32.12, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 32.12, "end": 32.659, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "E", "start": 32.659, "end": 32.779, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 32.779, "end": 32.799, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "speriamo", "start": 32.799, "end": 33.22, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 33.22, "end": 33.259, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "che", "start": 33.259, "end": 33.34, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 33.34, "end": 33.36, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "il", "start": 33.36, "end": 33.44, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 33.44, "end": 33.5, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "mare", "start": 33.5, "end": 33.74, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 33.74, "end": 33.779, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "non", "start": 33.779, "end": 34.0, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 34.0, "end": 34.0, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "faccia", "start": 34.0, "end": 34.279, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 34.279, "end": 34.299, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "brutti", "start": 34.299, "end": 34.599, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 34.599, "end": 34.599, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "stia-", "start": 34.599, "end": 34.84, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 34.84, "end": 34.84, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Stia", "start": 34.84, "end": 34.88, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 34.88, "end": 34.919, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "tranquillo,", "start": 34.919, "end": 35.279, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 35.279, "end": 35.279, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "è", "start": 35.279, "end": 35.34, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 35.34, "end": 35.34, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "una", "start": 35.34, "end": 35.479, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 35.479, "end": 35.479, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "tavola.", "start": 35.479, "end": 35.899, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 35.899, "end": 35.899, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "È", "start": 35.899, "end": 36.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 36.019, "end": 36.059, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "tardi,", "start": 36.059, "end": 36.399, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 36.399, "end": 36.399, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "dobb<PERSON><PERSON>", "start": 36.399, "end": 36.74, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 36.74, "end": 36.779, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "partire.", "start": 36.779, "end": 37.3, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 37.3, "end": 42.379, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "I", "start": 42.379, "end": 42.499, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 42.499, "end": 42.52, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "confetti", "start": 42.52, "end": 43.04, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 43.04, "end": 43.079, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 43.079, "end": 43.68, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 43.68, "end": 43.7, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "alla", "start": 43.7, "end": 43.819, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 43.819, "end": 43.84, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "questura", "start": 43.84, "end": 44.18, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 44.18, "end": 44.259, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "centrale.", "start": 44.259, "end": 45.059, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}], "language_detected": null, "confidence": null, "processing_time": 6.699341297149658, "metadata": {"service": "elevenlabs", "mode": "free", "model": "scribe_v1", "raw_result": {"language_code": "ita", "language_probability": 0.9953143000602722, "text": "Lo seppelliranno vicino a <PERSON>a. È meglio che sia finita così per tutti. <PERSON><PERSON>, <PERSON><PERSON>, speriam<PERSON> <PERSON> r<PERSON><PERSON><PERSON> presto, eh. <PERSON><PERSON> a Palermo. <PERSON><PERSON><PERSON>, dottore, sa<PERSON><PERSON> ora e poi un viaggio. <PERSON><PERSON>ie, arrived<PERSON>ci. Commissario, volevamo salutarla. Ciao ragazzi. Addio. E speriamo che il mare non faccia brutti stia- <PERSON><PERSON> tranquillo, è una tavola. È tardi, dobbiamo partire. I confetti mandatemeli alla questura centrale.", "words": [{"text": "Lo", "start": 15.219, "end": 15.359, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 15.359, "end": 15.359, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "start": 15.359, "end": 16.079, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 16.079, "end": 16.079, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "vicino", "start": 16.079, "end": 16.359, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 16.359, "end": 16.359, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 16.359, "end": 16.419, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 16.419, "end": 16.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Carmela.", "start": 16.479, "end": 17.02, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 17.02, "end": 17.26, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "È", "start": 17.26, "end": 17.399, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 17.399, "end": 17.42, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "meglio", "start": 17.42, "end": 17.659, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 17.659, "end": 17.659, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "che", "start": 17.659, "end": 17.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 17.799, "end": 17.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sia", "start": 17.799, "end": 18.039, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 18.039, "end": 18.039, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "finita", "start": 18.039, "end": 18.379, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 18.379, "end": 18.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "così", "start": 18.379, "end": 18.62, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 18.62, "end": 18.76, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "per", "start": 18.76, "end": 18.999, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 18.999, "end": 19.0, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "tutti.", "start": 19.0, "end": 19.42, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 19.42, "end": 20.399, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON>,", "start": 20.399, "end": 20.659, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 20.659, "end": 20.659, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "caro", "start": 20.659, "end": 20.879, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 20.879, "end": 20.879, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>,", "start": 20.879, "end": 21.44, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 21.44, "end": 21.539, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "speriamo", "start": 21.539, "end": 22.019, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 22.019, "end": 22.079, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "di", "start": 22.079, "end": 22.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 22.159, "end": 22.159, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 22.159, "end": 22.68, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 22.68, "end": 22.699, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "presto,", "start": 22.699, "end": 23.079, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 23.079, "end": 23.079, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "eh.", "start": 23.079, "end": 23.219, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 23.219, "end": 23.6, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 23.6, "end": 23.859, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 23.859, "end": 23.899, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "a", "start": 23.899, "end": 23.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 23.979, "end": 23.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Palermo.", "start": 23.979, "end": 24.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 24.479, "end": 24.699, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Ma<PERSON>i,", "start": 24.699, "end": 25.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 25.159, "end": 25.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "dottore,", "start": 25.159, "end": 25.539, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 25.539, "end": 25.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>re<PERSON>", "start": 25.539, "end": 25.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 25.899, "end": 25.92, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ora", "start": 25.92, "end": 26.059, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 26.059, "end": 26.099, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "e", "start": 26.099, "end": 26.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 26.119, "end": 26.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "poi", "start": 26.119, "end": 26.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 26.239, "end": 26.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "un", "start": 26.239, "end": 26.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 26.299, "end": 26.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "viaggio.", "start": 26.299, "end": 26.78, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 26.78, "end": 26.92, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>,", "start": 26.92, "end": 27.36, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 27.36, "end": 27.439, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>.", "start": 27.439, "end": 27.959, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 27.959, "end": 29.279, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>mmissa<PERSON>,", "start": 29.279, "end": 29.719, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 29.719, "end": 29.719, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "voleva<PERSON>", "start": 29.719, "end": 30.099, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 30.099, "end": 30.099, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "salutarla.", "start": 30.099, "end": 30.679, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 30.679, "end": 30.819, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Ciao", "start": 30.819, "end": 31.119, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 31.119, "end": 31.179, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "ragazzi.", "start": 31.179, "end": 31.679, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 31.679, "end": 31.679, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Addio.", "start": 31.679, "end": 32.12, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 32.12, "end": 32.659, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "E", "start": 32.659, "end": 32.779, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 32.779, "end": 32.799, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "speriamo", "start": 32.799, "end": 33.22, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 33.22, "end": 33.259, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "che", "start": 33.259, "end": 33.34, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 33.34, "end": 33.36, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "il", "start": 33.36, "end": 33.44, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 33.44, "end": 33.5, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "mare", "start": 33.5, "end": 33.74, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 33.74, "end": 33.779, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "non", "start": 33.779, "end": 34.0, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 34.0, "end": 34.0, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "faccia", "start": 34.0, "end": 34.279, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 34.279, "end": 34.299, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "brutti", "start": 34.299, "end": 34.599, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 34.599, "end": 34.599, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "stia-", "start": 34.599, "end": 34.84, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 34.84, "end": 34.84, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Stia", "start": 34.84, "end": 34.88, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 34.88, "end": 34.919, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "tranquillo,", "start": 34.919, "end": 35.279, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 35.279, "end": 35.279, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "è", "start": 35.279, "end": 35.34, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 35.34, "end": 35.34, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "una", "start": 35.34, "end": 35.479, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 35.479, "end": 35.479, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "tavola.", "start": 35.479, "end": 35.899, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 35.899, "end": 35.899, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "È", "start": 35.899, "end": 36.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 36.019, "end": 36.059, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "tardi,", "start": 36.059, "end": 36.399, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 36.399, "end": 36.399, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "dobb<PERSON><PERSON>", "start": 36.399, "end": 36.74, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 36.74, "end": 36.779, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "partire.", "start": 36.779, "end": 37.3, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 37.3, "end": 42.379, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "I", "start": 42.379, "end": 42.499, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 42.499, "end": 42.52, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "confetti", "start": 42.52, "end": 43.04, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 43.04, "end": 43.079, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 43.079, "end": 43.68, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 43.68, "end": 43.7, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "alla", "start": 43.7, "end": 43.819, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 43.819, "end": 43.84, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "questura", "start": 43.84, "end": 44.18, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 44.18, "end": 44.259, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "centrale.", "start": 44.259, "end": 45.059, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}]}}, "created_at": 1754319847.7579808}