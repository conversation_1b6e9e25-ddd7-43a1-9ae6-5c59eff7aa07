import sys
import os
import flet as ft

# 添加当前目录到Python路径，解决打包后的导入问题
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# 尝试导入，如果失败则添加src路径
try:
    from utils.file_utils import setup_faulthandler
    from ui.main_application import main as flet_main
except ImportError:
    # 打包后可能需要添加src路径
    src_path = os.path.join(current_dir, 'src')
    if os.path.exists(src_path) and src_path not in sys.path:
        sys.path.insert(0, src_path)

    from utils.file_utils import setup_faulthandler
    from ui.main_application import main as flet_main


def main():
    """主入口函数"""
    setup_faulthandler()

    # 启动 Flet 应用
    ft.app(
        target=flet_main,
        name="EvaTrans",
        assets_dir="assets",
        web_renderer="html"
    )


if __name__ == "__main__":
    main()