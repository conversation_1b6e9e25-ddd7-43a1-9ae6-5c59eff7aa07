"""
分段处理工具类

提供音频分段文件的检测、状态分析和进度计算功能。
支持分段转录和字幕生成的完整工作流程。
"""

import os
from typing import List, Dict, Optional


class SegmentationUtils:
    """分段处理工具类 - 提供音频分段文件的检测和状态分析功能

    核心功能：
    - 分段文件检测和排序
    - 分段处理状态分析
    - ASR-LLM组合完成度计算
    - 分段转录进度统计
    """
    
    @staticmethod
    def find_segment_files(task_folder: str, file_name: str) -> List[str]:
        """查找指定文件的所有分段文件

        搜索模式：{file_name}_part{N}_{timestamp}.mp3

        Args:
            task_folder: 任务文件夹路径
            file_name: 原始文件名（不含扩展名）

        Returns:
            List[str]: 分段文件完整路径列表，按文件名自然排序
        """
        try:
            segment_files = []
            
            # 查找分段文件模式：filename_part1_00-00-00.mp3
            for file in os.listdir(task_folder):
                if file.startswith(f"{file_name}_part") and file.endswith('.mp3'):
                    segment_files.append(os.path.join(task_folder, file))
            
            # 按文件名排序确保顺序
            segment_files.sort()
            return segment_files
            
        except Exception as e:
            # 静默处理异常，返回空列表
            return []
    
    @staticmethod
    def count_completed_segments(task_folder: str, segment_files: List[str], 
                               enabled_asr_services: List[str], enabled_llm_apis: List[str]) -> int:
        """统计完全完成所有ASR-LLM组合处理的分段数量

        完成标准：分段的所有ASR-LLM组合都生成了对应的字幕文件

        Args:
            task_folder: 任务文件夹路径
            segment_files: 分段文件路径列表
            enabled_asr_services: 启用的ASR服务列表
            enabled_llm_apis: 启用的LLM API列表

        Returns:
            int: 完全完成处理的分段数量
        """
        try:
            if not enabled_asr_services or not enabled_llm_apis:
                return 0
            
            completed_count = 0
            
            for segment_file in segment_files:
                segment_base = os.path.splitext(os.path.basename(segment_file))[0]
                segment_completed = True
                
                # 检查该分段的所有ASR-LLM组合是否完成
                for asr_service in enabled_asr_services:
                    for llm_api in enabled_llm_apis:
                        subtitle_file = f"{segment_base}-{asr_service}-{llm_api}.srt"
                        subtitle_path = os.path.join(task_folder, subtitle_file)
                        
                        if not os.path.exists(subtitle_path):
                            segment_completed = False
                            break
                    
                    if not segment_completed:
                        break
                
                if segment_completed:
                    completed_count += 1
            
            return completed_count
            
        except Exception as e:
            # 静默处理异常，返回0
            return 0
    
    @staticmethod
    def analyze_segmented_combinations_status(task_folder: str, segment_files: List[str],
                                            enabled_asr_services: List[str], enabled_llm_apis: List[str],
                                            validate_transcription_func: Optional[callable] = None) -> Dict:
        """分析分段文件的ASR-LLM组合状态
        
        Args:
            task_folder: 任务文件夹路径
            segment_files: 分段文件路径列表
            enabled_asr_services: 启用的ASR服务列表
            enabled_llm_apis: 启用的LLM API列表
            validate_transcription_func: 转录文件验证函数（可选）
            
        Returns:
            Dict: 分段组合状态信息
        """
        try:
            if not enabled_asr_services or not enabled_llm_apis:
                return {
                    'total_combinations': 0,
                    'transcription_ready': 0,
                    'subtitle_ready': 0,
                    'transcription_complete': False,
                    'subtitle_complete': False,
                    'missing_transcriptions': [],
                    'missing_subtitles': [],
                    'is_segmented': True,
                    'total_segments': len(segment_files),
                    'completed_segments': 0
                }
            
            # 计算总组合数：分段数 × ASR服务数 × LLM API数
            total_combinations = len(segment_files) * len(enabled_asr_services) * len(enabled_llm_apis)
            
            transcription_ready = 0
            subtitle_ready = 0
            missing_transcriptions = []
            missing_subtitles = []
            
            # 检查每个分段的每个组合
            for segment_file in segment_files:
                segment_base = os.path.splitext(os.path.basename(segment_file))[0]
                
                for asr_service in enabled_asr_services:
                    # 检查转录状态
                    parsed_file = f"{segment_base}-{asr_service}-parsed.json"
                    parsed_path = os.path.join(task_folder, parsed_file)
                    
                    # 验证转录文件
                    transcription_valid = os.path.exists(parsed_path)
                    if transcription_valid and validate_transcription_func:
                        try:
                            transcription_valid = validate_transcription_func(parsed_path)
                        except:
                            transcription_valid = False
                    
                    if transcription_valid:
                        transcription_ready += len(enabled_llm_apis)  # 一个转录对应多个LLM
                    else:
                        for llm_api in enabled_llm_apis:
                            missing_transcriptions.append({
                                'segment': segment_base,
                                'asr_service': asr_service,
                                'llm_api': llm_api,
                                'transcription_path': parsed_path,
                                'subtitle_path': os.path.join(task_folder, f"{segment_base}-{asr_service}-{llm_api}.srt")
                            })
                    
                    # 检查字幕状态
                    for llm_api in enabled_llm_apis:
                        subtitle_file = f"{segment_base}-{asr_service}-{llm_api}.srt"
                        subtitle_path = os.path.join(task_folder, subtitle_file)
                        
                        if os.path.exists(subtitle_path):
                            subtitle_ready += 1
                        else:
                            missing_subtitles.append({
                                'segment': segment_base,
                                'asr_service': asr_service,
                                'llm_api': llm_api,
                                'transcription_path': parsed_path,
                                'subtitle_path': subtitle_path
                            })
            
            # 计算完成的分段数
            completed_segments = SegmentationUtils.count_completed_segments(
                task_folder, segment_files, enabled_asr_services, enabled_llm_apis)
            
            return {
                'total_combinations': total_combinations,
                'transcription_ready': transcription_ready,
                'subtitle_ready': subtitle_ready,
                'transcription_complete': transcription_ready == total_combinations,
                'subtitle_complete': subtitle_ready == total_combinations,
                'missing_transcriptions': missing_transcriptions,
                'missing_subtitles': missing_subtitles,
                'is_segmented': True,  # 标记为分段处理
                'total_segments': len(segment_files),
                'completed_segments': completed_segments
            }
            
        except Exception as e:
            # 静默处理异常，返回默认状态
            return {
                'total_combinations': 0,
                'transcription_ready': 0,
                'subtitle_ready': 0,
                'transcription_complete': False,
                'subtitle_complete': False,
                'missing_transcriptions': [],
                'missing_subtitles': [],
                'is_segmented': True,
                'total_segments': len(segment_files) if segment_files else 0,
                'completed_segments': 0
            }
    
    @staticmethod
    def is_segmented_processing(task_folder: str, file_name: str) -> bool:
        """判断是否为分段处理
        
        Args:
            task_folder: 任务文件夹路径
            file_name: 原始文件名（不含扩展名）
            
        Returns:
            bool: 是否为分段处理
        """
        segment_files = SegmentationUtils.find_segment_files(task_folder, file_name)
        return len(segment_files) > 0
