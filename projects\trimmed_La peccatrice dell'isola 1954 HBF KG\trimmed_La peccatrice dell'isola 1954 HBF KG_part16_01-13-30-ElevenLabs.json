{"success": true, "service": "elevenlabs", "transcription_id": "elevenlabs_1754319808", "text": "La trovarono morta. <PERSON><PERSON><PERSON> potuto attendere Rosario e raccontargli tutto prima di fare una cosa simile, ma era troppo buona e non ebbe il coraggio di farmi del male. Vin, lo sai che quanto mi hai detto è più che sufficiente per farti arrestare? E che cosa posso farci ormai? Tutto è andato storto. Era destino. Tutto doveva ricadere su di me. La morte di Carmela poteva portarmi solo sve-- ", "words": [{"text": "La", "start": 35.84, "end": 35.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 35.979, "end": 36.0, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "trovarono", "start": 36.0, "end": 36.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 36.479, "end": 36.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "morta.", "start": 36.479, "end": 36.999, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 36.999, "end": 39.2, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 39.2, "end": 39.599, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 39.599, "end": 39.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "potuto", "start": 39.599, "end": 39.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 39.899, "end": 39.899, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "attendere", "start": 39.899, "end": 40.279, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 40.279, "end": 40.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Rosario", "start": 40.319, "end": 40.84, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 40.84, "end": 41.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "e", "start": 41.739, "end": 41.839, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 41.839, "end": 41.86, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "raccontar<PERSON>", "start": 41.86, "end": 42.42, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 42.42, "end": 42.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tutto", "start": 42.479, "end": 42.74, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 42.74, "end": 42.759, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "prima", "start": 42.759, "end": 42.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 42.959, "end": 42.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 42.979, "end": 43.04, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 43.04, "end": 43.079, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "fare", "start": 43.079, "end": 43.22, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 43.22, "end": 43.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "una", "start": 43.239, "end": 43.36, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 43.36, "end": 43.36, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "cosa", "start": 43.36, "end": 43.58, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 43.58, "end": 43.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "simile,", "start": 43.619, "end": 44.079, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 44.079, "end": 45.039, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ma", "start": 45.039, "end": 45.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 45.159, "end": 45.18, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "era", "start": 45.18, "end": 45.34, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 45.34, "end": 45.36, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "troppo", "start": 45.36, "end": 45.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 45.659, "end": 45.7, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "buona", "start": 45.7, "end": 46.059, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 46.059, "end": 46.099, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "e", "start": 46.099, "end": 46.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 46.199, "end": 46.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "non", "start": 46.219, "end": 46.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 46.319, "end": 46.34, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ebbe", "start": 46.34, "end": 46.52, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 46.52, "end": 46.52, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "il", "start": 46.52, "end": 46.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 46.619, "end": 46.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "coraggio", "start": 46.619, "end": 47.0, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 47.0, "end": 47.02, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 47.02, "end": 47.1, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 47.1, "end": 47.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "farmi", "start": 47.119, "end": 47.38, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 47.38, "end": 47.399, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "del", "start": 47.399, "end": 47.5, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 47.5, "end": 47.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "male.", "start": 47.559, "end": 47.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 47.979, "end": 48.399, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Vin,", "start": 48.399, "end": 48.78, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 48.78, "end": 49.5, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "lo", "start": 49.5, "end": 49.599, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 49.599, "end": 49.619, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sai", "start": 49.619, "end": 49.839, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 49.839, "end": 49.84, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "che", "start": 49.84, "end": 49.959, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 49.959, "end": 49.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "quanto", "start": 49.979, "end": 50.219, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 50.219, "end": 50.219, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mi", "start": 50.219, "end": 50.319, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 50.319, "end": 50.319, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "hai", "start": 50.319, "end": 50.439, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 50.439, "end": 50.459, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "detto", "start": 50.459, "end": 50.68, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 50.68, "end": 50.68, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "è", "start": 50.68, "end": 50.779, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 50.779, "end": 50.779, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "più", "start": 50.779, "end": 50.899, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 50.899, "end": 50.899, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "che", "start": 50.899, "end": 51.02, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 51.02, "end": 51.02, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sufficiente", "start": 51.02, "end": 51.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 51.559, "end": 51.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "per", "start": 51.559, "end": 51.719, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 51.719, "end": 51.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>ti", "start": 51.739, "end": 51.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 51.979, "end": 52.0, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "arrestare?", "start": 52.0, "end": 52.639, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 52.639, "end": 52.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "E", "start": 52.639, "end": 52.739, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 52.739, "end": 52.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 52.739, "end": 52.86, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 52.86, "end": 52.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "cosa", "start": 52.919, "end": 53.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 53.119, "end": 53.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "posso", "start": 53.119, "end": 53.34, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 53.34, "end": 53.399, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "farci", "start": 53.399, "end": 53.68, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 53.68, "end": 53.68, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ormai?", "start": 53.68, "end": 54.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 54.159, "end": 54.579, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 54.579, "end": 54.84, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 54.84, "end": 54.86, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "è", "start": 54.86, "end": 54.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 54.899, "end": 54.899, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "andato", "start": 54.899, "end": 55.18, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 55.18, "end": 55.18, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "storto.", "start": 55.18, "end": 55.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 55.759, "end": 56.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Era", "start": 56.479, "end": 56.68, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 56.68, "end": 56.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "destino.", "start": 56.699, "end": 57.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 57.239, "end": 57.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 57.979, "end": 58.34, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 58.34, "end": 58.359, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 58.359, "end": 58.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 58.659, "end": 58.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ricadere", "start": 58.659, "end": 59.079, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 59.079, "end": 59.099, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "su", "start": 59.099, "end": 59.2, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 59.2, "end": 59.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 59.219, "end": 59.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 59.319, "end": 59.34, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "me.", "start": 59.34, "end": 59.58, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 59.58, "end": 60.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "La", "start": 60.559, "end": 60.68, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 60.68, "end": 60.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "morte", "start": 60.699, "end": 61.0, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 61.0, "end": 61.02, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 61.02, "end": 61.099, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 61.099, "end": 61.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Carmel<PERSON>", "start": 61.139, "end": 61.599, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 61.599, "end": 61.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 61.619, "end": 61.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 61.919, "end": 61.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "portarmi", "start": 61.919, "end": 62.42, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 62.42, "end": 62.439, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "solo", "start": 62.439, "end": 62.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 62.659, "end": 62.68, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sve--", "start": 62.68, "end": 63.059, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 63.059, "end": 63.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}], "language_detected": null, "confidence": null, "processing_time": 6.986489772796631, "metadata": {"service": "elevenlabs", "mode": "free", "model": "scribe_v1", "raw_result": {"language_code": "ita", "language_probability": 0.998618483543396, "text": "La trovarono morta. <PERSON><PERSON><PERSON> potuto attendere Rosario e raccontargli tutto prima di fare una cosa simile, ma era troppo buona e non ebbe il coraggio di farmi del male. Vin, lo sai che quanto mi hai detto è più che sufficiente per farti arrestare? E che cosa posso farci ormai? Tutto è andato storto. Era destino. Tutto doveva ricadere su di me. La morte di Carmela poteva portarmi solo sve-- ", "words": [{"text": "La", "start": 35.84, "end": 35.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 35.979, "end": 36.0, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "trovarono", "start": 36.0, "end": 36.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 36.479, "end": 36.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "morta.", "start": 36.479, "end": 36.999, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 36.999, "end": 39.2, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 39.2, "end": 39.599, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 39.599, "end": 39.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "potuto", "start": 39.599, "end": 39.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 39.899, "end": 39.899, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "attendere", "start": 39.899, "end": 40.279, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 40.279, "end": 40.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Rosario", "start": 40.319, "end": 40.84, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 40.84, "end": 41.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "e", "start": 41.739, "end": 41.839, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 41.839, "end": 41.86, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "raccontar<PERSON>", "start": 41.86, "end": 42.42, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 42.42, "end": 42.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tutto", "start": 42.479, "end": 42.74, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 42.74, "end": 42.759, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "prima", "start": 42.759, "end": 42.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 42.959, "end": 42.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 42.979, "end": 43.04, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 43.04, "end": 43.079, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "fare", "start": 43.079, "end": 43.22, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 43.22, "end": 43.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "una", "start": 43.239, "end": 43.36, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 43.36, "end": 43.36, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "cosa", "start": 43.36, "end": 43.58, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 43.58, "end": 43.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "simile,", "start": 43.619, "end": 44.079, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 44.079, "end": 45.039, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ma", "start": 45.039, "end": 45.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 45.159, "end": 45.18, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "era", "start": 45.18, "end": 45.34, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 45.34, "end": 45.36, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "troppo", "start": 45.36, "end": 45.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 45.659, "end": 45.7, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "buona", "start": 45.7, "end": 46.059, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 46.059, "end": 46.099, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "e", "start": 46.099, "end": 46.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 46.199, "end": 46.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "non", "start": 46.219, "end": 46.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 46.319, "end": 46.34, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ebbe", "start": 46.34, "end": 46.52, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 46.52, "end": 46.52, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "il", "start": 46.52, "end": 46.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 46.619, "end": 46.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "coraggio", "start": 46.619, "end": 47.0, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 47.0, "end": 47.02, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 47.02, "end": 47.1, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 47.1, "end": 47.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "farmi", "start": 47.119, "end": 47.38, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 47.38, "end": 47.399, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "del", "start": 47.399, "end": 47.5, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 47.5, "end": 47.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "male.", "start": 47.559, "end": 47.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 47.979, "end": 48.399, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Vin,", "start": 48.399, "end": 48.78, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 48.78, "end": 49.5, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "lo", "start": 49.5, "end": 49.599, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 49.599, "end": 49.619, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sai", "start": 49.619, "end": 49.839, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 49.839, "end": 49.84, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "che", "start": 49.84, "end": 49.959, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 49.959, "end": 49.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "quanto", "start": 49.979, "end": 50.219, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 50.219, "end": 50.219, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mi", "start": 50.219, "end": 50.319, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 50.319, "end": 50.319, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "hai", "start": 50.319, "end": 50.439, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 50.439, "end": 50.459, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "detto", "start": 50.459, "end": 50.68, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 50.68, "end": 50.68, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "è", "start": 50.68, "end": 50.779, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 50.779, "end": 50.779, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "più", "start": 50.779, "end": 50.899, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 50.899, "end": 50.899, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "che", "start": 50.899, "end": 51.02, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 51.02, "end": 51.02, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sufficiente", "start": 51.02, "end": 51.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 51.559, "end": 51.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "per", "start": 51.559, "end": 51.719, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 51.719, "end": 51.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>ti", "start": 51.739, "end": 51.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 51.979, "end": 52.0, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "arrestare?", "start": 52.0, "end": 52.639, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 52.639, "end": 52.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "E", "start": 52.639, "end": 52.739, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 52.739, "end": 52.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "che", "start": 52.739, "end": 52.86, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 52.86, "end": 52.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "cosa", "start": 52.919, "end": 53.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 53.119, "end": 53.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "posso", "start": 53.119, "end": 53.34, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 53.34, "end": 53.399, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "farci", "start": 53.399, "end": 53.68, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 53.68, "end": 53.68, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ormai?", "start": 53.68, "end": 54.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 54.159, "end": 54.579, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 54.579, "end": 54.84, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 54.84, "end": 54.86, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "è", "start": 54.86, "end": 54.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 54.899, "end": 54.899, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "andato", "start": 54.899, "end": 55.18, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 55.18, "end": 55.18, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "storto.", "start": 55.18, "end": 55.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 55.759, "end": 56.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Era", "start": 56.479, "end": 56.68, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 56.68, "end": 56.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "destino.", "start": 56.699, "end": 57.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 57.239, "end": 57.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 57.979, "end": 58.34, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 58.34, "end": 58.359, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 58.359, "end": 58.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 58.659, "end": 58.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ricadere", "start": 58.659, "end": 59.079, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 59.079, "end": 59.099, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "su", "start": 59.099, "end": 59.2, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 59.2, "end": 59.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 59.219, "end": 59.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 59.319, "end": 59.34, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "me.", "start": 59.34, "end": 59.58, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 59.58, "end": 60.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "La", "start": 60.559, "end": 60.68, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 60.68, "end": 60.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "morte", "start": 60.699, "end": 61.0, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 61.0, "end": 61.02, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "di", "start": 61.02, "end": 61.099, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 61.099, "end": 61.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Carmel<PERSON>", "start": 61.139, "end": 61.599, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 61.599, "end": 61.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 61.619, "end": 61.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 61.919, "end": 61.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "portarmi", "start": 61.919, "end": 62.42, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 62.42, "end": 62.439, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "solo", "start": 62.439, "end": 62.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 62.659, "end": 62.68, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sve--", "start": 62.68, "end": 63.059, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 63.059, "end": 63.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}]}}, "created_at": 1754319815.8037748}