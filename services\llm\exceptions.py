"""
LLM服务异常类

定义LLM API调用和处理过程中的异常类型。
提供详细的错误信息和错误代码以便于调试和错误处理。
"""

import time
from typing import Dict, Any, Optional


class LLMError(Exception):
    """LLM服务基础异常类
    
    所有LLM相关异常的基类，提供统一的异常处理接口和错误信息格式。
    """
    
    def __init__(self, message: str, error_code: str = None, details: dict = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        self.timestamp = time.time()

    def __str__(self):
        if self.error_code:
            return f"[{self.error_code}] {self.message}"
        return self.message



