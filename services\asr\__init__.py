"""
语音识别服务模块

提供统一的ASR服务接口，支持多种语音识别提供商：
- ElevenLabs：API模式和免费模式
- AssemblyAI：官方Python SDK
- Deepgram：官方Python SDK

主要功能：
- 统一服务接口和数据模型
- 服务管理和实例创建
- 转录结果解析和格式化
- 标准化异常处理
"""

# 基础组件
from .base.interface import (
    ASRServiceInterface,
    ASRRequest,
    ASRResponse,
    ASRStatus
)

from .base.models import (
    TimestampedWord,
    TranscriptionMetadata,
    ASRServiceConfig
)

from .base.exceptions import (
    ASRException,
    ASRConfigurationError
)

# 服务工厂
from .manager import create_asr_service



# ASR服务实现
from .elevenlabs import ElevenLabsASRService, ElevenLabsConfig
from .assemblyai import AssemblyAIASRService, AssemblyAIConfig
from .deepgram import DeepgramASRService, DeepgramConfig

# 注意：已在上面导入，这里删除重复导入

# 转录解析器
from .parser import (
    TranscriptionParser,
    ParsedTranscription
)

# 版本信息
__version__ = "1.0.0"
__author__ = "EvaTrans Team"

# 导出主要接口
__all__ = [
    # 基础接口
    'ASRServiceInterface',
    'ASRRequest',
    'ASRResponse',
    'ASRStatus',

    # 数据模型
    'TimestampedWord',
    'TranscriptionMetadata',
    'ASRServiceConfig',

    # 异常类
    'ASRException',
    'ASRConfigurationError',

    # ASR服务实现
    'ElevenLabsASRService',
    'ElevenLabsConfig',
    'AssemblyAIASRService',
    'AssemblyAIConfig',
    'DeepgramASRService',
    'DeepgramConfig',

    # 服务创建和管理
    'create_asr_service',

    # 转录解析器
    'TranscriptionParser',
    'ParsedTranscription'
]
