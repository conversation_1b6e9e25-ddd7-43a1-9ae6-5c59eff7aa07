"""
工具函数模块

提供幻觉修复工具需要的各种工具函数。
"""

import re
import os
import tempfile
from typing import List, Tuple, Optional
from .models import SubtitleEntry


def parse_srt_time(time_str: str) -> float:
    """解析SRT时间戳为秒数
    
    Args:
        time_str: SRT格式时间戳 (HH:MM:SS,mmm)
        
    Returns:
        float: 总秒数
    """
    try:
        time_part, ms_part = time_str.split(',')
        h, m, s = map(int, time_part.split(':'))
        ms = int(ms_part)
        return h * 3600 + m * 60 + s + ms / 1000.0
    except (ValueError, IndexError):
        return 0.0


def calculate_entry_duration(entry: SubtitleEntry) -> float:
    """计算字幕条目的持续时间（秒）
    
    Args:
        entry: 字幕条目
        
    Returns:
        float: 持续时间（秒）
    """
    start_seconds = parse_srt_time(entry.start_time)
    end_seconds = parse_srt_time(entry.end_time)
    return end_seconds - start_seconds


def format_duration(seconds: float) -> str:
    """格式化持续时间为可读字符串
    
    Args:
        seconds: 秒数
        
    Returns:
        str: 格式化的时间字符串
    """
    if seconds < 60:
        return f"{seconds:.1f}秒"
    elif seconds < 3600:
        minutes = int(seconds // 60)
        secs = seconds % 60
        return f"{minutes}分{secs:.1f}秒"
    else:
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = seconds % 60
        return f"{hours}时{minutes}分{secs:.1f}秒"


def parse_srt_content(srt_content: str) -> List[SubtitleEntry]:
    """解析SRT文件内容为字幕条目列表
    
    Args:
        srt_content: SRT文件内容
        
    Returns:
        List[SubtitleEntry]: 字幕条目列表
    """
    entries = []
    
    # SRT条目正则表达式
    srt_pattern = re.compile(
        r'(\d+)\n(\d{2}:\d{2}:\d{2},\d{3}) --> (\d{2}:\d{2}:\d{2},\d{3})\n(.*?)(?=\n\d+\n|\n*$)',
        re.DOTALL
    )
    
    matches = srt_pattern.findall(srt_content)
    
    for match in matches:
        index, start_time, end_time, text = match
        
        # 清理文本内容
        text = text.strip()
        
        entries.append(SubtitleEntry(
            index=int(index),
            start_time=start_time,
            end_time=end_time,
            text=text
        ))
    
    return entries


def find_long_entries(entries: List[SubtitleEntry], threshold_seconds: float = 15.0) -> List[SubtitleEntry]:
    """查找超过阈值的长条目
    
    Args:
        entries: 字幕条目列表
        threshold_seconds: 时长阈值（秒）
        
    Returns:
        List[SubtitleEntry]: 长条目列表
    """
    long_entries = []
    
    for entry in entries:
        duration = calculate_entry_duration(entry)
        if duration > threshold_seconds:
            long_entries.append(entry)
    
    return long_entries


def create_temp_directory() -> str:
    """创建临时目录
    
    Returns:
        str: 临时目录路径
    """
    return tempfile.mkdtemp(prefix="hallucination_fix_")


def cleanup_temp_files(temp_dir: str) -> None:
    """清理临时文件
    
    Args:
        temp_dir: 临时目录路径
    """
    try:
        import shutil
        if os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)
    except Exception:
        pass  # 静默处理清理失败


def detect_service_from_filename(filename: str) -> Tuple[Optional[str], Optional[str]]:
    """从文件名检测ASR和LLM服务
    
    Args:
        filename: 字幕文件名
        
    Returns:
        Tuple[Optional[str], Optional[str]]: (ASR服务, LLM服务)
    """
    # 解析格式：filename-ASRService-LLMService.srt
    pattern = r'(.+)-([^-]+)-([^-]+)\.srt$'
    match = re.match(pattern, filename)
    
    if match:
        _, asr_service, llm_service = match.groups()
        return asr_service.lower(), llm_service.lower()
    
    return None, None


def generate_output_filename(original_filename: str, suffix: str = "_fixed") -> str:
    """生成输出文件名
    
    Args:
        original_filename: 原始文件名
        suffix: 后缀
        
    Returns:
        str: 输出文件名
    """
    name, ext = os.path.splitext(original_filename)
    return f"{name}{suffix}{ext}"
