"""
音频处理器 - WAV一体化处理引擎

专门为WAV统一输出设计的极简处理器。
集成分析、声道处理、转换为一体化流程。

核心功能：
- FFprobe声道检测：提取声道数和时长信息
- 专业声道处理：多声道到立体声的高质量降混
- WAV转换：使用PCM 16-bit的固定参数转换
- 一体化接口：process_file(input, output) → result

技术特性：
- 无外部依赖：所有功能内联实现
- 固定参数：硬编码最优配置
- 专业滤镜：基于音频工程学的声道降混
- 简化错误：统一异常处理
"""

import os
import subprocess
import json
import time
from typing import Optional
from dataclasses import dataclass

from .exceptions import AudioError, AudioExceptions
from .logger_mixin import AudioLoggerMixin


# ============================================================================
# 数据模型定义
# ============================================================================

@dataclass
class ProcessingResult:
    """音频处理结果（简化版）

    包含处理状态、文件信息、性能数据和错误信息。
    专门为WAV统一输出设计。
    """
    success: bool = False
    input_path: str = ""
    output_path: str = ""

    # 音频信息
    channels: int = 0
    duration_seconds: Optional[float] = None

    # 性能信息
    processing_time_seconds: float = 0.0
    input_size_bytes: int = 0
    output_size_bytes: int = 0

    # 错误信息
    error_message: str = ""


# ValidationResult类已删除 - 不再需要预验证，直接在process_file中处理


# 使用统一异常+日志体系


# ============================================================================
# 声道处理配置映射表
# ============================================================================

# 专业声道降混滤镜映射表（基于音频工程学原理）
CHANNEL_FILTERS = {
    1: None,  # 单声道保持不变
    2: None,  # 立体声保持不变
    3: "pan=stereo|FL=FL+0.5*LFE|FR=FR+0.5*LFE",  # 2.1环绕
    4: "pan=stereo|FL=0.7*FL+0.3*BL|FR=0.7*FR+0.3*BR",  # 4.0环绕
    5: "pan=stereo|FL=0.6*FL+0.3*BL+0.4*LFE|FR=0.6*FR+0.3*BR+0.4*LFE",  # 4.1环绕
    6: "pan=stereo|FL=0.5*FL+0.707*FC+0.5*BL+0.3*LFE|FR=0.5*FR+0.707*FC+0.5*BR+0.3*LFE",  # 5.1环绕
    7: "pan=stereo|FL=0.4*FL+0.6*FC+0.4*BL+0.1*BC+0.3*LFE|FR=0.4*FR+0.6*FC+0.4*BR+0.1*BC+0.3*LFE",  # 6.1环绕
    8: "pan=stereo|FL=0.4*FL+0.6*FC+0.4*BL+0.2*SL+0.3*LFE|FR=0.4*FR+0.6*FC+0.4*BR+0.2*SR+0.3*LFE",  # 7.1环绕
}

# 超过8声道的降级处理滤镜（兜底策略）
FALLBACK_FILTER = "pan=stereo|FL=0.4*FL+0.6*FC+0.4*BL+0.2*SL+0.3*LFE|FR=0.4*FR+0.6*FC+0.4*BR+0.2*SR+0.3*LFE"


# ============================================================================
# 音频处理器主类
# ============================================================================

class AudioProcessor(AudioLoggerMixin):
    """音频处理器 - WAV一体化处理引擎

    集成FFprobe分析、声道处理、FFmpeg转换为统一接口。
    专门为WAV统一输出优化，使用分离的异常+日志体系。

    固定配置：
    - FFmpeg: PCM 16-bit, 立体声, 4线程
    - FFprobe: JSON输出，30秒超时
    - 声道处理: 专业降混滤镜，自动检测目标声道
    """

    # 固定配置常量
    FFMPEG_PATH = "ffmpeg"
    FFPROBE_PATH = "ffprobe"
    WAV_BIT_DEPTH = "16"
    THREADS = "4"
    TIMEOUT = 3600  # FFmpeg超时（秒）
    PROBE_TIMEOUT = 30  # FFprobe超时（秒）

    def __init__(self):
        """初始化一体化音频处理器"""
        super().__init__()
    
    # validate_file方法已删除 - 音频流验证已集成到process_file中

    def process_file(self, input_path: str, output_path: str,
                    progress_callback=None) -> ProcessingResult:
        """处理音频文件为WAV（简化进度回调）

        一体化处理流程：
        1. 音频流验证和信息提取
        2. 声道处理滤镜生成
        3. FFmpeg转换为WAV
        4. 结果统计和返回

        Args:
            input_path: 输入音频文件路径
            output_path: 输出WAV文件路径
            progress_callback: 可选的进度回调函数，只报告开始(0%)和完成(100%)

        Returns:
            ProcessingResult: 处理结果和统计信息
        """
        start_time = time.time()

        # 确保输出路径以.wav结尾（在try块外处理，确保异常时也能返回正确路径）
        if not output_path.lower().endswith('.wav'):
            output_path = os.path.splitext(output_path)[0] + '.wav'

        self._log_info(f"开始处理: {os.path.basename(input_path)}")

        try:
            # 快速检测：如果是16bit WAV立体声，直接复制
            if self._can_direct_copy(input_path):
                return self._direct_copy(input_path, output_path, progress_callback)

            if progress_callback:
                progress_callback(0, "开始处理")

            # 1. 音频流验证和信息提取（集成验证）
            # 使用FFprobe进行音频流验证，自然检查文件存在性和有效性
            channels, duration = self._probe_audio_info(input_path)

            if channels <= 0:
                raise AudioExceptions.input_file_invalid(input_path, "无效声道数")



            # 确保输出目录存在
            output_dir = os.path.dirname(output_path)
            if output_dir:
                os.makedirs(output_dir, exist_ok=True)

            # 2. 生成声道处理滤镜
            channel_filter = self._get_channel_filter(channels)

            # 3. FFmpeg转换
            self._convert_to_wav(input_path, output_path, channel_filter)

            # 4. 获取输出文件信息（信任FFmpeg执行结果）
            output_size = os.path.getsize(output_path)
            processing_time = time.time() - start_time

            self._log_info(f"处理完成: {os.path.basename(output_path)} ({processing_time:.1f}s)")

            if progress_callback:
                progress_callback(100, "处理完成")

            return ProcessingResult(
                success=True,
                input_path=input_path,
                output_path=output_path,
                channels=channels,
                duration_seconds=duration,
                processing_time_seconds=processing_time,
                input_size_bytes=0,  # 不再获取输入文件大小
                output_size_bytes=output_size
            )
            
        except AudioError as e:
            # 手动记录异常日志
            self._log_exception(e)
            processing_time = time.time() - start_time
            return ProcessingResult(
                success=False,
                input_path=input_path,
                output_path=output_path,
                processing_time_seconds=processing_time,
                error_message=e.user_message
            )
        except Exception as e:
            # 未预期异常，手动记录日志
            processing_time = time.time() - start_time
            error_msg = f"未知错误: {str(e)}"
            self._log_error(f"处理失败: {error_msg}")

            return ProcessingResult(
                success=False,
                input_path=input_path,
                output_path=output_path,
                processing_time_seconds=processing_time,
                error_message=error_msg
            )
    
    def _can_direct_copy(self, input_path: str) -> bool:
        """检测是否可以直接复制（16bit WAV立体声）"""
        if not input_path.lower().endswith('.wav'):
            return False

        try:
            cmd = [
                self.FFPROBE_PATH, '-v', 'quiet',
                '-show_entries', 'stream=codec_name,channels',
                '-of', 'csv=p=0', input_path
            ]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=self.PROBE_TIMEOUT)

            if result.returncode == 0:
                info = result.stdout.strip().split(',')
                codec = info[0] if len(info) > 0 else ''
                channels = int(info[1]) if len(info) > 1 else 0

                return codec in ['pcm_s16le', 'pcm_s16be'] and channels == 2
        except:
            pass

        return False

    def _direct_copy(self, input_path: str, output_path: str, progress_callback=None) -> ProcessingResult:
        """直接复制16bit WAV文件"""
        start_time = time.time()

        try:
            # 确保输出目录存在
            os.makedirs(os.path.dirname(output_path), exist_ok=True)

            if progress_callback:
                progress_callback(0)

            # 使用FFmpeg复制，保持一致性
            cmd = [
                self.FFMPEG_PATH,
                '-i', input_path,
                '-c', 'copy',
                '-y', output_path
            ]

            result = subprocess.run(cmd, capture_output=True, text=False, timeout=self.TIMEOUT)

            if result.returncode != 0:
                raise AudioExceptions.ffmpeg_execution_failed(f"FFmpeg复制失败: {result.stderr.decode('utf-8', errors='ignore')}")

            if progress_callback:
                progress_callback(100)

            # 获取文件大小
            input_size = os.path.getsize(input_path)
            output_size = os.path.getsize(output_path)
            processing_time = time.time() - start_time

            self._log_info(f"直接复制: {os.path.basename(output_path)} ({processing_time:.1f}s)")

            return ProcessingResult(
                success=True,
                input_path=input_path,
                output_path=output_path,
                channels=2,  # 已知是立体声
                duration_seconds=None,  # 不获取时长信息
                processing_time_seconds=processing_time,
                input_size_bytes=input_size,
                output_size_bytes=output_size
            )

        except Exception as e:
            processing_time = time.time() - start_time
            error_msg = str(e)
            self._log_error(f"直接复制失败: {error_msg}")

            return ProcessingResult(
                success=False,
                input_path=input_path,
                output_path=output_path,
                error_message=error_msg,
                processing_time_seconds=processing_time
            )

    def _probe_audio_info(self, file_path: str) -> tuple[int, Optional[float]]:
        """使用FFprobe检测音频信息

        Args:
            file_path: 音频文件路径

        Returns:
            tuple: (声道数, 时长秒数)

        Raises:
            AudioProcessingError: 检测失败时抛出异常
        """
        try:
            # 构建FFprobe命令
            cmd = [
                self.FFPROBE_PATH,
                '-v', 'quiet',
                '-print_format', 'json',
                '-show_streams',
                '-select_streams', 'a:0',  # 只选择第一个音频流
                file_path
            ]

            # 执行FFprobe
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=False,
                timeout=self.PROBE_TIMEOUT
            )

            # 手动解码输出，避免编码问题
            stdout_text = result.stdout.decode('utf-8', errors='ignore') if result.stdout else ""
            stderr_text = result.stderr.decode('utf-8', errors='ignore') if result.stderr else ""

            if result.returncode != 0:
                # 根据错误类型抛出相应异常
                stderr = stderr_text.lower()
                if "no such file" in stderr:
                    raise AudioExceptions.input_file_not_found(file_path)
                elif "invalid data" in stderr or "format" in stderr:
                    raise AudioExceptions.input_file_invalid(file_path, stderr_text)
                else:
                    raise AudioExceptions.process_analysis_failed(file_path, stderr_text)

            # 解析JSON结果
            try:
                data = json.loads(stdout_text)
            except json.JSONDecodeError:
                raise AudioExceptions.input_file_corrupted(file_path)

            streams = data.get('streams', [])
            if not streams:
                raise AudioExceptions.input_file_invalid(file_path, "未找到音频流")

            audio_stream = streams[0]

            # 提取声道数
            channels = audio_stream.get('channels', 2)
            if channels <= 0:
                raise AudioExceptions.input_file_invalid(file_path, "无效声道数")

            # 提取时长
            duration_str = audio_stream.get('duration')
            duration = float(duration_str) if duration_str else None

            return channels, duration

        except FileNotFoundError:
            raise AudioExceptions.system_ffmpeg_missing()
        except subprocess.TimeoutExpired:
            raise AudioExceptions.process_timeout(file_path, self.PROBE_TIMEOUT)
        except PermissionError:
            raise AudioExceptions.system_no_permission(file_path)
    
    def _get_channel_filter(self, channels: int) -> Optional[str]:
        """获取声道处理滤镜

        基于专业音频工程学原理的声道降混滤镜。
        支持1-8声道的精确处理，超过8声道使用兜底策略。

        Args:
            channels: 输入声道数

        Returns:
            Optional[str]: FFmpeg声道滤镜字符串，None表示无需处理
        """
        if channels <= 2:
            # 单声道和立体声无需处理
            return None
        elif channels <= 8:
            # 使用专业降混滤镜映射表
            return CHANNEL_FILTERS.get(channels)
        else:
            # 超过8声道使用兜底策略
            return FALLBACK_FILTER
    
    def _convert_to_wav(self, input_path: str, output_path: str,
                       channel_filter: Optional[str]) -> None:
        """转换音频为WAV

        使用PCM编码器，16-bit立体声，4线程并行处理。

        Args:
            input_path: 输入文件路径
            output_path: 输出文件路径
            channel_filter: 声道处理滤镜

        Raises:
            AudioProcessingError: 转换失败时抛出异常
        """
        try:
            # 构建FFmpeg命令
            cmd = [
                self.FFMPEG_PATH,
                '-i', input_path,
                '-c:a', 'pcm_s16le',  # 使用PCM 16-bit编码器
                '-ac', '2',  # 立体声
                '-threads', self.THREADS,  # 4线程并行
                '-y',  # 覆盖输出文件
            ]

            # 添加声道处理滤镜（如果需要）
            if channel_filter:
                cmd.extend(['-af', channel_filter])

            # 添加输出文件路径
            cmd.append(output_path)

            # 执行FFmpeg转换
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=False,
                timeout=self.TIMEOUT
            )

            # 手动解码stderr，避免编码问题
            stderr_text = result.stderr.decode('utf-8', errors='ignore') if result.stderr else ""

            if result.returncode != 0:
                stderr = stderr_text.lower()
                if "no space left" in stderr or "disk full" in stderr:
                    raise AudioExceptions.system_no_space(output_path)
                elif "permission denied" in stderr:
                    raise AudioExceptions.system_no_permission(output_path)
                else:
                    raise AudioExceptions.process_conversion_failed(input_path, stderr_text)

        except FileNotFoundError:
            raise AudioExceptions.system_ffmpeg_missing()
        except subprocess.TimeoutExpired:
            raise AudioExceptions.process_timeout(input_path, self.TIMEOUT)
        except PermissionError:
            raise AudioExceptions.system_no_permission(output_path)

