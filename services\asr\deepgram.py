"""
Deepgram语音识别服务
使用官方Python SDK进行音频转录
"""

import os
import time
from typing import Dict, Any, List, Optional

from .base.interface import ASRServiceInterface, ASRRequest, ASRResponse, ASRStatus
from .base.models import ASRServiceConfig, TimestampedWord
from .base.exceptions import ASRException, ASRConfigurationError


class DeepgramConfig(ASRServiceConfig):
    """Deepgram配置类"""

    def __init__(self, service_name: str = "deepgram", api_key: str = "", **kwargs):
        # 防御性清理UI字段
        kwargs = self._clean_ui_fields(kwargs)

        # Deepgram特定参数提取（支持新旧字段名）
        deepgram_model = kwargs.pop('model', 'nova-3')
        deepgram_language = kwargs.pop('language', 'auto')

        # 说话人分离：支持新字段名 enable_diarization 和旧字段名 diarize
        self.enable_diarization = kwargs.pop('enable_diarization', kwargs.pop('diarize', False))

        # 标点符号：支持新字段名 enable_punctuation 和旧字段名 punctuate
        self.enable_punctuation = kwargs.pop('enable_punctuation', kwargs.pop('punctuate', True))

        # 其他Deepgram特定设置
        self.utterances = kwargs.pop('utterances', True)
        self.smart_format = kwargs.pop('smart_format', True)
        self.paragraphs = kwargs.pop('paragraphs', True)
        self.detect_language = kwargs.pop('detect_language', True)

        # 父类初始化
        super().__init__(
            service_name=service_name,
            api_key=api_key,
            model=deepgram_model,
            language=deepgram_language,
            enable_diarization=self.enable_diarization,
            enable_punctuation=self.enable_punctuation,
            **kwargs
        )

    def validate(self) -> tuple[bool, List[str]]:
        """配置验证"""
        is_valid, errors = super().validate()

        # Deepgram特定验证（目前无额外验证规则）

        return len(errors) == 0, errors


class DeepgramASRService(ASRServiceInterface):
    """Deepgram ASR服务"""
    
    def __init__(self, config: Dict[str, Any]):
        """初始化服务

        Args:
            config: 服务配置
        """
        # 配置对象创建
        if isinstance(config, dict):
            self.config = DeepgramConfig(**config)
        else:
            self.config = config
        
        # 配置验证
        is_valid, errors = self.config.validate()
        if not is_valid:
            raise ASRConfigurationError(f"Deepgram配置无效: {'; '.join(errors)}", "deepgram")

        self.client = None

    @property
    def service_name(self) -> str:
        return "deepgram"
    

    
    def transcribe(self, request: ASRRequest) -> ASRResponse:
        """转录执行"""
        # 重试逻辑
        max_retries = self.config.max_retries
        last_exception = None

        for attempt in range(max_retries + 1):
            try:
                if attempt > 0:
                    wait_time = 5 * attempt  # 5, 10, 15, 20, 25秒（SDK调用间隔更长）
                    print(f"[Deepgram] SDK重试第{attempt}次，等待{wait_time}秒...")
                    time.sleep(wait_time)

                print(f"[Deepgram] SDK转录尝试 {attempt + 1}/{max_retries + 1}")

                start_time = time.time()
                transcription_id = f"deepgram_{int(start_time)}"

                # 导入Deepgram SDK
                from deepgram import DeepgramClient, PrerecordedOptions, FileSource

                # 客户端初始化
                self.client = DeepgramClient(self.config.api_key)

                # 转录选项创建
                options = self._create_transcription_options(request, PrerecordedOptions)

                # 音频文件读取
                with open(request.audio_file_path, 'rb') as file:
                    buffer_data = file.read()

                payload: FileSource = {"buffer": buffer_data}

                # 转录执行
                response = self.client.listen.prerecorded.v("1").transcribe_file(payload, options)

                # 结果解析
                print(f"[Deepgram] SDK转录成功 (尝试 {attempt + 1})")
                return self._parse_transcription_result(response, transcription_id, start_time)

            except Exception as e:
                last_exception = e
                print(f"[Deepgram] SDK调用异常 (尝试 {attempt + 1}/{max_retries + 1}): {str(e)}")
                if attempt == max_retries:
                    # 包装为ASR异常
                    raise ASRException(f"SDK调用失败: {str(e)}", "deepgram", "SDK_ERROR") from e

        # 理论上不会到达这里
        if last_exception:
            raise ASRException(f"重试耗尽: {str(last_exception)}", "deepgram", "RETRY_EXHAUSTED") from last_exception
        else:
            raise ASRException("转录重试耗尽", "deepgram", "RETRY_EXHAUSTED")
    
    def _create_transcription_options(self, request: ASRRequest, PrerecordedOptions) -> 'PrerecordedOptions':
        """转录选项创建"""
        options_dict = {
            'model': self.config.model,
            'diarize': request.enable_diarization or self.config.enable_diarization,
            'punctuate': request.enable_punctuation and self.config.enable_punctuation,
            'utterances': self.config.utterances,
            'smart_format': self.config.smart_format,
            'paragraphs': self.config.paragraphs,
        }
        
        # 语言配置
        language = request.language or self.config.language
        if language == 'auto':
            options_dict['detect_language'] = True
        else:
            options_dict['language'] = language
            options_dict['detect_language'] = False
        
        return PrerecordedOptions(**options_dict)
    

    
    def _parse_transcription_result(self, response, transcription_id: str, start_time: float) -> ASRResponse:
        """转录结果解析"""
        # 基本信息提取
        full_text = ""
        words = []
        language_detected = None
        confidence = None

        # 响应结构检查

        # 转录文本和详细信息提取
        if (hasattr(response.results, 'channels') and
            response.results.channels and
            len(response.results.channels) > 0):

            channel = response.results.channels[0]
            if (hasattr(channel, 'alternatives') and
                channel.alternatives and
                len(channel.alternatives) > 0):

                alternative = channel.alternatives[0]

                # 文本提取
                if hasattr(alternative, 'transcript'):
                    full_text = alternative.transcript

                # 置信度提取
                if hasattr(alternative, 'confidence'):
                    confidence = alternative.confidence
                    
                    # 词汇信息提取
                    if hasattr(alternative, 'words') and alternative.words:
                        for word in alternative.words:
                            word_data = {
                                'text': getattr(word, 'word', ''),
                                'start': getattr(word, 'start', 0),
                                'end': getattr(word, 'end', 0),
                                'confidence': getattr(word, 'confidence', 0.0),
                                'speaker': getattr(word, 'speaker', None)
                            }
                            words.append(word_data)

        # 语言检测结果提取
        if hasattr(response.results, 'metadata') and response.results.metadata:
            metadata = response.results.metadata
            if hasattr(metadata, 'detected_language'):
                language_detected = metadata.detected_language

        processing_time = time.time() - start_time

        # 元数据构建
        metadata = {
            "service": "deepgram",
            "model": self.config.model,
            "language_setting": self.config.language,
            "options": {
                "diarize": self.config.enable_diarization,
                "punctuate": self.config.enable_punctuation,
                "utterances": self.config.utterances,
                "smart_format": self.config.smart_format,
                "paragraphs": self.config.paragraphs
            }
        }

        # 原始结果添加
        if hasattr(response.results, 'to_dict'):
            metadata['raw_result'] = response.results.to_dict()
        else:
            metadata['raw_result'] = str(response.results)

        return ASRResponse(
            transcription_id=transcription_id,
            status=ASRStatus.COMPLETED,
            full_text=full_text,
            words=words,
            language_detected=language_detected,
            confidence=confidence,
            processing_time=processing_time,
            metadata=metadata
        )
