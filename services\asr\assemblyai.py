"""
AssemblyAI语音识别服务
使用官方Python SDK进行音频转录
"""

import os
import time
from typing import Dict, Any, List, Optional

from .base.interface import ASRServiceInterface, ASRRequest, ASRResponse, ASRStatus
from .base.models import ASRServiceConfig, TimestampedWord
from .base.exceptions import ASRException, ASRConfigurationError

def safe_getattr(obj, attr: str, default=None):
    """安全属性提取，避免重复getattr调用"""
    return getattr(obj, attr, default)

class AssemblyAIConfig(ASRServiceConfig):
    """AssemblyAI配置类"""
    
    def __init__(self, service_name: str = "assemblyai", api_key: str = "", **kwargs):
        # 防御性清理UI字段
        kwargs = self._clean_ui_fields(kwargs)

        # AssemblyAI特定属性
        self.speech_model = kwargs.pop('speech_model', 'universal')
        self.language_code = kwargs.pop('language_code', 'auto')
        self.language_detection = kwargs.pop('language_detection', True)
        self.speakers_expected = kwargs.pop('speakers_expected', 0)
        self.speaker_labels = kwargs.pop('speaker_labels', True)
        self.punctuate = kwargs.pop('punctuate', True)
        self.format_text = kwargs.pop('format_text', True)
        self.disfluencies = kwargs.pop('disfluencies', False)
        self.sentiment_analysis = kwargs.pop('sentiment_analysis', False)
        self.entity_detection = kwargs.pop('entity_detection', False)
        self.auto_highlights = kwargs.pop('auto_highlights', False)
        self.auto_chapters = kwargs.pop('auto_chapters', False)
        self.summarization = kwargs.pop('summarization', False)
        
        # 父类初始化
        super().__init__(service_name=service_name, api_key=api_key, **kwargs)
    
    def validate(self) -> tuple[bool, List[str]]:
        """配置验证"""
        is_valid, errors = super().validate()

        # AssemblyAI特定验证
        if not 0 <= self.speakers_expected <= 10:
            errors.append("说话人数量必须在0-10之间")

        return len(errors) == 0, errors


class AssemblyAIASRService(ASRServiceInterface):
    """AssemblyAI ASR服务"""
    
    def __init__(self, config: Dict[str, Any]):
        """初始化服务

        Args:
            config: 服务配置
        """
        # 配置对象创建
        if isinstance(config, dict):
            self.config = AssemblyAIConfig(**config)
        else:
            self.config = config
        
        # 配置验证
        is_valid, errors = self.config.validate()
        if not is_valid:
            raise ASRConfigurationError(f"AssemblyAI配置无效: {'; '.join(errors)}", "assemblyai")


        self.transcriber = None

    @property
    def service_name(self) -> str:
        return "assemblyai"
    

    
    def transcribe(self, request: ASRRequest) -> ASRResponse:
        """转录执行"""
        # 重试逻辑
        max_retries = self.config.max_retries
        last_exception = None

        for attempt in range(max_retries + 1):
            try:
                if attempt > 0:
                    wait_time = 5 * attempt  # 5, 10, 15, 20, 25秒（SDK调用间隔更长）
                    print(f"[AssemblyAI] SDK重试第{attempt}次，等待{wait_time}秒...")
                    time.sleep(wait_time)

                print(f"[AssemblyAI] SDK转录尝试 {attempt + 1}/{max_retries + 1}")

                start_time = time.time()
                transcription_id = f"assemblyai_{int(start_time)}"

                # 导入AssemblyAI SDK
                import assemblyai as aai

                # API密钥设置
                aai.settings.api_key = self.config.api_key
                self.transcriber = aai.Transcriber()

                # 转录配置创建
                transcription_config = self._create_transcription_config(request, aai)

                # 转录执行
                transcript = self.transcriber.transcribe(request.audio_file_path, config=transcription_config)

                # 状态检查
                if transcript.status == aai.TranscriptStatus.error:
                    error_msg = f"转录失败: {transcript.error}"
                    print(f"[AssemblyAI] SDK转录状态错误 (尝试 {attempt + 1}/{max_retries + 1}): {error_msg}")

                    # SDK错误，可能值得重试
                    if attempt == max_retries:
                        raise ASRException(error_msg, "assemblyai", "PROCESSING_ERROR")
                    else:
                        continue  # 重试

                # 结果解析
                print(f"[AssemblyAI] SDK转录成功 (尝试 {attempt + 1})")
                return self._parse_transcription_result(transcript, transcription_id, start_time)

            except ASRException:
                # ASR异常直接重新抛出（已经在上面处理过）
                raise

            except Exception as e:
                last_exception = e
                print(f"[AssemblyAI] SDK调用异常 (尝试 {attempt + 1}/{max_retries + 1}): {str(e)}")
                if attempt == max_retries:
                    # 包装为ASR异常
                    raise ASRException(f"SDK调用失败: {str(e)}", "assemblyai", "SDK_ERROR") from e

        # 理论上不会到达这里
        if last_exception:
            raise ASRException(f"重试耗尽: {str(last_exception)}", "assemblyai", "RETRY_EXHAUSTED") from last_exception
        else:
            raise ASRException("转录重试耗尽", "assemblyai", "RETRY_EXHAUSTED")
    
    def _create_transcription_config(self, request: ASRRequest, aai) -> 'aai.TranscriptionConfig':
        """创建转录配置"""
        # 基础设置
        config_params = {
            'speech_model': self.config.speech_model,
            'punctuate': self.config.punctuate,
            'format_text': self.config.format_text,
            'disfluencies': self.config.disfluencies,
        }

        # 语言设置
        language = request.language or self.config.language_code
        config_params.update({
            'language_detection': language == 'auto',
            'language_code': None if language == 'auto' else language
        })

        # 说话人分离
        config_params['speaker_labels'] = request.enable_diarization or self.config.speaker_labels
        speakers_expected = request.num_speakers or self.config.speakers_expected
        if speakers_expected > 0:
            config_params['speakers_expected'] = speakers_expected

        # 高级功能设置
        advanced_features = ['sentiment_analysis', 'entity_detection', 'auto_highlights', 'auto_chapters', 'summarization']
        config_params.update({feature: getattr(self.config, feature) for feature in advanced_features})

        return aai.TranscriptionConfig(**config_params)
    
    def _extract_word_data(self, word) -> Dict[str, Any]:
        """词汇数据提取"""
        return {
            'text': safe_getattr(word, 'text', ''),
            'start': safe_getattr(word, 'start', 0),
            'end': safe_getattr(word, 'end', 0),
            'confidence': safe_getattr(word, 'confidence', 0.0),
            'speaker': safe_getattr(word, 'speaker', None)
        }

    def _parse_transcription_result(self, transcript, transcription_id: str, start_time: float) -> ASRResponse:
        """转录结果解析"""
        # 基本信息提取
        full_text = transcript.text or ""
        confidence = safe_getattr(transcript, 'confidence', None)
        audio_duration = safe_getattr(transcript, 'audio_duration', None)

        # 词汇信息提取
        words = []
        if hasattr(transcript, 'words') and transcript.words:
            words = [self._extract_word_data(word) for word in transcript.words]

        # 说话人信息提取
        utterances = []
        if hasattr(transcript, 'utterances') and transcript.utterances:
            for utterance in transcript.utterances:
                utterance_data = {
                    'text': safe_getattr(utterance, 'text', ''),
                    'start': safe_getattr(utterance, 'start', 0),
                    'end': safe_getattr(utterance, 'end', 0),
                    'confidence': safe_getattr(utterance, 'confidence', 0.0),
                    'speaker': safe_getattr(utterance, 'speaker', None),
                    'words': []
                }

                # 词汇提取
                if hasattr(utterance, 'words') and utterance.words:
                    utterance_data['words'] = [self._extract_word_data(word) for word in utterance.words]

                    utterances.append(utterance_data)

        processing_time = time.time() - start_time

        # 元数据构建
        metadata = {
            "service": "assemblyai",
            "model": self.config.speech_model,
            "transcript_id": transcript.id,
            "utterances": utterances
        }

        # 高级功能结果
        if hasattr(transcript, 'sentiment_analysis') and transcript.sentiment_analysis:
            metadata['sentiment_analysis'] = [
                {
                    'text': item.text,
                    'sentiment': str(item.sentiment),
                    'confidence': item.confidence,
                    'start': item.start / 1000.0,
                    'end': item.end / 1000.0
                } for item in transcript.sentiment_analysis
            ]

        if hasattr(transcript, 'entities') and transcript.entities:
            metadata['entities'] = [
                {
                    'text': entity.text,
                    'entity_type': str(entity.entity_type),
                    'start': entity.start / 1000.0,
                    'end': entity.end / 1000.0
                } for entity in transcript.entities
            ]

        # 原始数据构建
        metadata['raw_result'] = {
            'id': getattr(transcript, 'id', ''),
            'text': getattr(transcript, 'text', ''),
            'words': words,
            'confidence': getattr(transcript, 'confidence', None),
            'audio_duration': getattr(transcript, 'audio_duration', None),
            'language_code': getattr(transcript, 'language_code', None),
            'status': str(getattr(transcript, 'status', None))
        }

        return ASRResponse(
            transcription_id=transcription_id,
            status=ASRStatus.COMPLETED,
            full_text=full_text,
            words=words,
            language_detected=getattr(transcript, 'language_code', None),
            confidence=confidence,
            processing_time=processing_time,
            metadata=metadata
        )
    

