# EvaTrans 运行日志

## 导出信息
- 导出时间：2025-08-03 17:07:57
- 日志条目数：5696

## 日志内容
[14:48:17] 已打开项目目录: E:\github\EvaTrans\projects
[16:33:34] 已添加 1 个音视频文件到任务列表
[16:33:40] 开始处理 1 个任务（智能增量检测）...
[16:33:40] 开始顺序转换，总任务数: 1
[16:33:40] [TaskFlow] 开始音频处理
[16:33:40] [TaskFlow] 阶段更新: starting -> audio_processing
[16:33:40] [TaskFlow] 开始音频处理: E:\trimmed_Honey Baby, Honey Baby (1974).wav -> ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974).wav
[16:33:40] ℹ️ 开始处理: trimmed_Honey Baby, Honey Baby (1974).wav
[16:33:41] ℹ️ 直接复制: trimmed_Honey Baby, Honey Baby (1974).wav (0.8s)
[16:33:41] [TaskFlow] 音频处理成功: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974).wav
[16:33:41] [TaskFlow] 音频时长 94.0 分钟，超过阈值 5.0 分钟，需要分割
[16:33:41] [TaskFlow] 音频时长超过阈值，开始音频分割
[16:33:41] [TaskFlow] 开始音频分割: trimmed_Honey Baby, Honey Baby (1974).wav
[16:34:53] [TaskFlow] 分割完成: 19 个分段，耗时 71.69s
[16:34:53] [TaskFlow] 音频分割完成，生成 19 个分段
[16:34:53] [TaskFlow] 音频分割完成，进入转录阶段
[16:34:53] [TaskFlow] 阶段更新: audio_processing -> transcription
[16:34:53] [TaskFlow] 开始分段转录，启用服务: ElevenLabs
[16:34:53] [TaskFlow] 处理ASR服务: ElevenLabs
[16:34:53] [TaskFlow] 处理分段 1/19: trimmed_Honey Baby, Honey Baby (1974)_part1_00-00-00
[16:34:53] [TaskFlow] 开始转录分段: trimmed_Honey Baby, Honey Baby (1974)_part1_00-00-00 - ElevenLabs
[16:34:53] 开始ElevenLabs free模式转录: trimmed_Honey Baby, Honey Baby (1974)_part1_00-00-00 (应用层尝试 1/3)
[16:35:03] ElevenLabs转录完成: trimmed_Honey Baby, Honey Baby (1974)_part1_00-00-00 (应用层尝试 1)
[16:35:03] JSON文件已保存: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part1_00-00-00-ElevenLabs.json
[16:35:03] [TaskFlow] 分段转录成功: trimmed_Honey Baby, Honey Baby (1974)_part1_00-00-00 - ElevenLabs
[16:35:03] [TaskFlow] 开始解析分段结果: trimmed_Honey Baby, Honey Baby (1974)_part1_00-00-00 - ElevenLabs
[16:35:03] 开始解析 ElevenLabs 的JSON文件
[16:35:03] [DEBUG] 🔍 使用regex库的Unicode属性进行字符分类
[16:35:03] [DEBUG] Detecting format for data with keys: ['success', 'service', 'transcription_id', 'text', 'words', 'language_detected', 'confidence', 'processing_time', 'metadata', 'created_at']
[16:35:03] [DEBUG] Detected elevenlabs standardized format from service field
[16:35:03] [DEBUG] Parsing ElevenLabs format
[16:35:03] [DEBUG] ElevenLabs data keys: ['success', 'service', 'transcription_id', 'text', 'words', 'language_detected', 'confidence', 'processing_time', 'metadata', 'created_at']
[16:35:03] [DEBUG] ElevenLabs full_text length: 2818
[16:35:03] [DEBUG] ElevenLabs words count: 1079
[16:35:03] [DEBUG] ElevenLabs result: 540 words, confidence: 0.000, speakers: 4
[16:35:03] ℹ️ 开始词汇分离处理，原始词汇数量: 540
[16:35:03] [DEBUG] 🔍 分离词汇: 'Hey,' -> ['Hey', ',']
[16:35:03] [DEBUG] 🔍 分离词汇: 'doing?' -> ['doing', '?']
[16:35:03] [DEBUG] 🔍 分离词汇: 'J.' -> ['J', '.']
[16:35:03] [DEBUG] 🔍 分离词汇: 'Beirut,' -> ['Beirut', ',']
[16:35:03] [DEBUG] 🔍 分离词汇: 'movie,' -> ['movie', ',']
[16:35:03] [DEBUG] 🔍 分离词汇: 'Baby,' -> ['Baby', ',']
[16:35:03] [DEBUG] 🔍 分离词汇: 'location,' -> ['location', ',']
[16:35:03] [DEBUG] 🔍 分离词汇: 'experience.' -> ['experience', '.']
[16:35:03] [DEBUG] 🔍 分离词汇: 'way,' -> ['way', ',']
[16:35:03] [DEBUG] 🔍 分离词汇: 'Skeeyee,' -> ['Skeeyee', ',']
[16:35:03] [DEBUG] 🔍 分离词汇: 'Malali.' -> ['Malali', '.']
[16:35:03] [DEBUG] 🔍 分离词汇: 'cousin,' -> ['cousin', ',']
[16:35:03] [DEBUG] 🔍 分离词汇: 'Baby,' -> ['Baby', ',']
[16:35:03] [DEBUG] 🔍 分离词汇: 'it,' -> ['it', ',']
[16:35:03] [DEBUG] 🔍 分离词汇: 'world.' -> ['world', '.']
[16:35:03] [DEBUG] 🔍 分离词汇: 'smart.' -> ['smart', '.']
[16:35:03] [DEBUG] 🔍 分离词汇: 'it.' -> ['it', '.']
[16:35:03] [DEBUG] 🔍 分离词汇: 'Harlem.' -> ['Harlem', '.']
[16:35:03] [DEBUG] 🔍 分离词汇: 'Anyway,' -> ['Anyway', ',']
[16:35:03] [DEBUG] 🔍 分离词汇: 'Beirut,' -> ['Beirut', ',']
[16:35:03] [DEBUG] 🔍 分离词汇: 'boat,' -> ['boat', ',']
[16:35:03] [DEBUG] 🔍 分离词汇: 'see.' -> ['see', '.']
[16:35:03] [DEBUG] 🔍 分离词汇: 'boat.' -> ['boat', '.']
[16:35:03] [DEBUG] 🔍 分离词汇: 'face.' -> ['face', '.']
[16:35:03] [DEBUG] 🔍 分离词汇: 'about?' -> ['about', '?']
[16:35:03] [DEBUG] 🔍 分离词汇: 'it...' -> ['it', '...']
[16:35:03] [DEBUG] 🔍 分离词汇: 'face,' -> ['face', ',']
[16:35:03] [DEBUG] 🔍 分离词汇: 'passport.' -> ['passport', '.']
[16:35:03] [DEBUG] 🔍 分离词汇: 'it.' -> ['it', '.']
[16:35:03] [DEBUG] 🔍 分离词汇: 'transaction.' -> ['transaction', '.']
[16:35:03] [DEBUG] 🔍 分离词汇: 'anyway,' -> ['anyway', ',']
[16:35:03] [DEBUG] 🔍 分离词汇: 'body.' -> ['body', '.']
[16:35:03] [DEBUG] 🔍 分离词汇: 'preserved.' -> ['preserved', '.']
[16:35:03] [DEBUG] 🔍 分离词汇: 'sticky.' -> ['sticky', '.']
[16:35:03] [DEBUG] 🔍 分离词汇: 'saying?' -> ['saying', '?']
[16:35:03] [DEBUG] 🔍 分离词汇: 'Yeah.' -> ['Yeah', '.']
[16:35:03] [DEBUG] 🔍 分离词汇: 'Right.' -> ['Right', '.']
[16:35:03] [DEBUG] 🔍 分离词汇: 'Hey,' -> ['Hey', ',']
[16:35:03] [DEBUG] 🔍 分离词汇: 'look,' -> ['look', ',']
[16:35:03] [DEBUG] 🔍 分离词汇: 'more.' -> ['more', '.']
[16:35:03] [DEBUG] 🔍 分离词汇: 'yourself.' -> ['yourself', '.']
[16:35:03] [DEBUG] 🔍 分离词汇: 'right?' -> ['right', '?']
[16:35:03] [DEBUG] 🔍 分离词汇: 'flick.' -> ['flick', '.']
[16:35:03] [DEBUG] 🔍 分离词汇: 'Yo,' -> ['Yo', ',']
[16:35:03] [DEBUG] 🔍 分离词汇: 'on.' -> ['on', '.']
[16:35:03] [DEBUG] 🔍 分离词汇: 'baby.' -> ['baby', '.']
[16:35:03] [DEBUG] 🔍 分离词汇: 'baby.' -> ['baby', '.']
[16:35:03] [DEBUG] 🔍 分离词汇: 'baby.' -> ['baby', '.']
[16:35:03] [DEBUG] 🔍 分离词汇: 'baby.' -> ['baby', '.']
[16:35:03] [DEBUG] 🔍 分离词汇: 'street.' -> ['street', '.']
[16:35:03] [DEBUG] 🔍 分离词汇: 'eat.' -> ['eat', '.']
[16:35:03] [DEBUG] 🔍 分离词汇: 'baby.' -> ['baby', '.']
[16:35:03] [DEBUG] 🔍 分离词汇: 'tough.' -> ['tough', '.']
[16:35:03] [DEBUG] 🔍 分离词汇: 'enough.' -> ['enough', '.']
[16:35:03] [DEBUG] 🔍 分离词汇: 'humanity.' -> ['humanity', '.']
[16:35:03] [DEBUG] 🔍 分离词汇: 'sound.' -> ['sound', '.']
[16:35:03] [DEBUG] 🔍 分离词汇: 'Man,' -> ['Man', ',']
[16:35:03] [DEBUG] 🔍 分离词汇: 'again?' -> ['again', '?']
[16:35:03] [DEBUG] 🔍 分离词汇: 'Wow.' -> ['Wow', '.']
[16:35:03] [DEBUG] 🔍 分离词汇: 'contest.' -> ['contest', '.']
[16:35:03] [DEBUG] 🔍 分离词汇: 'Hey,' -> ['Hey', ',']
[16:35:03] [DEBUG] 🔍 分离词汇: 'man,' -> ['man', ',']
[16:35:03] [DEBUG] 🔍 分离词汇: 'dignity,' -> ['dignity', ',']
[16:35:03] [DEBUG] 🔍 分离词汇: 'here,' -> ['here', ',']
[16:35:03] [DEBUG] 🔍 分离词汇: 'right?' -> ['right', '?']
[16:35:04] [DEBUG] 🔍 分离词汇: 'weeks.' -> ['weeks', '.']
[16:35:04] [DEBUG] 🔍 分离词汇: 'Look,' -> ['Look', ',']
[16:35:04] [DEBUG] 🔍 分离词汇: 'understand.' -> ['understand', '.']
[16:35:04] [DEBUG] 🔍 分离词汇: 'go...' -> ['go', '...']
[16:35:04] [DEBUG] 🔍 分离词汇: 'Tripoli.' -> ['Tripoli', '.']
[16:35:04] [DEBUG] 🔍 分离词汇: 'that,' -> ['that', ',']
[16:35:04] [DEBUG] 🔍 分离词汇: 'tug,' -> ['tug', ',']
[16:35:04] [DEBUG] 🔍 分离词汇: 'boat,' -> ['boat', ',']
[16:35:04] [DEBUG] 🔍 分离词汇: 'Beirut.' -> ['Beirut', '.']
[16:35:04] [DEBUG] 🔍 分离词汇: 'Man,' -> ['Man', ',']
[16:35:04] [DEBUG] 🔍 分离词汇: 'liner,' -> ['liner', ',']
[16:35:04] [DEBUG] 🔍 分离词汇: 'man.' -> ['man', '.']
[16:35:04] [DEBUG] 🔍 分离词汇: 'Yeah,' -> ['Yeah', ',']
[16:35:04] [DEBUG] 🔍 分离词汇: 'yeah.' -> ['yeah', '.']
[16:35:04] [DEBUG] 🔍 分离词汇: 'Hey,' -> ['Hey', ',']
[16:35:04] [DEBUG] 🔍 分离词汇: 'man,' -> ['man', ',']
[16:35:04] [DEBUG] 🔍 分离词汇: 'smoking.' -> ['smoking', '.']
[16:35:04] [DEBUG] 🔍 分离词汇: 'trip.' -> ['trip', '.']
[16:35:04] [DEBUG] 🔍 分离词汇: 'Now,' -> ['Now', ',']
[16:35:04] [DEBUG] 🔍 分离词汇: 'us.' -> ['us', '.']
[16:35:04] [DEBUG] 🔍 分离词汇: '"I' -> ['"', 'I']
[16:35:04] [DEBUG] 🔍 分离词汇: 'now.' -> ['now', '.']
[16:35:04] [DEBUG] 🔍 分离词汇: 'Lord,' -> ['Lord', ',']
[16:35:04] [DEBUG] 🔍 分离词汇: 'mercy.' -> ['mercy', '.']
[16:35:04] [DEBUG] 🔍 分离词汇: 'Arthur,' -> ['Arthur', ',']
[16:35:04] [DEBUG] 🔍 分离词汇: 'suitcase,' -> ['suitcase', ',']
[16:35:04] [DEBUG] 🔍 分离词汇: 'darling?' -> ['darling', '?']
[16:35:04] [DEBUG] 🔍 分离词汇: 'And,' -> ['And', ',']
[16:35:04] [DEBUG] 🔍 分离词汇: 'um,' -> ['um', ',']
[16:35:04] [DEBUG] 🔍 分离词汇: 'Honey,' -> ['Honey', ',']
[16:35:04] [DEBUG] 🔍 分离词汇: 'my,' -> ['my', ',']
[16:35:04] [DEBUG] 🔍 分离词汇: 'um,' -> ['um', ',']
[16:35:04] [DEBUG] 🔍 分离词汇: 'oh,' -> ['oh', ',']
[16:35:04] [DEBUG] 🔍 分离词汇: 'shade.' -> ['shade', '.']
[16:35:04] [DEBUG] 🔍 分离词汇: 'view.' -> ['view', '.']
[16:35:04] [DEBUG] 🔍 分离词汇: 'And,' -> ['And', ',']
[16:35:04] [DEBUG] 🔍 分离词汇: 'uh,' -> ['uh', ',']
[16:35:04] [DEBUG] 🔍 分离词汇: 'handlers,' -> ['handlers', ',']
[16:35:04] [DEBUG] 🔍 分离词汇: 'tan."' -> ['tan', '."']
[16:35:04] [DEBUG] 🔍 分离词汇: 'Man,' -> ['Man', ',']
[16:35:04] [DEBUG] 🔍 分离词汇: 'man.' -> ['man', '.']
[16:35:04] [DEBUG] 🔍 分离词汇: 'Hey,' -> ['Hey', ',']
[16:35:04] [DEBUG] 🔍 分离词汇: 'man.' -> ['man', '.']
[16:35:04] [DEBUG] 🔍 分离词汇: 'boat.' -> ['boat', '.']
[16:35:04] [DEBUG] 🔍 分离词汇: 'Hey,' -> ['Hey', ',']
[16:35:04] [DEBUG] 🔍 分离词汇: 'dude,' -> ['dude', ',']
[16:35:04] [DEBUG] 🔍 分离词汇: 'is...' -> ['is', '...']
[16:35:04] [DEBUG] 🔍 分离词汇: 'nothing,' -> ['nothing', ',']
[16:35:04] [DEBUG] 🔍 分离词汇: 'man.' -> ['man', '.']
[16:35:04] [DEBUG] 🔍 分离词汇: 'happy.' -> ['happy', '.']
[16:35:04] [DEBUG] 🔍 分离词汇: 'June,' -> ['June', ',']
[16:35:04] [DEBUG] 🔍 分离词汇: 'George,' -> ['George', ',']
[16:35:04] [DEBUG] 🔍 分离词汇: 'hand.' -> ['hand', '.']
[16:35:04] [DEBUG] 🔍 分离词汇: 'is?' -> ['is', '?']
[16:35:04] [DEBUG] 🔍 分离词汇: 'Arthur,' -> ['Arthur', ',']
[16:35:04] [DEBUG] 🔍 分离词汇: 'home.' -> ['home', '.']
[16:35:04] [DEBUG] 🔍 分离词汇: 'morning.' -> ['morning', '.']
[16:35:04] ℹ️ 词汇分离完成，分离了 122 个混合词汇，最终词汇数量: 662
[16:35:04] ℹ️ Parsed trimmed_Honey Baby, Honey Baby (1974)_part1_00-00-00-ElevenLabs.json in 0.871s
[16:35:04] [OK] ElevenLabs JSON解析完成，已保存: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part1_00-00-00-ElevenLabs-parsed.json
[16:35:04]   词汇数量: 662
[16:35:04]   语言: unknown
[16:35:04]   置信度: 0.00
[16:35:04] [TaskFlow] 分段转录和解析完成: trimmed_Honey Baby, Honey Baby (1974)_part1_00-00-00 - ElevenLabs
[16:35:04] [TaskFlow] 处理分段 2/19: trimmed_Honey Baby, Honey Baby (1974)_part2_00-05-18
[16:35:04] [TaskFlow] 开始转录分段: trimmed_Honey Baby, Honey Baby (1974)_part2_00-05-18 - ElevenLabs
[16:35:04] 开始ElevenLabs free模式转录: trimmed_Honey Baby, Honey Baby (1974)_part2_00-05-18 (应用层尝试 1/3)
[16:35:11] ElevenLabs转录完成: trimmed_Honey Baby, Honey Baby (1974)_part2_00-05-18 (应用层尝试 1)
[16:35:11] JSON文件已保存: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part2_00-05-18-ElevenLabs.json
[16:35:11] [TaskFlow] 分段转录成功: trimmed_Honey Baby, Honey Baby (1974)_part2_00-05-18 - ElevenLabs
[16:35:11] [TaskFlow] 开始解析分段结果: trimmed_Honey Baby, Honey Baby (1974)_part2_00-05-18 - ElevenLabs
[16:35:11] 开始解析 ElevenLabs 的JSON文件
[16:35:11] [DEBUG] 🔍 使用regex库的Unicode属性进行字符分类
[16:35:11] [DEBUG] Detecting format for data with keys: ['success', 'service', 'transcription_id', 'text', 'words', 'language_detected', 'confidence', 'processing_time', 'metadata', 'created_at']
[16:35:11] [DEBUG] Detected elevenlabs standardized format from service field
[16:35:11] [DEBUG] Parsing ElevenLabs format
[16:35:11] [DEBUG] ElevenLabs data keys: ['success', 'service', 'transcription_id', 'text', 'words', 'language_detected', 'confidence', 'processing_time', 'metadata', 'created_at']
[16:35:11] [DEBUG] ElevenLabs full_text length: 1261
[16:35:11] [DEBUG] ElevenLabs words count: 459
[16:35:11] [DEBUG] ElevenLabs result: 230 words, confidence: 0.000, speakers: 8
[16:35:11] ℹ️ 开始词汇分离处理，原始词汇数量: 230
[16:35:11] [DEBUG] 🔍 分离词汇: 'that?' -> ['that', '?']
[16:35:11] [DEBUG] 🔍 分离词汇: 'you,' -> ['you', ',']
[16:35:11] [DEBUG] 🔍 分离词汇: 'Sam.' -> ['Sam', '.']
[16:35:11] [DEBUG] 🔍 分离词汇: 'Oh,' -> ['Oh', ',']
[16:35:11] [DEBUG] 🔍 分离词汇: 'yeah?' -> ['yeah', '?']
[16:35:11] [DEBUG] 🔍 分离词汇: 'Yeah.' -> ['Yeah', '.']
[16:35:11] [DEBUG] 🔍 分离词汇: 'yesterday,' -> ['yesterday', ',']
[16:35:11] [DEBUG] 🔍 分离词汇: 'Avenue,' -> ['Avenue', ',']
[16:35:11] [DEBUG] 🔍 分离词汇: 'today,' -> ['today', ',']
[16:35:11] [DEBUG] 🔍 分离词汇: 'Beirut.' -> ['Beirut', '.']
[16:35:11] [DEBUG] 🔍 分离词汇: 'yet.' -> ['yet', '.']
[16:35:11] [DEBUG] 🔍 分离词汇: 'yet.' -> ['yet', '.']
[16:35:11] [DEBUG] 🔍 分离词汇: 'around.' -> ['around', '.']
[16:35:11] [DEBUG] 🔍 分离词汇: 'Skippy.' -> ['Skippy', '.']
[16:35:11] [DEBUG] 🔍 分离词汇: 'Skippy,' -> ['Skippy', ',']
[16:35:11] [DEBUG] 🔍 分离词汇: 'down?' -> ['down', '?']
[16:35:11] [DEBUG] 🔍 分离词汇: 'loose.' -> ['loose', '.']
[16:35:11] [DEBUG] 🔍 分离词汇: 'Yeah.' -> ['Yeah', '.']
[16:35:11] [DEBUG] 🔍 分离词汇: 'free.' -> ['free', '.']
[16:35:11] [DEBUG] 🔍 分离词汇: 'on,' -> ['on', ',']
[16:35:11] [DEBUG] 🔍 分离词汇: 'Skippy.' -> ['Skippy', '.']
[16:35:11] [DEBUG] 🔍 分离词汇: 'Stop.' -> ['Stop', '.']
[16:35:11] [DEBUG] 🔍 分离词汇: 'Honey,' -> ['Honey', ',']
[16:35:11] [DEBUG] 🔍 分离词汇: 'baby.' -> ['baby', '.']
[16:35:11] [DEBUG] 🔍 分离词汇: 'name?' -> ['name', '?']
[16:35:11] [DEBUG] 🔍 分离词汇: 'Skippy.' -> ['Skippy', '.']
[16:35:11] [DEBUG] 🔍 分离词汇: 'nickname.' -> ['nickname', '.']
[16:35:11] [DEBUG] 🔍 分离词汇: 'Oh,' -> ['Oh', ',']
[16:35:11] [DEBUG] 🔍 分离词汇: 'name?' -> ['name', '?']
[16:35:11] [DEBUG] 🔍 分离词汇: 'Arthur.' -> ['Arthur', '.']
[16:35:11] [DEBUG] 🔍 分离词汇: 'Lewis.' -> ['Lewis', '.']
[16:35:11] [DEBUG] 🔍 分离词汇: 'name.' -> ['name', '.']
[16:35:11] [DEBUG] 🔍 分离词汇: 'Mullele.' -> ['Mullele', '.']
[16:35:11] [DEBUG] 🔍 分离词汇: 'Lewis.' -> ['Lewis', '.']
[16:35:11] [DEBUG] 🔍 分离词汇: 'is.' -> ['is', '.']
[16:35:11] [DEBUG] 🔍 分离词汇: 'prizes.' -> ['prizes', '.']
[16:35:11] [DEBUG] 🔍 分离词汇: 'look?' -> ['look', '?']
[16:35:11] [DEBUG] 🔍 分离词汇: 'Ah,' -> ['Ah', ',']
[16:35:11] [DEBUG] 🔍 分离词汇: 'yes.' -> ['yes', '.']
[16:35:11] [DEBUG] 🔍 分离词汇: 'you.' -> ['you', '.']
[16:35:11] [DEBUG] 🔍 分离词汇: 'Yeah.' -> ['Yeah', '.']
[16:35:11] [DEBUG] 🔍 分离词汇: 'dollars.' -> ['dollars', '.']
[16:35:11] [DEBUG] 🔍 分离词汇: 'silence.' -> ['silence', '.']
[16:35:11] [DEBUG] 🔍 分离词汇: 'court.' -> ['court', '.']
[16:35:11] [DEBUG] 🔍 分离词汇: 'God.' -> ['God', '.']
[16:35:11] [DEBUG] 🔍 分离词汇: 'car.' -> ['car', '.']
[16:35:11] [DEBUG] 🔍 分离词汇: 'me.' -> ['me', '.']
[16:35:11] [DEBUG] 🔍 分离词汇: 'Harry,' -> ['Harry', ',']
[16:35:11] [DEBUG] 🔍 分离词汇: 'our...' -> ['our', '...']
[16:35:11] [DEBUG] 🔍 分离词汇: 'me,' -> ['me', ',']
[16:35:11] [DEBUG] 🔍 分离词汇: 'relationship?' -> ['relationship', '?']
[16:35:11] [DEBUG] 🔍 分离词汇: 'question.' -> ['question', '.']
[16:35:11] [DEBUG] 🔍 分离词汇: 'question.' -> ['question', '.']
[16:35:11] [DEBUG] 🔍 分离词汇: 'mother,' -> ['mother', ',']
[16:35:11] [DEBUG] 🔍 分离词汇: 'indiscretion.' -> ['indiscretion', '.']
[16:35:11] [DEBUG] 🔍 分离词汇: 'airplane?' -> ['airplane', '?']
[16:35:11] [DEBUG] 🔍 分离词汇: 'know.' -> ['know', '.']
[16:35:11] [DEBUG] 🔍 分离词汇: 'concept,' -> ['concept', ',']
[16:35:11] [DEBUG] 🔍 分离词汇: 'plane?' -> ['plane', '?']
[16:35:11] [DEBUG] 🔍 分离词汇: 'Oh,' -> ['Oh', ',']
[16:35:11] [DEBUG] 🔍 分离词汇: 'Sam,' -> ['Sam', ',']
[16:35:11] [DEBUG] 🔍 分离词汇: 'time.' -> ['time', '.']
[16:35:11] [DEBUG] 🔍 分离词汇: 'launch,' -> ['launch', ',']
[16:35:11] [DEBUG] 🔍 分离词汇: 'premature.' -> ['premature', '.']
[16:35:11] ℹ️ 词汇分离完成，分离了 64 个混合词汇，最终词汇数量: 294
[16:35:11] ℹ️ Parsed trimmed_Honey Baby, Honey Baby (1974)_part2_00-05-18-ElevenLabs.json in 0.490s
[16:35:11] [OK] ElevenLabs JSON解析完成，已保存: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part2_00-05-18-ElevenLabs-parsed.json
[16:35:11]   词汇数量: 294
[16:35:11]   语言: unknown
[16:35:11]   置信度: 0.00
[16:35:11] [TaskFlow] 分段转录和解析完成: trimmed_Honey Baby, Honey Baby (1974)_part2_00-05-18 - ElevenLabs
[16:35:11] [TaskFlow] 处理分段 3/19: trimmed_Honey Baby, Honey Baby (1974)_part3_00-10-14
[16:35:11] [TaskFlow] 开始转录分段: trimmed_Honey Baby, Honey Baby (1974)_part3_00-10-14 - ElevenLabs
[16:35:11] 开始ElevenLabs free模式转录: trimmed_Honey Baby, Honey Baby (1974)_part3_00-10-14 (应用层尝试 1/3)
[16:35:18] ElevenLabs转录完成: trimmed_Honey Baby, Honey Baby (1974)_part3_00-10-14 (应用层尝试 1)
[16:35:18] JSON文件已保存: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part3_00-10-14-ElevenLabs.json
[16:35:18] [TaskFlow] 分段转录成功: trimmed_Honey Baby, Honey Baby (1974)_part3_00-10-14 - ElevenLabs
[16:35:18] [TaskFlow] 开始解析分段结果: trimmed_Honey Baby, Honey Baby (1974)_part3_00-10-14 - ElevenLabs
[16:35:18] 开始解析 ElevenLabs 的JSON文件
[16:35:18] [DEBUG] 🔍 使用regex库的Unicode属性进行字符分类
[16:35:18] [DEBUG] Detecting format for data with keys: ['success', 'service', 'transcription_id', 'text', 'words', 'language_detected', 'confidence', 'processing_time', 'metadata', 'created_at']
[16:35:18] [DEBUG] Detected elevenlabs standardized format from service field
[16:35:18] [DEBUG] Parsing ElevenLabs format
[16:35:18] [DEBUG] ElevenLabs data keys: ['success', 'service', 'transcription_id', 'text', 'words', 'language_detected', 'confidence', 'processing_time', 'metadata', 'created_at']
[16:35:18] [DEBUG] ElevenLabs full_text length: 1502
[16:35:19] [DEBUG] ElevenLabs words count: 582
[16:35:19] [DEBUG] ElevenLabs result: 291 words, confidence: 0.000, speakers: 1
[16:35:19] ℹ️ 开始词汇分离处理，原始词汇数量: 291
[16:35:19] [DEBUG] 🔍 分离词汇: 'Oh.' -> ['Oh', '.']
[16:35:19] [DEBUG] 🔍 分离词汇: 'board.' -> ['board', '.']
[16:35:19] [DEBUG] 🔍 分离词汇: 'drawers.' -> ['drawers', '.']
[16:35:19] [DEBUG] 🔍 分离词汇: 'way.' -> ['way', '.']
[16:35:19] [DEBUG] 🔍 分离词汇: 'way.' -> ['way', '.']
[16:35:19] [DEBUG] 🔍 分离词汇: 'through.' -> ['through', '.']
[16:35:19] [DEBUG] 🔍 分离词汇: 'No,' -> ['No', ',']
[16:35:19] [DEBUG] 🔍 分离词汇: 'no.' -> ['no', '.']
[16:35:19] [DEBUG] 🔍 分离词汇: 'through.' -> ['through', '.']
[16:35:19] [DEBUG] 🔍 分离词汇: 'off.' -> ['off', '.']
[16:35:19] [DEBUG] 🔍 分离词汇: 'go?' -> ['go', '?']
[16:35:19] [DEBUG] 🔍 分离词汇: 'Yeah,' -> ['Yeah', ',']
[16:35:19] [DEBUG] 🔍 分离词汇: 'yeah.' -> ['yeah', '.']
[16:35:19] [DEBUG] 🔍 分离词汇: 'go.' -> ['go', '.']
[16:35:19] [DEBUG] 🔍 分离词汇: 'No,' -> ['No', ',']
[16:35:19] [DEBUG] 🔍 分离词汇: 'us,' -> ['us', ',']
[16:35:19] [DEBUG] 🔍 分离词汇: 'too.' -> ['too', '.']
[16:35:19] [DEBUG] 🔍 分离词汇: 'Yeah,' -> ['Yeah', ',']
[16:35:19] [DEBUG] 🔍 分离词汇: 'so.' -> ['so', '.']
[16:35:19] [DEBUG] 🔍 分离词汇: 'fact,' -> ['fact', ',']
[16:35:19] [DEBUG] 🔍 分离词汇: 'us.' -> ['us', '.']
[16:35:19] [DEBUG] 🔍 分离词汇: 'Sure.' -> ['Sure', '.']
[16:35:19] [DEBUG] 🔍 分离词汇: 'You,' -> ['You', ',']
[16:35:19] [DEBUG] 🔍 分离词汇: 'in.' -> ['in', '.']
[16:35:19] [DEBUG] 🔍 分离词汇: 'La,' -> ['La', ',']
[16:35:19] [DEBUG] 🔍 分离词汇: 'la,' -> ['la', ',']
[16:35:19] [DEBUG] 🔍 分离词汇: 'la,' -> ['la', ',']
[16:35:19] [DEBUG] 🔍 分离词汇: 'la.' -> ['la', '.']
[16:35:19] [DEBUG] 🔍 分离词汇: 'You,' -> ['You', ',']
[16:35:19] [DEBUG] 🔍 分离词汇: 'in.' -> ['in', '.']
[16:35:19] [DEBUG] 🔍 分离词汇: 'Me?' -> ['Me', '?']
[16:35:19] [DEBUG] 🔍 分离词汇: 'in.' -> ['in', '.']
[16:35:19] [DEBUG] 🔍 分离词汇: 'No,' -> ['No', ',']
[16:35:19] [DEBUG] 🔍 分离词汇: 'no.' -> ['no', '.']
[16:35:19] [DEBUG] 🔍 分离词汇: 'go.' -> ['go', '.']
[16:35:19] [DEBUG] 🔍 分离词汇: 'through.' -> ['through', '.']
[16:35:19] [DEBUG] 🔍 分离词汇: 'off.' -> ['off', '.']
[16:35:19] [DEBUG] 🔍 分离词汇: 'you,' -> ['you', ',']
[16:35:19] [DEBUG] 🔍 分离词汇: 'feed.' -> ['feed', '.']
[16:35:19] [DEBUG] 🔍 分离词汇: 'this.' -> ['this', '.']
[16:35:19] [DEBUG] 🔍 分离词汇: 'right.' -> ['right', '.']
[16:35:19] [DEBUG] 🔍 分离词汇: 'fault.' -> ['fault', '.']
[16:35:19] [DEBUG] 🔍 分离词汇: 'farm.' -> ['farm', '.']
[16:35:19] [DEBUG] 🔍 分离词汇: 'know?' -> ['know', '?']
[16:35:19] [DEBUG] 🔍 分离词汇: 'people.' -> ['people', '.']
[16:35:19] [DEBUG] 🔍 分离词汇: 'taxes.' -> ['taxes', '.']
[16:35:19] [DEBUG] 🔍 分离词汇: 'country.' -> ['country', '.']
[16:35:19] [DEBUG] 🔍 分离词汇: 'this?' -> ['this', '?']
[16:35:19] [DEBUG] 🔍 分离词汇: 'You,' -> ['You', ',']
[16:35:19] [DEBUG] 🔍 分离词汇: 'in.' -> ['in', '.']
[16:35:19] [DEBUG] 🔍 分离词汇: 'You,' -> ['You', ',']
[16:35:19] [DEBUG] 🔍 分离词汇: 'in.' -> ['in', '.']
[16:35:19] [DEBUG] 🔍 分离词汇: 'Oh,' -> ['Oh', ',']
[16:35:19] [DEBUG] 🔍 分离词汇: 'you,' -> ['you', ',']
[16:35:19] [DEBUG] 🔍 分离词汇: 'you.' -> ['you', '.']
[16:35:19] [DEBUG] 🔍 分离词汇: 'Oh,' -> ['Oh', ',']
[16:35:19] [DEBUG] 🔍 分离词汇: 'you.' -> ['you', '.']
[16:35:19] [DEBUG] 🔍 分离词汇: 'okay.' -> ['okay', '.']
[16:35:19] [DEBUG] 🔍 分离词汇: 'right.' -> ['right', '.']
[16:35:19] [DEBUG] 🔍 分离词汇: 'Oh,' -> ['Oh', ',']
[16:35:19] [DEBUG] 🔍 分离词汇: 'no,' -> ['no', ',']
[16:35:19] [DEBUG] 🔍 分离词汇: 'worry.' -> ['worry', '.']
[16:35:19] [DEBUG] 🔍 分离词汇: 'right.' -> ['right', '.']
[16:35:19] [DEBUG] 🔍 分离词汇: 'doing?' -> ['doing', '?']
[16:35:19] [DEBUG] 🔍 分离词汇: 'Nothing,' -> ['Nothing', ',']
[16:35:19] [DEBUG] 🔍 分离词汇: 'a...' -> ['a', '...']
[16:35:19] [DEBUG] 🔍 分离词汇: 'Hey,' -> ['Hey', ',']
[16:35:19] [DEBUG] 🔍 分离词汇: 'hey.' -> ['hey', '.']
[16:35:19] [DEBUG] 🔍 分离词汇: 'contract.' -> ['contract', '.']
[16:35:19] [DEBUG] 🔍 分离词汇: 'Harry,' -> ['Harry', ',']
[16:35:19] [DEBUG] 🔍 分离词汇: 'part,' -> ['part', ',']
[16:35:19] [DEBUG] 🔍 分离词汇: 'Africa.' -> ['Africa', '.']
[16:35:19] [DEBUG] 🔍 分离词汇: 'parade.' -> ['parade', '.']
[16:35:19] [DEBUG] 🔍 分离词汇: 'good,' -> ['good', ',']
[16:35:19] [DEBUG] 🔍 分离词汇: 'money!' -> ['money', '!']
[16:35:19] [DEBUG] 🔍 分离词汇: 'flesh!' -> ['flesh', '!']
[16:35:19] [DEBUG] 🔍 分离词汇: 'Here!' -> ['Here', '!']
[16:35:19] [DEBUG] 🔍 分离词汇: 'me.' -> ['me', '.']
[16:35:19] [DEBUG] 🔍 分离词汇: 'me,' -> ['me', ',']
[16:35:19] [DEBUG] 🔍 分离词汇: 'fool!' -> ['fool', '!']
[16:35:19] [DEBUG] 🔍 分离词汇: 'Behold,' -> ['Behold', ',']
[16:35:19] [DEBUG] 🔍 分离词汇: 'Harry,' -> ['Harry', ',']
[16:35:19] [DEBUG] 🔍 分离词汇: 'friend.' -> ['friend', '.']
[16:35:19] [DEBUG] 🔍 分离词汇: 'Hm?' -> ['Hm', '?']
[16:35:19] [DEBUG] 🔍 分离词汇: 'have?' -> ['have', '?']
[16:35:19] [DEBUG] 🔍 分离词汇: 'content,' -> ['content', ',']
[16:35:19] [DEBUG] 🔍 分离词汇: 'shape,' -> ['shape', ',']
[16:35:19] [DEBUG] 🔍 分离词汇: 'size,' -> ['size', ',']
[16:35:19] [DEBUG] 🔍 分离词汇: 'form.' -> ['form', '.']
[16:35:19] [DEBUG] 🔍 分离词汇: 'knows,' -> ['knows', ',']
[16:35:19] [DEBUG] 🔍 分离词汇: 'huh?' -> ['huh', '?']
[16:35:19] [DEBUG] 🔍 分离词汇: 'idea,' -> ['idea', ',']
[16:35:19] [DEBUG] 🔍 分离词汇: 'Liam.' -> ['Liam', '.']
[16:35:19] [DEBUG] 🔍 分离词汇: 'pass.' -> ['pass', '.']
[16:35:19] ℹ️ 词汇分离完成，分离了 94 个混合词汇，最终词汇数量: 385
[16:35:19] ℹ️ Parsed trimmed_Honey Baby, Honey Baby (1974)_part3_00-10-14-ElevenLabs.json in 0.884s
[16:35:19] [OK] ElevenLabs JSON解析完成，已保存: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part3_00-10-14-ElevenLabs-parsed.json
[16:35:19]   词汇数量: 385
[16:35:19]   语言: unknown
[16:35:19]   置信度: 0.00
[16:35:19] [TaskFlow] 分段转录和解析完成: trimmed_Honey Baby, Honey Baby (1974)_part3_00-10-14 - ElevenLabs
[16:35:19] [TaskFlow] 处理分段 4/19: trimmed_Honey Baby, Honey Baby (1974)_part4_00-14-25
[16:35:19] [TaskFlow] 开始转录分段: trimmed_Honey Baby, Honey Baby (1974)_part4_00-14-25 - ElevenLabs
[16:35:19] 开始ElevenLabs free模式转录: trimmed_Honey Baby, Honey Baby (1974)_part4_00-14-25 (应用层尝试 1/3)
[16:35:32] ElevenLabs转录完成: trimmed_Honey Baby, Honey Baby (1974)_part4_00-14-25 (应用层尝试 1)
[16:35:32] JSON文件已保存: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part4_00-14-25-ElevenLabs.json
[16:35:32] [TaskFlow] 分段转录成功: trimmed_Honey Baby, Honey Baby (1974)_part4_00-14-25 - ElevenLabs
[16:35:32] [TaskFlow] 开始解析分段结果: trimmed_Honey Baby, Honey Baby (1974)_part4_00-14-25 - ElevenLabs
[16:35:32] 开始解析 ElevenLabs 的JSON文件
[16:35:32] [DEBUG] 🔍 使用regex库的Unicode属性进行字符分类
[16:35:32] [DEBUG] Detecting format for data with keys: ['success', 'service', 'transcription_id', 'text', 'words', 'language_detected', 'confidence', 'processing_time', 'metadata', 'created_at']
[16:35:32] [DEBUG] Detected elevenlabs standardized format from service field
[16:35:32] [DEBUG] Parsing ElevenLabs format
[16:35:32] [DEBUG] ElevenLabs data keys: ['success', 'service', 'transcription_id', 'text', 'words', 'language_detected', 'confidence', 'processing_time', 'metadata', 'created_at']
[16:35:32] [DEBUG] ElevenLabs full_text length: 2995
[16:35:32] [DEBUG] ElevenLabs words count: 1157
[16:35:32] [DEBUG] ElevenLabs result: 579 words, confidence: 0.000, speakers: 8
[16:35:32] ℹ️ 开始词汇分离处理，原始词汇数量: 579
[16:35:32] [DEBUG] 🔍 分离词汇: 'Oh,' -> ['Oh', ',']
[16:35:32] [DEBUG] 🔍 分离词汇: 'god.' -> ['god', '.']
[16:35:32] [DEBUG] 🔍 分离词汇: 'Look.' -> ['Look', '.']
[16:35:32] [DEBUG] 🔍 分离词汇: 'Look,' -> ['Look', ',']
[16:35:32] [DEBUG] 🔍 分离词汇: 'soup.' -> ['soup', '.']
[16:35:32] [DEBUG] 🔍 分离词汇: 'soup?' -> ['soup', '?']
[16:35:32] [DEBUG] 🔍 分离词汇: 'Yes.' -> ['Yes', '.']
[16:35:32] [DEBUG] 🔍 分离词汇: 'Oh,' -> ['Oh', ',']
[16:35:32] [DEBUG] 🔍 分离词汇: 'stop,' -> ['stop', ',']
[16:35:32] [DEBUG] 🔍 分离词汇: 'uh,' -> ['uh', ',']
[16:35:32] [DEBUG] 🔍 分离词汇: 'car.' -> ['car', '.']
[16:35:32] [DEBUG] 🔍 分离词汇: 'Wait,' -> ['Wait', ',']
[16:35:32] [DEBUG] 🔍 分离词汇: 'wait.' -> ['wait', '.']
[16:35:32] [DEBUG] 🔍 分离词汇: 'soup.' -> ['soup', '.']
[16:35:32] [DEBUG] 🔍 分离词汇: 'marketplace.' -> ['marketplace', '.']
[16:35:32] [DEBUG] 🔍 分离词汇: 'Street,' -> ['Street', ',']
[16:35:32] [DEBUG] 🔍 分离词汇: 'somewhat,' -> ['somewhat', ',']
[16:35:32] [DEBUG] 🔍 分离词汇: 'older.' -> ['older', '.']
[16:35:32] [DEBUG] 🔍 分离词汇: 'Wait,' -> ['Wait', ',']
[16:35:32] [DEBUG] 🔍 分离词汇: 'wait,' -> ['wait', ',']
[16:35:32] [DEBUG] 🔍 分离词汇: 'wait,' -> ['wait', ',']
[16:35:32] [DEBUG] 🔍 分离词汇: 'wait,' -> ['wait', ',']
[16:35:32] [DEBUG] 🔍 分离词汇: 'wait.' -> ['wait', '.']
[16:35:32] [DEBUG] 🔍 分离词汇: 'hotel.' -> ['hotel', '.']
[16:35:32] [DEBUG] 🔍 分离词汇: 'Well,' -> ['Well', ',']
[16:35:32] [DEBUG] 🔍 分离词汇: 'minute.' -> ['minute', '.']
[16:35:32] [DEBUG] 🔍 分离词汇: 'minute,' -> ['minute', ',']
[16:35:32] [DEBUG] 🔍 分离词汇: 'Sam.' -> ['Sam', '.']
[16:35:32] [DEBUG] 🔍 分离词汇: 'Uh,' -> ['Uh', ',']
[16:35:32] [DEBUG] 🔍 分离词汇: 'uh,' -> ['uh', ',']
[16:35:32] [DEBUG] 🔍 分离词汇: 'second.' -> ['second', '.']
[16:35:32] [DEBUG] 🔍 分离词汇: 'Laura.' -> ['Laura', '.']
[16:35:32] [DEBUG] 🔍 分离词汇: 'Wait.' -> ['Wait', '.']
[16:35:32] [DEBUG] 🔍 分离词汇: 'Five,' -> ['Five', ',']
[16:35:32] [DEBUG] 🔍 分离词汇: 'five,' -> ['five', ',']
[16:35:32] [DEBUG] 🔍 分离词汇: 'five,' -> ['five', ',']
[16:35:32] [DEBUG] 🔍 分离词汇: 'five,' -> ['five', ',']
[16:35:32] [DEBUG] 🔍 分离词汇: 'five.' -> ['five', '.']
[16:35:32] [DEBUG] 🔍 分离词汇: 'Oh,' -> ['Oh', ',']
[16:35:32] [DEBUG] 🔍 分离词汇: 'no,' -> ['no', ',']
[16:35:32] [DEBUG] 🔍 分离词汇: 'them.' -> ['them', '.']
[16:35:32] [DEBUG] 🔍 分离词汇: 'deliberately.' -> ['deliberately', '.']
[16:35:32] [DEBUG] 🔍 分离词汇: 'deliberately.' -> ['deliberately', '.']
[16:35:32] [DEBUG] 🔍 分离词汇: 'them?' -> ['them', '?']
[16:35:32] [DEBUG] 🔍 分离词汇: 'do,' -> ['do', ',']
[16:35:32] [DEBUG] 🔍 分离词汇: 'Harry?' -> ['Harry', '?']
[16:35:32] [DEBUG] 🔍 分离词汇: 'wrong,' -> ['wrong', ',']
[16:35:32] [DEBUG] 🔍 分离词汇: 'off.' -> ['off', '.']
[16:35:32] [DEBUG] 🔍 分离词汇: 'money,' -> ['money', ',']
[16:35:32] [DEBUG] 🔍 分离词汇: 'Harry.' -> ['Harry', '.']
[16:35:32] [DEBUG] 🔍 分离词汇: 'money.' -> ['money', '.']
[16:35:32] [DEBUG] 🔍 分离词汇: 'money.' -> ['money', '.']
[16:35:32] [DEBUG] 🔍 分离词汇: 'Hey,' -> ['Hey', ',']
[16:35:32] [DEBUG] 🔍 分离词汇: 'film.' -> ['film', '.']
[16:35:32] [DEBUG] 🔍 分离词汇: 'Yeah.' -> ['Yeah', '.']
[16:35:32] [DEBUG] 🔍 分离词汇: 'it?' -> ['it', '?']
[16:35:32] [DEBUG] 🔍 分离词汇: 'film.' -> ['film', '.']
[16:35:33] [DEBUG] 🔍 分离词汇: 'picture.' -> ['picture', '.']
[16:35:33] [DEBUG] 🔍 分离词汇: 'please?' -> ['please', '?']
[16:35:33] [DEBUG] 🔍 分离词汇: 'Sure.' -> ['Sure', '.']
[16:35:33] [DEBUG] 🔍 分离词汇: 'me,' -> ['me', ',']
[16:35:33] [DEBUG] 🔍 分离词汇: 'Lewis,' -> ['Lewis', ',']
[16:35:33] [DEBUG] 🔍 分离词汇: 'Malole.' -> ['Malole', '.']
[16:35:33] [DEBUG] 🔍 分离词汇: 'Hello,' -> ['Hello', ',']
[16:35:33] [DEBUG] 🔍 分离词汇: 'hello.' -> ['hello', '.']
[16:35:33] [DEBUG] 🔍 分离词汇: 'Hello.' -> ['Hello', '.']
[16:35:33] [DEBUG] 🔍 分离词汇: 'Hello.' -> ['Hello', '.']
[16:35:33] [DEBUG] 🔍 分离词汇: 'you?' -> ['you', '?']
[16:35:33] [DEBUG] 🔍 分离词汇: 'you?' -> ['you', '?']
[16:35:33] [DEBUG] 🔍 分离词汇: 'Good.' -> ['Good', '.']
[16:35:33] [DEBUG] 🔍 分离词汇: 'Uh,' -> ['Uh', ',']
[16:35:33] [DEBUG] 🔍 分离词汇: 'you?' -> ['you', '?']
[16:35:33] [DEBUG] 🔍 分离词汇: 'you,' -> ['you', ',']
[16:35:33] [DEBUG] 🔍 分离词汇: 'you.' -> ['you', '.']
[16:35:33] [DEBUG] 🔍 分离词汇: 'Harry.' -> ['Harry', '.']
[16:35:33] [DEBUG] 🔍 分离词汇: 'disappeared.' -> ['disappeared', '.']
[16:35:33] [DEBUG] 🔍 分离词汇: 'Seriously?' -> ['Seriously', '?']
[16:35:33] [DEBUG] 🔍 分离词汇: 'you?' -> ['you', '?']
[16:35:33] [DEBUG] 🔍 分离词汇: 'me.' -> ['me', '.']
[16:35:33] [DEBUG] 🔍 分离词汇: 'Oh,' -> ['Oh', ',']
[16:35:33] [DEBUG] 🔍 分离词汇: 'funny.' -> ['funny', '.']
[16:35:33] [DEBUG] 🔍 分离词汇: 'trip,' -> ['trip', ',']
[16:35:33] [DEBUG] 🔍 分离词汇: 'right?' -> ['right', '?']
[16:35:33] [DEBUG] 🔍 分离词汇: 'trip.' -> ['trip', '.']
[16:35:33] [DEBUG] 🔍 分离词汇: 'Okay.' -> ['Okay', '.']
[16:35:33] [DEBUG] 🔍 分离词汇: 'her...' -> ['her', '...']
[16:35:33] [DEBUG] 🔍 分离词汇: 'her...' -> ['her', '...']
[16:35:33] [DEBUG] 🔍 分离词汇: 'Her,' -> ['Her', ',']
[16:35:33] [DEBUG] 🔍 分离词汇: 'for?' -> ['for', '?']
[16:35:33] [DEBUG] 🔍 分离词汇: 'mean,' -> ['mean', ',']
[16:35:33] [DEBUG] 🔍 分离词汇: 'desk.' -> ['desk', '.']
[16:35:33] [DEBUG] 🔍 分离词汇: 'Why?' -> ['Why', '?']
[16:35:33] [DEBUG] 🔍 分离词汇: 'lungs.' -> ['lungs', '.']
[16:35:33] [DEBUG] 🔍 分离词汇: 'Oh,' -> ['Oh', ',']
[16:35:33] [DEBUG] 🔍 分离词汇: 'Sam.' -> ['Sam', '.']
[16:35:33] [DEBUG] 🔍 分离词汇: 'please?' -> ['please', '?']
[16:35:33] [DEBUG] 🔍 分离词汇: 'Sure.' -> ['Sure', '.']
[16:35:33] [DEBUG] 🔍 分离词汇: 'Sure,' -> ['Sure', ',']
[16:35:33] [DEBUG] 🔍 分离词汇: 'sure.' -> ['sure', '.']
[16:35:33] [DEBUG] 🔍 分离词汇: 'Hello.' -> ['Hello', '.']
[16:35:33] [DEBUG] 🔍 分离词汇: 'Hello.' -> ['Hello', '.']
[16:35:33] [DEBUG] 🔍 分离词汇: 'you?' -> ['you', '?']
[16:35:33] [DEBUG] 🔍 分离词汇: 'Hi.' -> ['Hi', '.']
[16:35:33] [DEBUG] 🔍 分离词汇: 'Yeah,' -> ['Yeah', ',']
[16:35:33] [DEBUG] 🔍 分离词汇: 'bad,' -> ['bad', ',']
[16:35:33] [DEBUG] 🔍 分离词汇: 'bad.' -> ['bad', '.']
[16:35:33] [DEBUG] 🔍 分离词汇: 'working.' -> ['working', '.']
[16:35:33] [DEBUG] 🔍 分离词汇: 'working?' -> ['working', '?']
[16:35:33] [DEBUG] 🔍 分离词汇: 'Yeah.' -> ['Yeah', '.']
[16:35:33] [DEBUG] 🔍 分离词汇: 'do?' -> ['do', '?']
[16:35:33] [DEBUG] 🔍 分离词汇: 'writer.' -> ['writer', '.']
[16:35:33] [DEBUG] 🔍 分离词汇: 'Oh,' -> ['Oh', ',']
[16:35:33] [DEBUG] 🔍 分离词汇: 'wonderful.' -> ['wonderful', '.']
[16:35:33] [DEBUG] 🔍 分离词汇: 'about?' -> ['about', '?']
[16:35:33] [DEBUG] 🔍 分离词汇: 'Uh,' -> ['Uh', ',']
[16:35:33] [DEBUG] 🔍 分离词汇: 'author.' -> ['author', '.']
[16:35:33] [DEBUG] 🔍 分离词汇: 'Oh,' -> ['Oh', ',']
[16:35:33] [DEBUG] 🔍 分离词汇: 'nice.' -> ['nice', '.']
[16:35:33] [DEBUG] 🔍 分离词汇: 'novelist.' -> ['novelist', '.']
[16:35:33] [DEBUG] 🔍 分离词汇: 'Novelist?' -> ['Novelist', '?']
[16:35:33] [DEBUG] 🔍 分离词汇: 'Yeah.' -> ['Yeah', '.']
[16:35:33] [DEBUG] 🔍 分离词汇: 'Yeah,' -> ['Yeah', ',']
[16:35:33] [DEBUG] 🔍 分离词汇: 'ahead.' -> ['ahead', '.']
[16:35:33] [DEBUG] 🔍 分离词汇: 'No,' -> ['No', ',']
[16:35:33] [DEBUG] 🔍 分离词汇: 'you.' -> ['you', '.']
[16:35:33] [DEBUG] 🔍 分离词汇: 'can't.' -> ["can't", '.']
[16:35:33] [DEBUG] 🔍 分离词汇: 'can't.' -> ["can't", '.']
[16:35:33] [DEBUG] 🔍 分离词汇: 'Uh,' -> ['Uh', ',']
[16:35:33] [DEBUG] 🔍 分离词汇: 'again?' -> ['again', '?']
[16:35:33] [DEBUG] 🔍 分离词汇: 'Laura.' -> ['Laura', '.']
[16:35:33] [DEBUG] 🔍 分离词汇: 'Laura,' -> ['Laura', ',']
[16:35:33] [DEBUG] 🔍 分离词汇: 'Laura,' -> ['Laura', ',']
[16:35:33] [DEBUG] 🔍 分离词汇: 'Laura,' -> ['Laura', ',']
[16:35:33] [DEBUG] 🔍 分离词汇: 'Laura.' -> ['Laura', '.']
[16:35:33] [DEBUG] 🔍 分离词汇: 'here?' -> ['here', '?']
[16:35:33] [DEBUG] 🔍 分离词汇: 'know.' -> ['know', '.']
[16:35:33] [DEBUG] 🔍 分离词汇: 'mean?' -> ['mean', '?']
[16:35:33] [DEBUG] 🔍 分离词汇: 'mean,' -> ['mean', ',']
[16:35:33] [DEBUG] 🔍 分离词汇: 'joint?' -> ['joint', '?']
[16:35:33] [DEBUG] 🔍 分离词汇: 'Oh,' -> ['Oh', ',']
[16:35:33] [DEBUG] 🔍 分离词汇: 'well,' -> ['well', ',']
[16:35:33] [DEBUG] 🔍 分离词汇: 'that,' -> ['that', ',']
[16:35:33] [DEBUG] 🔍 分离词汇: 'thing.' -> ['thing', '.']
[16:35:33] [DEBUG] 🔍 分离词汇: 'we?' -> ['we', '?']
[16:35:33] [DEBUG] 🔍 分离词汇: 'No.' -> ['No', '.']
[16:35:33] [DEBUG] 🔍 分离词汇: 'here.' -> ['here', '.']
[16:35:33] [DEBUG] 🔍 分离词汇: 'way.' -> ['way', '.']
[16:35:33] [DEBUG] 🔍 分离词汇: 'here,' -> ['here', ',']
[16:35:33] [DEBUG] 🔍 分离词汇: 'elbows.' -> ['elbows', '.']
[16:35:33] [DEBUG] 🔍 分离词汇: 'Okay,' -> ['Okay', ',']
[16:35:33] [DEBUG] 🔍 分离词汇: 'Skiggy,' -> ['Skiggy', ',']
[16:35:33] [DEBUG] 🔍 分离词汇: 'at?' -> ['at', '?']
[16:35:33] [DEBUG] 🔍 分离词汇: 'Malole.' -> ['Malole', '.']
[16:35:33] [DEBUG] 🔍 分离词汇: 'you,' -> ['you', ',']
[16:35:34] [DEBUG] 🔍 分离词汇: 'hmm?' -> ['hmm', '?']
[16:35:34] [DEBUG] 🔍 分离词汇: 'boat?' -> ['boat', '?']
[16:35:34] [DEBUG] 🔍 分离词汇: 'Yeah.' -> ['Yeah', '.']
[16:35:34] [DEBUG] 🔍 分离词汇: 'now.' -> ['now', '.']
[16:35:34] [DEBUG] 🔍 分离词汇: 'about?' -> ['about', '?']
[16:35:34] [DEBUG] 🔍 分离词汇: 'her.' -> ['her', '.']
[16:35:34] [DEBUG] 🔍 分离词汇: 'What?' -> ['What', '?']
[16:35:34] [DEBUG] 🔍 分离词汇: 'us.' -> ['us', '.']
[16:35:34] [DEBUG] 🔍 分离词汇: 'Oh,' -> ['Oh', ',']
[16:35:34] [DEBUG] 🔍 分离词汇: 'Jesus,' -> ['Jesus', ',']
[16:35:34] [DEBUG] 🔍 分离词汇: 'Skiggy.' -> ['Skiggy', '.']
[16:35:34] [DEBUG] 🔍 分离词汇: 'it,' -> ['it', ',']
[16:35:34] [DEBUG] 🔍 分离词汇: 'Laura.' -> ['Laura', '.']
[16:35:34] [DEBUG] 🔍 分离词汇: 'scared.' -> ['scared', '.']
[16:35:34] [DEBUG] 🔍 分离词汇: 'minute.' -> ['minute', '.']
[16:35:34] [DEBUG] 🔍 分离词汇: 'do?' -> ['do', '?']
[16:35:34] [DEBUG] 🔍 分离词汇: 'Well,' -> ['Well', ',']
[16:35:34] [DEBUG] 🔍 分离词汇: 'here.' -> ['here', '.']
[16:35:34] [DEBUG] 🔍 分离词汇: 'Oh,' -> ['Oh', ',']
[16:35:34] [DEBUG] 🔍 分离词汇: 'do,' -> ['do', ',']
[16:35:34] [DEBUG] 🔍 分离词汇: 'Skiggy.' -> ['Skiggy', '.']
[16:35:34] [DEBUG] 🔍 分离词汇: 'going?' -> ['going', '?']
[16:35:34] [DEBUG] 🔍 分离词汇: 'police.' -> ['police', '.']
[16:35:34] [DEBUG] 🔍 分离词汇: 'police?' -> ['police', '?']
[16:35:34] [DEBUG] 🔍 分离词汇: 'worry.' -> ['worry', '.']
[16:35:34] [DEBUG] 🔍 分离词汇: 'out.' -> ['out', '.']
[16:35:34] [DEBUG] 🔍 分离词汇: 'down.' -> ['down', '.']
[16:35:34] ℹ️ 词汇分离完成，分离了 181 个混合词汇，最终词汇数量: 760
[16:35:34] ℹ️ Parsed trimmed_Honey Baby, Honey Baby (1974)_part4_00-14-25-ElevenLabs.json in 1.965s
[16:35:34] [OK] ElevenLabs JSON解析完成，已保存: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part4_00-14-25-ElevenLabs-parsed.json
[16:35:34]   词汇数量: 760
[16:35:34]   语言: unknown
[16:35:34]   置信度: 0.00
[16:35:34] [TaskFlow] 分段转录和解析完成: trimmed_Honey Baby, Honey Baby (1974)_part4_00-14-25 - ElevenLabs
[16:35:34] [TaskFlow] 处理分段 5/19: trimmed_Honey Baby, Honey Baby (1974)_part5_00-19-51
[16:35:34] [TaskFlow] 开始转录分段: trimmed_Honey Baby, Honey Baby (1974)_part5_00-19-51 - ElevenLabs
[16:35:34] 开始ElevenLabs free模式转录: trimmed_Honey Baby, Honey Baby (1974)_part5_00-19-51 (应用层尝试 1/3)
[16:35:42] ElevenLabs转录完成: trimmed_Honey Baby, Honey Baby (1974)_part5_00-19-51 (应用层尝试 1)
[16:35:42] JSON文件已保存: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part5_00-19-51-ElevenLabs.json
[16:35:42] [TaskFlow] 分段转录成功: trimmed_Honey Baby, Honey Baby (1974)_part5_00-19-51 - ElevenLabs
[16:35:42] [TaskFlow] 开始解析分段结果: trimmed_Honey Baby, Honey Baby (1974)_part5_00-19-51 - ElevenLabs
[16:35:42] 开始解析 ElevenLabs 的JSON文件
[16:35:42] [DEBUG] 🔍 使用regex库的Unicode属性进行字符分类
[16:35:42] [DEBUG] Detecting format for data with keys: ['success', 'service', 'transcription_id', 'text', 'words', 'language_detected', 'confidence', 'processing_time', 'metadata', 'created_at']
[16:35:42] [DEBUG] Detected elevenlabs standardized format from service field
[16:35:42] [DEBUG] Parsing ElevenLabs format
[16:35:42] [DEBUG] ElevenLabs data keys: ['success', 'service', 'transcription_id', 'text', 'words', 'language_detected', 'confidence', 'processing_time', 'metadata', 'created_at']
[16:35:42] [DEBUG] ElevenLabs full_text length: 1469
[16:35:42] [DEBUG] ElevenLabs words count: 541
[16:35:42] [DEBUG] ElevenLabs result: 271 words, confidence: 0.000, speakers: 5
[16:35:42] ℹ️ 开始词汇分离处理，原始词汇数量: 271
[16:35:42] [DEBUG] 🔍 分离词汇: 'no,' -> ['no', ',']
[16:35:42] [DEBUG] 🔍 分离词汇: 'schooner.' -> ['schooner', '.']
[16:35:42] [DEBUG] 🔍 分离词汇: 'London,' -> ['London', ',']
[16:35:42] [DEBUG] 🔍 分离词汇: 'always.' -> ['always', '.']
[16:35:42] [DEBUG] 🔍 分离词汇: 'Hello.' -> ['Hello', '.']
[16:35:42] [DEBUG] 🔍 分离词汇: 'Uh,' -> ['Uh', ',']
[16:35:42] [DEBUG] 🔍 分离词汇: 'uh,' -> ['uh', ',']
[16:35:42] [DEBUG] 🔍 分离词汇: 'yes.' -> ['yes', '.']
[16:35:42] [DEBUG] 🔍 分离词汇: 'Yes,' -> ['Yes', ',']
[16:35:42] [DEBUG] 🔍 分离词汇: 'Mr.' -> ['Mr', '.']
[16:35:42] [DEBUG] 🔍 分离词汇: 'Mokuba.' -> ['Mokuba', '.']
[16:35:42] [DEBUG] 🔍 分离词汇: 'call.' -> ['call', '.']
[16:35:42] [DEBUG] 🔍 分离词汇: 'Yes.' -> ['Yes', '.']
[16:35:42] [DEBUG] 🔍 分离词汇: 'Yes,' -> ['Yes', ',']
[16:35:42] [DEBUG] 🔍 分离词汇: 'Mr.' -> ['Mr', '.']
[16:35:42] [DEBUG] 🔍 分离词汇: 'Mokuba.' -> ['Mokuba', '.']
[16:35:42] [DEBUG] 🔍 分离词汇: 'Okay,' -> ['Okay', ',']
[16:35:42] [DEBUG] 🔍 分离词汇: 'Mr.' -> ['Mr', '.']
[16:35:42] [DEBUG] 🔍 分离词汇: 'Harry,' -> ['Harry', ',']
[16:35:42] [DEBUG] 🔍 分离词汇: '7:30,' -> ['7', ':', '30', ',']
[16:35:42] [DEBUG] 🔍 分离词汇: 'eh?' -> ['eh', '?']
[16:35:42] [DEBUG] 🔍 分离词汇: 'Okay,' -> ['Okay', ',']
[16:35:42] [DEBUG] 🔍 分离词汇: 'bye.' -> ['bye', '.']
[16:35:42] [DEBUG] 🔍 分离词汇: 'yours.' -> ['yours', '.']
[16:35:42] [DEBUG] 🔍 分离词汇: 'me.' -> ['me', '.']
[16:35:42] [DEBUG] 🔍 分离词汇: 'Mr.' -> ['Mr', '.']
[16:35:42] [DEBUG] 🔍 分离词汇: 'Mokuba?' -> ['Mokuba', '?']
[16:35:42] [DEBUG] 🔍 分离词汇: 'Yes.' -> ['Yes', '.']
[16:35:42] [DEBUG] 🔍 分离词汇: 'us.' -> ['us', '.']
[16:35:42] [DEBUG] 🔍 分离词汇: 'alive.' -> ['alive', '.']
[16:35:42] [DEBUG] 🔍 分离词汇: 'Mr.' -> ['Mr', '.']
[16:35:42] [DEBUG] 🔍 分离词汇: 'Mokuba,' -> ['Mokuba', ',']
[16:35:42] [DEBUG] 🔍 分离词汇: 'prolific.' -> ['prolific', '.']
[16:35:42] [DEBUG] 🔍 分离词汇: 'body,' -> ['body', ',']
[16:35:42] [DEBUG] 🔍 分离词汇: 'Howard.' -> ['Howard', '.']
[16:35:42] [DEBUG] 🔍 分离词汇: 'woman.' -> ['woman', '.']
[16:35:42] [DEBUG] 🔍 分离词汇: 'you.' -> ['you', '.']
[16:35:42] [DEBUG] 🔍 分离词汇: 'also.' -> ['also', '.']
[16:35:42] [DEBUG] 🔍 分离词汇: 'enough,' -> ['enough', ',']
[16:35:42] [DEBUG] 🔍 分离词汇: 'Mr.' -> ['Mr', '.']
[16:35:42] [DEBUG] 🔍 分离词汇: 'Mokuba.' -> ['Mokuba', '.']
[16:35:42] [DEBUG] 🔍 分离词汇: 'Good,' -> ['Good', ',']
[16:35:42] [DEBUG] 🔍 分离词汇: 'solid,' -> ['solid', ',']
[16:35:42] [DEBUG] 🔍 分离词汇: 'you,' -> ['you', ',']
[16:35:42] [DEBUG] 🔍 分离词汇: 'exchanges.' -> ['exchanges', '.']
[16:35:42] [DEBUG] 🔍 分离词汇: 'him!' -> ['him', '!']
[16:35:42] [DEBUG] 🔍 分离词汇: 'late.' -> ['late', '.']
[16:35:42] [DEBUG] 🔍 分离词汇: 'capsule.' -> ['capsule', '.']
[16:35:42] [DEBUG] 🔍 分离词汇: 'blast.' -> ['blast', '.']
[16:35:42] [DEBUG] 🔍 分离词汇: 'Fanatics.' -> ['Fanatics', '.']
[16:35:42] [DEBUG] 🔍 分离词汇: 'him.' -> ['him', '.']
[16:35:43] [DEBUG] 🔍 分离词汇: 'No.' -> ['No', '.']
[16:35:43] [DEBUG] 🔍 分离词汇: 'Mr.' -> ['Mr', '.']
[16:35:43] [DEBUG] 🔍 分离词汇: 'Mokuba,' -> ['Mokuba', ',']
[16:35:43] [DEBUG] 🔍 分离词汇: 'sir.' -> ['sir', '.']
[16:35:43] [DEBUG] 🔍 分离词汇: 'in.' -> ['in', '.']
[16:35:43] [DEBUG] 🔍 分离词汇: 'in.' -> ['in', '.']
[16:35:43] [DEBUG] 🔍 分离词汇: 'You're,' -> ["You're", ',']
[16:35:43] [DEBUG] 🔍 分离词汇: 'uh,' -> ['uh', ',']
[16:35:43] [DEBUG] 🔍 分离词汇: 'you.' -> ['you', '.']
[16:35:43] [DEBUG] 🔍 分离词汇: 'me?' -> ['me', '?']
[16:35:43] [DEBUG] 🔍 分离词汇: 'Um,' -> ['Um', ',']
[16:35:43] [DEBUG] 🔍 分离词汇: 'younger.' -> ['younger', '.']
[16:35:43] [DEBUG] 🔍 分离词汇: 'You,' -> ['You', ',']
[16:35:43] [DEBUG] 🔍 分离词汇: 'uh,' -> ['uh', ',']
[16:35:43] [DEBUG] 🔍 分离词汇: 'telephone.' -> ['telephone', '.']
[16:35:43] [DEBUG] 🔍 分离词汇: 'Uh,' -> ['Uh', ',']
[16:35:43] [DEBUG] 🔍 分离词汇: 'down?' -> ['down', '?']
[16:35:43] [DEBUG] 🔍 分离词汇: 'here?' -> ['here', '?']
[16:35:43] [DEBUG] 🔍 分离词汇: 'No.' -> ['No', '.']
[16:35:43] [DEBUG] 🔍 分离词汇: 'No,' -> ['No', ',']
[16:35:43] [DEBUG] 🔍 分离词汇: 'we've,' -> ["we've", ',']
[16:35:43] [DEBUG] 🔍 分离词汇: 'uh,' -> ['uh', ',']
[16:35:43] [DEBUG] 🔍 分离词汇: 'small,' -> ['small', ',']
[16:35:43] [DEBUG] 🔍 分离词汇: 'uh,' -> ['uh', ',']
[16:35:43] [DEBUG] 🔍 分离词汇: 'mishap.' -> ['mishap', '.']
[16:35:43] [DEBUG] 🔍 分离词汇: 'Uh,' -> ['Uh', ',']
[16:35:43] [DEBUG] 🔍 分离词汇: 'package.' -> ['package', '.']
[16:35:43] [DEBUG] 🔍 分离词汇: 'together.' -> ['together', '.']
[16:35:43] [DEBUG] 🔍 分离词汇: 'useless.' -> ['useless', '.']
[16:35:43] [DEBUG] 🔍 分离词汇: 'and,' -> ['and', ',']
[16:35:43] [DEBUG] 🔍 分离词汇: 'frankly,' -> ['frankly', ',']
[16:35:43] [DEBUG] 🔍 分离词汇: 'now.' -> ['now', '.']
[16:35:43] [DEBUG] 🔍 分离词汇: 'woman,' -> ['woman', ',']
[16:35:43] [DEBUG] 🔍 分离词汇: 'will,' -> ['will', ',']
[16:35:43] [DEBUG] 🔍 分离词汇: 'time,' -> ['time', ',']
[16:35:43] [DEBUG] 🔍 分离词汇: 'fish.' -> ['fish', '.']
[16:35:43] [DEBUG] 🔍 分离词汇: 'me.' -> ['me', '.']
[16:35:43] [DEBUG] 🔍 分离词汇: 'Yes.' -> ['Yes', '.']
[16:35:43] [DEBUG] 🔍 分离词汇: 'Yes.' -> ['Yes', '.']
[16:35:43] [DEBUG] 🔍 分离词汇: 'that.' -> ['that', '.']
[16:35:43] [DEBUG] 🔍 分离词汇: 'it?' -> ['it', '?']
[16:35:43] [DEBUG] 🔍 分离词汇: 'course.' -> ['course', '.']
[16:35:43] ℹ️ 词汇分离完成，分离了 93 个混合词汇，最终词汇数量: 366
[16:35:43] ℹ️ Parsed trimmed_Honey Baby, Honey Baby (1974)_part5_00-19-51-ElevenLabs.json in 0.975s
[16:35:43] [OK] ElevenLabs JSON解析完成，已保存: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part5_00-19-51-ElevenLabs-parsed.json
[16:35:43]   词汇数量: 366
[16:35:43]   语言: unknown
[16:35:43]   置信度: 0.00
[16:35:43] [TaskFlow] 分段转录和解析完成: trimmed_Honey Baby, Honey Baby (1974)_part5_00-19-51 - ElevenLabs
[16:35:43] [TaskFlow] 处理分段 6/19: trimmed_Honey Baby, Honey Baby (1974)_part6_00-23-39
[16:35:43] [TaskFlow] 开始转录分段: trimmed_Honey Baby, Honey Baby (1974)_part6_00-23-39 - ElevenLabs
[16:35:43] 开始ElevenLabs free模式转录: trimmed_Honey Baby, Honey Baby (1974)_part6_00-23-39 (应用层尝试 1/3)
[16:36:00] ElevenLabs转录完成: trimmed_Honey Baby, Honey Baby (1974)_part6_00-23-39 (应用层尝试 1)
[16:36:00] JSON文件已保存: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part6_00-23-39-ElevenLabs.json
[16:36:00] [TaskFlow] 分段转录成功: trimmed_Honey Baby, Honey Baby (1974)_part6_00-23-39 - ElevenLabs
[16:36:00] [TaskFlow] 开始解析分段结果: trimmed_Honey Baby, Honey Baby (1974)_part6_00-23-39 - ElevenLabs
[16:36:00] 开始解析 ElevenLabs 的JSON文件
[16:36:00] [DEBUG] 🔍 使用regex库的Unicode属性进行字符分类
[16:36:00] [DEBUG] Detecting format for data with keys: ['success', 'service', 'transcription_id', 'text', 'words', 'language_detected', 'confidence', 'processing_time', 'metadata', 'created_at']
[16:36:00] [DEBUG] Detected elevenlabs standardized format from service field
[16:36:00] [DEBUG] Parsing ElevenLabs format
[16:36:00] [DEBUG] ElevenLabs data keys: ['success', 'service', 'transcription_id', 'text', 'words', 'language_detected', 'confidence', 'processing_time', 'metadata', 'created_at']
[16:36:00] [DEBUG] ElevenLabs full_text length: 3877
[16:36:00] [DEBUG] ElevenLabs words count: 1489
[16:36:00] [DEBUG] ElevenLabs result: 745 words, confidence: 0.000, speakers: 5
[16:36:00] ℹ️ 开始词汇分离处理，原始词汇数量: 745
[16:36:00] [DEBUG] 🔍 分离词汇: 'doing?' -> ['doing', '?']
[16:36:00] [DEBUG] 🔍 分离词汇: 'American?' -> ['American', '?']
[16:36:00] [DEBUG] 🔍 分离词汇: 'skin.' -> ['skin', '.']
[16:36:00] [DEBUG] 🔍 分离词汇: 'me.' -> ['me', '.']
[16:36:00] [DEBUG] 🔍 分离词汇: 'there,' -> ['there', ',']
[16:36:00] [DEBUG] 🔍 分离词汇: 'sister.' -> ['sister', '.']
[16:36:00] [DEBUG] 🔍 分离词汇: 'Why?' -> ['Why', '?']
[16:36:00] [DEBUG] 🔍 分离词汇: 'team,' -> ['team', ',']
[16:36:00] [DEBUG] 🔍 分离词汇: 'you.' -> ['you', '.']
[16:36:00] [DEBUG] 🔍 分离词汇: 'Life.' -> ['Life', '.']
[16:36:00] [DEBUG] 🔍 分离词汇: 'No.' -> ['No', '.']
[16:36:00] [DEBUG] 🔍 分离词汇: 'Life.' -> ['Life', '.']
[16:36:00] [DEBUG] 🔍 分离词汇: 'fact,' -> ['fact', ',']
[16:36:00] [DEBUG] 🔍 分离词汇: 'you.' -> ['you', '.']
[16:36:00] [DEBUG] 🔍 分离词汇: 'want?' -> ['want', '?']
[16:36:00] [DEBUG] 🔍 分离词汇: 'me?' -> ['me', '?']
[16:36:00] [DEBUG] 🔍 分离词汇: 'years,' -> ['years', ',']
[16:36:00] [DEBUG] 🔍 分离词汇: 'mother,' -> ['mother', ',']
[16:36:00] [DEBUG] 🔍 分离词汇: 'sister,' -> ['sister', ',']
[16:36:00] [DEBUG] 🔍 分离词汇: 'girlfriend,' -> ['girlfriend', ',']
[16:36:00] [DEBUG] 🔍 分离词汇: 'girlfriend.' -> ['girlfriend', '.']
[16:36:00] [DEBUG] 🔍 分离词汇: 'short,' -> ['short', ',']
[16:36:00] [DEBUG] 🔍 分离词汇: 'pickup.' -> ['pickup', '.']
[16:36:00] [DEBUG] 🔍 分离词汇: 'Oh,' -> ['Oh', ',']
[16:36:00] [DEBUG] 🔍 分离词汇: 'no.' -> ['no', '.']
[16:36:00] [DEBUG] 🔍 分离词汇: 'dance,' -> ['dance', ',']
[16:36:00] [DEBUG] 🔍 分离词汇: 'stuff.' -> ['stuff', '.']
[16:36:00] [DEBUG] 🔍 分离词汇: 'fact,' -> ['fact', ',']
[16:36:00] [DEBUG] 🔍 分离词汇: 'girlfriend.' -> ['girlfriend', '.']
[16:36:00] [DEBUG] 🔍 分离词汇: 'ugly.' -> ['ugly', '.']
[16:36:00] [DEBUG] 🔍 分离词汇: 'No,' -> ['No', ',']
[16:36:00] [DEBUG] 🔍 分离词汇: 'you.' -> ['you', '.']
[16:36:00] [DEBUG] 🔍 分离词汇: 'opinion,' -> ['opinion', ',']
[16:36:00] [DEBUG] 🔍 分离词汇: 'Laura?' -> ['Laura', '?']
[16:36:00] [DEBUG] 🔍 分离词汇: 'name?' -> ['name', '?']
[16:36:00] [DEBUG] 🔍 分离词汇: 'hotel.' -> ['hotel', '.']
[16:36:00] [DEBUG] 🔍 分离词汇: 'beautiful.' -> ['beautiful', '.']
[16:36:00] [DEBUG] 🔍 分离词汇: 'Tutankhamun,' -> ['Tutankhamun', ',']
[16:36:00] [DEBUG] 🔍 分离词汇: 'Tut.' -> ['Tut', '.']
[16:36:00] [DEBUG] 🔍 分离词汇: 'me,' -> ['me', ',']
[16:36:00] [DEBUG] 🔍 分离词汇: 'this,' -> ['this', ',']
[16:36:00] [DEBUG] 🔍 分离词汇: 'uh,' -> ['uh', ',']
[16:36:00] [DEBUG] 🔍 分离词汇: 'someplace?' -> ['someplace', '?']
[16:36:00] [DEBUG] 🔍 分离词汇: 'extensive.' -> ['extensive', '.']
[16:36:00] [DEBUG] 🔍 分离词汇: 'place,' -> ['place', ',']
[16:36:00] [DEBUG] 🔍 分离词汇: 'baby.' -> ['baby', '.']
[16:36:00] [DEBUG] 🔍 分离词汇: 'Also,' -> ['Also', ',']
[16:36:00] [DEBUG] 🔍 分离词汇: 'you.' -> ['you', '.']
[16:36:00] [DEBUG] 🔍 分离词汇: 'him,' -> ['him', ',']
[16:36:00] [DEBUG] 🔍 分离词汇: 'Herb?' -> ['Herb', '?']
[16:36:00] [DEBUG] 🔍 分离词汇: 'me.' -> ['me', '.']
[16:36:00] [DEBUG] 🔍 分离词汇: 'you?' -> ['you', '?']
[16:36:00] [DEBUG] 🔍 分离词汇: 'you?' -> ['you', '?']
[16:36:00] [DEBUG] 🔍 分离词汇: 'museum.' -> ['museum', '.']
[16:36:00] [DEBUG] 🔍 分离词汇: 'museum?' -> ['museum', '?']
[16:36:00] [DEBUG] 🔍 分离词汇: 'Museum.' -> ['Museum', '.']
[16:36:00] [DEBUG] 🔍 分离词汇: 'Ha.' -> ['Ha', '.']
[16:36:00] [DEBUG] 🔍 分离词汇: 'Herb.' -> ['Herb', '.']
[16:36:00] [DEBUG] 🔍 分离词汇: 'mean,' -> ['mean', ',']
[16:36:00] [DEBUG] 🔍 分离词汇: 'understand,' -> ['understand', ',']
[16:36:00] [DEBUG] 🔍 分离词汇: 'Laura,' -> ['Laura', ',']
[16:36:00] [DEBUG] 🔍 分离词汇: 'show.' -> ['show', '.']
[16:36:00] [DEBUG] 🔍 分离词汇: 'Oh,' -> ['Oh', ',']
[16:36:00] [DEBUG] 🔍 分离词汇: 'understand.' -> ['understand', '.']
[16:36:00] [DEBUG] 🔍 分离词汇: 'photographed,' -> ['photographed', ',']
[16:36:00] [DEBUG] 🔍 分离词汇: 'projected,' -> ['projected', ',']
[16:36:00] [DEBUG] 🔍 分离词汇: 'happened,' -> ['happened', ',']
[16:36:00] [DEBUG] 🔍 分离词汇: 'right?' -> ['right', '?']
[16:36:00] [DEBUG] 🔍 分离词汇: 'Look,' -> ['Look', ',']
[16:36:00] [DEBUG] 🔍 分离词汇: 'Laura,' -> ['Laura', ',']
[16:36:00] [DEBUG] 🔍 分离词汇: 'reasonable.' -> ['reasonable', '.']
[16:36:00] [DEBUG] 🔍 分离词汇: 'museum,' -> ['museum', ',']
[16:36:00] [DEBUG] 🔍 分离词汇: 'right?' -> ['right', '?']
[16:36:00] [DEBUG] 🔍 分离词汇: 'Right.' -> ['Right', '.']
[16:36:00] [DEBUG] 🔍 分离词汇: 'at.' -> ['at', '.']
[16:36:00] [DEBUG] 🔍 分离词汇: 'won.' -> ['won', '.']
[16:36:00] [DEBUG] 🔍 分离词汇: 'look,' -> ['look', ',']
[16:36:00] [DEBUG] 🔍 分离词汇: 'Sam,' -> ['Sam', ',']
[16:36:00] [DEBUG] 🔍 分离词汇: 'trip,' -> ['trip', ',']
[16:36:00] [DEBUG] 🔍 分离词汇: 'it,' -> ['it', ',']
[16:36:00] [DEBUG] 🔍 分离词汇: 'myself,' -> ['myself', ',']
[16:36:00] [DEBUG] 🔍 分离词汇: 'choice.' -> ['choice', '.']
[16:36:00] [DEBUG] 🔍 分离词汇: 'things,' -> ['things', ',']
[16:36:00] [DEBUG] 🔍 分离词汇: 'places,' -> ['places', ',']
[16:36:00] [DEBUG] 🔍 分离词汇: 'time.' -> ['time', '.']
[16:36:00] [DEBUG] 🔍 分离词汇: 'Listen,' -> ['Listen', ',']
[16:36:00] [DEBUG] 🔍 分离词汇: 'Lewis.' -> ['Lewis', '.']
[16:36:00] [DEBUG] 🔍 分离词汇: 'Yeah?' -> ['Yeah', '?']
[16:36:01] [DEBUG] 🔍 分离词汇: 'No,' -> ['No', ',']
[16:36:01] [DEBUG] 🔍 分离词汇: 'no,' -> ['no', ',']
[16:36:01] [DEBUG] 🔍 分离词汇: 'no,' -> ['no', ',']
[16:36:01] [DEBUG] 🔍 分离词汇: 'Sam.' -> ['Sam', '.']
[16:36:01] [DEBUG] 🔍 分离词汇: 'cooperate.' -> ['cooperate', '.']
[16:36:01] [DEBUG] 🔍 分离词汇: 'No,' -> ['No', ',']
[16:36:01] [DEBUG] 🔍 分离词汇: 'clause.' -> ['clause', '.']
[16:36:01] [DEBUG] 🔍 分离词汇: 'Yes,' -> ['Yes', ',']
[16:36:01] [DEBUG] 🔍 分离词汇: 'did.' -> ['did', '.']
[16:36:01] [DEBUG] 🔍 分离词汇: 'ya,' -> ['ya', ',']
[16:36:01] [DEBUG] 🔍 分离词汇: 'face.' -> ['face', '.']
[16:36:01] [DEBUG] 🔍 分离词汇: 'Herb.' -> ['Herb', '.']
[16:36:01] [DEBUG] 🔍 分离词汇: 'Herb.' -> ['Herb', '.']
[16:36:01] [DEBUG] 🔍 分离词汇: 'Yes,' -> ['Yes', ',']
[16:36:01] [DEBUG] 🔍 分离词汇: 'did.' -> ['did', '.']
[16:36:01] [DEBUG] 🔍 分离词汇: 'When?' -> ['When', '?']
[16:36:01] [DEBUG] 🔍 分离词汇: 'When?' -> ['When', '?']
[16:36:01] [DEBUG] 🔍 分离词汇: 'form.' -> ['form', '.']
[16:36:01] [DEBUG] 🔍 分离词汇: 'in.' -> ['in', '.']
[16:36:01] [DEBUG] 🔍 分离词汇: 'Oh,' -> ['Oh', ',']
[16:36:01] [DEBUG] 🔍 分离词汇: 'no?' -> ['no', '?']
[16:36:01] [DEBUG] 🔍 分离词汇: 'did?' -> ['did', '?']
[16:36:01] [DEBUG] 🔍 分离词汇: 'here.' -> ['here', '.']
[16:36:01] [DEBUG] 🔍 分离词汇: 'idea.' -> ['idea', '.']
[16:36:01] [DEBUG] 🔍 分离词汇: 'Nonetheless,' -> ['Nonetheless', ',']
[16:36:01] [DEBUG] 🔍 分离词汇: 'it.' -> ['it', '.']
[16:36:01] [DEBUG] 🔍 分离词汇: 'Listen,' -> ['Listen', ',']
[16:36:01] [DEBUG] 🔍 分离词汇: 'Laura,' -> ['Laura', ',']
[16:36:01] [DEBUG] 🔍 分离词汇: 'I'm,' -> ["I'm", ',']
[16:36:01] [DEBUG] 🔍 分离词汇: 'want.' -> ['want', '.']
[16:36:01] [DEBUG] 🔍 分离词汇: 'Damn.' -> ['Damn', '.']
[16:36:01] [DEBUG] 🔍 分离词汇: 'person.' -> ['person', '.']
[16:36:01] [DEBUG] 🔍 分离词汇: 'Laura?' -> ['Laura', '?']
[16:36:01] [DEBUG] 🔍 分离词汇: 'Okay?' -> ['Okay', '?']
[16:36:01] [DEBUG] 🔍 分离词汇: 'say?' -> ['say', '?']
[16:36:01] [DEBUG] 🔍 分离词汇: 'say?' -> ['say', '?']
[16:36:01] [DEBUG] 🔍 分离词汇: 'say?' -> ['say', '?']
[16:36:01] [DEBUG] 🔍 分离词汇: 'said.' -> ['said', '.']
[16:36:01] [DEBUG] 🔍 分离词汇: 'said,' -> ['said', ',']
[16:36:01] [DEBUG] 🔍 分离词汇: 'said,' -> ['said', ',']
[16:36:01] [DEBUG] 🔍 分离词汇: '"We're' -> ['"', "We're"]
[16:36:01] [DEBUG] 🔍 分离词汇: 'settle.' -> ['settle', '.']
[16:36:01] [DEBUG] 🔍 分离词汇: 'promote."' -> ['promote', '."']
[16:36:01] [DEBUG] 🔍 分离词汇: 'cops.' -> ['cops', '.']
[16:36:01] [DEBUG] 🔍 分离词汇: 'Oh,' -> ['Oh', ',']
[16:36:01] [DEBUG] 🔍 分离词汇: 'cops.' -> ['cops', '.']
[16:36:01] [DEBUG] 🔍 分离词汇: 'Oh.' -> ['Oh', '.']
[16:36:01] [DEBUG] 🔍 分离词汇: 'said,' -> ['said', ',']
[16:36:01] [DEBUG] 🔍 分离词汇: 'women,' -> ['women', ',']
[16:36:01] [DEBUG] 🔍 分离词汇: 'women,' -> ['women', ',']
[16:36:01] [DEBUG] 🔍 分离词汇: 'American.' -> ['American', '.']
[16:36:01] [DEBUG] 🔍 分离词汇: 'crazy.' -> ['crazy', '.']
[16:36:01] [DEBUG] 🔍 分离词汇: 'boat.' -> ['boat', '.']
[16:36:01] [DEBUG] 🔍 分离词汇: 'Yeah,' -> ['Yeah', ',']
[16:36:01] [DEBUG] 🔍 分离词汇: 'thoroughly,' -> ['thoroughly', ',']
[16:36:01] [DEBUG] 🔍 分离词汇: 'women.' -> ['women', '.']
[16:36:01] [DEBUG] 🔍 分离词汇: 'game.' -> ['game', '.']
[16:36:01] [DEBUG] 🔍 分离词汇: 'that,' -> ['that', ',']
[16:36:01] [DEBUG] 🔍 分离词汇: 'Skip.' -> ['Skip', '.']
[16:36:01] [DEBUG] 🔍 分离词汇: 'Herb.' -> ['Herb', '.']
[16:36:01] [DEBUG] 🔍 分离词汇: 'exist,' -> ['exist', ',']
[16:36:01] [DEBUG] 🔍 分离词汇: 'exist.' -> ['exist', '.']
[16:36:01] [DEBUG] 🔍 分离词汇: 'clear,' -> ['clear', ',']
[16:36:01] [DEBUG] 🔍 分离词汇: 'right?' -> ['right', '?']
[16:36:01] [DEBUG] 🔍 分离词汇: 'Yeah,' -> ['Yeah', ',']
[16:36:01] [DEBUG] 🔍 分离词汇: 'that.' -> ['that', '.']
[16:36:01] [DEBUG] 🔍 分离词汇: 'way.' -> ['way', '.']
[16:36:01] [DEBUG] 🔍 分离词汇: 'Hey,' -> ['Hey', ',']
[16:36:01] [DEBUG] 🔍 分离词汇: 'Herb,' -> ['Herb', ',']
[16:36:01] [DEBUG] 🔍 分离词汇: 'fountain.' -> ['fountain', '.']
[16:36:01] [DEBUG] 🔍 分离词汇: 'Yeah.' -> ['Yeah', '.']
[16:36:01] [DEBUG] 🔍 分离词汇: 'Hey,' -> ['Hey', ',']
[16:36:01] [DEBUG] 🔍 分离词汇: 'Herb,' -> ['Herb', ',']
[16:36:01] [DEBUG] 🔍 分离词汇: 'picture.' -> ['picture', '.']
[16:36:01] [DEBUG] 🔍 分离词汇: 'beautiful,' -> ['beautiful', ',']
[16:36:01] [DEBUG] 🔍 分离词汇: 'they?' -> ['they', '?']
[16:36:01] [DEBUG] 🔍 分离词汇: 'Yes,' -> ['Yes', ',']
[16:36:01] [DEBUG] 🔍 分离词汇: 'yes,' -> ['yes', ',']
[16:36:01] [DEBUG] 🔍 分离词汇: 'are.' -> ['are', '.']
[16:36:01] [DEBUG] 🔍 分离词汇: 'here.' -> ['here', '.']
[16:36:01] [DEBUG] 🔍 分离词汇: 'Really?' -> ['Really', '?']
[16:36:01] [DEBUG] 🔍 分离词汇: 'show.' -> ['show', '.']
[16:36:01] [DEBUG] 🔍 分离词汇: 'Okay.' -> ['Okay', '.']
[16:36:01] [DEBUG] 🔍 分离词汇: 'Hey,' -> ['Hey', ',']
[16:36:01] [DEBUG] 🔍 分离词汇: 'Laura,' -> ['Laura', ',']
[16:36:01] [DEBUG] 🔍 分离词汇: 'this,' -> ['this', ',']
[16:36:01] [DEBUG] 🔍 分离词汇: 'Sam.' -> ['Sam', '.']
[16:36:01] [DEBUG] 🔍 分离词汇: 'outside.' -> ['outside', '.']
[16:36:01] [DEBUG] 🔍 分离词汇: 'Okay?' -> ['Okay', '?']
[16:36:01] [DEBUG] 🔍 分离词汇: 'Oh,' -> ['Oh', ',']
[16:36:01] [DEBUG] 🔍 分离词汇: 'okay.' -> ['okay', '.']
[16:36:01] [DEBUG] 🔍 分离词汇: 'question,' -> ['question', ',']
[16:36:01] [DEBUG] 🔍 分离词汇: 'men.' -> ['men', '.']
[16:36:01] [DEBUG] 🔍 分离词汇: 'Lewis?' -> ['Lewis', '?']
[16:36:01] [DEBUG] 🔍 分离词汇: 'Yes?' -> ['Yes', '?']
[16:36:01] [DEBUG] 🔍 分离词汇: 'Can,' -> ['Can', ',']
[16:36:01] [DEBUG] 🔍 分离词汇: 'hotel?' -> ['hotel', '?']
[16:36:01] [DEBUG] 🔍 分离词汇: 'alone.' -> ['alone', '.']
[16:36:01] [DEBUG] 🔍 分离词汇: 'Why?' -> ['Why', '?']
[16:36:01] [DEBUG] 🔍 分离词汇: 'Chiang,' -> ['Chiang', ',']
[16:36:01] [DEBUG] 🔍 分离词汇: 'woman,' -> ['woman', ',']
[16:36:01] [DEBUG] 🔍 分离词汇: 'missing.' -> ['missing', '.']
[16:36:01] [DEBUG] 🔍 分离词汇: 'is?' -> ['is', '?']
[16:36:01] ℹ️ 词汇分离完成，分离了 191 个混合词汇，最终词汇数量: 936
[16:36:01] ℹ️ Parsed trimmed_Honey Baby, Honey Baby (1974)_part6_00-23-39-ElevenLabs.json in 1.706s
[16:36:01] [OK] ElevenLabs JSON解析完成，已保存: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part6_00-23-39-ElevenLabs-parsed.json
[16:36:01]   词汇数量: 936
[16:36:01]   语言: unknown
[16:36:01]   置信度: 0.00
[16:36:01] [TaskFlow] 分段转录和解析完成: trimmed_Honey Baby, Honey Baby (1974)_part6_00-23-39 - ElevenLabs
[16:36:01] [TaskFlow] 处理分段 7/19: trimmed_Honey Baby, Honey Baby (1974)_part7_00-30-41
[16:36:01] [TaskFlow] 开始转录分段: trimmed_Honey Baby, Honey Baby (1974)_part7_00-30-41 - ElevenLabs
[16:36:01] 开始ElevenLabs free模式转录: trimmed_Honey Baby, Honey Baby (1974)_part7_00-30-41 (应用层尝试 1/3)
[16:36:09] ElevenLabs转录完成: trimmed_Honey Baby, Honey Baby (1974)_part7_00-30-41 (应用层尝试 1)
[16:36:09] JSON文件已保存: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part7_00-30-41-ElevenLabs.json
[16:36:09] [TaskFlow] 分段转录成功: trimmed_Honey Baby, Honey Baby (1974)_part7_00-30-41 - ElevenLabs
[16:36:09] [TaskFlow] 开始解析分段结果: trimmed_Honey Baby, Honey Baby (1974)_part7_00-30-41 - ElevenLabs
[16:36:09] 开始解析 ElevenLabs 的JSON文件
[16:36:09] [DEBUG] 🔍 使用regex库的Unicode属性进行字符分类
[16:36:09] [DEBUG] Detecting format for data with keys: ['success', 'service', 'transcription_id', 'text', 'words', 'language_detected', 'confidence', 'processing_time', 'metadata', 'created_at']
[16:36:09] [DEBUG] Detected elevenlabs standardized format from service field
[16:36:09] [DEBUG] Parsing ElevenLabs format
[16:36:09] [DEBUG] ElevenLabs data keys: ['success', 'service', 'transcription_id', 'text', 'words', 'language_detected', 'confidence', 'processing_time', 'metadata', 'created_at']
[16:36:09] [DEBUG] ElevenLabs full_text length: 1391
[16:36:09] [DEBUG] ElevenLabs words count: 547
[16:36:09] [DEBUG] ElevenLabs result: 274 words, confidence: 0.000, speakers: 5
[16:36:09] ℹ️ 开始词汇分离处理，原始词汇数量: 274
[16:36:09] [DEBUG] 🔍 分离词汇: 'Yeah.' -> ['Yeah', '.']
[16:36:09] [DEBUG] 🔍 分离词汇: 'up.' -> ['up', '.']
[16:36:09] [DEBUG] 🔍 分离词汇: 'up.' -> ['up', '.']
[16:36:09] [DEBUG] 🔍 分离词汇: 'Uh-huh.' -> ['Uh-huh', '.']
[16:36:09] [DEBUG] 🔍 分离词汇: 'right?' -> ['right', '?']
[16:36:09] [DEBUG] 🔍 分离词汇: 'Yeah.' -> ['Yeah', '.']
[16:36:09] [DEBUG] 🔍 分离词汇: 'air.' -> ['air', '.']
[16:36:09] [DEBUG] 🔍 分离词汇: 'slow.' -> ['slow', '.']
[16:36:09] [DEBUG] 🔍 分离词汇: 'slow.' -> ['slow', '.']
[16:36:09] [DEBUG] 🔍 分离词汇: 'turning.' -> ['turning', '.']
[16:36:09] [DEBUG] 🔍 分离词汇: 'Aah.' -> ['Aah', '.']
[16:36:09] [DEBUG] 🔍 分离词汇: 'Wait.' -> ['Wait', '.']
[16:36:09] [DEBUG] 🔍 分离词汇: 'there.' -> ['there', '.']
[16:36:09] [DEBUG] 🔍 分离词汇: 'there.' -> ['there', '.']
[16:36:09] [DEBUG] 🔍 分离词汇: 'it?' -> ['it', '?']
[16:36:09] [DEBUG] 🔍 分离词汇: 'see.' -> ['see', '.']
[16:36:09] [DEBUG] 🔍 分离词汇: 'it?' -> ['it', '?']
[16:36:09] [DEBUG] 🔍 分离词汇: 'Oh,' -> ['Oh', ',']
[16:36:09] [DEBUG] 🔍 分离词汇: 'the...' -> ['the', '...']
[16:36:09] [DEBUG] 🔍 分离词汇: 'Fabulous.' -> ['Fabulous', '.']
[16:36:09] [DEBUG] 🔍 分离词汇: 'illusion.' -> ['illusion', '.']
[16:36:09] [DEBUG] 🔍 分离词汇: 'Oh!' -> ['Oh', '!']
[16:36:09] [DEBUG] 🔍 分离词汇: 'gosh.' -> ['gosh', '.']
[16:36:09] [DEBUG] 🔍 分离词汇: 'thinking,' -> ['thinking', ',']
[16:36:09] [DEBUG] 🔍 分离词汇: 'Laura?' -> ['Laura', '?']
[16:36:09] [DEBUG] 🔍 分离词汇: 'That,' -> ['That', ',']
[16:36:09] [DEBUG] 🔍 分离词汇: 'uh...' -> ['uh', '...']
[16:36:09] [DEBUG] 🔍 分离词汇: 'thing,' -> ['thing', ',']
[16:36:09] [DEBUG] 🔍 分离词汇: 'uh,' -> ['uh', ',']
[16:36:09] [DEBUG] 🔍 分离词汇: 'that...' -> ['that', '...']
[16:36:09] [DEBUG] 🔍 分离词汇: 'know,' -> ['know', ',']
[16:36:09] [DEBUG] 🔍 分离词汇: 'now.' -> ['now', '.']
[16:36:09] [DEBUG] 🔍 分离词汇: 'lip.' -> ['lip', '.']
[16:36:09] [DEBUG] 🔍 分离词汇: 'Now,' -> ['Now', ',']
[16:36:09] [DEBUG] 🔍 分离词汇: 'minute.' -> ['minute', '.']
[16:36:09] [DEBUG] 🔍 分离词汇: 'pant-suit,' -> ['pant-suit', ',']
[16:36:09] [DEBUG] 🔍 分离词汇: 'hairdo?' -> ['hairdo', '?']
[16:36:09] [DEBUG] 🔍 分离词汇: 'it.' -> ['it', '.']
[16:36:09] [DEBUG] 🔍 分离词汇: 'it.' -> ['it', '.']
[16:36:09] [DEBUG] 🔍 分离词汇: 'Sam,' -> ['Sam', ',']
[16:36:09] [DEBUG] 🔍 分离词汇: 'me.' -> ['me', '.']
[16:36:09] [DEBUG] 🔍 分离词汇: 'fashion...' -> ['fashion', '...']
[16:36:09] [DEBUG] 🔍 分离词汇: 'symbol.' -> ['symbol', '.']
[16:36:10] [DEBUG] 🔍 分离词汇: 'me.' -> ['me', '.']
[16:36:10] [DEBUG] 🔍 分离词汇: 'Oh,' -> ['Oh', ',']
[16:36:10] [DEBUG] 🔍 分离词汇: 'wait,' -> ['wait', ',']
[16:36:10] [DEBUG] 🔍 分离词汇: 'wait,' -> ['wait', ',']
[16:36:10] [DEBUG] 🔍 分离词汇: 'wait,' -> ['wait', ',']
[16:36:10] [DEBUG] 🔍 分离词汇: 'wait,' -> ['wait', ',']
[16:36:10] [DEBUG] 🔍 分离词汇: 'wait,' -> ['wait', ',']
[16:36:10] [DEBUG] 🔍 分离词汇: 'wait.' -> ['wait', '.']
[16:36:10] [DEBUG] 🔍 分离词汇: 'that.' -> ['that', '.']
[16:36:10] [DEBUG] 🔍 分离词汇: 'Yes,' -> ['Yes', ',']
[16:36:10] [DEBUG] 🔍 分离词汇: 'yes,' -> ['yes', ',']
[16:36:10] [DEBUG] 🔍 分离词汇: 'yes,' -> ['yes', ',']
[16:36:10] [DEBUG] 🔍 分离词汇: 'did.' -> ['did', '.']
[16:36:10] [DEBUG] 🔍 分离词汇: 'Yes.' -> ['Yes', '.']
[16:36:10] [DEBUG] 🔍 分离词汇: 'image,' -> ['image', ',']
[16:36:10] [DEBUG] 🔍 分离词汇: 'Sam.' -> ['Sam', '.']
[16:36:10] [DEBUG] 🔍 分离词汇: 'look,' -> ['look', ',']
[16:36:10] [DEBUG] 🔍 分离词汇: 'loosely.' -> ['loosely', '.']
[16:36:10] [DEBUG] 🔍 分离词汇: 'program.' -> ['program', '.']
[16:36:10] [DEBUG] 🔍 分离词汇: 'Hey,' -> ['Hey', ',']
[16:36:10] [DEBUG] 🔍 分离词汇: 'Sam.' -> ['Sam', '.']
[16:36:10] [DEBUG] 🔍 分离词汇: 'What?' -> ['What', '?']
[16:36:10] [DEBUG] 🔍 分离词汇: 'up?' -> ['up', '?']
[16:36:10] [DEBUG] 🔍 分离词汇: 'day.' -> ['day', '.']
[16:36:10] [DEBUG] 🔍 分离词汇: 'Why,' -> ['Why', ',']
[16:36:10] [DEBUG] 🔍 分离词汇: 'tonight,' -> ['tonight', ',']
[16:36:10] [DEBUG] 🔍 分离词汇: 'day.' -> ['day', '.']
[16:36:10] [DEBUG] 🔍 分离词汇: 'it,' -> ['it', ',']
[16:36:10] [DEBUG] 🔍 分离词汇: 'Sam.' -> ['Sam', '.']
[16:36:10] [DEBUG] 🔍 分离词汇: 'prize.' -> ['prize', '.']
[16:36:10] [DEBUG] 🔍 分离词汇: 'you,' -> ['you', ',']
[16:36:10] [DEBUG] 🔍 分离词汇: 'Sam.' -> ['Sam', '.']
[16:36:10] [DEBUG] 🔍 分离词汇: 'Uh,' -> ['Uh', ',']
[16:36:10] [DEBUG] 🔍 分离词汇: 'wait.' -> ['wait', '.']
[16:36:10] [DEBUG] 🔍 分离词汇: 'second.' -> ['second', '.']
[16:36:10] [DEBUG] 🔍 分离词汇: 'really,' -> ['really', ',']
[16:36:10] [DEBUG] 🔍 分离词汇: 'uh...' -> ['uh', '...']
[16:36:10] [DEBUG] 🔍 分离词汇: 'this,' -> ['this', ',']
[16:36:10] [DEBUG] 🔍 分离词汇: 'that,' -> ['that', ',']
[16:36:10] [DEBUG] 🔍 分离词汇: 'uh,' -> ['uh', ',']
[16:36:10] [DEBUG] 🔍 分离词汇: 'me.' -> ['me', '.']
[16:36:10] [DEBUG] 🔍 分离词汇: 'Herb,' -> ['Herb', ',']
[16:36:10] [DEBUG] 🔍 分离词汇: 'beautiful.' -> ['beautiful', '.']
[16:36:10] [DEBUG] 🔍 分离词汇: 'Thanks,' -> ['Thanks', ',']
[16:36:10] [DEBUG] 🔍 分离词汇: 'Herb.' -> ['Herb', '.']
[16:36:10] [DEBUG] 🔍 分离词汇: 'Oh,' -> ['Oh', ',']
[16:36:10] [DEBUG] 🔍 分离词汇: 'no,' -> ['no', ',']
[16:36:10] [DEBUG] 🔍 分离词汇: 'me.' -> ['me', '.']
[16:36:10] [DEBUG] 🔍 分离词汇: 'thank?' -> ['thank', '?']
[16:36:10] [DEBUG] 🔍 分离词汇: 'Egypt.' -> ['Egypt', '.']
[16:36:10] [DEBUG] 🔍 分离词汇: 'Oh,' -> ['Oh', ',']
[16:36:10] [DEBUG] 🔍 分离词汇: 'ho,' -> ['ho', ',']
[16:36:10] [DEBUG] 🔍 分离词汇: 'ho,' -> ['ho', ',']
[16:36:10] [DEBUG] 🔍 分离词汇: 'ho.' -> ['ho', '.']
[16:36:10] [DEBUG] 🔍 分离词汇: 'Lebanon.' -> ['Lebanon', '.']
[16:36:10] ℹ️ 词汇分离完成，分离了 98 个混合词汇，最终词汇数量: 372
[16:36:10] ℹ️ Parsed trimmed_Honey Baby, Honey Baby (1974)_part7_00-30-41-ElevenLabs.json in 0.858s
[16:36:10] [OK] ElevenLabs JSON解析完成，已保存: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part7_00-30-41-ElevenLabs-parsed.json
[16:36:10]   词汇数量: 372
[16:36:10]   语言: unknown
[16:36:10]   置信度: 0.00
[16:36:10] [TaskFlow] 分段转录和解析完成: trimmed_Honey Baby, Honey Baby (1974)_part7_00-30-41 - ElevenLabs
[16:36:10] [TaskFlow] 处理分段 8/19: trimmed_Honey Baby, Honey Baby (1974)_part8_00-34-17
[16:36:10] [TaskFlow] 开始转录分段: trimmed_Honey Baby, Honey Baby (1974)_part8_00-34-17 - ElevenLabs
[16:36:10] 开始ElevenLabs free模式转录: trimmed_Honey Baby, Honey Baby (1974)_part8_00-34-17 (应用层尝试 1/3)
[16:36:16] 已打开项目目录: E:\github\EvaTrans\projects
[16:36:21] ElevenLabs转录完成: trimmed_Honey Baby, Honey Baby (1974)_part8_00-34-17 (应用层尝试 1)
[16:36:21] JSON文件已保存: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part8_00-34-17-ElevenLabs.json
[16:36:21] [TaskFlow] 分段转录成功: trimmed_Honey Baby, Honey Baby (1974)_part8_00-34-17 - ElevenLabs
[16:36:21] [TaskFlow] 开始解析分段结果: trimmed_Honey Baby, Honey Baby (1974)_part8_00-34-17 - ElevenLabs
[16:36:21] 开始解析 ElevenLabs 的JSON文件
[16:36:21] [DEBUG] 🔍 使用regex库的Unicode属性进行字符分类
[16:36:21] [DEBUG] Detecting format for data with keys: ['success', 'service', 'transcription_id', 'text', 'words', 'language_detected', 'confidence', 'processing_time', 'metadata', 'created_at']
[16:36:21] [DEBUG] Detected elevenlabs standardized format from service field
[16:36:21] [DEBUG] Parsing ElevenLabs format
[16:36:21] [DEBUG] ElevenLabs data keys: ['success', 'service', 'transcription_id', 'text', 'words', 'language_detected', 'confidence', 'processing_time', 'metadata', 'created_at']
[16:36:21] [DEBUG] ElevenLabs full_text length: 2454
[16:36:21] [DEBUG] ElevenLabs words count: 935
[16:36:21] [DEBUG] ElevenLabs result: 468 words, confidence: 0.000, speakers: 4
[16:36:21] ℹ️ 开始词汇分离处理，原始词汇数量: 468
[16:36:21] [DEBUG] 🔍 分离词汇: 'pillar.' -> ['pillar', '.']
[16:36:21] [DEBUG] 🔍 分离词汇: 'Natural.' -> ['Natural', '.']
[16:36:21] [DEBUG] 🔍 分离词汇: 'Yeah,' -> ['Yeah', ',']
[16:36:21] [DEBUG] 🔍 分离词汇: 'yeah.' -> ['yeah', '.']
[16:36:21] [DEBUG] 🔍 分离词汇: 'Oh.' -> ['Oh', '.']
[16:36:21] [DEBUG] 🔍 分离词汇: 'Oh.' -> ['Oh', '.']
[16:36:21] [DEBUG] 🔍 分离词汇: 'her.' -> ['her', '.']
[16:36:21] [DEBUG] 🔍 分离词汇: 'Uh,' -> ['Uh', ',']
[16:36:21] [DEBUG] 🔍 分离词汇: 'like,' -> ['like', ',']
[16:36:21] [DEBUG] 🔍 分离词汇: 'like,' -> ['like', ',']
[16:36:21] [DEBUG] 🔍 分离词汇: 'uh,' -> ['uh', ',']
[16:36:21] [DEBUG] 🔍 分离词汇: 'Nefertiti.' -> ['Nefertiti', '.']
[16:36:21] [DEBUG] 🔍 分离词汇: 'ruined.' -> ['ruined', '.']
[16:36:21] [DEBUG] 🔍 分离词汇: 'Oh.' -> ['Oh', '.']
[16:36:21] [DEBUG] 🔍 分离词汇: 'it.' -> ['it', '.']
[16:36:22] [DEBUG] 🔍 分离词汇: 'Oh,' -> ['Oh', ',']
[16:36:22] [DEBUG] 🔍 分离词汇: 'oh.' -> ['oh', '.']
[16:36:22] [DEBUG] 🔍 分离词汇: 'troubled.' -> ['troubled', '.']
[16:36:22] [DEBUG] 🔍 分离词汇: 'Oh,' -> ['Oh', ',']
[16:36:22] [DEBUG] 🔍 分离词汇: 'no,' -> ['no', ',']
[16:36:22] [DEBUG] 🔍 分离词汇: 'perfect.' -> ['perfect', '.']
[16:36:22] [DEBUG] 🔍 分离词汇: 'sender.' -> ['sender', '.']
[16:36:22] [DEBUG] 🔍 分离词汇: 'be.' -> ['be', '.']
[16:36:22] [DEBUG] 🔍 分离词汇: 'Oh,' -> ['Oh', ',']
[16:36:22] [DEBUG] 🔍 分离词汇: 'why,' -> ['why', ',']
[16:36:22] [DEBUG] 🔍 分离词汇: 'needed,' -> ['needed', ',']
[16:36:22] [DEBUG] 🔍 分离词汇: 'romance.' -> ['romance', '.']
[16:36:22] [DEBUG] 🔍 分离词汇: 'Oh,' -> ['Oh', ',']
[16:36:22] [DEBUG] 🔍 分离词汇: 'guy.' -> ['guy', '.']
[16:36:22] [DEBUG] 🔍 分离词汇: 'Oh,' -> ['Oh', ',']
[16:36:22] [DEBUG] 🔍 分离词汇: 'something.' -> ['something', '.']
[16:36:22] [DEBUG] 🔍 分离词汇: 'Oh,' -> ['Oh', ',']
[16:36:22] [DEBUG] 🔍 分离词汇: 'flower.' -> ['flower', '.']
[16:36:22] [DEBUG] 🔍 分离词汇: 'crazy.' -> ['crazy', '.']
[16:36:22] [DEBUG] 🔍 分离词汇: 'Oh,' -> ['Oh', ',']
[16:36:22] [DEBUG] 🔍 分离词汇: 'Venice.' -> ['Venice', '.']
[16:36:22] [DEBUG] 🔍 分离词汇: 'Venice.' -> ['Venice', '.']
[16:36:22] [DEBUG] 🔍 分离词汇: 'know,' -> ['know', ',']
[16:36:22] [DEBUG] 🔍 分离词汇: 'Carr?' -> ['Carr', '?']
[16:36:22] [DEBUG] 🔍 分离词汇: 'know,' -> ['know', ',']
[16:36:22] [DEBUG] 🔍 分离词汇: 'which...' -> ['which', '...']
[16:36:22] [DEBUG] 🔍 分离词汇: 'mind.' -> ['mind', '.']
[16:36:22] [DEBUG] 🔍 分离词汇: 'picture,' -> ['picture', ',']
[16:36:22] [DEBUG] 🔍 分离词汇: 'Herb.' -> ['Herb', '.']
[16:36:22] [DEBUG] 🔍 分离词汇: 'pic...' -> ['pic', '...']
[16:36:22] [DEBUG] 🔍 分离词汇: 'a,' -> ['a', ',']
[16:36:22] [DEBUG] 🔍 分离词汇: 'get...' -> ['get', '...']
[16:36:22] [DEBUG] 🔍 分离词汇: 'dinner.' -> ['dinner', '.']
[16:36:22] [DEBUG] 🔍 分离词汇: 'load.' -> ['load', '.']
[16:36:22] [DEBUG] 🔍 分离词汇: 'Christ,' -> ['Christ', ',']
[16:36:22] [DEBUG] 🔍 分离词汇: 'Herb.' -> ['Herb', '.']
[16:36:22] [DEBUG] 🔍 分离词汇: 'tonight?' -> ['tonight', '?']
[16:36:22] [DEBUG] 🔍 分离词汇: 'can't,' -> ["can't", ',']
[16:36:22] [DEBUG] 🔍 分离词汇: 'um,' -> ['um', ',']
[16:36:22] [DEBUG] 🔍 分离词汇: 'um,' -> ['um', ',']
[16:36:22] [DEBUG] 🔍 分离词汇: 'tired.' -> ['tired', '.']
[16:36:22] [DEBUG] 🔍 分离词汇: 'Oh,' -> ['Oh', ',']
[16:36:22] [DEBUG] 🔍 分离词汇: 'dinner.' -> ['dinner', '.']
[16:36:22] [DEBUG] 🔍 分离词汇: 'hotel.' -> ['hotel', '.']
[16:36:22] [DEBUG] 🔍 分离词汇: 'me,' -> ['me', ',']
[16:36:22] [DEBUG] 🔍 分离词汇: 'ass.' -> ['ass', '.']
[16:36:22] [DEBUG] 🔍 分离词汇: 'prince.' -> ['prince', '.']
[16:36:22] [DEBUG] 🔍 分离词汇: 'Street.' -> ['Street', '.']
[16:36:22] [DEBUG] 🔍 分离词汇: 'breast.' -> ['breast', '.']
[16:36:22] [DEBUG] 🔍 分离词汇: 'see,' -> ['see', ',']
[16:36:22] [DEBUG] 🔍 分离词汇: 'woman,' -> ['woman', ',']
[16:36:22] [DEBUG] 🔍 分离词汇: 'us.' -> ['us', '.']
[16:36:22] [DEBUG] 🔍 分离词汇: 'joke,' -> ['joke', ',']
[16:36:22] [DEBUG] 🔍 分离词汇: 'dearly.' -> ['dearly', '.']
[16:36:22] [DEBUG] 🔍 分离词汇: 'faker.' -> ['faker', '.']
[16:36:22] [DEBUG] 🔍 分离词汇: 'formula.' -> ['formula', '.']
[16:36:22] [DEBUG] 🔍 分离词汇: 'night?' -> ['night', '?']
[16:36:22] [DEBUG] 🔍 分离词汇: 'depend.' -> ['depend', '.']
[16:36:22] [DEBUG] 🔍 分离词汇: 'what?' -> ['what', '?']
[16:36:22] [DEBUG] 🔍 分离词汇: 'together.' -> ['together', '.']
[16:36:22] [DEBUG] 🔍 分离词汇: 'yourself.' -> ['yourself', '.']
[16:36:22] [DEBUG] 🔍 分离词汇: 'know,' -> ['know', ',']
[16:36:22] [DEBUG] 🔍 分离词汇: 'money.' -> ['money', '.']
[16:36:22] [DEBUG] 🔍 分离词汇: 'Right,' -> ['Right', ',']
[16:36:22] [DEBUG] 🔍 分离词汇: 'dear.' -> ['dear', '.']
[16:36:22] [DEBUG] 🔍 分离词汇: 'are.' -> ['are', '.']
[16:36:22] [DEBUG] 🔍 分离词汇: 'you.' -> ['you', '.']
[16:36:22] [DEBUG] 🔍 分离词汇: '£10?' -> ['£', '10', '?']
[16:36:22] [DEBUG] 🔍 分离词汇: '$3.' -> ['$', '3', '.']
[16:36:22] [DEBUG] 🔍 分离词汇: 'Honey,' -> ['Honey', ',']
[16:36:22] [DEBUG] 🔍 分离词汇: 'baby,' -> ['baby', ',']
[16:36:22] [DEBUG] 🔍 分离词汇: 'look.' -> ['look', '.']
[16:36:22] [DEBUG] 🔍 分离词汇: 'on,' -> ['on', ',']
[16:36:22] [DEBUG] 🔍 分离词汇: 'money.' -> ['money', '.']
[16:36:22] [DEBUG] 🔍 分离词汇: 'this.' -> ['this', '.']
[16:36:22] [DEBUG] 🔍 分离词汇: 'little,' -> ['little', ',']
[16:36:22] [DEBUG] 🔍 分离词汇: 'here.' -> ['here', '.']
[16:36:22] [DEBUG] 🔍 分离词汇: 'Hey,' -> ['Hey', ',']
[16:36:22] [DEBUG] 🔍 分离词汇: 'look,' -> ['look', ',']
[16:36:22] [DEBUG] 🔍 分离词汇: 'baby.' -> ['baby', '.']
[16:36:22] [DEBUG] 🔍 分离词汇: 'lemonade.' -> ['lemonade', '.']
[16:36:22] [DEBUG] 🔍 分离词汇: 'on,' -> ['on', ',']
[16:36:22] [DEBUG] 🔍 分离词汇: 'baby.' -> ['baby', '.']
[16:36:22] [DEBUG] 🔍 分离词汇: 'money.' -> ['money', '.']
[16:36:22] [DEBUG] 🔍 分离词汇: 'now.' -> ['now', '.']
[16:36:22] [DEBUG] 🔍 分离词汇: 'careful.' -> ['careful', '.']
[16:36:22] [DEBUG] 🔍 分离词汇: 'Well,' -> ['Well', ',']
[16:36:22] [DEBUG] 🔍 分离词汇: 'you.' -> ['you', '.']
[16:36:22] [DEBUG] 🔍 分离词汇: 'them,' -> ['them', ',']
[16:36:22] [DEBUG] 🔍 分离词汇: 'man,' -> ['man', ',']
[16:36:22] [DEBUG] 🔍 分离词汇: 'years,' -> ['years', ',']
[16:36:22] [DEBUG] 🔍 分离词汇: 'right?' -> ['right', '?']
[16:36:22] [DEBUG] 🔍 分离词汇: 'it.' -> ['it', '.']
[16:36:22] [DEBUG] 🔍 分离词汇: 'Hmm.' -> ['Hmm', '.']
[16:36:22] [DEBUG] 🔍 分离词汇: 'cool.' -> ['cool', '.']
[16:36:22] [DEBUG] 🔍 分离词汇: 'late.' -> ['late', '.']
[16:36:22] [DEBUG] 🔍 分离词汇: 'right.' -> ['right', '.']
[16:36:22] [DEBUG] 🔍 分离词汇: 'time,' -> ['time', ',']
[16:36:22] [DEBUG] 🔍 分离词汇: 'hear?' -> ['hear', '?']
[16:36:22] [DEBUG] 🔍 分离词汇: 'money.' -> ['money', '.']
[16:36:22] [DEBUG] 🔍 分离词汇: 'Oh,' -> ['Oh', ',']
[16:36:22] [DEBUG] 🔍 分离词汇: 'well,' -> ['well', ',']
[16:36:22] [DEBUG] 🔍 分离词汇: 'long.' -> ['long', '.']
[16:36:22] [DEBUG] 🔍 分离词汇: 'Look,' -> ['Look', ',']
[16:36:22] [DEBUG] 🔍 分离词汇: 'you.' -> ['you', '.']
[16:36:22] [DEBUG] 🔍 分离词汇: 'either.' -> ['either', '.']
[16:36:22] [DEBUG] 🔍 分离词汇: 'him.' -> ['him', '.']
[16:36:22] [DEBUG] 🔍 分离词汇: 'Oh,' -> ['Oh', ',']
[16:36:22] [DEBUG] 🔍 分离词汇: 'doing?' -> ['doing', '?']
[16:36:22] [DEBUG] 🔍 分离词汇: 'Oh,' -> ['Oh', ',']
[16:36:22] [DEBUG] 🔍 分离词汇: 'here.' -> ['here', '.']
[16:36:22] [DEBUG] 🔍 分离词汇: 'you.' -> ['you', '.']
[16:36:22] [DEBUG] 🔍 分离词汇: 'woman.' -> ['woman', '.']
[16:36:22] [DEBUG] 🔍 分离词汇: 'Well,' -> ['Well', ',']
[16:36:22] [DEBUG] 🔍 分离词汇: 'that.' -> ['that', '.']
[16:36:22] [DEBUG] 🔍 分离词汇: 'Lebanon.' -> ['Lebanon', '.']
[16:36:22] [DEBUG] 🔍 分离词汇: 'Oh,' -> ['Oh', ',']
[16:36:22] [DEBUG] 🔍 分离词汇: 'tomorrow.' -> ['tomorrow', '.']
[16:36:22] [DEBUG] 🔍 分离词汇: 'way,' -> ['way', ',']
[16:36:22] [DEBUG] 🔍 分离词汇: 'flowers,' -> ['flowers', ',']
[16:36:22] [DEBUG] 🔍 分离词汇: 'also.' -> ['also', '.']
[16:36:22] [DEBUG] 🔍 分离词汇: 'nothing,' -> ['nothing', ',']
[16:36:22] [DEBUG] 🔍 分离词汇: 'ma'am.' -> ["ma'am", '.']
[16:36:22] [DEBUG] 🔍 分离词汇: 'eyes.' -> ['eyes', '.']
[16:36:22] [DEBUG] 🔍 分离词汇: 'Why?' -> ['Why', '?']
[16:36:22] [DEBUG] 🔍 分离词汇: 'signs.' -> ['signs', '.']
[16:36:22] [DEBUG] 🔍 分离词汇: 'Oh,' -> ['Oh', ',']
[16:36:22] [DEBUG] 🔍 分离词汇: 'right,' -> ['right', ',']
[16:36:22] [DEBUG] 🔍 分离词汇: 'right.' -> ['right', '.']
[16:36:22] [DEBUG] 🔍 分离词汇: 'closed.' -> ['closed', '.']
[16:36:22] ℹ️ 词汇分离完成，分离了 145 个混合词汇，最终词汇数量: 615
[16:36:22] ℹ️ Parsed trimmed_Honey Baby, Honey Baby (1974)_part8_00-34-17-ElevenLabs.json in 1.109s
[16:36:22] [OK] ElevenLabs JSON解析完成，已保存: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part8_00-34-17-ElevenLabs-parsed.json
[16:36:22]   词汇数量: 615
[16:36:22]   语言: unknown
[16:36:22]   置信度: 0.00
[16:36:22] [TaskFlow] 分段转录和解析完成: trimmed_Honey Baby, Honey Baby (1974)_part8_00-34-17 - ElevenLabs
[16:36:22] [TaskFlow] 处理分段 9/19: trimmed_Honey Baby, Honey Baby (1974)_part9_00-40-29
[16:36:22] [TaskFlow] 开始转录分段: trimmed_Honey Baby, Honey Baby (1974)_part9_00-40-29 - ElevenLabs
[16:36:23] 开始ElevenLabs free模式转录: trimmed_Honey Baby, Honey Baby (1974)_part9_00-40-29 (应用层尝试 1/3)
[16:36:29] ElevenLabs转录完成: trimmed_Honey Baby, Honey Baby (1974)_part9_00-40-29 (应用层尝试 1)
[16:36:29] JSON文件已保存: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part9_00-40-29-ElevenLabs.json
[16:36:29] [TaskFlow] 分段转录成功: trimmed_Honey Baby, Honey Baby (1974)_part9_00-40-29 - ElevenLabs
[16:36:29] [TaskFlow] 开始解析分段结果: trimmed_Honey Baby, Honey Baby (1974)_part9_00-40-29 - ElevenLabs
[16:36:29] 开始解析 ElevenLabs 的JSON文件
[16:36:29] [DEBUG] 🔍 使用regex库的Unicode属性进行字符分类
[16:36:29] [DEBUG] Detecting format for data with keys: ['success', 'service', 'transcription_id', 'text', 'words', 'language_detected', 'confidence', 'processing_time', 'metadata', 'created_at']
[16:36:29] [DEBUG] Detected elevenlabs standardized format from service field
[16:36:29] [DEBUG] Parsing ElevenLabs format
[16:36:29] [DEBUG] ElevenLabs data keys: ['success', 'service', 'transcription_id', 'text', 'words', 'language_detected', 'confidence', 'processing_time', 'metadata', 'created_at']
[16:36:29] [DEBUG] ElevenLabs full_text length: 1343
[16:36:29] [DEBUG] ElevenLabs words count: 489
[16:36:29] [DEBUG] ElevenLabs result: 245 words, confidence: 0.000, speakers: 3
[16:36:29] ℹ️ 开始词汇分离处理，原始词汇数量: 245
[16:36:29] [DEBUG] 🔍 分离词汇: 'Hello,' -> ['Hello', ',']
[16:36:29] [DEBUG] 🔍 分离词汇: 'cher.' -> ['cher', '.']
[16:36:29] [DEBUG] 🔍 分离词汇: 'Hello.' -> ['Hello', '.']
[16:36:29] [DEBUG] 🔍 分离词汇: 'mother.' -> ['mother', '.']
[16:36:29] [DEBUG] 🔍 分离词汇: 'mother.' -> ['mother', '.']
[16:36:29] [DEBUG] 🔍 分离词汇: 'dear.' -> ['dear', '.']
[16:36:29] [DEBUG] 🔍 分离词汇: 'mademoiselle.' -> ['mademoiselle', '.']
[16:36:29] [DEBUG] 🔍 分离词汇: 'Dora?' -> ['Dora', '?']
[16:36:29] [DEBUG] 🔍 分离词汇: 'Ah.' -> ['Ah', '.']
[16:36:29] [DEBUG] 🔍 分离词汇: 'prepele?' -> ['prepele', '?']
[16:36:29] [DEBUG] 🔍 分离词汇: 'train?' -> ['train', '?']
[16:36:30] [DEBUG] 🔍 分离词汇: 'Oui.' -> ['Oui', '.']
[16:36:30] [DEBUG] 🔍 分离词汇: 'traditionally,' -> ['traditionally', ',']
[16:36:30] [DEBUG] 🔍 分离词汇: 'cher.' -> ['cher', '.']
[16:36:30] [DEBUG] 🔍 分离词汇: 'Oh,' -> ['Oh', ',']
[16:36:30] [DEBUG] 🔍 分离词汇: 'yeah.' -> ['yeah', '.']
[16:36:30] [DEBUG] 🔍 分离词汇: 'Classy.' -> ['Classy', '.']
[16:36:30] [DEBUG] 🔍 分离词汇: 'up?' -> ['up', '?']
[16:36:30] [DEBUG] 🔍 分离词汇: 'No.' -> ['No', '.']
[16:36:30] [DEBUG] 🔍 分离词汇: 'me,' -> ['me', ',']
[16:36:30] [DEBUG] 🔍 分离词汇: 'you.' -> ['you', '.']
[16:36:30] [DEBUG] 🔍 分离词汇: 'castle.' -> ['castle', '.']
[16:36:30] [DEBUG] 🔍 分离词汇: 'century.' -> ['century', '.']
[16:36:30] [DEBUG] 🔍 分离词汇: 'much.' -> ['much', '.']
[16:36:30] [DEBUG] 🔍 分离词汇: 'Oh,' -> ['Oh', ',']
[16:36:30] [DEBUG] 🔍 分离词汇: 'I,' -> ['I', ',']
[16:36:30] [DEBUG] 🔍 分离词汇: 'know.' -> ['know', '.']
[16:36:30] [DEBUG] 🔍 分离词汇: 'up.' -> ['up', '.']
[16:36:30] [DEBUG] 🔍 分离词汇: 'No,' -> ['No', ',']
[16:36:30] [DEBUG] 🔍 分离词汇: 'no,' -> ['no', ',']
[16:36:30] [DEBUG] 🔍 分离词汇: 'no.' -> ['no', '.']
[16:36:30] [DEBUG] 🔍 分离词汇: 'up.' -> ['up', '.']
[16:36:30] [DEBUG] 🔍 分离词汇: 'clue.' -> ['clue', '.']
[16:36:30] [DEBUG] 🔍 分离词汇: 'What,' -> ['What', ',']
[16:36:30] [DEBUG] 🔍 分离词汇: 'keeper?' -> ['keeper', '?']
[16:36:30] [DEBUG] 🔍 分离词汇: 'clue?' -> ['clue', '?']
[16:36:30] [DEBUG] 🔍 分离词汇: 'Mm-hmm.' -> ['Mm-hmm', '.']
[16:36:30] [DEBUG] 🔍 分离词汇: 'keeper?' -> ['keeper', '?']
[16:36:30] [DEBUG] 🔍 分离词汇: 'Cain?' -> ['Cain', '?']
[16:36:30] [DEBUG] 🔍 分离词汇: 'Abel?' -> ['Abel', '?']
[16:36:30] [DEBUG] 🔍 分离词汇: 'Cain.' -> ['Cain', '.']
[16:36:30] [DEBUG] 🔍 分离词汇: 'Cain?' -> ['Cain', '?']
[16:36:30] [DEBUG] 🔍 分离词汇: 'boats.' -> ['boats', '.']
[16:36:30] [DEBUG] 🔍 分离词汇: 'me.' -> ['me', '.']
[16:36:30] [DEBUG] 🔍 分离词汇: 'Oh,' -> ['Oh', ',']
[16:36:30] [DEBUG] 🔍 分离词汇: 'no.' -> ['no', '.']
[16:36:30] [DEBUG] 🔍 分离词汇: 'word.' -> ['word', '.']
[16:36:30] [DEBUG] 🔍 分离词汇: 'Well,' -> ['Well', ',']
[16:36:30] [DEBUG] 🔍 分离词汇: 'connection?' -> ['connection', '?']
[16:36:30] [DEBUG] 🔍 分离词汇: 'paper.' -> ['paper', '.']
[16:36:30] [DEBUG] 🔍 分离词汇: 'Papyrus.' -> ['Papyrus', '.']
[16:36:30] [DEBUG] 🔍 分离词汇: 'Lebanon.' -> ['Lebanon', '.']
[16:36:30] [DEBUG] 🔍 分离词汇: 'byblos,' -> ['byblos', ',']
[16:36:30] [DEBUG] 🔍 分离词汇: 'Bible.' -> ['Bible', '.']
[16:36:30] [DEBUG] 🔍 分离词汇: 'Cain,' -> ['Cain', ',']
[16:36:30] [DEBUG] 🔍 分离词汇: 'Canaanites,' -> ['Canaanites', ',']
[16:36:30] [DEBUG] 🔍 分离词汇: 'Phoenicians,' -> ['Phoenicians', ',']
[16:36:30] [DEBUG] 🔍 分离词汇: 'states:' -> ['states', ':']
[16:36:30] [DEBUG] 🔍 分离词汇: 'Tyre,' -> ['Tyre', ',']
[16:36:30] [DEBUG] 🔍 分离词汇: 'Sidon,' -> ['Sidon', ',']
[16:36:30] [DEBUG] 🔍 分离词汇: 'Gebal.' -> ['Gebal', '.']
[16:36:30] [DEBUG] 🔍 分离词汇: 'Byblos,' -> ['Byblos', ',']
[16:36:30] [DEBUG] 🔍 分离词汇: 'Byblos.' -> ['Byblos', '.']
[16:36:30] [DEBUG] 🔍 分离词汇: 'on,' -> ['on', ',']
[16:36:30] [DEBUG] 🔍 分离词汇: 'sister.' -> ['sister', '.']
[16:36:30] [DEBUG] 🔍 分离词汇: 'brilliant.' -> ['brilliant', '.']
[16:36:30] [DEBUG] 🔍 分离词汇: 'Listen,' -> ['Listen', ',']
[16:36:30] [DEBUG] 🔍 分离词汇: 'honey,' -> ['honey', ',']
[16:36:30] [DEBUG] 🔍 分离词汇: 'home,' -> ['home', ',']
[16:36:30] [DEBUG] 🔍 分离词汇: 'know?' -> ['know', '?']
[16:36:30] [DEBUG] 🔍 分离词汇: 'Yeah.' -> ['Yeah', '.']
[16:36:30] [DEBUG] 🔍 分离词汇: 'Danessa.' -> ['Danessa', '.']
[16:36:30] [DEBUG] 🔍 分离词汇: 'What?' -> ['What', '?']
[16:36:30] [DEBUG] 🔍 分离词汇: 'you.' -> ['you', '.']
[16:36:30] [DEBUG] 🔍 分离词汇: 'well.' -> ['well', '.']
[16:36:30] [DEBUG] 🔍 分离词汇: 'will.' -> ['will', '.']
[16:36:30] [DEBUG] 🔍 分离词汇: 'you.' -> ['you', '.']
[16:36:30] [DEBUG] 🔍 分离词汇: 'job.' -> ['job', '.']
[16:36:30] [DEBUG] 🔍 分离词汇: 'can.' -> ['can', '.']
[16:36:30] [DEBUG] 🔍 分离词汇: 'down?' -> ['down', '?']
[16:36:30] [DEBUG] 🔍 分离词汇: 'Yes.' -> ['Yes', '.']
[16:36:30] [DEBUG] 🔍 分离词汇: 'something,' -> ['something', ',']
[16:36:30] [DEBUG] 🔍 分离词汇: 'mademoiselle,' -> ['mademoiselle', ',']
[16:36:30] [DEBUG] 🔍 分离词汇: 'traditional.' -> ['traditional', '.']
[16:36:30] [DEBUG] 🔍 分离词汇: 'Everything?' -> ['Everything', '?']
[16:36:30] ℹ️ 词汇分离完成，分离了 85 个混合词汇，最终词汇数量: 330
[16:36:30] ℹ️ Parsed trimmed_Honey Baby, Honey Baby (1974)_part9_00-40-29-ElevenLabs.json in 0.709s
[16:36:30] [OK] ElevenLabs JSON解析完成，已保存: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part9_00-40-29-ElevenLabs-parsed.json
[16:36:30]   词汇数量: 330
[16:36:30]   语言: unknown
[16:36:30]   置信度: 0.00
[16:36:30] [TaskFlow] 分段转录和解析完成: trimmed_Honey Baby, Honey Baby (1974)_part9_00-40-29 - ElevenLabs
[16:36:30] [TaskFlow] 处理分段 10/19: trimmed_Honey Baby, Honey Baby (1974)_part10_00-45-50
[16:36:30] [TaskFlow] 开始转录分段: trimmed_Honey Baby, Honey Baby (1974)_part10_00-45-50 - ElevenLabs
[16:36:30] 开始ElevenLabs free模式转录: trimmed_Honey Baby, Honey Baby (1974)_part10_00-45-50 (应用层尝试 1/3)
[16:36:40] ElevenLabs转录完成: trimmed_Honey Baby, Honey Baby (1974)_part10_00-45-50 (应用层尝试 1)
[16:36:40] JSON文件已保存: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part10_00-45-50-ElevenLabs.json
[16:36:40] [TaskFlow] 分段转录成功: trimmed_Honey Baby, Honey Baby (1974)_part10_00-45-50 - ElevenLabs
[16:36:40] [TaskFlow] 开始解析分段结果: trimmed_Honey Baby, Honey Baby (1974)_part10_00-45-50 - ElevenLabs
[16:36:40] 开始解析 ElevenLabs 的JSON文件
[16:36:40] [DEBUG] 🔍 使用regex库的Unicode属性进行字符分类
[16:36:40] [DEBUG] Detecting format for data with keys: ['success', 'service', 'transcription_id', 'text', 'words', 'language_detected', 'confidence', 'processing_time', 'metadata', 'created_at']
[16:36:40] [DEBUG] Detected elevenlabs standardized format from service field
[16:36:40] [DEBUG] Parsing ElevenLabs format
[16:36:40] [DEBUG] ElevenLabs data keys: ['success', 'service', 'transcription_id', 'text', 'words', 'language_detected', 'confidence', 'processing_time', 'metadata', 'created_at']
[16:36:40] [DEBUG] ElevenLabs full_text length: 1767
[16:36:40] [DEBUG] ElevenLabs words count: 687
[16:36:40] [DEBUG] ElevenLabs result: 344 words, confidence: 0.000, speakers: 3
[16:36:40] ℹ️ 开始词汇分离处理，原始词汇数量: 344
[16:36:40] [DEBUG] 🔍 分离词汇: 'know?' -> ['know', '?']
[16:36:40] [DEBUG] 🔍 分离词汇: 'Hm?' -> ['Hm', '?']
[16:36:40] [DEBUG] 🔍 分离词汇: 'thought?' -> ['thought', '?']
[16:36:40] [DEBUG] 🔍 分离词汇: 'that?' -> ['that', '?']
[16:36:40] [DEBUG] 🔍 分离词汇: 'trip.' -> ['trip', '.']
[16:36:40] [DEBUG] 🔍 分离词汇: 'was,' -> ['was', ',']
[16:36:40] [DEBUG] 🔍 分离词汇: '"I' -> ['"', 'I']
[16:36:40] [DEBUG] 🔍 分离词汇: 'involved."' -> ['involved', '."']
[16:36:40] [DEBUG] 🔍 分离词汇: 'involved?' -> ['involved', '?']
[16:36:40] [DEBUG] 🔍 分离词汇: 'Well,' -> ['Well', ',']
[16:36:40] [DEBUG] 🔍 分离词汇: 'woman,' -> ['woman', ',']
[16:36:40] [DEBUG] 🔍 分离词汇: 'African.' -> ['African', '.']
[16:36:40] [DEBUG] 🔍 分离词汇: 'Well,' -> ['Well', ',']
[16:36:40] [DEBUG] 🔍 分离词汇: 'woman?' -> ['woman', '?']
[16:36:40] [DEBUG] 🔍 分离词汇: 'say?' -> ['say', '?']
[16:36:40] [DEBUG] 🔍 分离词汇: 'Uh,' -> ['Uh', ',']
[16:36:40] [DEBUG] 🔍 分离词汇: 'were.' -> ['were', '.']
[16:36:40] [DEBUG] 🔍 分离词汇: 'Well,' -> ['Well', ',']
[16:36:40] [DEBUG] 🔍 分离词汇: 'woman?' -> ['woman', '?']
[16:36:40] [DEBUG] 🔍 分离词汇: 'that.' -> ['that', '.']
[16:36:40] [DEBUG] 🔍 分离词汇: 'joking.' -> ['joking', '.']
[16:36:40] [DEBUG] 🔍 分离词汇: 'Look,' -> ['Look', ',']
[16:36:40] [DEBUG] 🔍 分离词汇: 'Laura,' -> ['Laura', ',']
[16:36:40] [DEBUG] 🔍 分离词汇: 'No,' -> ['No', ',']
[16:36:40] [DEBUG] 🔍 分离词汇: 'please.' -> ['please', '.']
[16:36:40] [DEBUG] 🔍 分离词汇: 'wait.' -> ['wait', '.']
[16:36:40] [DEBUG] 🔍 分离词汇: 'Look,' -> ['Look', ',']
[16:36:40] [DEBUG] 🔍 分离词汇: 'schoolgirl.' -> ['schoolgirl', '.']
[16:36:40] [DEBUG] 🔍 分离词汇: 'were.' -> ['were', '.']
[16:36:40] [DEBUG] 🔍 分离词汇: 'Well,' -> ['Well', ',']
[16:36:40] [DEBUG] 🔍 分离词汇: 'did.' -> ['did', '.']
[16:36:40] [DEBUG] 🔍 分离词汇: 'that,' -> ['that', ',']
[16:36:40] [DEBUG] 🔍 分离词汇: 'setup.' -> ['setup', '.']
[16:36:40] [DEBUG] 🔍 分离词汇: 'Laura,' -> ['Laura', ',']
[16:36:40] [DEBUG] 🔍 分离词汇: 'something?' -> ['something', '?']
[16:36:40] [DEBUG] 🔍 分离词汇: 'Look,' -> ['Look', ',']
[16:36:40] [DEBUG] 🔍 分离词汇: 'look.' -> ['look', '.']
[16:36:40] [DEBUG] 🔍 分离词汇: 'some...' -> ['some', '...']
[16:36:40] [DEBUG] 🔍 分离词汇: 'mean?' -> ['mean', '?']
[16:36:40] [DEBUG] 🔍 分离词汇: 'Uh,' -> ['Uh', ',']
[16:36:40] [DEBUG] 🔍 分离词汇: 'uh,' -> ['uh', ',']
[16:36:40] [DEBUG] 🔍 分离词汇: 'minute.' -> ['minute', '.']
[16:36:40] [DEBUG] 🔍 分离词汇: 'now.' -> ['now', '.']
[16:36:40] [DEBUG] 🔍 分离词汇: 'before.' -> ['before', '.']
[16:36:40] [DEBUG] 🔍 分离词汇: 'said.' -> ['said', '.']
[16:36:40] [DEBUG] 🔍 分离词汇: 'Okay.' -> ['Okay', '.']
[16:36:40] [DEBUG] 🔍 分离词汇: 'wrong.' -> ['wrong', '.']
[16:36:40] [DEBUG] 🔍 分离词汇: 'Okay.' -> ['Okay', '.']
[16:36:40] [DEBUG] 🔍 分离词汇: 'you?' -> ['you', '?']
[16:36:40] [DEBUG] 🔍 分离词汇: 'wrong,' -> ['wrong', ',']
[16:36:40] [DEBUG] 🔍 分离词汇: 'too.' -> ['too', '.']
[16:36:40] [DEBUG] 🔍 分离词汇: 'sorry.' -> ['sorry', '.']
[16:36:40] [DEBUG] 🔍 分离词汇: 'thing.' -> ['thing', '.']
[16:36:40] [DEBUG] 🔍 分离词汇: 'Okay?' -> ['Okay', '?']
[16:36:40] [DEBUG] 🔍 分离词汇: 'See,' -> ['See', ',']
[16:36:40] [DEBUG] 🔍 分离词汇: 'Chan.' -> ['Chan', '.']
[16:36:40] [DEBUG] 🔍 分离词汇: 'Chan?' -> ['Chan', '?']
[16:36:40] [DEBUG] 🔍 分离词汇: 'Yeah,' -> ['Yeah', ',']
[16:36:40] [DEBUG] 🔍 分离词汇: 'boat.' -> ['boat', '.']
[16:36:40] [DEBUG] 🔍 分离词汇: 'her?' -> ['her', '?']
[16:36:40] [DEBUG] 🔍 分离词汇: 'later.' -> ['later', '.']
[16:36:40] [DEBUG] 🔍 分离词汇: 'now?' -> ['now', '?']
[16:36:40] [DEBUG] 🔍 分离词汇: 'drive.' -> ['drive', '.']
[16:36:40] [DEBUG] 🔍 分离词汇: 'cool.' -> ['cool', '.']
[16:36:40] [DEBUG] 🔍 分离词汇: 'know,' -> ['know', ',']
[16:36:40] [DEBUG] 🔍 分离词汇: 'place.' -> ['place', '.']
[16:36:40] [DEBUG] 🔍 分离词汇: 'it.' -> ['it', '.']
[16:36:40] [DEBUG] 🔍 分离词汇: 'Okay,' -> ['Okay', ',']
[16:36:40] [DEBUG] 🔍 分离词汇: 'but,' -> ['but', ',']
[16:36:40] [DEBUG] 🔍 分离词汇: 'um,' -> ['um', ',']
[16:36:40] [DEBUG] 🔍 分离词汇: 'favor.' -> ['favor', '.']
[16:36:41] [DEBUG] 🔍 分离词汇: 'that?' -> ['that', '?']
[16:36:41] [DEBUG] 🔍 分离词汇: 'surprises.' -> ['surprises', '.']
[16:36:41] [DEBUG] 🔍 分离词汇: 'temper.' -> ['temper', '.']
[16:36:41] [DEBUG] 🔍 分离词汇: 'temper.' -> ['temper', '.']
[16:36:41] [DEBUG] 🔍 分离词汇: 'don't?' -> ["don't", '?']
[16:36:41] [DEBUG] 🔍 分离词汇: 'No.' -> ['No', '.']
[16:36:41] [DEBUG] 🔍 分离词汇: 'fast?' -> ['fast', '?']
[16:36:41] [DEBUG] 🔍 分离词汇: 'No.' -> ['No', '.']
[16:36:41] [DEBUG] 🔍 分离词汇: 'No.' -> ['No', '.']
[16:36:41] [DEBUG] 🔍 分离词汇: 'Hm.' -> ['Hm', '.']
[16:36:41] [DEBUG] 🔍 分离词汇: 'you,' -> ['you', ',']
[16:36:41] [DEBUG] 🔍 分离词汇: 'though.' -> ['though', '.']
[16:36:41] [DEBUG] 🔍 分离词汇: 'No.' -> ['No', '.']
[16:36:41] [DEBUG] 🔍 分离词汇: 'much,' -> ['much', ',']
[16:36:41] [DEBUG] 🔍 分离词汇: 'much.' -> ['much', '.']
[16:36:41] [DEBUG] 🔍 分离词汇: 'No.' -> ['No', '.']
[16:36:41] [DEBUG] 🔍 分离词汇: 'No,' -> ['No', ',']
[16:36:41] [DEBUG] 🔍 分离词汇: 'seriously.' -> ['seriously', '.']
[16:36:41] [DEBUG] 🔍 分离词汇: 'little.' -> ['little', '.']
[16:36:41] ℹ️ 词汇分离完成，分离了 90 个混合词汇，最终词汇数量: 434
[16:36:41] ℹ️ Parsed trimmed_Honey Baby, Honey Baby (1974)_part10_00-45-50-ElevenLabs.json in 0.781s
[16:36:41] [OK] ElevenLabs JSON解析完成，已保存: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part10_00-45-50-ElevenLabs-parsed.json
[16:36:41]   词汇数量: 434
[16:36:41]   语言: unknown
[16:36:41]   置信度: 0.00
[16:36:41] [TaskFlow] 分段转录和解析完成: trimmed_Honey Baby, Honey Baby (1974)_part10_00-45-50 - ElevenLabs
[16:36:41] [TaskFlow] 处理分段 11/19: trimmed_Honey Baby, Honey Baby (1974)_part11_00-50-41
[16:36:41] [TaskFlow] 开始转录分段: trimmed_Honey Baby, Honey Baby (1974)_part11_00-50-41 - ElevenLabs
[16:36:41] 开始ElevenLabs free模式转录: trimmed_Honey Baby, Honey Baby (1974)_part11_00-50-41 (应用层尝试 1/3)
[16:36:47] ElevenLabs转录完成: trimmed_Honey Baby, Honey Baby (1974)_part11_00-50-41 (应用层尝试 1)
[16:36:47] JSON文件已保存: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part11_00-50-41-ElevenLabs.json
[16:36:47] [TaskFlow] 分段转录成功: trimmed_Honey Baby, Honey Baby (1974)_part11_00-50-41 - ElevenLabs
[16:36:47] [TaskFlow] 开始解析分段结果: trimmed_Honey Baby, Honey Baby (1974)_part11_00-50-41 - ElevenLabs
[16:36:47] 开始解析 ElevenLabs 的JSON文件
[16:36:47] [DEBUG] 🔍 使用regex库的Unicode属性进行字符分类
[16:36:47] [DEBUG] Detecting format for data with keys: ['success', 'service', 'transcription_id', 'text', 'words', 'language_detected', 'confidence', 'processing_time', 'metadata', 'created_at']
[16:36:47] [DEBUG] Detected elevenlabs standardized format from service field
[16:36:47] [DEBUG] Parsing ElevenLabs format
[16:36:47] [DEBUG] ElevenLabs data keys: ['success', 'service', 'transcription_id', 'text', 'words', 'language_detected', 'confidence', 'processing_time', 'metadata', 'created_at']
[16:36:47] [DEBUG] ElevenLabs full_text length: 1082
[16:36:47] [DEBUG] ElevenLabs words count: 431
[16:36:47] [DEBUG] ElevenLabs result: 216 words, confidence: 0.000, speakers: 2
[16:36:47] ℹ️ 开始词汇分离处理，原始词汇数量: 216
[16:36:47] [DEBUG] 🔍 分离词汇: 'you.' -> ['you', '.']
[16:36:47] [DEBUG] 🔍 分离词汇: 'them.' -> ['them', '.']
[16:36:47] [DEBUG] 🔍 分离词汇: 'see.' -> ['see', '.']
[16:36:47] [DEBUG] 🔍 分离词汇: 'here,' -> ['here', ',']
[16:36:47] [DEBUG] 🔍 分离词汇: 'habit,' -> ['habit', ',']
[16:36:47] [DEBUG] 🔍 分离词汇: 'know?' -> ['know', '?']
[16:36:47] [DEBUG] 🔍 分离词汇: 'steel.' -> ['steel', '.']
[16:36:47] [DEBUG] 🔍 分离词汇: 'you,' -> ['you', ',']
[16:36:47] [DEBUG] 🔍 分离词汇: 'it?' -> ['it', '?']
[16:36:47] [DEBUG] 🔍 分离词汇: 'Truly.' -> ['Truly', '.']
[16:36:47] [DEBUG] 🔍 分离词汇: 'know,' -> ['know', ',']
[16:36:47] [DEBUG] 🔍 分离词汇: 'friz,' -> ['friz', ',']
[16:36:47] [DEBUG] 🔍 分离词汇: 'that,' -> ['that', ',']
[16:36:47] [DEBUG] 🔍 分离词汇: 'uh,' -> ['uh', ',']
[16:36:47] [DEBUG] 🔍 分离词汇: 'No,' -> ['No', ',']
[16:36:47] [DEBUG] 🔍 分离词汇: 'no.' -> ['no', '.']
[16:36:47] [DEBUG] 🔍 分离词汇: 'my,' -> ['my', ',']
[16:36:47] [DEBUG] 🔍 分离词汇: 'my,' -> ['my', ',']
[16:36:47] [DEBUG] 🔍 分离词汇: 'brains.' -> ['brains', '.']
[16:36:47] [DEBUG] 🔍 分离词汇: 'thought,' -> ['thought', ',']
[16:36:47] [DEBUG] 🔍 分离词汇: 'know,' -> ['know', ',']
[16:36:47] [DEBUG] 🔍 分离词汇: 'crises,' -> ['crises', ',']
[16:36:47] [DEBUG] 🔍 分离词汇: 'they,' -> ['they', ',']
[16:36:47] [DEBUG] 🔍 分离词汇: 'panic.' -> ['panic', '.']
[16:36:47] [DEBUG] 🔍 分离词汇: 'stress,' -> ['stress', ',']
[16:36:47] [DEBUG] 🔍 分离词汇: 'people,' -> ['people', ',']
[16:36:47] [DEBUG] 🔍 分离词汇: 'really.' -> ['really', '.']
[16:36:47] [DEBUG] 🔍 分离词汇: 'it.' -> ['it', '.']
[16:36:47] [DEBUG] 🔍 分离词汇: 'well.' -> ['well', '.']
[16:36:47] [DEBUG] 🔍 分离词汇: 'matter?' -> ['matter', '?']
[16:36:47] [DEBUG] 🔍 分离词汇: 'Nothing.' -> ['Nothing', '.']
[16:36:47] [DEBUG] 🔍 分离词汇: 'wrong?' -> ['wrong', '?']
[16:36:47] [DEBUG] 🔍 分离词汇: 'Nothing.' -> ['Nothing', '.']
[16:36:47] [DEBUG] 🔍 分离词汇: 'No.' -> ['No', '.']
[16:36:47] [DEBUG] 🔍 分离词汇: 'Oh.' -> ['Oh', '.']
[16:36:47] [DEBUG] 🔍 分离词汇: 'out.' -> ['out', '.']
[16:36:47] [DEBUG] 🔍 分离词汇: 'out.' -> ['out', '.']
[16:36:47] [DEBUG] 🔍 分离词汇: 'me.' -> ['me', '.']
[16:36:47] [DEBUG] 🔍 分离词汇: 'Oh.' -> ['Oh', '.']
[16:36:47] [DEBUG] 🔍 分离词汇: 'Oh.' -> ['Oh', '.']
[16:36:47] [DEBUG] 🔍 分离词汇: 'Oh.' -> ['Oh', '.']
[16:36:47] [DEBUG] 🔍 分离词汇: 'gun.' -> ['gun', '.']
[16:36:48] [DEBUG] 🔍 分离词汇: 'Oh,' -> ['Oh', ',']
[16:36:48] [DEBUG] 🔍 分离词汇: 'no.' -> ['no', '.']
[16:36:48] [DEBUG] 🔍 分离词汇: 'Oh,' -> ['Oh', ',']
[16:36:48] [DEBUG] 🔍 分离词汇: 'no.' -> ['no', '.']
[16:36:48] [DEBUG] 🔍 分离词汇: 'What?' -> ['What', '?']
[16:36:48] [DEBUG] 🔍 分离词汇: 'on?' -> ['on', '?']
[16:36:48] [DEBUG] 🔍 分离词汇: 'shoes.' -> ['shoes', '.']
[16:36:48] [DEBUG] 🔍 分离词汇: 'Oh,' -> ['Oh', ',']
[16:36:48] [DEBUG] 🔍 分离词汇: 'it.' -> ['it', '.']
[16:36:48] [DEBUG] 🔍 分离词汇: 'Now?' -> ['Now', '?']
[16:36:48] [DEBUG] 🔍 分离词汇: 'Yes.' -> ['Yes', '.']
[16:36:48] [DEBUG] 🔍 分离词汇: 'Oh.' -> ['Oh', '.']
[16:36:48] [DEBUG] 🔍 分离词汇: 'Oh.' -> ['Oh', '.']
[16:36:48] [DEBUG] 🔍 分离词汇: 'Oh.' -> ['Oh', '.']
[16:36:48] [DEBUG] 🔍 分离词汇: 'Now,' -> ['Now', ',']
[16:36:48] [DEBUG] 🔍 分离词汇: 'listen.' -> ['listen', '.']
[16:36:48] [DEBUG] 🔍 分离词汇: 'Yes?' -> ['Yes', '?']
[16:36:48] [DEBUG] 🔍 分离词汇: 'here,' -> ['here', ',']
[16:36:48] [DEBUG] 🔍 分离词汇: 'now.' -> ['now', '.']
[16:36:48] [DEBUG] 🔍 分离词汇: 'right.' -> ['right', '.']
[16:36:48] [DEBUG] 🔍 分离词汇: 'Shh.' -> ['Shh', '.']
[16:36:48] [DEBUG] 🔍 分离词汇: 'coming.' -> ['coming', '.']
[16:36:48] [DEBUG] 🔍 分离词汇: 'right.' -> ['right', '.']
[16:36:48] [DEBUG] 🔍 分离词汇: 'Now,' -> ['Now', ',']
[16:36:48] [DEBUG] 🔍 分离词汇: 'look.' -> ['look', '.']
[16:36:48] [DEBUG] 🔍 分离词汇: 'close,' -> ['close', ',']
[16:36:48] [DEBUG] 🔍 分离词汇: 'gun.' -> ['gun', '.']
[16:36:48] [DEBUG] 🔍 分离词汇: 'gun?' -> ['gun', '?']
[16:36:48] [DEBUG] 🔍 分离词汇: 'right?' -> ['right', '?']
[16:36:48] [DEBUG] 🔍 分离词汇: 'Yeah.' -> ['Yeah', '.']
[16:36:48] [DEBUG] 🔍 分离词汇: 'here.' -> ['here', '.']
[16:36:48] [DEBUG] 🔍 分离词汇: 'it?' -> ['it', '?']
[16:36:48] [DEBUG] 🔍 分离词汇: 'Yeah.' -> ['Yeah', '.']
[16:36:48] [DEBUG] 🔍 分离词汇: 'throughout...' -> ['throughout', '...']
[16:36:48] [DEBUG] 🔍 分离词汇: 'Shoot!' -> ['Shoot', '!']
[16:36:48] [DEBUG] 🔍 分离词汇: 'Shoot!' -> ['Shoot', '!']
[16:36:48] ℹ️ 词汇分离完成，分离了 78 个混合词汇，最终词汇数量: 294
[16:36:48] ℹ️ Parsed trimmed_Honey Baby, Honey Baby (1974)_part11_00-50-41-ElevenLabs.json in 0.748s
[16:36:48] [OK] ElevenLabs JSON解析完成，已保存: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part11_00-50-41-ElevenLabs-parsed.json
[16:36:48]   词汇数量: 294
[16:36:48]   语言: unknown
[16:36:48]   置信度: 0.00
[16:36:48] [TaskFlow] 分段转录和解析完成: trimmed_Honey Baby, Honey Baby (1974)_part11_00-50-41 - ElevenLabs
[16:36:48] [TaskFlow] 处理分段 12/19: trimmed_Honey Baby, Honey Baby (1974)_part12_00-53-55
[16:36:48] [TaskFlow] 开始转录分段: trimmed_Honey Baby, Honey Baby (1974)_part12_00-53-55 - ElevenLabs
[16:36:48] 开始ElevenLabs free模式转录: trimmed_Honey Baby, Honey Baby (1974)_part12_00-53-55 (应用层尝试 1/3)
[16:36:53] ElevenLabs转录完成: trimmed_Honey Baby, Honey Baby (1974)_part12_00-53-55 (应用层尝试 1)
[16:36:53] JSON文件已保存: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part12_00-53-55-ElevenLabs.json
[16:36:53] [TaskFlow] 分段转录成功: trimmed_Honey Baby, Honey Baby (1974)_part12_00-53-55 - ElevenLabs
[16:36:53] [TaskFlow] 开始解析分段结果: trimmed_Honey Baby, Honey Baby (1974)_part12_00-53-55 - ElevenLabs
[16:36:53] 开始解析 ElevenLabs 的JSON文件
[16:36:53] [DEBUG] 🔍 使用regex库的Unicode属性进行字符分类
[16:36:53] [DEBUG] Detecting format for data with keys: ['success', 'service', 'transcription_id', 'text', 'words', 'language_detected', 'confidence', 'processing_time', 'metadata', 'created_at']
[16:36:53] [DEBUG] Detected elevenlabs standardized format from service field
[16:36:53] [DEBUG] Parsing ElevenLabs format
[16:36:53] [DEBUG] ElevenLabs data keys: ['success', 'service', 'transcription_id', 'text', 'words', 'language_detected', 'confidence', 'processing_time', 'metadata', 'created_at']
[16:36:53] [DEBUG] ElevenLabs full_text length: 974
[16:36:53] [DEBUG] ElevenLabs words count: 380
[16:36:53] [DEBUG] ElevenLabs result: 190 words, confidence: 0.000, speakers: 2
[16:36:53] ℹ️ 开始词汇分离处理，原始词汇数量: 190
[16:36:53] [DEBUG] 🔍 分离词汇: 'nothing,' -> ['nothing', ',']
[16:36:53] [DEBUG] 🔍 分离词汇: 'you?' -> ['you', '?']
[16:36:53] [DEBUG] 🔍 分离词汇: 'Ugh.' -> ['Ugh', '.']
[16:36:53] [DEBUG] 🔍 分离词汇: 'Ah.' -> ['Ah', '.']
[16:36:53] [DEBUG] 🔍 分离词汇: 'top?' -> ['top', '?']
[16:36:53] [DEBUG] 🔍 分离词汇: 'right.' -> ['right', '.']
[16:36:53] [DEBUG] 🔍 分离词汇: 'it.' -> ['it', '.']
[16:36:53] [DEBUG] 🔍 分离词汇: 'car?' -> ['car', '?']
[16:36:53] [DEBUG] 🔍 分离词汇: 'me.' -> ['me', '.']
[16:36:53] [DEBUG] 🔍 分离词汇: 'Liv?' -> ['Liv', '?']
[16:36:53] [DEBUG] 🔍 分离词汇: 'Mm.' -> ['Mm', '.']
[16:36:53] [DEBUG] 🔍 分离词汇: 'you?' -> ['you', '?']
[16:36:53] [DEBUG] 🔍 分离词汇: 'to.' -> ['to', '.']
[16:36:53] [DEBUG] 🔍 分离词汇: 'you.' -> ['you', '.']
[16:36:53] [DEBUG] 🔍 分离词汇: 'why?' -> ['why', '?']
[16:36:53] [DEBUG] 🔍 分离词汇: 'woman.' -> ['woman', '.']
[16:36:53] [DEBUG] 🔍 分离词汇: 'woman?' -> ['woman', '?']
[16:36:53] [DEBUG] 🔍 分离词汇: 'her.' -> ['her', '.']
[16:36:53] [DEBUG] 🔍 分离词汇: 'me?' -> ['me', '?']
[16:36:53] [DEBUG] 🔍 分离词汇: 'know,' -> ['know', ',']
[16:36:53] [DEBUG] 🔍 分离词汇: 'out.' -> ['out', '.']
[16:36:53] [DEBUG] 🔍 分离词汇: 'here,' -> ['here', ',']
[16:36:53] [DEBUG] 🔍 分离词汇: 'road.' -> ['road', '.']
[16:36:53] [DEBUG] 🔍 分离词汇: 'Look,' -> ['Look', ',']
[16:36:53] [DEBUG] 🔍 分离词汇: 'look,' -> ['look', ',']
[16:36:53] [DEBUG] 🔍 分离词汇: 'look,' -> ['look', ',']
[16:36:53] [DEBUG] 🔍 分离词汇: 'Liv.' -> ['Liv', '.']
[16:36:53] [DEBUG] 🔍 分离词汇: 'I,' -> ['I', ',']
[16:36:53] [DEBUG] 🔍 分离词汇: 'I,' -> ['I', ',']
[16:36:54] [DEBUG] 🔍 分离词汇: 'say,' -> ['say', ',']
[16:36:54] [DEBUG] 🔍 分离词汇: 'anywhere.' -> ['anywhere', '.']
[16:36:54] [DEBUG] 🔍 分离词汇: 'hurt.' -> ['hurt', '.']
[16:36:54] [DEBUG] 🔍 分离词汇: 'way?' -> ['way', '?']
[16:36:54] [DEBUG] 🔍 分离词汇: 'you?' -> ['you', '?']
[16:36:54] [DEBUG] 🔍 分离词汇: 'strong,' -> ['strong', ',']
[16:36:54] [DEBUG] 🔍 分离词汇: 'invulnerable.' -> ['invulnerable', '.']
[16:36:54] [DEBUG] 🔍 分离词汇: 'be.' -> ['be', '.']
[16:36:54] [DEBUG] 🔍 分离词汇: 'So,' -> ['So', ',']
[16:36:54] [DEBUG] 🔍 分离词汇: 'do?' -> ['do', '?']
[16:36:54] [DEBUG] 🔍 分离词汇: 'something.' -> ['something', '.']
[16:36:54] ℹ️ 词汇分离完成，分离了 40 个混合词汇，最终词汇数量: 230
[16:36:54] ℹ️ Parsed trimmed_Honey Baby, Honey Baby (1974)_part12_00-53-55-ElevenLabs.json in 0.472s
[16:36:54] [OK] ElevenLabs JSON解析完成，已保存: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part12_00-53-55-ElevenLabs-parsed.json
[16:36:54]   词汇数量: 230
[16:36:54]   语言: unknown
[16:36:54]   置信度: 0.00
[16:36:54] [TaskFlow] 分段转录和解析完成: trimmed_Honey Baby, Honey Baby (1974)_part12_00-53-55 - ElevenLabs
[16:36:54] [TaskFlow] 处理分段 13/19: trimmed_Honey Baby, Honey Baby (1974)_part13_00-58-16
[16:36:54] [TaskFlow] 开始转录分段: trimmed_Honey Baby, Honey Baby (1974)_part13_00-58-16 - ElevenLabs
[16:36:54] 开始ElevenLabs free模式转录: trimmed_Honey Baby, Honey Baby (1974)_part13_00-58-16 (应用层尝试 1/3)
[16:37:04] ElevenLabs转录完成: trimmed_Honey Baby, Honey Baby (1974)_part13_00-58-16 (应用层尝试 1)
[16:37:04] JSON文件已保存: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part13_00-58-16-ElevenLabs.json
[16:37:04] [TaskFlow] 分段转录成功: trimmed_Honey Baby, Honey Baby (1974)_part13_00-58-16 - ElevenLabs
[16:37:04] [TaskFlow] 开始解析分段结果: trimmed_Honey Baby, Honey Baby (1974)_part13_00-58-16 - ElevenLabs
[16:37:04] 开始解析 ElevenLabs 的JSON文件
[16:37:04] [DEBUG] 🔍 使用regex库的Unicode属性进行字符分类
[16:37:04] [DEBUG] Detecting format for data with keys: ['success', 'service', 'transcription_id', 'text', 'words', 'language_detected', 'confidence', 'processing_time', 'metadata', 'created_at']
[16:37:04] [DEBUG] Detected elevenlabs standardized format from service field
[16:37:04] [DEBUG] Parsing ElevenLabs format
[16:37:04] [DEBUG] ElevenLabs data keys: ['success', 'service', 'transcription_id', 'text', 'words', 'language_detected', 'confidence', 'processing_time', 'metadata', 'created_at']
[16:37:04] [DEBUG] ElevenLabs full_text length: 1672
[16:37:04] [DEBUG] ElevenLabs words count: 645
[16:37:04] [DEBUG] ElevenLabs result: 323 words, confidence: 0.000, speakers: 4
[16:37:04] ℹ️ 开始词汇分离处理，原始词汇数量: 323
[16:37:04] [DEBUG] 🔍 分离词汇: 'is?' -> ['is', '?']
[16:37:04] [DEBUG] 🔍 分离词汇: 'Lasufi.' -> ['Lasufi', '.']
[16:37:04] [DEBUG] 🔍 分离词汇: 'papers.' -> ['papers', '.']
[16:37:04] [DEBUG] 🔍 分离词汇: 'independence.' -> ['independence', '.']
[16:37:04] [DEBUG] 🔍 分离词汇: 'enemies,' -> ['enemies', ',']
[16:37:04] [DEBUG] 🔍 分离词汇: 'country,' -> ['country', ',']
[16:37:04] [DEBUG] 🔍 分离词汇: 'him,' -> ['him', ',']
[16:37:04] [DEBUG] 🔍 分离词汇: 'innocent...' -> ['innocent', '...']
[16:37:04] [DEBUG] 🔍 分离词汇: 'friend.' -> ['friend', '.']
[16:37:04] [DEBUG] 🔍 分离词汇: 'Well,' -> ['Well', ',']
[16:37:04] [DEBUG] 🔍 分离词汇: 'him.' -> ['him', '.']
[16:37:04] [DEBUG] 🔍 分离词汇: 'No,' -> ['No', ',']
[16:37:04] [DEBUG] 🔍 分离词汇: 'Lev.' -> ['Lev', '.']
[16:37:04] [DEBUG] 🔍 分离词汇: 'him.' -> ['him', '.']
[16:37:04] [DEBUG] 🔍 分离词汇: 'So,' -> ['So', ',']
[16:37:04] [DEBUG] 🔍 分离词汇: 'time?' -> ['time', '?']
[16:37:04] [DEBUG] 🔍 分离词汇: 'Harry?' -> ['Harry', '?']
[16:37:04] [DEBUG] 🔍 分离词汇: 'Yeah,' -> ['Yeah', ',']
[16:37:04] [DEBUG] 🔍 分离词汇: 'snake.' -> ['snake', '.']
[16:37:04] [DEBUG] 🔍 分离词汇: 'this.' -> ['this', '.']
[16:37:04] [DEBUG] 🔍 分离词汇: 'you?' -> ['you', '?']
[16:37:04] [DEBUG] 🔍 分离词汇: 'ton.' -> ['ton', '.']
[16:37:04] [DEBUG] 🔍 分离词汇: 'mobile.' -> ['mobile', '.']
[16:37:04] [DEBUG] 🔍 分离词汇: 'caviar.' -> ['caviar', '.']
[16:37:04] [DEBUG] 🔍 分离词汇: 'salmon.' -> ['salmon', '.']
[16:37:04] [DEBUG] 🔍 分离词汇: 'tonight?' -> ['tonight', '?']
[16:37:04] [DEBUG] 🔍 分离词汇: 'there?' -> ['there', '?']
[16:37:04] [DEBUG] 🔍 分离词汇: 'saloon,' -> ['saloon', ',']
[16:37:04] [DEBUG] 🔍 分离词汇: 'ward.' -> ['ward', '.']
[16:37:04] [DEBUG] 🔍 分离词汇: 'Angelo,' -> ['Angelo', ',']
[16:37:04] [DEBUG] 🔍 分离词汇: 'Angelo.' -> ['Angelo', '.']
[16:37:04] [DEBUG] 🔍 分离词汇: 'drinking.' -> ['drinking', '.']
[16:37:04] [DEBUG] 🔍 分离词汇: 'comprends?' -> ['comprends', '?']
[16:37:04] [DEBUG] 🔍 分离词汇: 'Si,' -> ['Si', ',']
[16:37:04] [DEBUG] 🔍 分离词汇: 'signore.' -> ['signore', '.']
[16:37:04] [DEBUG] 🔍 分离词汇: 'Okay.' -> ['Okay', '.']
[16:37:04] [DEBUG] 🔍 分离词汇: 'life.' -> ['life', '.']
[16:37:04] [DEBUG] 🔍 分离词汇: 'this.' -> ['this', '.']
[16:37:04] [DEBUG] 🔍 分离词汇: 'well.' -> ['well', '.']
[16:37:04] [DEBUG] 🔍 分离词汇: 'began,' -> ['began', ',']
[16:37:04] [DEBUG] 🔍 分离词汇: 'pants.' -> ['pants', '.']
[16:37:04] [DEBUG] 🔍 分离词汇: 'Oh,' -> ['Oh', ',']
[16:37:04] [DEBUG] 🔍 分离词汇: 'God,' -> ['God', ',']
[16:37:05] [DEBUG] 🔍 分离词汇: 'Skippy.' -> ['Skippy', '.']
[16:37:05] [DEBUG] 🔍 分离词汇: 'him?' -> ['him', '?']
[16:37:05] [DEBUG] 🔍 分离词汇: 'Skippy.' -> ['Skippy', '.']
[16:37:05] [DEBUG] 🔍 分离词汇: 'Skippy.' -> ['Skippy', '.']
[16:37:05] [DEBUG] 🔍 分离词汇: 'if...' -> ['if', '...']
[16:37:05] [DEBUG] 🔍 分离词汇: 'right,' -> ['right', ',']
[16:37:05] [DEBUG] 🔍 分离词汇: 'right,' -> ['right', ',']
[16:37:05] [DEBUG] 🔍 分离词汇: 'right.' -> ['right', '.']
[16:37:05] [DEBUG] 🔍 分离词汇: 'Well,' -> ['Well', ',']
[16:37:05] [DEBUG] 🔍 分离词汇: 'yeah.' -> ['yeah', '.']
[16:37:05] [DEBUG] 🔍 分离词汇: 'Yeah,' -> ['Yeah', ',']
[16:37:05] [DEBUG] 🔍 分离词汇: 'anyway.' -> ['anyway', '.']
[16:37:05] [DEBUG] 🔍 分离词汇: 'Yeah,' -> ['Yeah', ',']
[16:37:05] [DEBUG] 🔍 分离词汇: 'stone.' -> ['stone', '.']
[16:37:05] [DEBUG] 🔍 分离词汇: 'Right.' -> ['Right', '.']
[16:37:05] [DEBUG] 🔍 分离词汇: 'late.' -> ['late', '.']
[16:37:05] [DEBUG] 🔍 分离词汇: 'home.' -> ['home', '.']
[16:37:05] [DEBUG] 🔍 分离词汇: 'Well,' -> ['Well', ',']
[16:37:05] [DEBUG] 🔍 分离词汇: 'me?' -> ['me', '?']
[16:37:05] [DEBUG] 🔍 分离词汇: 'No,' -> ['No', ',']
[16:37:05] [DEBUG] 🔍 分离词汇: 'yet.' -> ['yet', '.']
[16:37:05] [DEBUG] 🔍 分离词汇: 'there,' -> ['there', ',']
[16:37:05] [DEBUG] 🔍 分离词汇: 'too,' -> ['too', ',']
[16:37:05] [DEBUG] 🔍 分离词汇: 'table.' -> ['table', '.']
[16:37:05] [DEBUG] 🔍 分离词汇: 'Bricktop?' -> ['Bricktop', '?']
[16:37:05] [DEBUG] 🔍 分离词汇: 'Yeah.' -> ['Yeah', '.']
[16:37:05] [DEBUG] 🔍 分离词汇: 'him.' -> ['him', '.']
[16:37:05] [DEBUG] 🔍 分离词汇: 'Okay.' -> ['Okay', '.']
[16:37:05] [DEBUG] 🔍 分离词汇: 'go?' -> ['go', '?']
[16:37:05] [DEBUG] 🔍 分离词汇: 'Mm-hmm.' -> ['Mm-hmm', '.']
[16:37:05] [DEBUG] 🔍 分离词汇: 'long.' -> ['long', '.']
[16:37:05] [DEBUG] 🔍 分离词汇: 'goodbye.' -> ['goodbye', '.']
[16:37:05] [DEBUG] 🔍 分离词汇: 'No,' -> ['No', ',']
[16:37:05] [DEBUG] 🔍 分离词汇: 'goodbye.' -> ['goodbye', '.']
[16:37:05] ℹ️ 词汇分离完成，分离了 77 个混合词汇，最终词汇数量: 400
[16:37:05] ℹ️ Parsed trimmed_Honey Baby, Honey Baby (1974)_part13_00-58-16-ElevenLabs.json in 0.733s
[16:37:05] [OK] ElevenLabs JSON解析完成，已保存: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part13_00-58-16-ElevenLabs-parsed.json
[16:37:05]   词汇数量: 400
[16:37:05]   语言: unknown
[16:37:05]   置信度: 0.00
[16:37:05] [TaskFlow] 分段转录和解析完成: trimmed_Honey Baby, Honey Baby (1974)_part13_00-58-16 - ElevenLabs
[16:37:05] [TaskFlow] 处理分段 14/19: trimmed_Honey Baby, Honey Baby (1974)_part14_01-05-07
[16:37:05] [TaskFlow] 开始转录分段: trimmed_Honey Baby, Honey Baby (1974)_part14_01-05-07 - ElevenLabs
[16:37:05] 开始ElevenLabs free模式转录: trimmed_Honey Baby, Honey Baby (1974)_part14_01-05-07 (应用层尝试 1/3)
[16:37:11] ElevenLabs转录完成: trimmed_Honey Baby, Honey Baby (1974)_part14_01-05-07 (应用层尝试 1)
[16:37:11] JSON文件已保存: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part14_01-05-07-ElevenLabs.json
[16:37:11] [TaskFlow] 分段转录成功: trimmed_Honey Baby, Honey Baby (1974)_part14_01-05-07 - ElevenLabs
[16:37:12] [TaskFlow] 开始解析分段结果: trimmed_Honey Baby, Honey Baby (1974)_part14_01-05-07 - ElevenLabs
[16:37:12] 开始解析 ElevenLabs 的JSON文件
[16:37:12] [DEBUG] 🔍 使用regex库的Unicode属性进行字符分类
[16:37:12] [DEBUG] Detecting format for data with keys: ['success', 'service', 'transcription_id', 'text', 'words', 'language_detected', 'confidence', 'processing_time', 'metadata', 'created_at']
[16:37:12] [DEBUG] Detected elevenlabs standardized format from service field
[16:37:12] [DEBUG] Parsing ElevenLabs format
[16:37:12] [DEBUG] ElevenLabs data keys: ['success', 'service', 'transcription_id', 'text', 'words', 'language_detected', 'confidence', 'processing_time', 'metadata', 'created_at']
[16:37:12] [DEBUG] ElevenLabs full_text length: 871
[16:37:12] [DEBUG] ElevenLabs words count: 335
[16:37:12] [DEBUG] ElevenLabs result: 168 words, confidence: 0.000, speakers: 4
[16:37:12] ℹ️ 开始词汇分离处理，原始词汇数量: 168
[16:37:12] [DEBUG] 🔍 分离词汇: 'she?' -> ['she', '?']
[16:37:12] [DEBUG] 🔍 分离词汇: 'before.' -> ['before', '.']
[16:37:12] [DEBUG] 🔍 分离词汇: 'Liv's.' -> ["Liv's", '.']
[16:37:12] [DEBUG] 🔍 分离词汇: 'No.' -> ['No', '.']
[16:37:12] [DEBUG] 🔍 分离词汇: 'Okay,' -> ['Okay', ',']
[16:37:12] [DEBUG] 🔍 分离词汇: 'Skippy.' -> ['Skippy', '.']
[16:37:12] [DEBUG] 🔍 分离词汇: 'you,' -> ['you', ',']
[16:37:12] [DEBUG] 🔍 分离词汇: 'go.' -> ['go', '.']
[16:37:12] [DEBUG] 🔍 分离词汇: 'here?' -> ['here', '?']
[16:37:12] [DEBUG] 🔍 分离词汇: 'here.' -> ['here', '.']
[16:37:12] [DEBUG] 🔍 分离词汇: 'go.' -> ['go', '.']
[16:37:12] [DEBUG] 🔍 分离词汇: 'it.' -> ['it', '.']
[16:37:12] [DEBUG] 🔍 分离词汇: 'get...' -> ['get', '...']
[16:37:12] [DEBUG] 🔍 分离词汇: 'important.' -> ['important', '.']
[16:37:12] [DEBUG] 🔍 分离词汇: 'you.' -> ['you', '.']
[16:37:12] [DEBUG] 🔍 分离词汇: 'important.' -> ['important', '.']
[16:37:12] [DEBUG] 🔍 分离词汇: 'it.' -> ['it', '.']
[16:37:12] [DEBUG] 🔍 分离词汇: 'mean...' -> ['mean', '...']
[16:37:12] [DEBUG] 🔍 分离词汇: 'Listen,' -> ['Listen', ',']
[16:37:12] [DEBUG] 🔍 分离词汇: 'here.' -> ['here', '.']
[16:37:12] [DEBUG] 🔍 分离词汇: 'back.' -> ['back', '.']
[16:37:12] [DEBUG] 🔍 分离词汇: 'that.' -> ['that', '.']
[16:37:12] [DEBUG] 🔍 分离词汇: 'right.' -> ['right', '.']
[16:37:12] [DEBUG] 🔍 分离词汇: 'Hey,' -> ['Hey', ',']
[16:37:12] [DEBUG] 🔍 分离词汇: 'now.' -> ['now', '.']
[16:37:12] [DEBUG] 🔍 分离词汇: 'cousin.' -> ['cousin', '.']
[16:37:12] [DEBUG] 🔍 分离词汇: 'down,' -> ['down', ',']
[16:37:12] [DEBUG] 🔍 分离词汇: 'her.' -> ['her', '.']
[16:37:12] [DEBUG] 🔍 分离词汇: 'Look,' -> ['Look', ',']
[16:37:12] [DEBUG] 🔍 分离词汇: 'tonight.' -> ['tonight', '.']
[16:37:12] [DEBUG] 🔍 分离词汇: 'right?' -> ['right', '?']
[16:37:12] [DEBUG] 🔍 分离词汇: 'Skippy.' -> ['Skippy', '.']
[16:37:12] [DEBUG] 🔍 分离词汇: 'right.' -> ['right', '.']
[16:37:12] [DEBUG] 🔍 分离词汇: 'later.' -> ['later', '.']
[16:37:12] [DEBUG] 🔍 分离词汇: 'Out,' -> ['Out', ',']
[16:37:12] [DEBUG] 🔍 分离词汇: 'out,' -> ['out', ',']
[16:37:12] [DEBUG] 🔍 分离词汇: 'out,' -> ['out', ',']
[16:37:12] [DEBUG] 🔍 分离词汇: 'out,' -> ['out', ',']
[16:37:12] [DEBUG] 🔍 分离词汇: 'out.' -> ['out', '.']
[16:37:12] [DEBUG] 🔍 分离词汇: 'Hmm.' -> ['Hmm', '.']
[16:37:12] [DEBUG] 🔍 分离词汇: 'Okay,' -> ['Okay', ',']
[16:37:12] [DEBUG] 🔍 分离词汇: 'it.' -> ['it', '.']
[16:37:12] [DEBUG] 🔍 分离词汇: 'Okay.' -> ['Okay', '.']
[16:37:12] [DEBUG] 🔍 分离词汇: 'now.' -> ['now', '.']
[16:37:12] [DEBUG] 🔍 分离词汇: 'Now,' -> ['Now', ',']
[16:37:12] [DEBUG] 🔍 分离词汇: 'trouble.' -> ['trouble', '.']
[16:37:12] [DEBUG] 🔍 分离词汇: 'on.' -> ['on', '.']
[16:37:12] [DEBUG] 🔍 分离词汇: 'on,' -> ['on', ',']
[16:37:12] [DEBUG] 🔍 分离词汇: 'baby.' -> ['baby', '.']
[16:37:12] [DEBUG] 🔍 分离词汇: 'me.' -> ['me', '.']
[16:37:12] [DEBUG] 🔍 分离词汇: 'No.' -> ['No', '.']
[16:37:12] [DEBUG] 🔍 分离词汇: 'right?' -> ['right', '?']
[16:37:12] [DEBUG] 🔍 分离词汇: 'right.' -> ['right', '.']
[16:37:12] [DEBUG] 🔍 分离词汇: 'Ciao.' -> ['Ciao', '.']
[16:37:12] [DEBUG] 🔍 分离词汇: 'careful.' -> ['careful', '.']
[16:37:12] [DEBUG] 🔍 分离词汇: 'Ah,' -> ['Ah', ',']
[16:37:12] [DEBUG] 🔍 分离词汇: 'now.' -> ['now', '.']
[16:37:12] [DEBUG] 🔍 分离词汇: 'bed,' -> ['bed', ',']
[16:37:12] [DEBUG] 🔍 分离词汇: 'huh?' -> ['huh', '?']
[16:37:12] ℹ️ 词汇分离完成，分离了 59 个混合词汇，最终词汇数量: 227
[16:37:12] ℹ️ Parsed trimmed_Honey Baby, Honey Baby (1974)_part14_01-05-07-ElevenLabs.json in 0.634s
[16:37:12] [OK] ElevenLabs JSON解析完成，已保存: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part14_01-05-07-ElevenLabs-parsed.json
[16:37:12]   词汇数量: 227
[16:37:12]   语言: unknown
[16:37:12]   置信度: 0.00
[16:37:12] [TaskFlow] 分段转录和解析完成: trimmed_Honey Baby, Honey Baby (1974)_part14_01-05-07 - ElevenLabs
[16:37:12] [TaskFlow] 处理分段 15/19: trimmed_Honey Baby, Honey Baby (1974)_part15_01-09-51
[16:37:12] [TaskFlow] 开始转录分段: trimmed_Honey Baby, Honey Baby (1974)_part15_01-09-51 - ElevenLabs
[16:37:12] 开始ElevenLabs free模式转录: trimmed_Honey Baby, Honey Baby (1974)_part15_01-09-51 (应用层尝试 1/3)
[16:37:25] ElevenLabs转录完成: trimmed_Honey Baby, Honey Baby (1974)_part15_01-09-51 (应用层尝试 1)
[16:37:25] JSON文件已保存: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part15_01-09-51-ElevenLabs.json
[16:37:25] [TaskFlow] 分段转录成功: trimmed_Honey Baby, Honey Baby (1974)_part15_01-09-51 - ElevenLabs
[16:37:25] [TaskFlow] 开始解析分段结果: trimmed_Honey Baby, Honey Baby (1974)_part15_01-09-51 - ElevenLabs
[16:37:25] 开始解析 ElevenLabs 的JSON文件
[16:37:25] [DEBUG] 🔍 使用regex库的Unicode属性进行字符分类
[16:37:25] [DEBUG] Detecting format for data with keys: ['success', 'service', 'transcription_id', 'text', 'words', 'language_detected', 'confidence', 'processing_time', 'metadata', 'created_at']
[16:37:25] [DEBUG] Detected elevenlabs standardized format from service field
[16:37:25] [DEBUG] Parsing ElevenLabs format
[16:37:25] [DEBUG] ElevenLabs data keys: ['success', 'service', 'transcription_id', 'text', 'words', 'language_detected', 'confidence', 'processing_time', 'metadata', 'created_at']
[16:37:25] [DEBUG] ElevenLabs full_text length: 2487
[16:37:25] [DEBUG] ElevenLabs words count: 958
[16:37:25] [DEBUG] ElevenLabs result: 479 words, confidence: 0.000, speakers: 6
[16:37:25] ℹ️ 开始词汇分离处理，原始词汇数量: 479
[16:37:25] [DEBUG] 🔍 分离词汇: 'Liv,' -> ['Liv', ',']
[16:37:25] [DEBUG] 🔍 分离词汇: 'breakfast.' -> ['breakfast', '.']
[16:37:25] [DEBUG] 🔍 分离词汇: 'Oh,' -> ['Oh', ',']
[16:37:25] [DEBUG] 🔍 分离词汇: 'flower.' -> ['flower', '.']
[16:37:25] [DEBUG] 🔍 分离词汇: 'on.' -> ['on', '.']
[16:37:25] [DEBUG] 🔍 分离词汇: 'tea.' -> ['tea', '.']
[16:37:25] [DEBUG] 🔍 分离词汇: 'container,' -> ['container', ',']
[16:37:25] [DEBUG] 🔍 分离词汇: 'Harry.' -> ['Harry', '.']
[16:37:25] [DEBUG] 🔍 分离词汇: 'what?' -> ['what', '?']
[16:37:25] [DEBUG] 🔍 分离词汇: 'me,' -> ['me', ',']
[16:37:25] [DEBUG] 🔍 分离词汇: 'Harry.' -> ['Harry', '.']
[16:37:25] [DEBUG] 🔍 分离词汇: 'too?' -> ['too', '?']
[16:37:25] [DEBUG] 🔍 分离词汇: 'No,' -> ['No', ',']
[16:37:25] [DEBUG] 🔍 分离词汇: 'idea.' -> ['idea', '.']
[16:37:25] [DEBUG] 🔍 分离词汇: 'fool,' -> ['fool', ',']
[16:37:25] [DEBUG] 🔍 分离词汇: 'Liv.' -> ['Liv', '.']
[16:37:25] [DEBUG] 🔍 分离词汇: 'container,' -> ['container', ',']
[16:37:25] [DEBUG] 🔍 分离词汇: 'disintegrate.' -> ['disintegrate', '.']
[16:37:25] [DEBUG] 🔍 分离词汇: 'dust!' -> ['dust', '!']
[16:37:25] [DEBUG] 🔍 分离词汇: 'Hey,' -> ['Hey', ',']
[16:37:25] [DEBUG] 🔍 分离词汇: 'there?' -> ['there', '?']
[16:37:25] [DEBUG] 🔍 分离词汇: 'sleep!' -> ['sleep', '!']
[16:37:25] [DEBUG] 🔍 分离词汇: 'pill,' -> ['pill', ',']
[16:37:25] [DEBUG] 🔍 分离词汇: 'woman!' -> ['woman', '!']
[16:37:25] [DEBUG] 🔍 分离词汇: 'pill!' -> ['pill', '!']
[16:37:25] [DEBUG] 🔍 分离词汇: 'Why,' -> ['Why', ',']
[16:37:25] [DEBUG] 🔍 分离词汇: 'go.' -> ['go', '.']
[16:37:25] [DEBUG] 🔍 分离词汇: 'now,' -> ['now', ',']
[16:37:25] [DEBUG] 🔍 分离词汇: 'Liv.' -> ['Liv', '.']
[16:37:25] [DEBUG] 🔍 分离词汇: 'look,' -> ['look', ',']
[16:37:25] [DEBUG] 🔍 分离词汇: 'you,' -> ['you', ',']
[16:37:25] [DEBUG] 🔍 分离词汇: 'huh?' -> ['huh', '?']
[16:37:25] [DEBUG] 🔍 分离词汇: 'night.' -> ['night', '.']
[16:37:25] [DEBUG] 🔍 分离词汇: 'Oh,' -> ['Oh', ',']
[16:37:25] [DEBUG] 🔍 分离词汇: 'sake,' -> ['sake', ',']
[16:37:25] [DEBUG] 🔍 分离词汇: 'goons.' -> ['goons', '.']
[16:37:25] [DEBUG] 🔍 分离词汇: 'story,' -> ['story', ',']
[16:37:25] [DEBUG] 🔍 分离词汇: 'Harry.' -> ['Harry', '.']
[16:37:25] [DEBUG] 🔍 分离词汇: 'right,' -> ['right', ',']
[16:37:25] [DEBUG] 🔍 分离词汇: 'right.' -> ['right', '.']
[16:37:25] [DEBUG] 🔍 分离词汇: 'look,' -> ['look', ',']
[16:37:25] [DEBUG] 🔍 分离词汇: 'me,' -> ['me', ',']
[16:37:25] [DEBUG] 🔍 分离词汇: 'us,' -> ['us', ',']
[16:37:25] [DEBUG] 🔍 分离词汇: 'A,' -> ['A', ',']
[16:37:25] [DEBUG] 🔍 分离词汇: 'junta,' -> ['junta', ',']
[16:37:25] [DEBUG] 🔍 分离词汇: 'B,' -> ['B', ',']
[16:37:25] [DEBUG] 🔍 分离词汇: 'them.' -> ['them', '.']
[16:37:25] [DEBUG] 🔍 分离词汇: 'Chas?' -> ['Chas', '?']
[16:37:25] [DEBUG] 🔍 分离词汇: 'body.' -> ['body', '.']
[16:37:25] [DEBUG] 🔍 分离词汇: 'microdot.' -> ['microdot', '.']
[16:37:25] [DEBUG] 🔍 分离词汇: 'stuff?' -> ['stuff', '?']
[16:37:25] [DEBUG] 🔍 分离词汇: 'know?' -> ['know', '?']
[16:37:25] [DEBUG] 🔍 分离词汇: 'junta,' -> ['junta', ',']
[16:37:25] [DEBUG] 🔍 分离词汇: 'suppose.' -> ['suppose', '.']
[16:37:25] [DEBUG] 🔍 分离词汇: 'listen,' -> ['listen', ',']
[16:37:25] [DEBUG] 🔍 分离词汇: 'look,' -> ['look', ',']
[16:37:25] [DEBUG] 🔍 分离词汇: 'Liv,' -> ['Liv', ',']
[16:37:25] [DEBUG] 🔍 分离词汇: 'Mr.' -> ['Mr', '.']
[16:37:25] [DEBUG] 🔍 分离词汇: 'body.' -> ['body', '.']
[16:37:25] [DEBUG] 🔍 分离词汇: 'No.' -> ['No', '.']
[16:37:25] [DEBUG] 🔍 分离词汇: 'Oh,' -> ['Oh', ',']
[16:37:25] [DEBUG] 🔍 分离词汇: 'on,' -> ['on', ',']
[16:37:25] [DEBUG] 🔍 分离词汇: 'Liv.' -> ['Liv', '.']
[16:37:25] [DEBUG] 🔍 分离词汇: 'loaf,' -> ['loaf', ',']
[16:37:25] [DEBUG] 🔍 分离词汇: 'loaf!' -> ['loaf', '!']
[16:37:25] [DEBUG] 🔍 分离词汇: 'disintegrates,' -> ['disintegrates', ',']
[16:37:25] [DEBUG] 🔍 分离词汇: 'nothing.' -> ['nothing', '.']
[16:37:25] [DEBUG] 🔍 分离词汇: 'head.' -> ['head', '.']
[16:37:25] [DEBUG] 🔍 分离词汇: 'Exactly.' -> ['Exactly', '.']
[16:37:25] [DEBUG] 🔍 分离词汇: 'U.S.!' -> ['U', '.', 'S', '.!']
[16:37:25] [DEBUG] 🔍 分离词汇: 'No.' -> ['No', '.']
[16:37:25] [DEBUG] 🔍 分离词汇: 'must,' -> ['must', ',']
[16:37:25] [DEBUG] 🔍 分离词汇: 'sir.' -> ['sir', '.']
[16:37:25] [DEBUG] 🔍 分离词汇: 'it.' -> ['it', '.']
[16:37:25] [DEBUG] 🔍 分离词汇: 'it.' -> ['it', '.']
[16:37:25] [DEBUG] 🔍 分离词汇: 'Uh,' -> ['Uh', ',']
[16:37:25] [DEBUG] 🔍 分离词汇: 'Mr.' -> ['Mr', '.']
[16:37:25] [DEBUG] 🔍 分离词汇: 'Mokuba.' -> ['Mokuba', '.']
[16:37:26] [DEBUG] 🔍 分离词汇: 'Uh,' -> ['Uh', ',']
[16:37:26] [DEBUG] 🔍 分离词汇: 'Liv.' -> ['Liv', '.']
[16:37:26] [DEBUG] 🔍 分离词汇: 'formula?' -> ['formula', '?']
[16:37:26] [DEBUG] 🔍 分离词汇: 'decently.' -> ['decently', '.']
[16:37:26] [DEBUG] 🔍 分离词汇: 'it.' -> ['it', '.']
[16:37:26] [DEBUG] 🔍 分离词汇: 'stinks.' -> ['stinks', '.']
[16:37:26] [DEBUG] 🔍 分离词汇: 'body?' -> ['body', '?']
[16:37:26] [DEBUG] 🔍 分离词汇: 'town.' -> ['town', '.']
[16:37:26] [DEBUG] 🔍 分离词汇: 'Oh,' -> ['Oh', ',']
[16:37:26] [DEBUG] 🔍 分离词汇: 'anymore.' -> ['anymore', '.']
[16:37:26] [DEBUG] 🔍 分离词汇: 'it.' -> ['it', '.']
[16:37:26] [DEBUG] 🔍 分离词汇: 'bastard.' -> ['bastard', '.']
[16:37:26] [DEBUG] 🔍 分离词汇: 'is.' -> ['is', '.']
[16:37:26] [DEBUG] 🔍 分离词汇: 'way,' -> ['way', ',']
[16:37:26] [DEBUG] 🔍 分离词汇: 'baby.' -> ['baby', '.']
[16:37:26] [DEBUG] 🔍 分离词汇: 'way.' -> ['way', '.']
[16:37:26] [DEBUG] 🔍 分离词汇: 'Oh,' -> ['Oh', ',']
[16:37:26] [DEBUG] 🔍 分离词汇: 'on,' -> ['on', ',']
[16:37:26] [DEBUG] 🔍 分离词汇: 'Liv.' -> ['Liv', '.']
[16:37:26] [DEBUG] 🔍 分离词汇: 'expenses.' -> ['expenses', '.']
[16:37:26] [DEBUG] 🔍 分离词汇: 'thing!' -> ['thing', '!']
[16:37:26] [DEBUG] 🔍 分离词汇: 'ki-...' -> ['ki-', '...']
[16:37:26] [DEBUG] 🔍 分离词汇: 'is,' -> ['is', ',']
[16:37:26] [DEBUG] 🔍 分离词汇: 'know.' -> ['know', '.']
[16:37:26] [DEBUG] 🔍 分离词汇: 'night.' -> ['night', '.']
[16:37:26] [DEBUG] 🔍 分离词汇: 'body.' -> ['body', '.']
[16:37:26] [DEBUG] 🔍 分离词汇: 'is.' -> ['is', '.']
[16:37:26] [DEBUG] 🔍 分离词汇: 'Okay,' -> ['Okay', ',']
[16:37:26] [DEBUG] 🔍 分离词汇: 'makeup,' -> ['makeup', ',']
[16:37:26] [DEBUG] 🔍 分离词汇: 'Shel.' -> ['Shel', '.']
[16:37:26] [DEBUG] 🔍 分离词汇: 'lot.' -> ['lot', '.']
[16:37:26] [DEBUG] 🔍 分离词汇: 'Okay,' -> ['Okay', ',']
[16:37:26] [DEBUG] 🔍 分离词汇: 'time.' -> ['time', '.']
[16:37:26] [DEBUG] 🔍 分离词汇: 'Thanks,' -> ['Thanks', ',']
[16:37:26] [DEBUG] 🔍 分离词汇: 'Midge.' -> ['Midge', '.']
[16:37:26] [DEBUG] 🔍 分离词汇: 'Here,' -> ['Here', ',']
[16:37:26] [DEBUG] 🔍 分离词汇: 'uh,' -> ['uh', ',']
[16:37:26] [DEBUG] 🔍 分离词汇: 'this.' -> ['this', '.']
[16:37:26] [DEBUG] 🔍 分离词汇: 'best,' -> ['best', ',']
[16:37:26] [DEBUG] 🔍 分离词汇: 'that.' -> ['that', '.']
[16:37:26] [DEBUG] 🔍 分离词汇: 'confidence,' -> ['confidence', ',']
[16:37:26] [DEBUG] 🔍 分离词汇: 'Sam.' -> ['Sam', '.']
[16:37:26] [DEBUG] 🔍 分离词汇: 'confidence.' -> ['confidence', '.']
[16:37:26] [DEBUG] 🔍 分离词汇: 'ya.' -> ['ya', '.']
[16:37:26] [DEBUG] 🔍 分离词汇: 'night,' -> ['night', ',']
[16:37:26] [DEBUG] 🔍 分离词汇: 'day.' -> ['day', '.']
[16:37:26] [DEBUG] 🔍 分离词汇: 'you.' -> ['you', '.']
[16:37:26] [DEBUG] 🔍 分离词汇: 'chance.' -> ['chance', '.']
[16:37:26] [DEBUG] 🔍 分离词汇: 'it.' -> ['it', '.']
[16:37:26] [DEBUG] 🔍 分离词汇: 'Okay,' -> ['Okay', ',']
[16:37:26] [DEBUG] 🔍 分离词汇: 'everybody.' -> ['everybody', '.']
[16:37:26] [DEBUG] 🔍 分离词汇: 'show!' -> ['show', '!']
[16:37:26] ℹ️ 词汇分离完成，分离了 130 个混合词汇，最终词汇数量: 611
[16:37:26] ℹ️ Parsed trimmed_Honey Baby, Honey Baby (1974)_part15_01-09-51-ElevenLabs.json in 1.414s
[16:37:26] [OK] ElevenLabs JSON解析完成，已保存: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part15_01-09-51-ElevenLabs-parsed.json
[16:37:26]   词汇数量: 611
[16:37:26]   语言: unknown
[16:37:26]   置信度: 0.00
[16:37:26] [TaskFlow] 分段转录和解析完成: trimmed_Honey Baby, Honey Baby (1974)_part15_01-09-51 - ElevenLabs
[16:37:26] [TaskFlow] 处理分段 16/19: trimmed_Honey Baby, Honey Baby (1974)_part16_01-14-29
[16:37:26] [TaskFlow] 开始转录分段: trimmed_Honey Baby, Honey Baby (1974)_part16_01-14-29 - ElevenLabs
[16:37:26] 开始ElevenLabs free模式转录: trimmed_Honey Baby, Honey Baby (1974)_part16_01-14-29 (应用层尝试 1/3)
[16:37:38] ElevenLabs转录完成: trimmed_Honey Baby, Honey Baby (1974)_part16_01-14-29 (应用层尝试 1)
[16:37:38] JSON文件已保存: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part16_01-14-29-ElevenLabs.json
[16:37:38] [TaskFlow] 分段转录成功: trimmed_Honey Baby, Honey Baby (1974)_part16_01-14-29 - ElevenLabs
[16:37:38] [TaskFlow] 开始解析分段结果: trimmed_Honey Baby, Honey Baby (1974)_part16_01-14-29 - ElevenLabs
[16:37:38] 开始解析 ElevenLabs 的JSON文件
[16:37:38] [DEBUG] 🔍 使用regex库的Unicode属性进行字符分类
[16:37:38] [DEBUG] Detecting format for data with keys: ['success', 'service', 'transcription_id', 'text', 'words', 'language_detected', 'confidence', 'processing_time', 'metadata', 'created_at']
[16:37:38] [DEBUG] Detected elevenlabs standardized format from service field
[16:37:38] [DEBUG] Parsing ElevenLabs format
[16:37:38] [DEBUG] ElevenLabs data keys: ['success', 'service', 'transcription_id', 'text', 'words', 'language_detected', 'confidence', 'processing_time', 'metadata', 'created_at']
[16:37:38] [DEBUG] ElevenLabs full_text length: 2417
[16:37:38] [DEBUG] ElevenLabs words count: 931
[16:37:38] [DEBUG] ElevenLabs result: 466 words, confidence: 0.000, speakers: 8
[16:37:38] ℹ️ 开始词汇分离处理，原始词汇数量: 466
[16:37:38] [DEBUG] 🔍 分离词汇: 'there.' -> ['there', '.']
[16:37:38] [DEBUG] 🔍 分离词汇: 'okay,' -> ['okay', ',']
[16:37:38] [DEBUG] 🔍 分离词汇: 'Laura?' -> ['Laura', '?']
[16:37:38] [DEBUG] 🔍 分离词汇: 'Yes,' -> ['Yes', ',']
[16:37:38] [DEBUG] 🔍 分离词汇: 'you,' -> ['you', ',']
[16:37:38] [DEBUG] 🔍 分离词汇: 'Sam.' -> ['Sam', '.']
[16:37:38] [DEBUG] 🔍 分离词汇: 'yet?' -> ['yet', '?']
[16:37:38] [DEBUG] 🔍 分离词汇: 'No,' -> ['No', ',']
[16:37:38] [DEBUG] 🔍 分离词汇: 'now.' -> ['now', '.']
[16:37:38] [DEBUG] 🔍 分离词汇: 'on.' -> ['on', '.']
[16:37:38] [DEBUG] 🔍 分离词汇: 'questions.' -> ['questions', '.']
[16:37:38] [DEBUG] 🔍 分离词汇: 'lifetime.' -> ['lifetime', '.']
[16:37:38] [DEBUG] 🔍 分离词汇: 'ready,' -> ['ready', ',']
[16:37:38] [DEBUG] 🔍 分离词汇: 'Laura?' -> ['Laura', '?']
[16:37:38] [DEBUG] 🔍 分离词汇: 'Yes,' -> ['Yes', ',']
[16:37:38] [DEBUG] 🔍 分离词汇: 'ready,' -> ['ready', ',']
[16:37:38] [DEBUG] 🔍 分离词汇: 'Sam.' -> ['Sam', '.']
[16:37:38] [DEBUG] 🔍 分离词汇: 'Okay.' -> ['Okay', '.']
[16:37:38] [DEBUG] 🔍 分离词汇: 'on?' -> ['on', '?']
[16:37:38] [DEBUG] 🔍 分离词汇: 'Oh,' -> ['Oh', ',']
[16:37:38] [DEBUG] 🔍 分离词汇: 'yes.' -> ['yes', '.']
[16:37:38] [DEBUG] 🔍 分离词汇: 'Yes,' -> ['Yes', ',']
[16:37:38] [DEBUG] 🔍 分离词汇: 'I'd,' -> ["I'd", ',']
[16:37:38] [DEBUG] 🔍 分离词汇: 'um,' -> ['um', ',']
[16:37:38] [DEBUG] 🔍 分离词汇: 'Kazdulan.' -> ['Kazdulan', '.']
[16:37:38] [DEBUG] 🔍 分离词汇: 'Aha.' -> ['Aha', '.']
[16:37:38] [DEBUG] 🔍 分离词汇: 'Kazdanland,' -> ['Kazdanland', ',']
[16:37:38] [DEBUG] 🔍 分离词汇: 'USA.' -> ['USA', '.']
[16:37:38] [DEBUG] 🔍 分离词汇: 'know,' -> ['know', ',']
[16:37:38] [DEBUG] 🔍 分离词汇: 'uh,' -> ['uh', ',']
[16:37:38] [DEBUG] 🔍 分离词汇: 'Street.' -> ['Street', '.']
[16:37:38] [DEBUG] 🔍 分离词汇: 'See?' -> ['See', '?']
[16:37:38] [DEBUG] 🔍 分离词汇: 'you?' -> ['you', '?']
[16:37:38] [DEBUG] 🔍 分离词汇: 'bastard.' -> ['bastard', '.']
[16:37:38] [DEBUG] 🔍 分离词汇: 'I...' -> ['I', '...']
[16:37:38] [DEBUG] 🔍 分离词汇: 'Ah,' -> ['Ah', ',']
[16:37:38] [DEBUG] 🔍 分离词汇: 'now.' -> ['now', '.']
[16:37:38] [DEBUG] 🔍 分离词汇: 'East.' -> ['East', '.']
[16:37:38] [DEBUG] 🔍 分离词汇: 'yet,' -> ['yet', ',']
[16:37:39] [DEBUG] 🔍 分离词汇: 'see.' -> ['see', '.']
[16:37:39] [DEBUG] 🔍 分离词汇: 'to.' -> ['to', '.']
[16:37:39] [DEBUG] 🔍 分离词汇: 'now,' -> ['now', ',']
[16:37:39] [DEBUG] 🔍 分离词汇: 'one.' -> ['one', '.']
[16:37:39] [DEBUG] 🔍 分离词汇: 'moving.' -> ['moving', '.']
[16:37:39] [DEBUG] 🔍 分离词汇: 'thing...' -> ['thing', '...']
[16:37:39] [DEBUG] 🔍 分离词汇: 'machine!' -> ['machine', '!']
[16:37:39] [DEBUG] 🔍 分离词汇: 'accident,' -> ['accident', ',']
[16:37:39] [DEBUG] 🔍 分离词汇: 'canceled.' -> ['canceled', '.']
[16:37:39] [DEBUG] 🔍 分离词汇: 'Ms.' -> ['Ms', '.']
[16:37:39] [DEBUG] 🔍 分离词汇: 'that?' -> ['that', '?']
[16:37:39] [DEBUG] 🔍 分离词汇: 'Sabotage.' -> ['Sabotage', '.']
[16:37:39] [DEBUG] 🔍 分离词汇: 'shit.' -> ['shit', '.']
[16:37:39] [DEBUG] 🔍 分离词汇: 'Nigga,' -> ['Nigga', ',']
[16:37:39] [DEBUG] 🔍 分离词汇: 'down.' -> ['down', '.']
[16:37:39] [DEBUG] 🔍 分离词汇: 'Lebanon.' -> ['Lebanon', '.']
[16:37:39] [DEBUG] 🔍 分离词汇: 'Hello?' -> ['Hello', '?']
[16:37:39] [DEBUG] 🔍 分离词汇: 'Ms.' -> ['Ms', '.']
[16:37:39] [DEBUG] 🔍 分离词汇: 'Lewis?' -> ['Lewis', '?']
[16:37:39] [DEBUG] 🔍 分离词汇: 'Yes?' -> ['Yes', '?']
[16:37:39] [DEBUG] 🔍 分离词汇: 'friend,' -> ['friend', ',']
[16:37:39] [DEBUG] 🔍 分离词汇: 'Liz.' -> ['Liz', '.']
[16:37:39] [DEBUG] 🔍 分离词汇: 'body.' -> ['body', '.']
[16:37:39] [DEBUG] 🔍 分离词汇: 'is?' -> ['is', '?']
[16:37:39] [DEBUG] 🔍 分离词汇: 'No.' -> ['No', '.']
[16:37:39] [DEBUG] 🔍 分离词汇: 'point,' -> ['point', ',']
[16:37:39] [DEBUG] 🔍 分离词汇: 'closet.' -> ['closet', '.']
[16:37:39] [DEBUG] 🔍 分离词汇: 'on.' -> ['on', '.']
[16:37:39] [DEBUG] 🔍 分离词汇: 'Ms.' -> ['Ms', '.']
[16:37:39] [DEBUG] 🔍 分离词汇: 'Lewis.' -> ['Lewis', '.']
[16:37:39] [DEBUG] 🔍 分离词汇: 'Ms.' -> ['Ms', '.']
[16:37:39] [DEBUG] 🔍 分离词汇: 'Lewis?' -> ['Lewis', '?']
[16:37:39] [DEBUG] 🔍 分离词汇: 'Yes?' -> ['Yes', '?']
[16:37:39] [DEBUG] 🔍 分离词汇: 'Oh,' -> ['Oh', ',']
[16:37:39] [DEBUG] 🔍 分离词汇: 'see,' -> ['see', ',']
[16:37:39] [DEBUG] 🔍 分离词汇: 'Ms.' -> ['Ms', '.']
[16:37:39] [DEBUG] 🔍 分离词汇: 'Lewis,' -> ['Lewis', ',']
[16:37:39] [DEBUG] 🔍 分离词汇: 'serious.' -> ['serious', '.']
[16:37:39] [DEBUG] 🔍 分离词汇: 'friend,' -> ['friend', ',']
[16:37:39] [DEBUG] 🔍 分离词汇: 'hope.' -> ['hope', '.']
[16:37:39] [DEBUG] 🔍 分离词汇: 'Ms.' -> ['Ms', '.']
[16:37:39] [DEBUG] 🔍 分离词汇: 'Lewis?' -> ['Lewis', '?']
[16:37:39] [DEBUG] 🔍 分离词汇: 'morning.' -> ['morning', '.']
[16:37:39] [DEBUG] 🔍 分离词汇: 'and,' -> ['and', ',']
[16:37:39] [DEBUG] 🔍 分离词汇: 'uh,' -> ['uh', ',']
[16:37:39] [DEBUG] 🔍 分离词汇: 'there.' -> ['there', '.']
[16:37:39] [DEBUG] 🔍 分离词汇: 'know,' -> ['know', ',']
[16:37:39] [DEBUG] 🔍 分离词汇: 'two.' -> ['two', '.']
[16:37:39] [DEBUG] 🔍 分离词汇: 'Well,' -> ['Well', ',']
[16:37:39] [DEBUG] 🔍 分离词汇: 'much.' -> ['much', '.']
[16:37:39] [DEBUG] 🔍 分离词汇: 'on,' -> ['on', ',']
[16:37:39] [DEBUG] 🔍 分离词汇: 'Skiki,' -> ['Skiki', ',']
[16:37:39] [DEBUG] 🔍 分离词汇: 'go.' -> ['go', '.']
[16:37:39] [DEBUG] 🔍 分离词汇: 'No,' -> ['No', ',']
[16:37:39] [DEBUG] 🔍 分离词汇: 'wait,' -> ['wait', ',']
[16:37:39] [DEBUG] 🔍 分离词汇: 'wait,' -> ['wait', ',']
[16:37:39] [DEBUG] 🔍 分离词汇: 'minute,' -> ['minute', ',']
[16:37:39] [DEBUG] 🔍 分离词汇: 'minute.' -> ['minute', '.']
[16:37:39] [DEBUG] 🔍 分离词汇: 'house,' -> ['house', ',']
[16:37:39] [DEBUG] 🔍 分离词汇: 'very,' -> ['very', ',']
[16:37:39] [DEBUG] 🔍 分离词汇: 'dogs.' -> ['dogs', '.']
[16:37:39] [DEBUG] 🔍 分离词汇: 'night,' -> ['night', ',']
[16:37:39] [DEBUG] 🔍 分离词汇: 'loose.' -> ['loose', '.']
[16:37:39] [DEBUG] 🔍 分离词汇: 'Harry,' -> ['Harry', ',']
[16:37:39] [DEBUG] 🔍 分离词汇: 'uh,' -> ['uh', ',']
[16:37:39] [DEBUG] 🔍 分离词汇: 'door?' -> ['door', '?']
[16:37:39] [DEBUG] 🔍 分离词汇: 'home?' -> ['home', '?']
[16:37:39] [DEBUG] 🔍 分离词汇: 'marshmallows.' -> ['marshmallows', '.']
[16:37:39] [DEBUG] 🔍 分离词汇: 'Marshmallows?' -> ['Marshmallows', '?']
[16:37:39] [DEBUG] 🔍 分离词汇: 'Yes.' -> ['Yes', '.']
[16:37:39] [DEBUG] 🔍 分离词汇: 'marshmallow.' -> ['marshmallow', '.']
[16:37:39] [DEBUG] 🔍 分离词汇: 'quiet.' -> ['quiet', '.']
[16:37:39] [DEBUG] 🔍 分离词汇: 'Mm-hmm.' -> ['Mm-hmm', '.']
[16:37:39] [DEBUG] 🔍 分离词汇: 'kind.' -> ['kind', '.']
[16:37:39] [DEBUG] 🔍 分离词汇: 'Gotcha.' -> ['Gotcha', '.']
[16:37:39] [DEBUG] 🔍 分离词汇: 'marshmallows.' -> ['marshmallows', '.']
[16:37:39] [DEBUG] 🔍 分离词汇: 'Skiki,' -> ['Skiki', ',']
[16:37:39] [DEBUG] 🔍 分离词汇: 'failure.' -> ['failure', '.']
[16:37:39] [DEBUG] 🔍 分离词汇: 'it.' -> ['it', '.']
[16:37:39] [DEBUG] 🔍 分离词汇: 'Aw,' -> ['Aw', ',']
[16:37:39] [DEBUG] 🔍 分离词汇: 'now.' -> ['now', '.']
[16:37:39] [DEBUG] 🔍 分离词汇: 'tomorrow.' -> ['tomorrow', '.']
[16:37:39] [DEBUG] 🔍 分离词汇: 'Yes.' -> ['Yes', '.']
[16:37:39] ℹ️ 词汇分离完成，分离了 122 个混合词汇，最终词汇数量: 588
[16:37:39] ℹ️ Parsed trimmed_Honey Baby, Honey Baby (1974)_part16_01-14-29-ElevenLabs.json in 1.260s
[16:37:39] [OK] ElevenLabs JSON解析完成，已保存: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part16_01-14-29-ElevenLabs-parsed.json
[16:37:39]   词汇数量: 588
[16:37:39]   语言: unknown
[16:37:39]   置信度: 0.00
[16:37:39] [TaskFlow] 分段转录和解析完成: trimmed_Honey Baby, Honey Baby (1974)_part16_01-14-29 - ElevenLabs
[16:37:39] [TaskFlow] 处理分段 17/19: trimmed_Honey Baby, Honey Baby (1974)_part17_01-20-54
[16:37:39] [TaskFlow] 开始转录分段: trimmed_Honey Baby, Honey Baby (1974)_part17_01-20-54 - ElevenLabs
[16:37:39] 开始ElevenLabs free模式转录: trimmed_Honey Baby, Honey Baby (1974)_part17_01-20-54 (应用层尝试 1/3)
[16:37:46] ElevenLabs转录完成: trimmed_Honey Baby, Honey Baby (1974)_part17_01-20-54 (应用层尝试 1)
[16:37:46] JSON文件已保存: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part17_01-20-54-ElevenLabs.json
[16:37:46] [TaskFlow] 分段转录成功: trimmed_Honey Baby, Honey Baby (1974)_part17_01-20-54 - ElevenLabs
[16:37:46] [TaskFlow] 开始解析分段结果: trimmed_Honey Baby, Honey Baby (1974)_part17_01-20-54 - ElevenLabs
[16:37:46] 开始解析 ElevenLabs 的JSON文件
[16:37:46] [DEBUG] 🔍 使用regex库的Unicode属性进行字符分类
[16:37:46] [DEBUG] Detecting format for data with keys: ['success', 'service', 'transcription_id', 'text', 'words', 'language_detected', 'confidence', 'processing_time', 'metadata', 'created_at']
[16:37:46] [DEBUG] Detected elevenlabs standardized format from service field
[16:37:46] [DEBUG] Parsing ElevenLabs format
[16:37:46] [DEBUG] ElevenLabs data keys: ['success', 'service', 'transcription_id', 'text', 'words', 'language_detected', 'confidence', 'processing_time', 'metadata', 'created_at']
[16:37:46] [DEBUG] ElevenLabs full_text length: 1029
[16:37:46] [DEBUG] ElevenLabs words count: 389
[16:37:46] [DEBUG] ElevenLabs result: 195 words, confidence: 0.000, speakers: 5
[16:37:46] ℹ️ 开始词汇分离处理，原始词汇数量: 195
[16:37:46] [DEBUG] 🔍 分离词汇: 'me,' -> ['me', ',']
[16:37:46] [DEBUG] 🔍 分离词汇: 'Lynn,' -> ['Lynn', ',']
[16:37:46] [DEBUG] 🔍 分离词汇: 'me.' -> ['me', '.']
[16:37:46] [DEBUG] 🔍 分离词汇: 'on,' -> ['on', ',']
[16:37:46] [DEBUG] 🔍 分离词汇: 'all?' -> ['all', '?']
[16:37:46] [DEBUG] 🔍 分离词汇: 'Oh,' -> ['Oh', ',']
[16:37:46] [DEBUG] 🔍 分离词汇: 'baby.' -> ['baby', '.']
[16:37:46] [DEBUG] 🔍 分离词汇: 'okay?' -> ['okay', '?']
[16:37:46] [DEBUG] 🔍 分离词汇: 'used...' -> ['used', '...']
[16:37:46] [DEBUG] 🔍 分离词汇: 'Huh?' -> ['Huh', '?']
[16:37:46] [DEBUG] 🔍 分离词汇: 'right,' -> ['right', ',']
[16:37:46] [DEBUG] 🔍 分离词汇: 'okay,' -> ['okay', ',']
[16:37:46] [DEBUG] 🔍 分离词汇: 'oh.' -> ['oh', '.']
[16:37:46] [DEBUG] 🔍 分离词汇: 'Hmm.' -> ['Hmm', '.']
[16:37:46] [DEBUG] 🔍 分离词汇: 'Oh,' -> ['Oh', ',']
[16:37:46] [DEBUG] 🔍 分离词汇: 'coming.' -> ['coming', '.']
[16:37:46] [DEBUG] 🔍 分离词汇: 'Ah.' -> ['Ah', '.']
[16:37:46] [DEBUG] 🔍 分离词汇: 'Ah.' -> ['Ah', '.']
[16:37:46] [DEBUG] 🔍 分离词汇: 'Listen,' -> ['Listen', ',']
[16:37:46] [DEBUG] 🔍 分离词汇: 'uh,' -> ['uh', ',']
[16:37:47] [DEBUG] 🔍 分离词汇: 'here?' -> ['here', '?']
[16:37:47] [DEBUG] 🔍 分离词汇: 'Hmm?' -> ['Hmm', '?']
[16:37:47] [DEBUG] 🔍 分离词汇: 'Awani,' -> ['Awani', ',']
[16:37:47] [DEBUG] 🔍 分离词汇: 'African.' -> ['African', '.']
[16:37:47] [DEBUG] 🔍 分离词汇: 'nightclub,' -> ['nightclub', ',']
[16:37:47] [DEBUG] 🔍 分离词汇: 'too.' -> ['too', '.']
[16:37:47] [DEBUG] 🔍 分离词汇: 'is.' -> ['is', '.']
[16:37:47] [DEBUG] 🔍 分离词汇: 'Mkhuwa.' -> ['Mkhuwa', '.']
[16:37:47] [DEBUG] 🔍 分离词汇: 'No.' -> ['No', '.']
[16:37:47] [DEBUG] 🔍 分离词汇: 'Awani.' -> ['Awani', '.']
[16:37:47] [DEBUG] 🔍 分离词汇: 'know?' -> ['know', '?']
[16:37:47] [DEBUG] 🔍 分离词汇: 'junta.' -> ['junta', '.']
[16:37:47] [DEBUG] 🔍 分离词汇: 'know?' -> ['know', '?']
[16:37:47] [DEBUG] 🔍 分离词汇: 'year.' -> ['year', '.']
[16:37:47] [DEBUG] 🔍 分离词汇: 'body.' -> ['body', '.']
[16:37:47] [DEBUG] 🔍 分离词汇: 'Hmm,' -> ['Hmm', ',']
[16:37:47] [DEBUG] 🔍 分离词汇: 'mother.' -> ['mother', '.']
[16:37:47] [DEBUG] 🔍 分离词汇: 'Compromising.' -> ['Compromising', '.']
[16:37:47] [DEBUG] 🔍 分离词汇: 'What?' -> ['What', '?']
[16:37:47] [DEBUG] 🔍 分离词汇: 'body,' -> ['body', ',']
[16:37:47] [DEBUG] 🔍 分离词汇: 'him.' -> ['him', '.']
[16:37:47] [DEBUG] 🔍 分离词汇: 'Right,' -> ['Right', ',']
[16:37:47] [DEBUG] 🔍 分离词汇: 'up.' -> ['up', '.']
[16:37:47] [DEBUG] 🔍 分离词汇: 'Okay.' -> ['Okay', '.']
[16:37:47] [DEBUG] 🔍 分离词汇: 'bait.' -> ['bait', '.']
[16:37:47] [DEBUG] 🔍 分离词汇: 'not?' -> ['not', '?']
[16:37:47] [DEBUG] 🔍 分离词汇: 'prisoner.' -> ['prisoner', '.']
[16:37:47] [DEBUG] 🔍 分离词汇: 'I,' -> ['I', ',']
[16:37:47] [DEBUG] 🔍 分离词汇: 'uh,' -> ['uh', ',']
[16:37:47] [DEBUG] 🔍 分离词汇: 'him.' -> ['him', '.']
[16:37:47] [DEBUG] 🔍 分离词汇: 'Oh.' -> ['Oh', '.']
[16:37:47] [DEBUG] 🔍 分离词汇: 'Shh,' -> ['Shh', ',']
[16:37:47] [DEBUG] 🔍 分离词汇: 'shh,' -> ['shh', ',']
[16:37:47] [DEBUG] 🔍 分离词汇: 'shh.' -> ['shh', '.']
[16:37:47] [DEBUG] 🔍 分离词汇: 'Oh.' -> ['Oh', '.']
[16:37:47] [DEBUG] 🔍 分离词汇: 'escape,' -> ['escape', ',']
[16:37:47] [DEBUG] 🔍 分离词汇: 'Lewis.' -> ['Lewis', '.']
[16:37:47] [DEBUG] 🔍 分离词汇: 'me,' -> ['me', ',']
[16:37:47] [DEBUG] 🔍 分离词汇: 'hesitation.' -> ['hesitation', '.']
[16:37:47] ℹ️ 词汇分离完成，分离了 59 个混合词汇，最终词汇数量: 254
[16:37:47] ℹ️ Parsed trimmed_Honey Baby, Honey Baby (1974)_part17_01-20-54-ElevenLabs.json in 0.681s
[16:37:47] [OK] ElevenLabs JSON解析完成，已保存: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part17_01-20-54-ElevenLabs-parsed.json
[16:37:47]   词汇数量: 254
[16:37:47]   语言: unknown
[16:37:47]   置信度: 0.00
[16:37:47] [TaskFlow] 分段转录和解析完成: trimmed_Honey Baby, Honey Baby (1974)_part17_01-20-54 - ElevenLabs
[16:37:47] [TaskFlow] 处理分段 18/19: trimmed_Honey Baby, Honey Baby (1974)_part18_01-24-10
[16:37:47] [TaskFlow] 开始转录分段: trimmed_Honey Baby, Honey Baby (1974)_part18_01-24-10 - ElevenLabs
[16:37:47] 开始ElevenLabs free模式转录: trimmed_Honey Baby, Honey Baby (1974)_part18_01-24-10 (应用层尝试 1/3)
[16:37:52] ElevenLabs转录完成: trimmed_Honey Baby, Honey Baby (1974)_part18_01-24-10 (应用层尝试 1)
[16:37:52] JSON文件已保存: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part18_01-24-10-ElevenLabs.json
[16:37:52] [TaskFlow] 分段转录成功: trimmed_Honey Baby, Honey Baby (1974)_part18_01-24-10 - ElevenLabs
[16:37:52] [TaskFlow] 开始解析分段结果: trimmed_Honey Baby, Honey Baby (1974)_part18_01-24-10 - ElevenLabs
[16:37:52] 开始解析 ElevenLabs 的JSON文件
[16:37:52] [DEBUG] 🔍 使用regex库的Unicode属性进行字符分类
[16:37:52] [DEBUG] Detecting format for data with keys: ['success', 'service', 'transcription_id', 'text', 'words', 'language_detected', 'confidence', 'processing_time', 'metadata', 'created_at']
[16:37:52] [DEBUG] Detected elevenlabs standardized format from service field
[16:37:52] [DEBUG] Parsing ElevenLabs format
[16:37:52] [DEBUG] ElevenLabs data keys: ['success', 'service', 'transcription_id', 'text', 'words', 'language_detected', 'confidence', 'processing_time', 'metadata', 'created_at']
[16:37:52] [DEBUG] ElevenLabs full_text length: 499
[16:37:52] [DEBUG] ElevenLabs words count: 202
[16:37:52] [DEBUG] ElevenLabs result: 101 words, confidence: 0.000, speakers: 2
[16:37:52] ℹ️ 开始词汇分离处理，原始词汇数量: 101
[16:37:52] [DEBUG] 🔍 分离词汇: 'key.' -> ['key', '.']
[16:37:52] [DEBUG] 🔍 分离词汇: 'key.' -> ['key', '.']
[16:37:52] [DEBUG] 🔍 分离词汇: 'you.' -> ['you', '.']
[16:37:52] [DEBUG] 🔍 分离词汇: 'right.' -> ['right', '.']
[16:37:52] [DEBUG] 🔍 分离词汇: 'Now,' -> ['Now', ',']
[16:37:52] [DEBUG] 🔍 分离词汇: 'either.' -> ['either', '.']
[16:37:52] [DEBUG] 🔍 分离词汇: 'know,' -> ['know', ',']
[16:37:52] [DEBUG] 🔍 分离词汇: 'course,' -> ['course', ',']
[16:37:52] [DEBUG] 🔍 分离词汇: 'here,' -> ['here', ',']
[16:37:52] [DEBUG] 🔍 分离词汇: 'necks.' -> ['necks', '.']
[16:37:52] [DEBUG] 🔍 分离词汇: 'So,' -> ['So', ',']
[16:37:52] [DEBUG] 🔍 分离词汇: 'deal?' -> ['deal', '?']
[16:37:52] [DEBUG] 🔍 分离词汇: 'terms?' -> ['terms', '?']
[16:37:52] [DEBUG] 🔍 分离词汇: 'Money.' -> ['Money', '.']
[16:37:52] [DEBUG] 🔍 分离词汇: 'you?' -> ['you', '?']
[16:37:52] [DEBUG] 🔍 分离词汇: 'head.' -> ['head', '.']
[16:37:52] [DEBUG] 🔍 分离词汇: 'is.' -> ['is', '.']
[16:37:52] [DEBUG] 🔍 分离词汇: 'upstairs.' -> ['upstairs', '.']
[16:37:52] [DEBUG] 🔍 分离词汇: 'money?' -> ['money', '?']
[16:37:52] [DEBUG] 🔍 分离词汇: 'that.' -> ['that', '.']
[16:37:52] [DEBUG] 🔍 分离词汇: 'way,' -> ['way', ',']
[16:37:52] [DEBUG] 🔍 分离词汇: 'body,' -> ['body', ',']
[16:37:52] [DEBUG] 🔍 分离词汇: 'basement.' -> ['basement', '.']
[16:37:52] ℹ️ 词汇分离完成，分离了 23 个混合词汇，最终词汇数量: 124
[16:37:52] ℹ️ Parsed trimmed_Honey Baby, Honey Baby (1974)_part18_01-24-10-ElevenLabs.json in 0.290s
[16:37:52] [OK] ElevenLabs JSON解析完成，已保存: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part18_01-24-10-ElevenLabs-parsed.json
[16:37:52]   词汇数量: 124
[16:37:52]   语言: unknown
[16:37:52]   置信度: 0.00
[16:37:52] [TaskFlow] 分段转录和解析完成: trimmed_Honey Baby, Honey Baby (1974)_part18_01-24-10 - ElevenLabs
[16:37:52] [TaskFlow] 处理分段 19/19: trimmed_Honey Baby, Honey Baby (1974)_part19_01-28-57
[16:37:52] [TaskFlow] 开始转录分段: trimmed_Honey Baby, Honey Baby (1974)_part19_01-28-57 - ElevenLabs
[16:37:52] 开始ElevenLabs free模式转录: trimmed_Honey Baby, Honey Baby (1974)_part19_01-28-57 (应用层尝试 1/3)
[16:38:00] ElevenLabs转录完成: trimmed_Honey Baby, Honey Baby (1974)_part19_01-28-57 (应用层尝试 1)
[16:38:01] JSON文件已保存: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part19_01-28-57-ElevenLabs.json
[16:38:01] [TaskFlow] 分段转录成功: trimmed_Honey Baby, Honey Baby (1974)_part19_01-28-57 - ElevenLabs
[16:38:01] [TaskFlow] 开始解析分段结果: trimmed_Honey Baby, Honey Baby (1974)_part19_01-28-57 - ElevenLabs
[16:38:01] 开始解析 ElevenLabs 的JSON文件
[16:38:01] [DEBUG] 🔍 使用regex库的Unicode属性进行字符分类
[16:38:01] [DEBUG] Detecting format for data with keys: ['success', 'service', 'transcription_id', 'text', 'words', 'language_detected', 'confidence', 'processing_time', 'metadata', 'created_at']
[16:38:01] [DEBUG] Detected elevenlabs standardized format from service field
[16:38:01] [DEBUG] Parsing ElevenLabs format
[16:38:01] [DEBUG] ElevenLabs data keys: ['success', 'service', 'transcription_id', 'text', 'words', 'language_detected', 'confidence', 'processing_time', 'metadata', 'created_at']
[16:38:01] [DEBUG] ElevenLabs full_text length: 1302
[16:38:01] [DEBUG] ElevenLabs words count: 483
[16:38:01] [DEBUG] ElevenLabs result: 242 words, confidence: 0.000, speakers: 4
[16:38:01] ℹ️ 开始词汇分离处理，原始词汇数量: 242
[16:38:01] [DEBUG] 🔍 分离词汇: 'job.' -> ['job', '.']
[16:38:01] [DEBUG] 🔍 分离词汇: 'specialist.' -> ['specialist', '.']
[16:38:01] [DEBUG] 🔍 分离词汇: 'on.' -> ['on', '.']
[16:38:01] [DEBUG] 🔍 分离词汇: 'Yeah.' -> ['Yeah', '.']
[16:38:01] [DEBUG] 🔍 分离词汇: 'right,' -> ['right', ',']
[16:38:01] [DEBUG] 🔍 分离词汇: 'right.' -> ['right', '.']
[16:38:01] [DEBUG] 🔍 分离词汇: 'him.' -> ['him', '.']
[16:38:01] [DEBUG] 🔍 分离词汇: 'right.' -> ['right', '.']
[16:38:01] [DEBUG] 🔍 分离词汇: 'arrest.' -> ['arrest', '.']
[16:38:01] [DEBUG] 🔍 分离词汇: 'available.' -> ['available', '.']
[16:38:01] [DEBUG] 🔍 分离词汇: 'Lesotho.' -> ['Lesotho', '.']
[16:38:01] [DEBUG] 🔍 分离词汇: 'yourself,' -> ['yourself', ',']
[16:38:01] [DEBUG] 🔍 分离词汇: 'Nefertiti.' -> ['Nefertiti', '.']
[16:38:01] [DEBUG] 🔍 分离词汇: 'Rah-Rah!' -> ['Rah-Rah', '!']
[16:38:01] [DEBUG] 🔍 分离词汇: 'on,' -> ['on', ',']
[16:38:01] [DEBUG] 🔍 分离词汇: 'ground.' -> ['ground', '.']
[16:38:01] [DEBUG] 🔍 分离词汇: 'Well.' -> ['Well', '.']
[16:38:01] [DEBUG] 🔍 分离词汇: 'Hey,' -> ['Hey', ',']
[16:38:01] [DEBUG] 🔍 分离词汇: 'going?' -> ['going', '?']
[16:38:01] [DEBUG] 🔍 分离词汇: 'you.' -> ['you', '.']
[16:38:01] [DEBUG] 🔍 分离词汇: 'breads,' -> ['breads', ',']
[16:38:01] [DEBUG] 🔍 分离词汇: 'baby.' -> ['baby', '.']
[16:38:01] [DEBUG] 🔍 分离词汇: 'cool.' -> ['cool', '.']
[16:38:01] [DEBUG] 🔍 分离词汇: 'later,' -> ['later', ',']
[16:38:01] [DEBUG] 🔍 分离词汇: 'yeah?' -> ['yeah', '?']
[16:38:01] [DEBUG] 🔍 分离词汇: 'yet?' -> ['yet', '?']
[16:38:01] [DEBUG] 🔍 分离词汇: 'No,' -> ['No', ',']
[16:38:01] [DEBUG] 🔍 分离词汇: 'mind.' -> ['mind', '.']
[16:38:01] [DEBUG] 🔍 分离词汇: 'Ooh.' -> ['Ooh', '.']
[16:38:01] [DEBUG] 🔍 分离词汇: 'do,' -> ['do', ',']
[16:38:01] [DEBUG] 🔍 分离词汇: 'know.' -> ['know', '.']
[16:38:01] [DEBUG] 🔍 分离词汇: 'is.' -> ['is', '.']
[16:38:01] [DEBUG] 🔍 分离词汇: 'might.' -> ['might', '.']
[16:38:01] [DEBUG] 🔍 分离词汇: 'baby.' -> ['baby', '.']
[16:38:01] [DEBUG] 🔍 分离词汇: 'baby.' -> ['baby', '.']
[16:38:01] [DEBUG] 🔍 分离词汇: 'baby.' -> ['baby', '.']
[16:38:01] [DEBUG] 🔍 分离词汇: 'Oh,' -> ['Oh', ',']
[16:38:01] [DEBUG] 🔍 分离词汇: 'baby.' -> ['baby', '.']
[16:38:01] [DEBUG] 🔍 分离词汇: 'baby,' -> ['baby', ',']
[16:38:01] [DEBUG] 🔍 分离词汇: 'street.' -> ['street', '.']
[16:38:01] [DEBUG] 🔍 分离词汇: 'eat.' -> ['eat', '.']
[16:38:01] [DEBUG] 🔍 分离词汇: 'baby,' -> ['baby', ',']
[16:38:01] [DEBUG] 🔍 分离词汇: 'tough.' -> ['tough', '.']
[16:38:01] [DEBUG] 🔍 分离词汇: 'enough.' -> ['enough', '.']
[16:38:01] [DEBUG] 🔍 分离词汇: 'Oh,' -> ['Oh', ',']
[16:38:01] [DEBUG] 🔍 分离词汇: 'baby.' -> ['baby', '.']
[16:38:01] [DEBUG] 🔍 分离词汇: 'baby.' -> ['baby', '.']
[16:38:01] [DEBUG] 🔍 分离词汇: 'baby.' -> ['baby', '.']
[16:38:01] [DEBUG] 🔍 分离词汇: 'Oh,' -> ['Oh', ',']
[16:38:01] [DEBUG] 🔍 分离词汇: 'baby.' -> ['baby', '.']
[16:38:01] [DEBUG] 🔍 分离词汇: 'Well,' -> ['Well', ',']
[16:38:01] [DEBUG] 🔍 分离词汇: 'baby.' -> ['baby', '.']
[16:38:01] [DEBUG] 🔍 分离词汇: 'time,' -> ['time', ',']
[16:38:01] [DEBUG] 🔍 分离词汇: 'you?' -> ['you', '?']
[16:38:01] [DEBUG] 🔍 分离词汇: 'time,' -> ['time', ',']
[16:38:01] [DEBUG] 🔍 分离词汇: 'now?' -> ['now', '?']
[16:38:01] [DEBUG] 🔍 分离词汇: 'Now,' -> ['Now', ',']
[16:38:01] [DEBUG] 🔍 分离词汇: 'before,' -> ['before', ',']
[16:38:01] [DEBUG] 🔍 分离词汇: 'grass.' -> ['grass', '.']
[16:38:01] [DEBUG] 🔍 分离词汇: 'last.' -> ['last', '.']
[16:38:01] [DEBUG] 🔍 分离词汇: 'sleep.' -> ['sleep', '.']
[16:38:01] [DEBUG] 🔍 分离词汇: 'gone.' -> ['gone', '.']
[16:38:01] [DEBUG] 🔍 分离词汇: 'Mohican.' -> ['Mohican', '.']
[16:38:01] ℹ️ 词汇分离完成，分离了 63 个混合词汇，最终词汇数量: 305
[16:38:01] ℹ️ Parsed trimmed_Honey Baby, Honey Baby (1974)_part19_01-28-57-ElevenLabs.json in 0.700s
[16:38:01] [OK] ElevenLabs JSON解析完成，已保存: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part19_01-28-57-ElevenLabs-parsed.json
[16:38:01]   词汇数量: 305
[16:38:01]   语言: unknown
[16:38:01]   置信度: 0.00
[16:38:01] [TaskFlow] 分段转录和解析完成: trimmed_Honey Baby, Honey Baby (1974)_part19_01-28-57 - ElevenLabs
[16:38:01] [TaskFlow] 所有分段ASR完成: 19/19
[16:38:01] [TaskFlow] 所有分段ASR处理完成，触发LLM阶段
[16:38:01] [TaskFlow] 开始分段LLM处理，ASR服务: ElevenLabs, LLM API: API2
[16:38:01] [TaskFlow] 处理组合: ElevenLabs-API2
[16:38:01] [TaskFlow] 处理分段字幕 1/19: trimmed_Honey Baby, Honey Baby (1974)_part1_00-00-00
[16:38:01] [TaskFlow] 开始生成分段字幕: trimmed_Honey Baby, Honey Baby (1974)_part1_00-00-00 - ElevenLabs-API2
[16:38:01] ℹ️ 开始字幕生成: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part1_00-00-00-ElevenLabs-parsed.json
[16:38:01] [DEBUG] 🔍 提取服务名称，输入文件: trimmed_Honey Baby, Honey Baby (1974)_part1_00-00-00-ElevenLabs-parsed.json
[16:38:01] [DEBUG] 🔍 正则匹配成功，服务名称: ElevenLabs
[16:38:01] ℹ️ 检测到服务名称: ElevenLabs
[16:38:01] ℹ️ 检测已存在的字幕文件...
[16:38:01] [DEBUG] 🔍 🔍 扫描目录: ./projects\trimmed_Honey Baby, Honey Baby (1974)
[16:38:01] [DEBUG] 🔍 🔍 查找模式: trimmed_Honey Baby, Honey Baby (1974)_part1_00-00-00-ElevenLabs-*.srt
[16:38:01] [DEBUG] 🔍 🔍 找到 0 个匹配文件
[16:38:01] ℹ️ 未发现已存在字幕文件
[16:38:01] ℹ️ 全部启用的API: ['API2']
[16:38:02] ℹ️ 已存在字幕的API: []
[16:38:02] ℹ️ 需要处理的API: ['API2']
[16:38:02] ℹ️ 正在分析音频静音段...
[16:38:02] ℹ️ 正在检测音频静音段: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part1_00-00-00.mp3
[16:38:02] ℹ️ 检测到 549 个静音段
[16:38:02] ℹ️ 静音检测完成，缓存结果用于后续处理
[16:38:02] ℹ️ 开始处理 API2...
[16:38:02] ℹ️ API2: 启用分块处理
[16:38:02] ℹ️ API2: 分割为 3 个块
[16:38:02] ℹ️ API2: 正在处理第 1/3 块 (938 字符)
[16:38:02] [DEBUG] 🔍 API2: 开始API调用处理第 1 块...
[16:38:02] [DEBUG] 🔍 API2: 开始API调用...
[16:38:42] [DEBUG] 🔍 API2: API调用成功，返回 948 字符
[16:38:42] ℹ️ API2: 第 1 块处理成功 ✅ (耗时: 39.5秒, 差异: 10 字符)
[16:38:42] ℹ️ API2: 正在处理第 2/3 块 (992 字符)
[16:38:42] ℹ️ API2: 等待请求间隔 10.0 秒...
[16:38:52] [DEBUG] 🔍 API2: 开始API调用处理第 2 块...
[16:38:52] [DEBUG] 🔍 API2: 开始API调用...
[16:39:21] [DEBUG] 🔍 API2: API调用成功，返回 997 字符
[16:39:21] ℹ️ API2: 第 2 块处理成功 ✅ (耗时: 29.7秒, 差异: 5 字符)
[16:39:21] ℹ️ API2: 正在处理第 3/3 块 (888 字符)
[16:39:21] ℹ️ API2: 等待请求间隔 10.0 秒...
[16:39:31] [DEBUG] 🔍 API2: 开始API调用处理第 3 块...
[16:39:31] [DEBUG] 🔍 API2: 开始API调用...
[16:39:35] 已打开项目目录: E:\github\EvaTrans\projects
[16:40:21] [DEBUG] 🔍 API2: API调用成功，返回 898 字符
[16:40:21] ℹ️ API2: 第 3 块处理成功 ✅ (耗时: 49.6秒, 差异: 10 字符)
[16:40:21] ℹ️ API2: 初始处理完成，成功 3/3 块
[16:40:21] ℹ️ API2: LLM调用成功，开始生成字幕...
[16:40:21] ℹ️ 对齐 API2 的文本分段...
[16:40:21] ℹ️ LLM分段调试数据已保存到: ./projects\trimmed_Honey Baby, Honey Baby (1974)\llm_segments_debug_20250803_164021.json
[16:40:21] ℹ️ 开始文本对齐处理（顺序约束），共 72 个片段
[16:40:21] [DEBUG] 🔍 片段处理进度:
[16:40:21] [DEBUG] 🔍   #1 | 精确匹配✅ → 对齐成功✅ | "Hey, how you doing?"
[16:40:21] [DEBUG] 🔍   #2 | 精确匹配✅ → 对齐成功✅ | "My name is J. Eric Bell//and I..."
[16:40:21] [DEBUG] 🔍   #3 | 精确匹配✅ → 对齐成功✅ | "while filming a movie,//Honey ..."
[16:40:21] [DEBUG] 🔍   #4 | 精确匹配✅ → 对齐成功✅ | "and it was a hell of an experi..."
[16:40:21] [DEBUG] 🔍   #5 | 精确匹配✅ → 对齐成功✅ | "By the way,//my name in the mo..."
[16:40:21] [DEBUG] 🔍   #6 | 精确匹配✅ → 对齐成功✅ | "better known as Umbeini Malali..."
[16:40:21] [DEBUG] 🔍   #7 | 精确匹配✅ → 对齐成功✅ | "I entered my cousin, Honey Bab..."
[16:40:21] [DEBUG] 🔍   #8 | 精确匹配✅ → 对齐成功✅ | "a trip around the world."
[16:40:21] [DEBUG] 🔍   #9 | 精确匹配✅ → 对齐成功✅ | "She's smart. I knew she'd win ..."
[16:40:21] [DEBUG] 🔍   #10 | 精确匹配✅ → 对齐成功✅ | "She's the smartest sister in H..."
[16:40:21] [DEBUG] 🔍   #11 | 精确匹配✅ → 对齐成功✅ | "Anyway, we're all to Beirut, L..."
[16:40:21] [DEBUG] 🔍   #12 | 精确匹配✅ → 对齐成功✅ | "And we meet this Chinese lady ..."
[16:40:21] [DEBUG] 🔍   #13 | 精确匹配✅ → 对齐成功✅ | "This Chinese lady has this mic..."
[16:40:21] [DEBUG] 🔍   #14 | 精确匹配✅ → 对齐成功✅ | "The microdot I'm telling you a..."
[16:40:21] [DEBUG] 🔍   #15 | 精确匹配✅ → 对齐成功✅ | "She takes this microdot and pu..."
[16:40:21] [DEBUG] 🔍   #16 | 精确匹配✅ → 对齐成功✅ | "puts it in Honey Baby's passpo..."
[16:40:21] [DEBUG] 🔍   #17 | 精确匹配✅ → 对齐成功✅ | "Nobody knows it."
[16:40:21] [DEBUG] 🔍   #18 | 精确匹配✅ → 对齐成功✅ | "She does it real slick//'cause..."
[16:40:21] [DEBUG] 🔍   #19 | 精确匹配✅ → 对齐成功✅ | "she drops her bag or something..."
[16:40:21] [DEBUG] 🔍   #20 | 精确匹配✅ → 对齐成功✅ | "But anyway, this microdot//has..."
[16:40:21] [DEBUG] 🔍   #21 | 精确匹配✅ → 对齐成功✅ | "of this African prime minister..."
[16:40:21] [DEBUG] 🔍   #22 | 精确匹配✅ → 对齐成功✅ | "Now this microdot has this for..."
[16:40:21] [DEBUG] 🔍   #23 | 精确匹配✅ → 对齐成功✅ | "Then stuff starts to get stick..."
[16:40:21] [DEBUG] 🔍   #24 | 精确匹配✅ → 对齐成功✅ | "You dig what I'm saying?"
[16:40:21] [DEBUG] 🔍   #25 | 精确匹配✅ → 对齐成功✅ | "Yeah."
[16:40:21] [DEBUG] 🔍   #26 | 精确匹配✅ → 对齐成功✅ | "Right."
[16:40:21] [DEBUG] 🔍   #27 | 精确匹配✅ → 对齐成功✅ | "Hey, look, I ain't gonna tell ..."
[16:40:21] [DEBUG] 🔍   #28 | 精确匹配✅ → 对齐成功✅ | "I'm gonna let you dig//the res..."
[16:40:21] [DEBUG] 🔍   #29 | 精确匹配✅ → 对齐成功✅ | "All right?"
[16:40:21] [DEBUG] 🔍   #30 | 精确匹配✅ → 对齐成功✅ | "When you gonna watch the flick..."
[16:40:21] [DEBUG] 🔍   #31 | 精确匹配✅ → 对齐成功✅ | "Yo, let's get it on."
[16:40:21] [DEBUG] 🔍   #32 | 精确匹配✅ → 对齐成功✅ | "Honey baby."
[16:40:21] [DEBUG] 🔍   #33 | 精确匹配✅ → 对齐成功✅ | "Honey baby."
[16:40:21] [DEBUG] 🔍   #34 | 精确匹配✅ → 对齐成功✅ | "Honey baby."
[16:40:21] [DEBUG] 🔍   #35 | 精确匹配✅ → 对齐成功✅ | "Honey baby."
[16:40:21] [DEBUG] 🔍   #36 | 精确匹配✅ → 对齐成功✅ | "You learned from the street."
[16:40:21] [DEBUG] 🔍   #37 | 精确匹配✅ → 对齐成功✅ | "That the world is living aroun..."
[16:40:22] [DEBUG] 🔍   #38 | 精确匹配✅ → 对齐成功✅ | "Honey baby."
[16:40:22] [DEBUG] 🔍   #39 | 精确匹配✅ → 对齐成功✅ | "You think that you're tough."
[16:40:22] [DEBUG] 🔍   #40 | 精确匹配✅ → 对齐成功✅ | "But that just ain't enough."
[16:40:22] [DEBUG] 🔍   #41 | 精确匹配✅ → 对齐成功✅ | "The ocean of humanity."
[16:40:22] [DEBUG] 🔍   #42 | 精确匹配✅ → 对齐成功✅ | "Searching to the sound."
[16:40:22] [DEBUG] 🔍   #43 | 精确匹配✅ → 对齐成功✅ | "Of- Man, that bag's on your ba..."
[16:40:22] [DEBUG] 🔍   #44 | 精确匹配✅ → 对齐成功✅ | "Wow."
[16:40:22] [DEBUG] 🔍   #45 | 精确匹配✅ → 对齐成功✅ | "I wish I never entered her in ..."
[16:40:22] [DEBUG] 🔍   #46 | 精确匹配✅ → 对齐成功✅ | "Hey, man, dignity, the importa..."
[16:40:22] [DEBUG] 🔍   #47 | 精确匹配✅ → 对齐成功✅ | "And you gonna leave for six we..."
[16:40:22] [DEBUG] 🔍   #48 | 精确匹配✅ → 对齐成功✅ | "Look, y'all don't understand."
[16:40:22] [DEBUG] 🔍   #49 | 精确匹配✅ → 对齐成功✅ | "Honey Baby done jam it up so w..."
[16:40:22] [DEBUG] 🔍   #50 | 精确匹配✅ → 对齐成功✅ | "we leaving from New York//and ..."
[16:40:22] [DEBUG] 🔍   #51 | 精确匹配✅ → 对齐成功✅ | "Not only that, we supposed to ..."
[16:40:22] [DEBUG] 🔍   #52 | 精确匹配✅ → 对齐成功✅ | "some kind of boat, to Beirut."
[16:40:22] [DEBUG] 🔍   #53 | 精确匹配✅ → 对齐成功✅ | "Man, an ocean liner, man."
[16:40:22] [DEBUG] 🔍   #54 | 精确匹配✅ → 对齐成功✅ | "Yeah, yeah. Hey, man, that's s..."
[16:40:22] [DEBUG] 🔍   #55 | 精确匹配✅ → 对齐成功✅ | "Some dizzy ass producer//and s..."
[16:40:22] [DEBUG] 🔍   #56 | 精确匹配✅ → 对齐成功✅ | "is supposed to be going with u..."
[16:40:22] [DEBUG] 🔍   #57 | 精确匹配✅ → 对齐成功✅ | "Now, I thought that me and Hon..."
[16:40:22] [DEBUG] 🔍   #58 | 精确匹配✅ → 对齐成功✅ | "just the two us."
[16:40:22] [DEBUG] 🔍   #59 | 精确匹配✅ → 对齐成功✅ | ""I can see Honey Baby now.//Lo..."
[16:40:22] [DEBUG] 🔍   #60 | 精确匹配✅ → 对齐成功✅ | "Arthur, could you bring me my ..."
[16:40:22] [DEBUG] 🔍   #61 | 精确匹配✅ → 对齐成功✅ | "And, um, Honey, could you plea..."
[16:40:22] [DEBUG] 🔍   #62 | 精确匹配✅ → 对齐成功✅ | "because I'm stepping into so m..."
[16:40:22] [DEBUG] 🔍   #63 | 精确匹配✅ → 对齐成功✅ | "And, uh, my stunt handlers,//y..."
[16:40:22] [DEBUG] 🔍   #64 | 精确匹配✅ → 对齐成功✅ | "Man, man. Hey, man."
[16:40:22] [DEBUG] 🔍   #65 | 精确匹配✅ → 对齐成功✅ | "I got to take money off with m..."
[16:40:22] [DEBUG] 🔍   #66 | 精确匹配✅ → 对齐成功✅ | "Hey, dude, this is..."
[16:40:22] [DEBUG] 🔍   #67 | 精确匹配✅ → 对齐成功✅ | "You ain't taking yourself noth..."
[16:40:22] [DEBUG] 🔍   #68 | 精确匹配✅ → 对齐成功✅ | "You going to the land of the h..."
[16:40:22] [DEBUG] 🔍   #69 | 精确匹配✅ → 对齐成功✅ | "June, George, bring your ass o..."
[16:40:22] [DEBUG] 🔍   #70 | 精确匹配✅ → 对齐成功✅ | "What the hell are you think th..."
[16:40:22] [DEBUG] 🔍   #71 | 精确匹配✅ → 对齐成功✅ | "Arthur, take your black ass on..."
[16:40:22] [DEBUG] 🔍   #72 | 精确匹配✅ → 对齐成功✅ | "You know you have to meet the ..."
[16:40:22] ℹ️ API2: 成功对齐 72 个文本分段
[16:40:22] ℹ️ 处理 API2 的字幕条目...
[16:40:22] [DEBUG] 🔍 开始处理 72 个字幕条目的时间间隔分割...
[16:40:22] ℹ️ 时间间隔分割完成：72 个条目 → 72 个条目
[16:40:22] [DEBUG] 🔍 短时长过滤完成：72 个条目，无需过滤
[16:40:22] ℹ️ 正在裁剪字幕静音区域...
[16:40:22] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:40:22] [DEBUG] 🔍 第一轮裁剪: 处理72个, 去除静音26.476秒
[16:40:22] [DEBUG] 🔍 开始第二轮静音裁剪...
[16:40:22] [DEBUG] 🔍 第二轮裁剪: 处理72个条目
[16:40:22] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:40:22] [DEBUG] 🔍 第二轮-开始时间: 在100ms偏移找到52个有效调整，剩余20个条目
[16:40:22] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:40:22] [DEBUG] 🔍 第二轮-开始时间: 在200ms偏移找到1个有效调整，剩余19个条目
[16:40:22] [DEBUG] 🔍 第二轮-开始时间: 总计处理53个条目，19个条目保持原样
[16:40:22] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:40:22] [DEBUG] 🔍 第二轮-结束时间: 在100ms偏移找到2个有效调整，剩余70个条目
[16:40:22] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:40:22] [DEBUG] 🔍 第二轮-结束时间: 200ms偏移无有效调整，剩余70个条目
[16:40:22] [DEBUG] 🔍 第二轮-结束时间: 总计处理2个条目，70个条目保持原样
[16:40:22] [DEBUG] 🔍 第二轮裁剪: 处理72个, 总计调整14个条目
[16:40:22] [DEBUG] 🔍 开始第三轮静音裁剪...
[16:40:22] [DEBUG] 🔍 第三轮裁剪: 处理72个条目
[16:40:22] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:40:22] [DEBUG] 🔍 第三轮-开始时间: 100ms偏移无有效调整，剩余72个条目
[16:40:22] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:40:22] [DEBUG] 🔍 第三轮-开始时间: 200ms偏移无有效调整，剩余72个条目
[16:40:22] [DEBUG] 🔍 第三轮-开始时间: 总计处理0个条目，72个条目保持原样
[16:40:22] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:40:22] [DEBUG] 🔍 第三轮-结束时间: 在100ms偏移找到49个有效调整，剩余23个条目
[16:40:22] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:40:22] [DEBUG] 🔍 第三轮-结束时间: 在200ms偏移找到2个有效调整，剩余21个条目
[16:40:22] [DEBUG] 🔍 第三轮-结束时间: 总计处理51个条目，21个条目保持原样
[16:40:22] [DEBUG] 🔍 第三轮裁剪: 处理72个, 总计调整2个条目
[16:40:22] ℹ️ 标准扩充: 增加显示 22.557 秒
[16:40:22] [DEBUG] 🔍 标准扩充详情: 完全25个, 部分47个
[16:40:22] [DEBUG] 🔍 解决扩充冲突: 31个
[16:40:22] ℹ️ 智能扩充: 处理6个短字幕, 增加显示 1.971 秒
[16:40:22] [DEBUG] 🔍 智能扩充详情: 完全4个, 部分2个, 目标1000ms
[16:40:22] ℹ️ 桥接处理: 填补间隔 1.872 秒
[16:40:22] [DEBUG] 🔍 桥接详情: 处理15个间隔, 阈值300ms
[16:40:22] ℹ️ API2: 字幕生成完成 - trimmed_Honey Baby, Honey Baby (1974)_part1_00-00-00-ElevenLabs-API2.srt
[16:40:22] ℹ️ 字幕生成完成，共生成 1 个字幕文件
[16:40:22] ℹ️ 文本处理统计:
[16:40:22] ℹ️   总LLM分段数: 72
[16:40:22] ℹ️   第一轮精确匹配: 72/72 (100.0%)
[16:40:22] ℹ️   第二轮锚点匹配: 0/72 (0.0%)
[16:40:22] ℹ️   文本匹配成功: 72/72 (100.0%)
[16:40:22] ℹ️   文本对齐成功: 72/72 (100.0%)
[16:40:22] [TaskFlow] 分段字幕生成成功: trimmed_Honey Baby, Honey Baby (1974)_part1_00-00-00 - ElevenLabs-API2
[16:40:22] [TaskFlow] 处理分段字幕 2/19: trimmed_Honey Baby, Honey Baby (1974)_part2_00-05-18
[16:40:22] [TaskFlow] 开始生成分段字幕: trimmed_Honey Baby, Honey Baby (1974)_part2_00-05-18 - ElevenLabs-API2
[16:40:22] ℹ️ 开始字幕生成: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part2_00-05-18-ElevenLabs-parsed.json
[16:40:22] [DEBUG] 🔍 提取服务名称，输入文件: trimmed_Honey Baby, Honey Baby (1974)_part2_00-05-18-ElevenLabs-parsed.json
[16:40:22] [DEBUG] 🔍 正则匹配成功，服务名称: ElevenLabs
[16:40:22] ℹ️ 检测到服务名称: ElevenLabs
[16:40:22] ℹ️ 检测已存在的字幕文件...
[16:40:22] [DEBUG] 🔍 🔍 扫描目录: ./projects\trimmed_Honey Baby, Honey Baby (1974)
[16:40:22] [DEBUG] 🔍 🔍 查找模式: trimmed_Honey Baby, Honey Baby (1974)_part2_00-05-18-ElevenLabs-*.srt
[16:40:22] [DEBUG] 🔍 🔍 找到 0 个匹配文件
[16:40:22] ℹ️ 未发现已存在字幕文件
[16:40:22] ℹ️ 全部启用的API: ['API2']
[16:40:22] ℹ️ 已存在字幕的API: []
[16:40:23] ℹ️ 需要处理的API: ['API2']
[16:40:23] ℹ️ 正在分析音频静音段...
[16:40:23] ℹ️ 正在检测音频静音段: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part2_00-05-18.mp3
[16:40:23] ℹ️ 检测到 318 个静音段
[16:40:23] ℹ️ 静音检测完成，缓存结果用于后续处理
[16:40:23] ℹ️ 开始处理 API2...
[16:40:23] ℹ️ API2: 启用分块处理
[16:40:23] ℹ️ API2: 分割为 2 个块
[16:40:23] ℹ️ API2: 正在处理第 1/2 块 (971 字符)
[16:40:23] [DEBUG] 🔍 API2: 开始API调用处理第 1 块...
[16:40:23] [DEBUG] 🔍 API2: 开始API调用...
[16:40:44] [DEBUG] 🔍 API2: API调用成功，返回 974 字符
[16:40:44] ℹ️ API2: 第 1 块处理成功 ✅ (耗时: 21.1秒, 差异: 3 字符)
[16:40:44] ℹ️ API2: 正在处理第 2/2 块 (290 字符)
[16:40:44] ℹ️ API2: 等待请求间隔 10.0 秒...
[16:40:54] [DEBUG] 🔍 API2: 开始API调用处理第 2 块...
[16:40:54] [DEBUG] 🔍 API2: 开始API调用...
[16:41:22] [DEBUG] 🔍 API2: API调用成功，返回 293 字符
[16:41:22] ℹ️ API2: 第 2 块处理成功 ✅ (耗时: 28.2秒, 差异: 3 字符)
[16:41:22] ℹ️ API2: 初始处理完成，成功 2/2 块
[16:41:22] ℹ️ API2: LLM调用成功，开始生成字幕...
[16:41:22] ℹ️ 对齐 API2 的文本分段...
[16:41:22] ℹ️ LLM分段调试数据已保存到: ./projects\trimmed_Honey Baby, Honey Baby (1974)\llm_segments_debug_20250803_164122.json
[16:41:22] ℹ️ 开始文本对齐处理（顺序约束），共 48 个片段
[16:41:22] [DEBUG] 🔍 片段处理进度:
[16:41:22] [DEBUG] 🔍   #1 | 精确匹配✅ → 对齐成功✅ | "You got a caption for that?"
[16:41:22] [DEBUG] 🔍   #2 | 精确匹配✅ → 对齐成功✅ | "I got a good one for you, Sam."
[16:41:22] [DEBUG] 🔍   #3 | 精确匹配✅ → 对齐成功✅ | "Oh, yeah?"
[16:41:23] [DEBUG] 🔍   #4 | 精确匹配✅ → 对齐成功✅ | "Yeah."
[16:41:23] [DEBUG] 🔍   #5 | 精确匹配✅ → 对齐成功✅ | "How about yesterday, Lenox Ave..."
[16:41:23] [DEBUG] 🔍   #6 | 精确匹配✅ → 对齐成功✅ | "Not yet."
[16:41:23] [DEBUG] 🔍   #7 | 精确匹配✅ → 对齐成功✅ | "Not quite yet."
[16:41:23] [DEBUG] 🔍   #8 | 精确匹配✅ → 对齐成功✅ | "I've been running around."
[16:41:23] [DEBUG] 🔍   #9 | 精确匹配✅ → 对齐成功✅ | "Skippy."
[16:41:23] [DEBUG] 🔍   #10 | 精确匹配✅ → 对齐成功✅ | "Skippy, will you turn the soun..."
[16:41:23] [DEBUG] 🔍   #11 | 精确匹配✅ → 对齐成功✅ | "I'm gonna break loose."
[16:41:23] [DEBUG] 🔍   #12 | 精确匹配✅ → 对齐成功✅ | "Yeah."
[16:41:23] [DEBUG] 🔍   #13 | 精确匹配✅ → 对齐成功✅ | "I'm gonna break free."
[16:41:23] [DEBUG] 🔍   #14 | 精确匹配✅ → 对齐成功✅ | "Come on, Skippy."
[16:41:23] [DEBUG] 🔍   #15 | 精确匹配✅ → 对齐成功✅ | "Stop."
[16:41:23] [DEBUG] 🔍   #16 | 精确匹配✅ → 对齐成功✅ | "Honey, baby."
[16:41:23] [DEBUG] 🔍   #17 | 精确匹配✅ → 对齐成功✅ | "Would you mind just once//call..."
[16:41:23] [DEBUG] 🔍   #18 | 精确匹配✅ → 对齐成功✅ | "I thought your name was Skippy..."
[16:41:23] [DEBUG] 🔍   #19 | 精确匹配✅ → 对齐成功✅ | "Skippy's a nickname."
[16:41:23] [DEBUG] 🔍   #20 | 精确匹配✅ → 对齐成功✅ | "Oh, what's his real name?"
[16:41:23] [DEBUG] 🔍   #21 | 精确匹配✅ → 对齐成功✅ | "Arthur."
[16:41:23] [DEBUG] 🔍   #22 | 精确匹配✅ → 对齐成功✅ | "Arthur Lewis."
[16:41:23] [DEBUG] 🔍   #23 | 精确匹配✅ → 对齐成功✅ | "That's my slave name."
[16:41:23] [DEBUG] 🔍   #24 | 精确匹配✅ → 对齐成功✅ | "My real name is Mbaini Mullele..."
[16:41:23] [DEBUG] 🔍   #25 | 精确匹配✅ → 对齐成功✅ | "Lewis."
[16:41:23] [DEBUG] 🔍   #26 | 精确匹配✅ → 对齐成功✅ | "There she is."
[16:41:23] [DEBUG] 🔍   #27 | 精确匹配✅ → 对齐成功✅ | "I already have two prizes."
[16:41:23] [DEBUG] 🔍   #28 | 精确匹配✅ → 对齐成功✅ | "Do you care to take a look?"
[16:41:23] [DEBUG] 🔍   #29 | 精确匹配✅ → 对齐成功✅ | "Ah, yes."
[16:41:23] [DEBUG] 🔍   #30 | 精确匹配✅ → 对齐成功✅ | "The other more beautiful half ..."
[16:41:23] [DEBUG] 🔍   #31 | 精确匹配✅ → 对齐成功✅ | ". Yeah. . ."
[16:41:23] [DEBUG] 🔍   #32 | 精确匹配✅ → 对齐成功✅ | "For a quarter of a million dol..."
[16:41:23] [DEBUG] 🔍   #33 | 精确匹配✅ → 对齐成功✅ | "Summoning silence."
[16:41:23] [DEBUG] 🔍   #34 | 精确匹配✅ → 对齐成功✅ | "Silence in the court."
[16:41:23] [DEBUG] 🔍   #35 | 精确匹配✅ → 对齐成功✅ | "Oh my God."
[16:41:23] [DEBUG] 🔍   #36 | 精确匹配✅ → 对齐成功✅ | "You left the old bag scotch sa..."
[16:41:23] [DEBUG] 🔍   #37 | 精确匹配✅ → 对齐成功✅ | "She bloody well murdered me."
[16:41:23] [DEBUG] 🔍   #38 | 精确匹配✅ → 对齐成功✅ | "Hotshot Harry, the scourge of ..."
[16:41:23] [DEBUG] 🔍   #39 | 精确匹配✅ → 对齐成功✅ | "Tell me, what exactly is this ..."
[16:41:23] [DEBUG] 🔍   #40 | 精确匹配✅ → 对齐成功✅ | "Only if it's not an embarrassi..."
[16:41:23] [DEBUG] 🔍   #41 | 精确匹配✅ → 对齐成功✅ | "It's an embarrassing question."
[16:41:23] [DEBUG] 🔍   #42 | 精确匹配✅ → 对齐成功✅ | "She's my mother,//a half Irish..."
[16:41:23] [DEBUG] 🔍   #43 | 精确匹配✅ → 对齐成功✅ | "What was wrong with an airplan..."
[16:41:23] [DEBUG] 🔍   #44 | 精确匹配✅ → 对齐成功✅ | "That's all I wanna know."
[16:41:23] [DEBUG] 🔍   #45 | 精确匹配✅ → 对齐成功✅ | "As a concept,//what was wrong ..."
[16:41:23] [DEBUG] 🔍   #46 | 精确匹配✅ → 对齐成功✅ | "Oh, Sam,//you can travel on a ..."
[16:41:23] [DEBUG] 🔍   #47 | 精确匹配✅ → 对齐成功✅ | "Customs launch,"
[16:41:23] [DEBUG] 🔍   #48 | 精确匹配✅ → 对齐成功✅ | "I'm afraid our celebration//mi..."
[16:41:23] ℹ️ API2: 成功对齐 48 个文本分段
[16:41:23] ℹ️ 处理 API2 的字幕条目...
[16:41:23] [DEBUG] 🔍 开始处理 48 个字幕条目的时间间隔分割...
[16:41:23] ℹ️ 时间间隔分割完成：48 个条目 → 48 个条目
[16:41:23] [DEBUG] 🔍 过滤短时长条目: 'Stop.' (00:01:49,040 --> 00:01:49,180, 持续时间: 0.140s)
[16:41:23] ℹ️ 短时长过滤完成：48 个条目 → 47 个条目（过滤掉 1 个短时长条目）
[16:41:23] ℹ️ 正在裁剪字幕静音区域...
[16:41:23] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:41:23] [DEBUG] 🔍 第一轮裁剪: 处理47个, 去除静音40.093秒
[16:41:23] [DEBUG] 🔍 开始第二轮静音裁剪...
[16:41:23] [DEBUG] 🔍 第二轮裁剪: 处理47个条目
[16:41:23] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:41:23] [DEBUG] 🔍 第二轮-开始时间: 在100ms偏移找到42个有效调整，剩余5个条目
[16:41:23] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:41:23] [DEBUG] 🔍 第二轮-开始时间: 在200ms偏移找到1个有效调整，剩余4个条目
[16:41:23] [DEBUG] 🔍 第二轮-开始时间: 总计处理43个条目，4个条目保持原样
[16:41:23] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:41:23] [DEBUG] 🔍 第二轮-结束时间: 在100ms偏移找到1个有效调整，剩余46个条目
[16:41:23] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:41:23] [DEBUG] 🔍 第二轮-结束时间: 在200ms偏移找到2个有效调整，剩余44个条目
[16:41:23] [DEBUG] 🔍 第二轮-结束时间: 总计处理3个条目，44个条目保持原样
[16:41:23] [DEBUG] 🔍 第二轮裁剪: 处理47个, 总计调整18个条目
[16:41:23] [DEBUG] 🔍 开始第三轮静音裁剪...
[16:41:23] [DEBUG] 🔍 第三轮裁剪: 处理47个条目
[16:41:23] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:41:23] [DEBUG] 🔍 第三轮-开始时间: 在100ms偏移找到2个有效调整，剩余45个条目
[16:41:23] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:41:23] [DEBUG] 🔍 第三轮-开始时间: 在200ms偏移找到2个有效调整，剩余43个条目
[16:41:23] [DEBUG] 🔍 第三轮-开始时间: 总计处理4个条目，43个条目保持原样
[16:41:23] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:41:23] [DEBUG] 🔍 第三轮-结束时间: 在100ms偏移找到43个有效调整，剩余4个条目
[16:41:23] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:41:23] [DEBUG] 🔍 第三轮-结束时间: 200ms偏移无有效调整，剩余4个条目
[16:41:23] [DEBUG] 🔍 第三轮-结束时间: 总计处理43个条目，4个条目保持原样
[16:41:23] [DEBUG] 🔍 第三轮裁剪: 处理47个, 总计调整4个条目
[16:41:23] ℹ️ 标准扩充: 增加显示 17.961 秒
[16:41:23] [DEBUG] 🔍 标准扩充详情: 完全36个, 部分11个
[16:41:23] [DEBUG] 🔍 解决扩充冲突: 6个
[16:41:23] ℹ️ 智能扩充: 处理7个短字幕, 增加显示 1.180 秒
[16:41:23] [DEBUG] 🔍 智能扩充详情: 完全6个, 部分1个, 目标1000ms
[16:41:23] ℹ️ 桥接处理: 填补间隔 0.694 秒
[16:41:23] [DEBUG] 🔍 桥接详情: 处理6个间隔, 阈值300ms
[16:41:23] ℹ️ API2: 字幕生成完成 - trimmed_Honey Baby, Honey Baby (1974)_part2_00-05-18-ElevenLabs-API2.srt
[16:41:23] ℹ️ 字幕生成完成，共生成 1 个字幕文件
[16:41:23] ℹ️ 文本处理统计:
[16:41:23] ℹ️   总LLM分段数: 48
[16:41:23] ℹ️   第一轮精确匹配: 48/48 (100.0%)
[16:41:23] ℹ️   第二轮锚点匹配: 0/48 (0.0%)
[16:41:23] ℹ️   文本匹配成功: 48/48 (100.0%)
[16:41:23] ℹ️   文本对齐成功: 48/48 (100.0%)
[16:41:23] [TaskFlow] 分段字幕生成成功: trimmed_Honey Baby, Honey Baby (1974)_part2_00-05-18 - ElevenLabs-API2
[16:41:23] [TaskFlow] 处理分段字幕 3/19: trimmed_Honey Baby, Honey Baby (1974)_part3_00-10-14
[16:41:23] [TaskFlow] 开始生成分段字幕: trimmed_Honey Baby, Honey Baby (1974)_part3_00-10-14 - ElevenLabs-API2
[16:41:23] ℹ️ 开始字幕生成: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part3_00-10-14-ElevenLabs-parsed.json
[16:41:23] [DEBUG] 🔍 提取服务名称，输入文件: trimmed_Honey Baby, Honey Baby (1974)_part3_00-10-14-ElevenLabs-parsed.json
[16:41:23] [DEBUG] 🔍 正则匹配成功，服务名称: ElevenLabs
[16:41:23] ℹ️ 检测到服务名称: ElevenLabs
[16:41:24] ℹ️ 检测已存在的字幕文件...
[16:41:24] [DEBUG] 🔍 🔍 扫描目录: ./projects\trimmed_Honey Baby, Honey Baby (1974)
[16:41:24] [DEBUG] 🔍 🔍 查找模式: trimmed_Honey Baby, Honey Baby (1974)_part3_00-10-14-ElevenLabs-*.srt
[16:41:24] [DEBUG] 🔍 🔍 找到 0 个匹配文件
[16:41:24] ℹ️ 未发现已存在字幕文件
[16:41:24] ℹ️ 全部启用的API: ['API2']
[16:41:24] ℹ️ 已存在字幕的API: []
[16:41:24] ℹ️ 需要处理的API: ['API2']
[16:41:24] ℹ️ 正在分析音频静音段...
[16:41:24] ℹ️ 正在检测音频静音段: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part3_00-10-14.mp3
[16:41:24] ℹ️ 检测到 375 个静音段
[16:41:24] ℹ️ 静音检测完成，缓存结果用于后续处理
[16:41:24] ℹ️ 开始处理 API2...
[16:41:24] ℹ️ API2: 启用分块处理
[16:41:24] ℹ️ API2: 分割为 2 个块
[16:41:24] ℹ️ API2: 正在处理第 1/2 块 (918 字符)
[16:41:24] [DEBUG] 🔍 API2: 开始API调用处理第 1 块...
[16:41:24] [DEBUG] 🔍 API2: 开始API调用...
[16:41:57] [DEBUG] 🔍 API2: API调用成功，返回 921 字符
[16:41:57] ℹ️ API2: 第 1 块处理成功 ✅ (耗时: 33.3秒, 差异: 3 字符)
[16:41:57] ℹ️ API2: 正在处理第 2/2 块 (584 字符)
[16:41:57] ℹ️ API2: 等待请求间隔 10.0 秒...
[16:42:07] [DEBUG] 🔍 API2: 开始API调用处理第 2 块...
[16:42:07] [DEBUG] 🔍 API2: 开始API调用...
[16:42:24] [DEBUG] 🔍 API2: API调用成功，返回 585 字符
[16:42:24] ℹ️ API2: 第 2 块处理成功 ✅ (耗时: 16.9秒, 差异: 1 字符)
[16:42:24] ℹ️ API2: 初始处理完成，成功 2/2 块
[16:42:24] ℹ️ API2: LLM调用成功，开始生成字幕...
[16:42:24] ℹ️ 对齐 API2 的文本分段...
[16:42:24] ℹ️ LLM分段调试数据已保存到: ./projects\trimmed_Honey Baby, Honey Baby (1974)\llm_segments_debug_20250803_164224.json
[16:42:24] ℹ️ 开始文本对齐处理（顺序约束），共 58 个片段
[16:42:24] [DEBUG] 🔍 片段处理进度:
[16:42:24] [DEBUG] 🔍   #1 | 精确匹配✅ → 对齐成功✅ | "Oh. They're going to board."
[16:42:24] [DEBUG] 🔍   #2 | 精确匹配✅ → 对齐成功✅ | "I hope she's got nothing//in h..."
[16:42:24] [DEBUG] 🔍   #3 | 精确匹配✅ → 对齐成功✅ | "Give way. Give way."
[16:42:24] [DEBUG] 🔍   #4 | 精确匹配✅ → 对齐成功✅ | "I want to go through."
[16:42:24] [DEBUG] 🔍   #5 | 精确匹配✅ → 对齐成功✅ | "No, no. We want to go through."
[16:42:24] [DEBUG] 🔍   #6 | 精确匹配✅ → 对齐成功✅ | "Let us off."
[16:42:24] [DEBUG] 🔍   #7 | 精确匹配✅ → 对齐成功✅ | "You want to go?"
[16:42:24] [DEBUG] 🔍   #8 | 精确匹配✅ → 对齐成功✅ | "Yeah, yeah. We want to go."
[16:42:24] [DEBUG] 🔍   #9 | 精确匹配✅ → 对齐成功✅ | "No, they're going to search us..."
[16:42:24] [DEBUG] 🔍   #10 | 精确匹配✅ → 对齐成功✅ | "Yeah, I expect so."
[16:42:24] [DEBUG] 🔍   #11 | 精确匹配✅ → 对齐成功✅ | "In fact, it's you and us."
[16:42:24] [DEBUG] 🔍   #12 | 精确匹配✅ → 对齐成功✅ | "Sure."
[16:42:24] [DEBUG] 🔍   #13 | 精确匹配✅ → 对齐成功✅ | "You, come in."
[16:42:24] [DEBUG] 🔍   #14 | 精确匹配✅ → 对齐成功✅ | "La, la, la, la."
[16:42:24] [DEBUG] 🔍   #15 | 精确匹配✅ → 对齐成功✅ | "You, come in."
[16:42:24] [DEBUG] 🔍   #16 | 精确匹配✅ → 对齐成功✅ | "Me?"
[16:42:24] [DEBUG] 🔍   #17 | 精确匹配✅ → 对齐成功✅ | "Come in."
[16:42:24] [DEBUG] 🔍   #18 | 精确匹配✅ → 对齐成功✅ | "No, no. We want to go."
[16:42:24] [DEBUG] 🔍   #19 | 精确匹配✅ → 对齐成功✅ | "We want to go through."
[16:42:24] [DEBUG] 🔍   #20 | 精确匹配✅ → 对齐成功✅ | "Let us off."
[16:42:24] [DEBUG] 🔍   #21 | 精确匹配✅ → 对齐成功✅ | "I'm telling you,//I have child..."
[16:42:24] [DEBUG] 🔍   #22 | 精确匹配✅ → 对齐成功✅ | "I can't go on like this."
[16:42:24] [DEBUG] 🔍   #23 | 精确匹配✅ → 对齐成功✅ | "This is not right."
[16:42:24] [DEBUG] 🔍   #24 | 精确匹配✅ → 对齐成功✅ | "This is not our fault."
[16:42:24] [DEBUG] 🔍   #25 | 精确匹配✅ → 对齐成功✅ | "We're working on the farm."
[16:42:24] [DEBUG] 🔍   #26 | 精确匹配✅ → 对齐成功✅ | "You know?"
[16:42:24] [DEBUG] 🔍   #27 | 精确匹配✅ → 对齐成功✅ | "We're good people."
[16:42:25] [DEBUG] 🔍   #28 | 精确匹配✅ → 对齐成功✅ | "We pay all our taxes."
[16:42:25] [DEBUG] 🔍   #29 | 精确匹配✅ → 对齐成功✅ | "We put money into the country."
[16:42:25] [DEBUG] 🔍   #30 | 精确匹配✅ → 对齐成功✅ | "Why should we be treated like ..."
[16:42:25] [DEBUG] 🔍   #31 | 精确匹配✅ → 对齐成功✅ | "You, come in."
[16:42:25] [DEBUG] 🔍   #32 | 精确匹配✅ → 对齐成功✅ | "You, come in."
[16:42:25] [DEBUG] 🔍   #33 | 精确匹配✅ → 对齐成功✅ | ". Oh, you, look at you."
[16:42:25] [DEBUG] 🔍   #34 | 精确匹配✅ → 对齐成功✅ | "Oh, you."
[16:42:25] [DEBUG] 🔍   #35 | 精确匹配✅ → 对齐成功✅ | "But it's okay. It's all right."
[16:42:25] [DEBUG] 🔍   #36 | 精确匹配✅ → 对齐成功✅ | "Oh, no, don't worry."
[16:42:25] [DEBUG] 🔍   #37 | 精确匹配✅ → 对齐成功✅ | "It'll be all right."
[16:42:25] [DEBUG] 🔍   #38 | 精确匹配✅ → 对齐成功✅ | "What are they doing?"
[16:42:25] [DEBUG] 🔍   #39 | 精确匹配✅ → 对齐成功✅ | "Nothing, it's a..."
[16:42:25] [DEBUG] 🔍   #40 | 精确匹配✅ → 对齐成功✅ | "Hey, hey."
[16:42:25] [DEBUG] 🔍   #41 | 精确匹配✅ → 对齐成功✅ | "The transportation of both art..."
[16:42:25] [DEBUG] 🔍   #42 | 精确匹配✅ → 对齐成功✅ | "Harry, I transported the most ..."
[16:42:25] [DEBUG] 🔍   #43 | 精确匹配✅ → 对齐成功✅ | "that half-ton crate halfway ac..."
[16:42:25] [DEBUG] 🔍   #44 | 精确匹配✅ → 对齐成功✅ | "Today was supposed to be a par..."
[16:42:25] [DEBUG] 🔍   #45 | 精确匹配✅ → 对齐成功✅ | "But one without the other is n..."
[16:42:25] [DEBUG] 🔍   #46 | 精确匹配✅ → 对齐成功✅ | "because without her//they won'..."
[16:42:25] [DEBUG] 🔍   #47 | 精确匹配✅ → 对齐成功✅ | "You and your goddamn disintegr..."
[16:42:25] [DEBUG] 🔍   #48 | 精确匹配✅ → 对齐成功✅ | "Here!"
[16:42:25] [DEBUG] 🔍   #49 | 精确匹配✅ → 对齐成功✅ | "Everybody blames me."
[16:42:25] [DEBUG] 🔍   #50 | 精确匹配✅ → 对齐成功✅ | "Wait for me, you bloody fool!"
[16:42:25] [DEBUG] 🔍   #51 | 精确匹配✅ → 对齐成功✅ | "Behold, Harry, my friend."
[16:42:25] [DEBUG] 🔍   #52 | 精确匹配✅ → 对齐成功✅ | "Hm?"
[16:42:25] [DEBUG] 🔍   #53 | 精确匹配✅ → 对齐成功✅ | "Just what is inside that conta..."
[16:42:25] [DEBUG] 🔍   #54 | 精确匹配✅ → 对齐成功✅ | "I never ask about content,"
[16:42:25] [DEBUG] 🔍   #55 | 精确匹配✅ → 对齐成功✅ | "only shape, size, and possibly..."
[16:42:25] [DEBUG] 🔍   #56 | 精确匹配✅ → 对齐成功✅ | "I'm gonna check that chick//an..."
[16:42:25] [DEBUG] 🔍   #57 | 精确匹配✅ → 对齐成功✅ | "Excellent idea, Liam."
[16:42:25] [DEBUG] 🔍   #58 | 精确匹配✅ → 对齐成功✅ | "I'll stand them all for the pa..."
[16:42:25] ℹ️ API2: 成功对齐 58 个文本分段
[16:42:25] ℹ️ 处理 API2 的字幕条目...
[16:42:25] [DEBUG] 🔍 开始处理 58 个字幕条目的时间间隔分割...
[16:42:25] ℹ️ 时间间隔分割完成：58 个条目 → 60 个条目
[16:42:25] [DEBUG] 🔍 过滤短时长条目: 'We want to go through.' (00:01:11,239 --> 00:01:11,339, 持续时间: 0.100s)
[16:42:25] [DEBUG] 🔍 过滤短时长条目: 'Let us off.' (00:01:11,339 --> 00:01:11,459, 持续时间: 0.120s)
[16:42:25] [DEBUG] 🔍 过滤短时长条目: 'You know?' (00:01:23,939 --> 00:01:23,959, 持续时间: 0.020s)
[16:42:25] ℹ️ 短时长过滤完成：60 个条目 → 57 个条目（过滤掉 3 个短时长条目）
[16:42:25] ℹ️ 正在裁剪字幕静音区域...
[16:42:25] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:42:25] [DEBUG] 🔍 第一轮裁剪: 处理57个, 去除静音8.401秒
[16:42:25] [DEBUG] 🔍 开始第二轮静音裁剪...
[16:42:25] [DEBUG] 🔍 第二轮裁剪: 处理57个条目
[16:42:25] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:42:25] [DEBUG] 🔍 第二轮-开始时间: 在100ms偏移找到34个有效调整，剩余23个条目
[16:42:25] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:42:25] [DEBUG] 🔍 第二轮-开始时间: 200ms偏移无有效调整，剩余23个条目
[16:42:25] [DEBUG] 🔍 第二轮-开始时间: 总计处理34个条目，23个条目保持原样
[16:42:25] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:42:25] [DEBUG] 🔍 第二轮-结束时间: 在100ms偏移找到1个有效调整，剩余56个条目
[16:42:25] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:42:25] [DEBUG] 🔍 第二轮-结束时间: 200ms偏移无有效调整，剩余56个条目
[16:42:25] [DEBUG] 🔍 第二轮-结束时间: 总计处理1个条目，56个条目保持原样
[16:42:25] [DEBUG] 🔍 第二轮裁剪: 处理57个, 总计调整11个条目
[16:42:25] [DEBUG] 🔍 开始第三轮静音裁剪...
[16:42:25] [DEBUG] 🔍 第三轮裁剪: 处理57个条目
[16:42:25] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:42:25] [DEBUG] 🔍 第三轮-开始时间: 100ms偏移无有效调整，剩余57个条目
[16:42:25] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:42:25] [DEBUG] 🔍 第三轮-开始时间: 200ms偏移无有效调整，剩余57个条目
[16:42:25] [DEBUG] 🔍 第三轮-开始时间: 总计处理0个条目，57个条目保持原样
[16:42:25] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:42:25] [DEBUG] 🔍 第三轮-结束时间: 在100ms偏移找到29个有效调整，剩余28个条目
[16:42:25] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:42:25] [DEBUG] 🔍 第三轮-结束时间: 在200ms偏移找到1个有效调整，剩余27个条目
[16:42:25] [DEBUG] 🔍 第三轮-结束时间: 总计处理30个条目，27个条目保持原样
[16:42:25] [DEBUG] 🔍 第三轮裁剪: 处理57个, 总计调整2个条目
[16:42:25] ℹ️ 标准扩充: 增加显示 17.364 秒
[16:42:25] [DEBUG] 🔍 标准扩充详情: 完全24个, 部分33个
[16:42:25] [DEBUG] 🔍 解决扩充冲突: 21个
[16:42:25] ℹ️ 智能扩充: 处理12个短字幕, 增加显示 2.664 秒
[16:42:25] [DEBUG] 🔍 智能扩充详情: 完全7个, 部分5个, 目标1000ms
[16:42:25] ℹ️ 桥接处理: 填补间隔 0.986 秒
[16:42:25] [DEBUG] 🔍 桥接详情: 处理7个间隔, 阈值300ms
[16:42:25] ℹ️ API2: 字幕生成完成 - trimmed_Honey Baby, Honey Baby (1974)_part3_00-10-14-ElevenLabs-API2.srt
[16:42:25] ℹ️ 字幕生成完成，共生成 1 个字幕文件
[16:42:25] ℹ️ 文本处理统计:
[16:42:25] ℹ️   总LLM分段数: 58
[16:42:25] ℹ️   第一轮精确匹配: 58/58 (100.0%)
[16:42:25] ℹ️   第二轮锚点匹配: 0/58 (0.0%)
[16:42:25] ℹ️   文本匹配成功: 58/58 (100.0%)
[16:42:25] ℹ️   文本对齐成功: 58/58 (100.0%)
[16:42:25] [TaskFlow] 分段字幕生成成功: trimmed_Honey Baby, Honey Baby (1974)_part3_00-10-14 - ElevenLabs-API2
[16:42:25] [TaskFlow] 处理分段字幕 4/19: trimmed_Honey Baby, Honey Baby (1974)_part4_00-14-25
[16:42:25] [TaskFlow] 开始生成分段字幕: trimmed_Honey Baby, Honey Baby (1974)_part4_00-14-25 - ElevenLabs-API2
[16:42:25] ℹ️ 开始字幕生成: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part4_00-14-25-ElevenLabs-parsed.json
[16:42:25] [DEBUG] 🔍 提取服务名称，输入文件: trimmed_Honey Baby, Honey Baby (1974)_part4_00-14-25-ElevenLabs-parsed.json
[16:42:25] [DEBUG] 🔍 正则匹配成功，服务名称: ElevenLabs
[16:42:25] ℹ️ 检测到服务名称: ElevenLabs
[16:42:25] ℹ️ 检测已存在的字幕文件...
[16:42:25] [DEBUG] 🔍 🔍 扫描目录: ./projects\trimmed_Honey Baby, Honey Baby (1974)
[16:42:25] [DEBUG] 🔍 🔍 查找模式: trimmed_Honey Baby, Honey Baby (1974)_part4_00-14-25-ElevenLabs-*.srt
[16:42:25] [DEBUG] 🔍 🔍 找到 0 个匹配文件
[16:42:25] ℹ️ 未发现已存在字幕文件
[16:42:25] ℹ️ 全部启用的API: ['API2']
[16:42:25] ℹ️ 已存在字幕的API: []
[16:42:25] ℹ️ 需要处理的API: ['API2']
[16:42:25] ℹ️ 正在分析音频静音段...
[16:42:25] ℹ️ 正在检测音频静音段: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part4_00-14-25.mp3
[16:42:26] ℹ️ 检测到 447 个静音段
[16:42:26] ℹ️ 静音检测完成，缓存结果用于后续处理
[16:42:26] ℹ️ 开始处理 API2...
[16:42:26] ℹ️ API2: 启用分块处理
[16:42:26] ℹ️ API2: 分割为 4 个块
[16:42:26] ℹ️ API2: 正在处理第 1/4 块 (995 字符)
[16:42:26] [DEBUG] 🔍 API2: 开始API调用处理第 1 块...
[16:42:26] [DEBUG] 🔍 API2: 开始API调用...
[16:43:00] [DEBUG] 🔍 API2: API调用成功，返回 999 字符
[16:43:00] ℹ️ API2: 第 1 块处理成功 ✅ (耗时: 33.9秒, 差异: 4 字符)
[16:43:00] ℹ️ API2: 正在处理第 2/4 块 (962 字符)
[16:43:00] ℹ️ API2: 等待请求间隔 10.0 秒...
[16:43:10] [DEBUG] 🔍 API2: 开始API调用处理第 2 块...
[16:43:10] [DEBUG] 🔍 API2: 开始API调用...
[16:43:13] 已打开项目目录: E:\github\EvaTrans\projects
[16:43:38] [DEBUG] 🔍 API2: API调用成功，返回 963 字符
[16:43:38] ℹ️ API2: 第 2 块处理成功 ✅ (耗时: 27.9秒, 差异: 1 字符)
[16:43:38] ℹ️ API2: 正在处理第 3/4 块 (972 字符)
[16:43:38] ℹ️ API2: 等待请求间隔 10.0 秒...
[16:43:48] [DEBUG] 🔍 API2: 开始API调用处理第 3 块...
[16:43:48] [DEBUG] 🔍 API2: 开始API调用...
[16:44:24] [DEBUG] 🔍 API2: API调用成功，返回 984 字符
[16:44:24] ℹ️ API2: 第 3 块处理成功 ✅ (耗时: 36.4秒, 差异: 12 字符)
[16:44:24] ℹ️ API2: 正在处理第 4/4 块 (66 字符)
[16:44:24] ℹ️ API2: 等待请求间隔 10.0 秒...
[16:44:34] [DEBUG] 🔍 API2: 开始API调用处理第 4 块...
[16:44:34] [DEBUG] 🔍 API2: 开始API调用...
[16:44:47] [DEBUG] 🔍 API2: API调用成功，返回 65 字符
[16:44:47] ℹ️ API2: 第 4 块处理成功 ✅ (耗时: 13.1秒, 差异: 1 字符)
[16:44:47] ℹ️ API2: 初始处理完成，成功 4/4 块
[16:44:47] ℹ️ API2: LLM调用成功，开始生成字幕...
[16:44:47] ℹ️ 对齐 API2 的文本分段...
[16:44:47] ℹ️ LLM分段调试数据已保存到: ./projects\trimmed_Honey Baby, Honey Baby (1974)\llm_segments_debug_20250803_164447.json
[16:44:47] ℹ️ 开始文本对齐处理（顺序约束），共 116 个片段
[16:44:47] [DEBUG] 🔍 片段处理进度:
[16:44:47] [DEBUG] 🔍   #1 | 精确匹配✅ → 对齐成功✅ | "Oh, my god. Look. Look, there'..."
[16:44:47] [DEBUG] 🔍   #2 | 精确匹配✅ → 对齐成功✅ | "A soup? Yes. Oh, stop, uh, sto..."
[16:44:47] [DEBUG] 🔍   #3 | 精确匹配✅ → 对齐成功✅ | "Wait, wait. There's a soup."
[16:44:47] [DEBUG] 🔍   #4 | 精确匹配✅ → 对齐成功✅ | "It's a marketplace."
[16:44:47] [DEBUG] 🔍   #5 | 精确匹配✅ → 对齐成功✅ | "Like Delancey Street, somewhat..."
[16:44:47] [DEBUG] 🔍   #6 | 精确匹配✅ → 对齐成功✅ | "Wait, wait, wait, wait, wait."
[16:44:47] [DEBUG] 🔍   #7 | 精确匹配✅ → 对齐成功✅ | "We- we- we've gotta get to the..."
[16:44:47] [DEBUG] 🔍   #8 | 精确匹配✅ → 对齐成功✅ | "Well, ju- ju- ju- just one min..."
[16:44:47] [DEBUG] 🔍   #9 | 精确匹配✅ → 对齐成功✅ | "Just one minute, Sam."
[16:44:47] [DEBUG] 🔍   #10 | 精确匹配✅ → 对齐成功✅ | "Uh, uh, uh- I'll only be a sec..."
[16:44:47] [DEBUG] 🔍   #11 | 精确匹配✅ → 对齐成功✅ | "L- Laura. Wait."
[16:44:47] [DEBUG] 🔍   #12 | 精确匹配✅ → 对齐成功✅ | "Five, five, five, five, five."
[16:44:47] [DEBUG] 🔍   #13 | 精确匹配✅ → 对齐成功✅ | "Oh, no, we've lost them."
[16:44:47] [DEBUG] 🔍   #14 | 精确匹配✅ → 对齐成功✅ | "You know she does it deliberat..."
[16:44:47] [DEBUG] 🔍   #15 | 精确匹配✅ → 对齐成功✅ | "You know she does it deliberat..."
[16:44:47] [DEBUG] 🔍   #16 | 精确匹配✅ → 对齐成功✅ | "Why didn't you keep your eye o..."
[16:44:47] [DEBUG] 🔍   #17 | 精确匹配✅ → 对齐成功✅ | "So what are we gonna do, Harry..."
[16:44:47] [DEBUG] 🔍   #18 | 精确匹配✅ → 对齐成功✅ | "I'm going home to face Mother ..."
[16:44:47] [DEBUG] 🔍   #19 | 精确匹配✅ → 对齐成功✅ | "I may very well get out//my ol..."
[16:44:47] [DEBUG] 🔍   #20 | 精确匹配✅ → 对齐成功✅ | "Not until we get the money, Ha..."
[16:44:47] [DEBUG] 🔍   #21 | 精确匹配✅ → 对齐成功✅ | "In order to get the money."
[16:44:47] [DEBUG] 🔍   #22 | 精确匹配✅ → 对齐成功✅ | "The only thing that interests ..."
[16:44:47] [DEBUG] 🔍   #23 | 精确匹配✅ → 对齐成功✅ | "Hey, don't waste my goddamn fi..."
[16:44:47] [DEBUG] 🔍   #24 | 精确匹配✅ → 对齐成功✅ | "Yeah."
[16:44:48] [DEBUG] 🔍   #25 | 精确匹配✅ → 对齐成功✅ | "What's the use of a stupid pic..."
[16:44:48] [DEBUG] 🔍   #26 | 精确匹配✅ → 对齐成功✅ | "without Laura Lewis in it?"
[16:44:48] [DEBUG] 🔍   #27 | 精确匹配✅ → 对齐成功✅ | "Don't waste my film. Take my p..."
[16:44:48] [DEBUG] 🔍   #28 | 精确匹配✅ → 对齐成功✅ | "May I have your passports plea..."
[16:44:48] [DEBUG] 🔍   #29 | 精确匹配✅ → 对齐成功✅ | "Sure."
[16:44:48] [DEBUG] 🔍   #30 | 精确匹配✅ → 对齐成功✅ | "It's me, Miss Laura Lewis,//au..."
[16:44:48] [DEBUG] 🔍   #31 | 精确匹配✅ → 对齐失败❌ | "Hello, hello."
[16:44:48] [DEBUG] 🔍   #32 | 精确匹配✅ → 对齐失败❌ | "Hello."
[16:44:48] [DEBUG] 🔍   #33 | 精确匹配✅ → 对齐失败❌ | "Hello."
[16:44:48] [DEBUG] 🔍   #34 | 精确匹配✅ → 对齐成功✅ | "How are you?"
[16:44:48] [DEBUG] 🔍   #35 | 精确匹配✅ → 对齐成功✅ | "How are you?"
[16:44:48] [DEBUG] 🔍   #36 | 精确匹配✅ → 对齐失败❌ | "Good."
[16:44:48] [DEBUG] 🔍   #37 | 精确匹配✅ → 对齐失败❌ | "Uh, but you're not what happen..."
[16:44:48] [DEBUG] 🔍   #38 | 精确匹配✅ → 对齐失败❌ | "We waited for you, we waited f..."
[16:44:48] [DEBUG] 🔍   #39 | 精确匹配✅ → 对齐成功✅ | "Harry."
[16:44:48] [DEBUG] 🔍   #40 | 精确匹配✅ → 对齐成功✅ | "You never show-"
[16:44:48] [DEBUG] 🔍   #41 | 精确匹配✅ → 对齐成功✅ | "What-"
[16:44:48] [DEBUG] 🔍   #42 | 精确匹配✅ → 对齐失败❌ | "You disappeared."
[16:44:48] [DEBUG] 🔍   #43 | 精确匹配✅ → 对齐成功✅ | "Seriously?"
[16:44:48] [DEBUG] 🔍   #44 | 精确匹配✅ → 对齐失败❌ | "What's with you?"
[16:44:48] [DEBUG] 🔍   #45 | 精确匹配✅ → 对齐失败❌ | "Just on me."
[16:44:48] [DEBUG] 🔍   #46 | 精确匹配✅ → 对齐失败❌ | "Oh, very funny."
[16:44:48] [DEBUG] 🔍   #47 | 精确匹配✅ → 对齐成功✅ | "That's the joke for the trip, ..."
[16:44:48] [DEBUG] 🔍   #48 | 精确匹配✅ → 对齐失败❌ | "That's the joke of the trip."
[16:44:48] [DEBUG] 🔍   #49 | 精确匹配✅ → 对齐成功✅ | "Okay."
[16:44:48] [DEBUG] 🔍   #50 | 精确匹配✅ → 对齐成功✅ | "What's her... Where's her..."
[16:44:48] [DEBUG] 🔍   #51 | 精确匹配✅ → 对齐成功✅ | "Her, what do you need her for?"
[16:44:48] [DEBUG] 🔍   #52 | 精确匹配✅ → 对齐成功✅ | "I mean, I gotta get a picture ..."
[16:44:48] [DEBUG] 🔍   #53 | 精确匹配✅ → 对齐成功✅ | "Why?"
[16:44:48] [DEBUG] 🔍   #54 | 精确匹配✅ → 对齐成功✅ | "I made a deal about the lungs."
[16:44:48] [DEBUG] 🔍   #55 | 精确匹配✅ → 对齐成功✅ | "Oh, Sam."
[16:44:48] [DEBUG] 🔍   #56 | 精确匹配✅ → 对齐失败❌ | "May I have my key please?"
[16:44:48] [DEBUG] 🔍   #57 | 精确匹配✅ → 对齐失败❌ | "Sure."
[16:44:48] [DEBUG] 🔍   #58 | 精确匹配✅ → 对齐成功✅ | "Sure, sure."
[16:44:48] [DEBUG] 🔍   #59 | 精确匹配✅ → 对齐成功✅ | "Hello."
[16:44:48] [DEBUG] 🔍   #60 | 精确匹配✅ → 对齐成功✅ | "Hello."
[16:44:48] [DEBUG] 🔍   #61 | 精确匹配✅ → 对齐成功✅ | "How are you?"
[16:44:48] [DEBUG] 🔍   #62 | 精确匹配✅ → 对齐失败❌ | "Hi."
[16:44:48] [DEBUG] 🔍   #63 | 精确匹配✅ → 对齐失败❌ | "Yeah, not bad, not bad."
[16:44:48] [DEBUG] 🔍   #64 | 精确匹配✅ → 对齐失败❌ | "Just working."
[16:44:48] [DEBUG] 🔍   #65 | 精确匹配✅ → 对齐失败❌ | "Just working?"
[16:44:48] [DEBUG] 🔍   #66 | 精确匹配✅ → 对齐失败❌ | "Yeah."
[16:44:48] [DEBUG] 🔍   #67 | 精确匹配✅ → 对齐失败❌ | "What do you do?"
[16:44:48] [DEBUG] 🔍   #68 | 精确匹配✅ → 对齐失败❌ | "I'm a writer."
[16:44:48] [DEBUG] 🔍   #69 | 精确匹配✅ → 对齐失败❌ | "Oh, that's wonderful."
[16:44:48] [DEBUG] 🔍   #70 | 精确匹配✅ → 对齐失败❌ | "What do you write about?"
[16:44:48] [DEBUG] 🔍   #71 | 精确匹配✅ → 对齐失败❌ | "Uh, I'm an author."
[16:44:48] [DEBUG] 🔍   #72 | 精确匹配✅ → 对齐失败❌ | "Oh, nice."
[16:44:48] [DEBUG] 🔍   #73 | 精确匹配✅ → 对齐失败❌ | "You should become a novelist."
[16:44:48] [DEBUG] 🔍   #74 | 精确匹配✅ → 对齐失败❌ | "Novelist?"
[16:44:48] [DEBUG] 🔍   #75 | 精确匹配✅ → 对齐失败❌ | "Yeah."
[16:44:48] [DEBUG] 🔍   #76 | 精确匹配✅ → 对齐失败❌ | "Yeah, go ahead."
[16:44:48] [DEBUG] 🔍   #77 | 精确匹配✅ → 对齐失败❌ | "No, thank you."
[16:44:48] [DEBUG] 🔍   #78 | 精确匹配✅ → 对齐失败❌ | "I'm afraid I can't."
[16:44:48] [DEBUG] 🔍   #79 | 精确匹配✅ → 对齐失败❌ | "I'm afraid I can't."
[16:44:48] [DEBUG] 🔍   #80 | 精确匹配✅ → 对齐失败❌ | "Uh, what's your name again?"
[16:44:48] [DEBUG] 🔍   #81 | 精确匹配✅ → 对齐失败❌ | "Laura."
[16:44:48] [DEBUG] 🔍   #82 | 精确匹配✅ → 对齐失败❌ | "Laura, Laura, Laura, Laura."
[16:44:48] [DEBUG] 🔍   #83 | 精确匹配✅ → 对齐失败❌ | "What do you think the police//..."
[16:44:48] [DEBUG] 🔍   #84 | 精确匹配✅ → 对齐成功✅ | "I don't know."
[16:44:48] [DEBUG] 🔍   #85 | 精确匹配✅ → 对齐成功✅ | "What do you mean?"
[16:44:48] [DEBUG] 🔍   #86 | 精确匹配✅ → 对齐失败❌ | "I mean, //like if they caught ..."
[16:44:48] [DEBUG] 🔍   #87 | 精确匹配✅ → 对齐成功✅ | "Oh, well, //we don't even have..."
[16:44:48] [DEBUG] 🔍   #88 | 精确匹配✅ → 对齐失败❌ | "because we don't do that sort ..."
[16:44:48] [DEBUG] 🔍   #89 | 精确匹配✅ → 对齐失败❌ | "Do we?"
[16:44:48] [DEBUG] 🔍   #90 | 精确匹配✅ → 对齐失败❌ | "No."
[16:44:48] [DEBUG] 🔍   #91 | 精确匹配✅ → 对齐失败❌ | "I was just wondering//if it wa..."
[16:44:48] [DEBUG] 🔍   #92 | 精确匹配✅ → 对齐失败❌ | "Let's put it this way."
[16:44:48] [DEBUG] 🔍   #93 | 精确匹配✅ → 对齐失败❌ | "Out here, //they cut your hand..."
[16:44:48] [DEBUG] 🔍   #94 | 精确匹配✅ → 对齐失败❌ | "Okay, Skiggy, where's your hea..."
[16:44:48] [DEBUG] 🔍   #95 | 精确匹配✅ → 对齐失败❌ | "My name is Mbele Malole."
[16:44:48] [DEBUG] 🔍   #96 | 精确匹配✅ → 对齐失败❌ | "Will you kindly cut the crap a..."
[16:44:48] [DEBUG] 🔍   #97 | 精确匹配✅ → 对齐失败❌ | "You know that Chinese lady on ..."
[16:44:48] [DEBUG] 🔍   #98 | 精确匹配✅ → 对齐失败❌ | "Yeah."
[16:44:48] [DEBUG] 🔍   #99 | 精确匹配✅ → 对齐失败❌ | "Lebanese cops would probably//..."
[16:44:48] [DEBUG] 🔍   #100 | 精确匹配✅ → 对齐成功✅ | "What the hell are you talking ..."
[16:44:48] [DEBUG] 🔍   #101 | 精确匹配✅ → 对齐失败❌ | "I laid a couple of joints on h..."
[16:44:48] [DEBUG] 🔍   #102 | 精确匹配✅ → 对齐成功✅ | "What?"
[16:44:48] [DEBUG] 🔍   #103 | 精确匹配✅ → 对齐失败❌ | "When the custom was searching ..."
[16:44:48] [DEBUG] 🔍   #104 | 精确匹配✅ → 对齐失败❌ | "Oh, Jesus, Skiggy."
[16:44:48] [DEBUG] 🔍   #105 | 精确匹配✅ → 对齐失败❌ | "I didn't mean to do it, Laura."
[16:44:48] [DEBUG] 🔍   #106 | 精确匹配✅ → 对齐失败❌ | "I was scared."
[16:44:48] [DEBUG] 🔍   #107 | 精确匹配✅ → 对齐失败❌ | "Wait a minute."
[16:44:48] [DEBUG] 🔍   #108 | 精确匹配✅ → 对齐成功✅ | "What are we gonna do?"
[16:44:48] [DEBUG] 🔍   #109 | 精确匹配✅ → 对齐成功✅ | "Well, we just can't//let her r..."
[16:44:48] [DEBUG] 🔍   #110 | 精确匹配✅ → 对齐成功✅ | "Oh, that was a terrible thing ..."
[16:44:48] [DEBUG] 🔍   #111 | 精确匹配✅ → 对齐成功✅ | "Where are you going?"
[16:44:48] [DEBUG] 🔍   #112 | 精确匹配✅ → 对齐成功✅ | "To the harbor police."
[16:44:48] [DEBUG] 🔍   #113 | 精确匹配✅ → 对齐成功✅ | "To the police?"
[16:44:48] [DEBUG] 🔍   #114 | 精确匹配✅ → 对齐成功✅ | "Just don't worry."
[16:44:48] [DEBUG] 🔍   #115 | 精确匹配✅ → 对齐成功✅ | "I'll think of something on the..."
[16:44:48] [DEBUG] 🔍   #116 | 精确匹配✅ → 对齐成功✅ | "And take that thing down."
[16:44:48] ℹ️ API2: 成功对齐 62 个文本分段
[16:44:48] ℹ️ 处理 API2 的字幕条目...
[16:44:48] [DEBUG] 🔍 开始处理 62 个字幕条目的时间间隔分割...
[16:44:48] ℹ️ 时间间隔分割完成：62 个条目 → 66 个条目
[16:44:48] [DEBUG] 🔍 过滤短时长条目: 'Wait, wait, wait, wait, wait.' (00:00:31,340 --> 00:00:31,360, 持续时间: 0.020s)
[16:44:48] [DEBUG] 🔍 过滤短时长条目: 'Sure.' (00:01:46,580 --> 00:01:46,580, 持续时间: 0.000s)
[16:44:48] [DEBUG] 🔍 过滤短时长条目: 'It's me, Miss Laura Lewis,//author Skiggy Mbele Malole.' (00:01:46,580 --> 00:01:46,580, 持续时间: 0.000s)
[16:44:48] [DEBUG] 🔍 过滤短时长条目: 'How are you?' (00:01:46,580 --> 00:01:46,580, 持续时间: 0.000s)
[16:44:48] [DEBUG] 🔍 过滤短时长条目: 'How are you?' (00:01:46,580 --> 00:01:46,580, 持续时间: 0.000s)
[16:44:48] [DEBUG] 🔍 过滤短时长条目: 'Harry.' (00:01:46,580 --> 00:01:46,580, 持续时间: 0.000s)
[16:44:48] [DEBUG] 🔍 过滤短时长条目: 'You never show-' (00:01:46,580 --> 00:01:46,580, 持续时间: 0.000s)
[16:44:48] [DEBUG] 🔍 过滤短时长条目: 'What-' (00:01:46,580 --> 00:01:46,580, 持续时间: 0.000s)
[16:44:48] [DEBUG] 🔍 过滤短时长条目: 'Seriously?' (00:01:46,580 --> 00:01:46,580, 持续时间: 0.000s)
[16:44:49] [DEBUG] 🔍 过滤短时长条目: 'That's the joke for the trip, right?' (00:01:46,580 --> 00:01:46,580, 持续时间: 0.000s)
[16:44:49] [DEBUG] 🔍 过滤短时长条目: 'What's her... Where's her...' (00:01:47,139 --> 00:01:47,280, 持续时间: 0.141s)
[16:44:49] [DEBUG] 🔍 过滤短时长条目: 'I made a deal about the lungs.' (00:01:49,179 --> 00:01:49,179, 持续时间: 0.000s)
[16:44:49] [DEBUG] 🔍 过滤短时长条目: 'Oh, Sam.' (00:01:49,179 --> 00:01:49,179, 持续时间: 0.000s)
[16:44:49] [DEBUG] 🔍 过滤短时长条目: 'Sure, sure.' (00:01:49,179 --> 00:01:49,179, 持续时间: 0.000s)
[16:44:49] [DEBUG] 🔍 过滤短时长条目: 'Hello.' (00:01:49,179 --> 00:01:49,179, 持续时间: 0.000s)
[16:44:49] [DEBUG] 🔍 过滤短时长条目: 'Hello.' (00:01:49,179 --> 00:01:49,179, 持续时间: 0.000s)
[16:44:49] [DEBUG] 🔍 过滤短时长条目: 'How are you?' (00:01:49,179 --> 00:01:49,179, 持续时间: 0.000s)
[16:44:49] [DEBUG] 🔍 过滤短时长条目: 'I don't know.' (00:01:49,179 --> 00:01:49,179, 持续时间: 0.000s)
[16:44:49] [DEBUG] 🔍 过滤短时长条目: 'What do you mean?' (00:01:49,179 --> 00:01:49,179, 持续时间: 0.000s)
[16:44:49] [DEBUG] 🔍 过滤短时长条目: 'Oh, well, //we don't even have to discuss that,' (00:01:49,179 --> 00:01:49,179, 持续时间: 0.000s)
[16:44:49] [DEBUG] 🔍 过滤短时长条目: 'What the hell are you talking about?' (00:01:49,179 --> 00:01:49,179, 持续时间: 0.000s)
[16:44:49] [DEBUG] 🔍 过滤短时长条目: 'What?' (00:01:49,179 --> 00:01:49,179, 持续时间: 0.000s)
[16:44:49] ℹ️ 短时长过滤完成：66 个条目 → 44 个条目（过滤掉 22 个短时长条目）
[16:44:49] ℹ️ 正在裁剪字幕静音区域...
[16:44:49] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:44:49] [DEBUG] 🔍 第一轮裁剪: 处理44个, 去除静音57.017秒
[16:44:49] [DEBUG] 🔍 开始第二轮静音裁剪...
[16:44:49] [DEBUG] 🔍 第二轮裁剪: 处理44个条目
[16:44:49] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:44:49] [DEBUG] 🔍 第二轮-开始时间: 在100ms偏移找到28个有效调整，剩余16个条目
[16:44:49] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:44:49] [DEBUG] 🔍 第二轮-开始时间: 在200ms偏移找到1个有效调整，剩余15个条目
[16:44:49] [DEBUG] 🔍 第二轮-开始时间: 总计处理29个条目，15个条目保持原样
[16:44:49] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:44:49] [DEBUG] 🔍 第二轮-结束时间: 在100ms偏移找到3个有效调整，剩余41个条目
[16:44:49] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:44:49] [DEBUG] 🔍 第二轮-结束时间: 在200ms偏移找到1个有效调整，剩余40个条目
[16:44:49] [DEBUG] 🔍 第二轮-结束时间: 总计处理4个条目，40个条目保持原样
[16:44:49] [DEBUG] 🔍 第二轮裁剪: 处理44个, 总计调整13个条目
[16:44:49] [DEBUG] 🔍 开始第三轮静音裁剪...
[16:44:49] [DEBUG] 🔍 第三轮裁剪: 处理44个条目
[16:44:49] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:44:49] [DEBUG] 🔍 第三轮-开始时间: 在100ms偏移找到1个有效调整，剩余43个条目
[16:44:49] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:44:49] [DEBUG] 🔍 第三轮-开始时间: 在200ms偏移找到1个有效调整，剩余42个条目
[16:44:49] [DEBUG] 🔍 第三轮-开始时间: 总计处理2个条目，42个条目保持原样
[16:44:49] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:44:49] [DEBUG] 🔍 第三轮-结束时间: 在100ms偏移找到32个有效调整，剩余12个条目
[16:44:49] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:44:49] [DEBUG] 🔍 第三轮-结束时间: 在200ms偏移找到1个有效调整，剩余11个条目
[16:44:49] [DEBUG] 🔍 第三轮-结束时间: 总计处理33个条目，11个条目保持原样
[16:44:49] [DEBUG] 🔍 第三轮裁剪: 处理44个, 总计调整4个条目
[16:44:49] ℹ️ 标准扩充: 增加显示 12.680 秒
[16:44:49] [DEBUG] 🔍 标准扩充详情: 完全19个, 部分25个
[16:44:49] [DEBUG] 🔍 解决扩充冲突: 17个
[16:44:49] ℹ️ 智能扩充: 处理8个短字幕, 增加显示 2.004 秒
[16:44:49] [DEBUG] 🔍 智能扩充详情: 完全5个, 部分3个, 目标1000ms
[16:44:49] ℹ️ 桥接处理: 填补间隔 0.588 秒
[16:44:49] [DEBUG] 🔍 桥接详情: 处理4个间隔, 阈值300ms
[16:44:49] ℹ️ API2: 字幕生成完成 - trimmed_Honey Baby, Honey Baby (1974)_part4_00-14-25-ElevenLabs-API2.srt
[16:44:49] ℹ️ 字幕生成完成，共生成 1 个字幕文件
[16:44:49] ℹ️ 文本处理统计:
[16:44:49] ℹ️   总LLM分段数: 116
[16:44:49] ℹ️   第一轮精确匹配: 116/116 (100.0%)
[16:44:49] ℹ️   第二轮锚点匹配: 0/116 (0.0%)
[16:44:49] ℹ️   文本匹配成功: 116/116 (100.0%)
[16:44:49] ℹ️   文本对齐成功: 62/116 (53.4%)
[16:44:49] [TaskFlow] 分段字幕生成成功: trimmed_Honey Baby, Honey Baby (1974)_part4_00-14-25 - ElevenLabs-API2
[16:44:49] [TaskFlow] 处理分段字幕 5/19: trimmed_Honey Baby, Honey Baby (1974)_part5_00-19-51
[16:44:49] [TaskFlow] 开始生成分段字幕: trimmed_Honey Baby, Honey Baby (1974)_part5_00-19-51 - ElevenLabs-API2
[16:44:49] ℹ️ 开始字幕生成: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part5_00-19-51-ElevenLabs-parsed.json
[16:44:49] [DEBUG] 🔍 提取服务名称，输入文件: trimmed_Honey Baby, Honey Baby (1974)_part5_00-19-51-ElevenLabs-parsed.json
[16:44:49] [DEBUG] 🔍 正则匹配成功，服务名称: ElevenLabs
[16:44:49] ℹ️ 检测到服务名称: ElevenLabs
[16:44:49] ℹ️ 检测已存在的字幕文件...
[16:44:49] [DEBUG] 🔍 🔍 扫描目录: ./projects\trimmed_Honey Baby, Honey Baby (1974)
[16:44:49] [DEBUG] 🔍 🔍 查找模式: trimmed_Honey Baby, Honey Baby (1974)_part5_00-19-51-ElevenLabs-*.srt
[16:44:49] [DEBUG] 🔍 🔍 找到 0 个匹配文件
[16:44:49] ℹ️ 未发现已存在字幕文件
[16:44:49] ℹ️ 全部启用的API: ['API2']
[16:44:49] ℹ️ 已存在字幕的API: []
[16:44:49] ℹ️ 需要处理的API: ['API2']
[16:44:49] ℹ️ 正在分析音频静音段...
[16:44:49] ℹ️ 正在检测音频静音段: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part5_00-19-51.mp3
[16:44:50] ℹ️ 检测到 307 个静音段
[16:44:50] ℹ️ 静音检测完成，缓存结果用于后续处理
[16:44:50] ℹ️ 开始处理 API2...
[16:44:50] ℹ️ API2: 启用分块处理
[16:44:50] ℹ️ API2: 分割为 2 个块
[16:44:50] ℹ️ API2: 正在处理第 1/2 块 (982 字符)
[16:44:50] [DEBUG] 🔍 API2: 开始API调用处理第 1 块...
[16:44:50] [DEBUG] 🔍 API2: 开始API调用...
[16:45:18] [DEBUG] 🔍 API2: API调用成功，返回 991 字符
[16:45:18] ℹ️ API2: 第 1 块处理成功 ✅ (耗时: 28.3秒, 差异: 9 字符)
[16:45:18] ℹ️ API2: 正在处理第 2/2 块 (487 字符)
[16:45:18] ℹ️ API2: 等待请求间隔 10.0 秒...
[16:45:28] [DEBUG] 🔍 API2: 开始API调用处理第 2 块...
[16:45:28] [DEBUG] 🔍 API2: 开始API调用...
[16:45:48] [DEBUG] 🔍 API2: API调用成功，返回 491 字符
[16:45:48] ℹ️ API2: 第 2 块处理成功 ✅ (耗时: 19.6秒, 差异: 4 字符)
[16:45:48] ℹ️ API2: 初始处理完成，成功 2/2 块
[16:45:48] ℹ️ API2: LLM调用成功，开始生成字幕...
[16:45:48] ℹ️ 对齐 API2 的文本分段...
[16:45:48] ℹ️ LLM分段调试数据已保存到: ./projects\trimmed_Honey Baby, Honey Baby (1974)\llm_segments_debug_20250803_164548.json
[16:45:48] ℹ️ 开始文本对齐处理（顺序约束），共 54 个片段
[16:45:48] [DEBUG] 🔍 片段处理进度:
[16:45:48] [DEBUG] 🔍   #1 | 精确匹配✅ → 对齐成功✅ | "... no, it was not on the scho..."
[16:45:48] [DEBUG] 🔍   #2 | 精确匹配✅ → 对齐成功✅ | "It was on the plane from Londo..."
[16:45:48] [DEBUG] 🔍   #3 | 精确匹配✅ → 对齐成功✅ | "Hello."
[16:45:48] [DEBUG] 🔍   #4 | 精确匹配✅ → 对齐成功✅ | "Uh, uh, yes."
[16:45:48] [DEBUG] 🔍   #5 | 精确匹配✅ → 对齐成功✅ | "Yes, Mr. Mokuba."
[16:45:48] [DEBUG] 🔍   #6 | 精确匹配✅ → 对齐成功✅ | "I've been expecting your call."
[16:45:48] [DEBUG] 🔍   #7 | 精确匹配✅ → 对齐成功✅ | "Yes."
[16:45:48] [DEBUG] 🔍   #8 | 精确匹配✅ → 对齐成功✅ | "Yes, Mr. Mokuba."
[16:45:48] [DEBUG] 🔍   #9 | 精确匹配✅ → 对齐成功✅ | "Okay, Mr. Harry,//I'll see you..."
[16:45:48] [DEBUG] 🔍   #10 | 精确匹配✅ → 对齐成功✅ | "Okay, bye."
[16:45:48] [DEBUG] 🔍   #11 | 精确匹配✅ → 对齐成功✅ | "You were drinking with that//n..."
[16:45:48] [DEBUG] 🔍   #12 | 精确匹配✅ → 对齐成功✅ | "Excuse me."
[16:45:48] [DEBUG] 🔍   #13 | 精确匹配✅ → 对齐成功✅ | "Are you Mr. Mokuba?"
[16:45:48] [DEBUG] 🔍   #14 | 精确匹配✅ → 对齐成功✅ | "Yes."
[16:45:48] [DEBUG] 🔍   #15 | 精确匹配✅ → 对齐成功✅ | "Come with us."
[16:45:48] [DEBUG] 🔍   #16 | 精确匹配✅ → 对齐成功✅ | "Stay quiet and you will stay a..."
[16:45:48] [DEBUG] 🔍   #17 | 精确匹配✅ → 对齐成功✅ | "Mr. Mokuba, your organization/..."
[16:45:48] [DEBUG] 🔍   #18 | 精确匹配✅ → 对齐成功✅ | "We have his body, General Howa..."
[16:45:48] [DEBUG] 🔍   #19 | 精确匹配✅ → 对齐成功✅ | "And we have the Chinese woman."
[16:45:48] [DEBUG] 🔍   #20 | 精确匹配✅ → 对齐成功✅ | "And now we have you."
[16:45:48] [DEBUG] 🔍   #21 | 精确匹配✅ → 对齐成功✅ | "Soon we will have his body als..."
[16:45:48] [DEBUG] 🔍   #22 | 精确匹配✅ → 对齐成功✅ | "Youthful idealism is not enoug..."
[16:45:48] [DEBUG] 🔍   #23 | 精确匹配✅ → 对齐成功✅ | "Good, solid, vested interests/..."
[16:45:48] [DEBUG] 🔍   #24 | 精确匹配✅ → 对齐成功✅ | "preferably with quotations//fr..."
[16:45:48] [DEBUG] 🔍   #25 | 精确匹配✅ → 对齐成功✅ | "Beat him!"
[16:45:48] [DEBUG] 🔍   #26 | 精确匹配✅ → 对齐成功✅ | "Too late."
[16:45:48] [DEBUG] 🔍   #27 | 精确匹配✅ → 对齐成功✅ | "Cyanide capsule."
[16:45:48] [DEBUG] 🔍   #28 | 精确匹配✅ → 对齐成功✅ | "Diamond blast."
[16:45:48] [DEBUG] 🔍   #29 | 精确匹配✅ → 对齐成功✅ | "Fanatics."
[16:45:48] [DEBUG] 🔍   #30 | 精确匹配✅ → 对齐成功✅ | "See that no one finds him."
[16:45:48] [DEBUG] 🔍   #31 | 精确匹配✅ → 对齐成功✅ | "No."
[16:45:48] [DEBUG] 🔍   #32 | 精确匹配✅ → 对齐成功✅ | "Mr. Mokuba, sir."
[16:45:48] [DEBUG] 🔍   #33 | 精确匹配✅ → 对齐成功✅ | "Bring him in."
[16:45:48] [DEBUG] 🔍   #34 | 精确匹配✅ → 对齐成功✅ | "Bring him in."
[16:45:48] [DEBUG] 🔍   #35 | 精确匹配✅ → 对齐成功✅ | "You're, uh, you're not exactly..."
[16:45:48] [DEBUG] 🔍   #36 | 精确匹配✅ → 对齐成功✅ | "And how would you imagine me?"
[16:45:48] [DEBUG] 🔍   #37 | 精确匹配✅ → 对齐成功✅ | "Um, younger."
[16:45:48] [DEBUG] 🔍   #38 | 精确匹配✅ → 对齐成功✅ | "You, uh, you sounded younger//..."
[16:45:48] [DEBUG] 🔍   #39 | 精确匹配✅ → 对齐成功✅ | "Uh, won't you sit down?"
[16:45:48] [DEBUG] 🔍   #40 | 精确匹配✅ → 对齐成功✅ | "Is Madeline Chan here?"
[16:45:48] [DEBUG] 🔍   #41 | 精确匹配✅ → 对齐成功✅ | "No."
[16:45:48] [DEBUG] 🔍   #42 | 精确匹配✅ → 对齐成功✅ | "No, we've, uh, we've had a sma..."
[16:45:48] [DEBUG] 🔍   #43 | 精确匹配✅ → 对齐成功✅ | "Uh, we only have one half of t..."
[16:45:48] [DEBUG] 🔍   #44 | 精确匹配✅ → 对齐成功✅ | "The two go together."
[16:45:48] [DEBUG] 🔍   #45 | 精确匹配✅ → 对齐成功✅ | "One without the other//is wors..."
[16:45:48] [DEBUG] 🔍   #46 | 精确匹配✅ → 对齐成功✅ | "I've had considerable expense ..."
[16:45:48] [DEBUG] 🔍   #47 | 精确匹配✅ → 对齐成功✅ | "Unless you find the Chinese wo..."
[16:45:48] [DEBUG] 🔍   #48 | 精确匹配✅ → 对齐成功✅ | "what you now hold will,//in tw..."
[16:45:48] [DEBUG] 🔍   #49 | 精确匹配✅ → 对齐成功✅ | "Fetid fish."
[16:45:48] [DEBUG] 🔍   #50 | 精确匹配✅ → 对齐成功✅ | "The preservation of the body//..."
[16:45:48] [DEBUG] 🔍   #51 | 精确匹配✅ → 对齐成功✅ | "Yes."
[16:45:48] [DEBUG] 🔍   #52 | 精确匹配✅ → 对齐成功✅ | "Yes. I understand that."
[16:45:48] [DEBUG] 🔍   #53 | 精确匹配✅ → 对齐成功✅ | "When can I see it?"
[16:45:48] [DEBUG] 🔍   #54 | 精确匹配✅ → 对齐成功✅ | "In due course."
[16:45:48] ℹ️ API2: 成功对齐 54 个文本分段
[16:45:48] ℹ️ 处理 API2 的字幕条目...
[16:45:48] [DEBUG] 🔍 开始处理 54 个字幕条目的时间间隔分割...
[16:45:48] ℹ️ 时间间隔分割完成：54 个条目 → 55 个条目
[16:45:48] [DEBUG] 🔍 过滤短时长条目: 'Is' (00:03:07,300 --> 00:03:07,320, 持续时间: 0.020s)
[16:45:48] ℹ️ 短时长过滤完成：55 个条目 → 54 个条目（过滤掉 1 个短时长条目）
[16:45:48] ℹ️ 正在裁剪字幕静音区域...
[16:45:48] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:45:48] [DEBUG] 🔍 第一轮裁剪: 处理54个, 去除静音10.897秒
[16:45:48] [DEBUG] 🔍 开始第二轮静音裁剪...
[16:45:48] [DEBUG] 🔍 第二轮裁剪: 处理54个条目
[16:45:48] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:45:48] [DEBUG] 🔍 第二轮-开始时间: 在100ms偏移找到41个有效调整，剩余13个条目
[16:45:49] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:45:49] [DEBUG] 🔍 第二轮-开始时间: 在200ms偏移找到2个有效调整，剩余11个条目
[16:45:49] [DEBUG] 🔍 第二轮-开始时间: 总计处理43个条目，11个条目保持原样
[16:45:49] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:45:49] [DEBUG] 🔍 第二轮-结束时间: 在100ms偏移找到2个有效调整，剩余52个条目
[16:45:49] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:45:49] [DEBUG] 🔍 第二轮-结束时间: 200ms偏移无有效调整，剩余52个条目
[16:45:49] [DEBUG] 🔍 第二轮-结束时间: 总计处理2个条目，52个条目保持原样
[16:45:49] [DEBUG] 🔍 第二轮裁剪: 处理54个, 总计调整13个条目
[16:45:49] [DEBUG] 🔍 开始第三轮静音裁剪...
[16:45:49] [DEBUG] 🔍 第三轮裁剪: 处理54个条目
[16:45:49] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:45:49] [DEBUG] 🔍 第三轮-开始时间: 在100ms偏移找到2个有效调整，剩余52个条目
[16:45:49] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:45:49] [DEBUG] 🔍 第三轮-开始时间: 200ms偏移无有效调整，剩余52个条目
[16:45:49] [DEBUG] 🔍 第三轮-开始时间: 总计处理2个条目，52个条目保持原样
[16:45:49] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:45:49] [DEBUG] 🔍 第三轮-结束时间: 在100ms偏移找到44个有效调整，剩余10个条目
[16:45:49] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:45:49] [DEBUG] 🔍 第三轮-结束时间: 在200ms偏移找到1个有效调整，剩余9个条目
[16:45:49] [DEBUG] 🔍 第三轮-结束时间: 总计处理45个条目，9个条目保持原样
[16:45:49] [DEBUG] 🔍 第三轮裁剪: 处理54个, 总计调整5个条目
[16:45:49] ℹ️ 标准扩充: 增加显示 17.172 秒
[16:45:49] [DEBUG] 🔍 标准扩充详情: 完全23个, 部分31个
[16:45:49] [DEBUG] 🔍 解决扩充冲突: 19个
[16:45:49] ℹ️ 智能扩充: 处理12个短字幕, 增加显示 2.212 秒
[16:45:49] [DEBUG] 🔍 智能扩充详情: 完全7个, 部分5个, 目标1000ms
[16:45:49] ℹ️ 桥接处理: 填补间隔 0.954 秒
[16:45:49] [DEBUG] 🔍 桥接详情: 处理6个间隔, 阈值300ms
[16:45:49] ℹ️ API2: 字幕生成完成 - trimmed_Honey Baby, Honey Baby (1974)_part5_00-19-51-ElevenLabs-API2.srt
[16:45:49] ℹ️ 字幕生成完成，共生成 1 个字幕文件
[16:45:49] ℹ️ 文本处理统计:
[16:45:49] ℹ️   总LLM分段数: 54
[16:45:49] ℹ️   第一轮精确匹配: 54/54 (100.0%)
[16:45:49] ℹ️   第二轮锚点匹配: 0/54 (0.0%)
[16:45:49] ℹ️   文本匹配成功: 54/54 (100.0%)
[16:45:49] ℹ️   文本对齐成功: 54/54 (100.0%)
[16:45:49] [TaskFlow] 分段字幕生成成功: trimmed_Honey Baby, Honey Baby (1974)_part5_00-19-51 - ElevenLabs-API2
[16:45:49] [TaskFlow] 处理分段字幕 6/19: trimmed_Honey Baby, Honey Baby (1974)_part6_00-23-39
[16:45:49] [TaskFlow] 开始生成分段字幕: trimmed_Honey Baby, Honey Baby (1974)_part6_00-23-39 - ElevenLabs-API2
[16:45:49] ℹ️ 开始字幕生成: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part6_00-23-39-ElevenLabs-parsed.json
[16:45:49] [DEBUG] 🔍 提取服务名称，输入文件: trimmed_Honey Baby, Honey Baby (1974)_part6_00-23-39-ElevenLabs-parsed.json
[16:45:49] [DEBUG] 🔍 正则匹配成功，服务名称: ElevenLabs
[16:45:49] ℹ️ 检测到服务名称: ElevenLabs
[16:45:49] ℹ️ 检测已存在的字幕文件...
[16:45:49] [DEBUG] 🔍 🔍 扫描目录: ./projects\trimmed_Honey Baby, Honey Baby (1974)
[16:45:49] [DEBUG] 🔍 🔍 查找模式: trimmed_Honey Baby, Honey Baby (1974)_part6_00-23-39-ElevenLabs-*.srt
[16:45:49] [DEBUG] 🔍 🔍 找到 0 个匹配文件
[16:45:49] ℹ️ 未发现已存在字幕文件
[16:45:49] ℹ️ 全部启用的API: ['API2']
[16:45:49] ℹ️ 已存在字幕的API: []
[16:45:49] ℹ️ 需要处理的API: ['API2']
[16:45:49] ℹ️ 正在分析音频静音段...
[16:45:49] ℹ️ 正在检测音频静音段: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part6_00-23-39.mp3
[16:45:50] ℹ️ 检测到 722 个静音段
[16:45:50] ℹ️ 静音检测完成，缓存结果用于后续处理
[16:45:50] ℹ️ 开始处理 API2...
[16:45:50] ℹ️ API2: 启用分块处理
[16:45:50] ℹ️ API2: 分割为 5 个块
[16:45:50] ℹ️ API2: 正在处理第 1/5 块 (971 字符)
[16:45:50] [DEBUG] 🔍 API2: 开始API调用处理第 1 块...
[16:45:50] [DEBUG] 🔍 API2: 开始API调用...
[16:46:13] [DEBUG] 🔍 API2: API调用成功，返回 977 字符
[16:46:13] ℹ️ API2: 第 1 块处理成功 ✅ (耗时: 23.6秒, 差异: 6 字符)
[16:46:13] ℹ️ API2: 正在处理第 2/5 块 (913 字符)
[16:46:13] ℹ️ API2: 等待请求间隔 10.0 秒...
[16:46:24] [DEBUG] 🔍 API2: 开始API调用处理第 2 块...
[16:46:24] [DEBUG] 🔍 API2: 开始API调用...
[16:46:55] [DEBUG] 🔍 API2: API调用成功，返回 919 字符
[16:46:55] ℹ️ API2: 第 2 块处理成功 ✅ (耗时: 31.1秒, 差异: 6 字符)
[16:46:55] ℹ️ API2: 正在处理第 3/5 块 (932 字符)
[16:46:55] ℹ️ API2: 等待请求间隔 10.0 秒...
[16:47:05] [DEBUG] 🔍 API2: 开始API调用处理第 3 块...
[16:47:05] [DEBUG] 🔍 API2: 开始API调用...
[16:47:50] [DEBUG] 🔍 API2: API调用成功，返回 938 字符
[16:47:50] ℹ️ API2: 第 3 块处理成功 ✅ (耗时: 45.7秒, 差异: 6 字符)
[16:47:50] ℹ️ API2: 正在处理第 4/5 块 (986 字符)
[16:47:50] ℹ️ API2: 等待请求间隔 10.0 秒...
[16:48:00] [DEBUG] 🔍 API2: 开始API调用处理第 4 块...
[16:48:00] [DEBUG] 🔍 API2: 开始API调用...
[16:48:34] [DEBUG] 🔍 API2: API调用成功，返回 992 字符
[16:48:34] ℹ️ API2: 第 4 块处理成功 ✅ (耗时: 33.1秒, 差异: 6 字符)
[16:48:34] ℹ️ API2: 正在处理第 5/5 块 (75 字符)
[16:48:34] ℹ️ API2: 等待请求间隔 10.0 秒...
[16:48:44] [DEBUG] 🔍 API2: 开始API调用处理第 5 块...
[16:48:44] [DEBUG] 🔍 API2: 开始API调用...
[16:49:08] [DEBUG] 🔍 API2: API调用成功，返回 75 字符
[16:49:08] ℹ️ API2: 第 5 块处理成功 ✅ (耗时: 24.4秒, 差异: 0 字符)
[16:49:08] ℹ️ API2: 初始处理完成，成功 5/5 块
[16:49:08] ℹ️ API2: LLM调用成功，开始生成字幕...
[16:49:08] ℹ️ 对齐 API2 的文本分段...
[16:49:08] ℹ️ LLM分段调试数据已保存到: ./projects\trimmed_Honey Baby, Honey Baby (1974)\llm_segments_debug_20250803_164908.json
[16:49:08] ℹ️ 开始文本对齐处理（顺序约束），共 124 个片段
[16:49:08] [DEBUG] 🔍 片段处理进度:
[16:49:08] [DEBUG] 🔍   #1 | 精确匹配✅ → 对齐成功✅ | "How you doing?"
[16:49:08] [DEBUG] 🔍   #2 | 精确匹配✅ → 对齐成功✅ | "You American?"
[16:49:08] [DEBUG] 🔍   #3 | 精确匹配✅ → 对齐成功✅ | "Only under the skin."
[16:49:08] [DEBUG] 🔍   #4 | 精确匹配✅ → 对齐成功✅ | "You've been following me."
[16:49:08] [DEBUG] 🔍   #5 | 精确匹配✅ → 对齐成功✅ | "You're right there, sister."
[16:49:08] [DEBUG] 🔍   #6 | 精确匹配✅ → 对齐成功✅ | "Why?"
[16:49:08] [DEBUG] 🔍   #7 | 精确匹配✅ → 对齐成功✅ | "I heard you were here with you..."
[16:49:08] [DEBUG] 🔍   #8 | 精确匹配✅ → 对齐成功✅ | "so I thought I'd like to do//a..."
[16:49:08] [DEBUG] 🔍   #9 | 精确匹配✅ → 对齐成功✅ | "I cover this part of the world..."
[16:49:08] [DEBUG] 🔍   #10 | 精确匹配✅ → 对齐成功✅ | "No."
[16:49:08] [DEBUG] 🔍   #11 | 精确匹配✅ → 对齐成功✅ | "I do not cover this part of th..."
[16:49:08] [DEBUG] 🔍   #12 | 精确匹配✅ → 对齐成功✅ | "And as a matter of fact,"
[16:49:08] [DEBUG] 🔍   #13 | 精确匹配✅ → 对齐成功✅ | "I wouldn't even like to do//a ..."
[16:49:08] [DEBUG] 🔍   #14 | 精确匹配✅ → 对齐成功✅ | "Well then what do you want?"
[16:49:08] [DEBUG] 🔍   #15 | 精确匹配✅ → 对齐成功✅ | "Why are you annoying me?"
[16:49:08] [DEBUG] 🔍   #16 | 精确匹配✅ → 对齐成功✅ | "Because you happen to be the m..."
[16:49:08] [DEBUG] 🔍   #17 | 精确匹配✅ → 对齐成功✅ | "I've seen in years,"
[16:49:08] [DEBUG] 🔍   #18 | 精确匹配✅ → 对齐成功✅ | "and you remind me of my mother..."
[16:49:08] [DEBUG] 🔍   #19 | 精确匹配✅ → 对齐成功✅ | "my sister, my first girlfriend..."
[16:49:08] [DEBUG] 🔍   #20 | 精确匹配✅ → 对齐成功✅ | "my last girlfriend."
[16:49:08] [DEBUG] 🔍   #21 | 精确匹配✅ → 对齐成功✅ | "In short, you remind me-"
[16:49:08] [DEBUG] 🔍   #22 | 精确匹配✅ → 对齐成功✅ | "Of a pickup."
[16:49:08] [DEBUG] 🔍   #23 | 精确匹配✅ → 对齐成功✅ | "Oh, no."
[16:49:08] [DEBUG] 🔍   #24 | 精确匹配✅ → 对齐成功✅ | "Dinner and dance, no ballroom ..."
[16:49:08] [DEBUG] 🔍   #25 | 精确匹配✅ → 对齐成功✅ | "As a matter of fact, you don't..."
[16:49:08] [DEBUG] 🔍   #26 | 精确匹配✅ → 对齐成功✅ | "of my first girlfriend//or my ..."
[16:49:08] [DEBUG] 🔍   #27 | 精确匹配✅ → 对齐成功✅ | "It was ugly."
[16:49:08] [DEBUG] 🔍   #28 | 精确匹配✅ → 对齐成功✅ | "No, your mother blessed you."
[16:49:08] [DEBUG] 🔍   #29 | 精确匹配✅ → 对齐成功✅ | "Is that your honest opinion, M..."
[16:49:08] [DEBUG] 🔍   #30 | 精确匹配✅ → 对齐成功✅ | "How did you know my name?"
[16:49:08] [DEBUG] 🔍   #31 | 精确匹配✅ → 对齐成功✅ | "I checked at the hotel."
[16:49:08] [DEBUG] 🔍   #32 | 精确匹配✅ → 对齐成功✅ | "You are that beautiful."
[16:49:08] [DEBUG] 🔍   #33 | 精确匹配✅ → 对齐成功✅ | "I'm Tutankhamun, King Tut."
[16:49:08] [DEBUG] 🔍   #34 | 精确匹配✅ → 对齐成功✅ | "Now tell me, is your interest ..."
[16:49:08] [DEBUG] 🔍   #35 | 精确匹配✅ → 对齐成功✅ | "My interest in the history and..."
[16:49:08] [DEBUG] 🔍   #36 | 精确匹配✅ → 对齐成功✅ | "And you and I can go no place,..."
[16:49:08] [DEBUG] 🔍   #37 | 精确匹配✅ → 对齐成功✅ | "Also, I doubt if I'm rich enou..."
[16:49:08] [DEBUG] 🔍   #38 | 精确匹配✅ → 对齐成功✅ | "Why don't you take a picture h..."
[16:49:08] [DEBUG] 🔍   #39 | 精确匹配✅ → 对齐成功✅ | "He'll pardon me."
[16:49:08] [DEBUG] 🔍   #40 | 精确匹配✅ → 对齐成功✅ | "Where were you?"
[16:49:09] [DEBUG] 🔍   #41 | 精确匹配✅ → 对齐失败❌ | "Where were you?"
[16:49:09] [DEBUG] 🔍   #42 | 精确匹配✅ → 对齐成功✅ | "In the museum."
[16:49:09] [DEBUG] 🔍   #43 | 精确匹配✅ → 对齐成功✅ | "What museum?"
[16:49:09] [DEBUG] 🔍   #44 | 精确匹配✅ → 对齐失败❌ | "The Beirut Museum."
[16:49:09] [DEBUG] 🔍   #45 | 精确匹配✅ → 对齐成功✅ | "Ha. Herb."
[16:49:09] [DEBUG] 🔍   #46 | 精确匹配✅ → 对齐成功✅ | "I mean, you have got to unders..."
[16:49:09] [DEBUG] 🔍   #47 | 精确匹配✅ → 对齐成功✅ | "that we send evidence of your ..."
[16:49:09] [DEBUG] 🔍   #48 | 精确匹配✅ → 对齐成功✅ | "Oh, I understand."
[16:49:09] [DEBUG] 🔍   #49 | 精确匹配✅ → 对齐失败❌ | "What you mean is it at the mom..."
[16:49:09] [DEBUG] 🔍   #50 | 精确匹配✅ → 对齐成功✅ | "but processed and projected,//..."
[16:49:09] [DEBUG] 🔍   #51 | 精确匹配✅ → 对齐失败❌ | "Look, Laura, be reasonable."
[16:49:09] [DEBUG] 🔍   #52 | 精确匹配✅ → 对齐成功✅ | "We know that you went to the m..."
[16:49:09] [DEBUG] 🔍   #53 | 精确匹配✅ → 对齐失败❌ | "Right."
[16:49:09] [DEBUG] 🔍   #54 | 精确匹配✅ → 对齐失败❌ | "To look at whatever you looked..."
[16:49:09] [DEBUG] 🔍   #55 | 精确匹配✅ → 对齐失败❌ | "But the people back home watch..."
[16:49:09] [DEBUG] 🔍   #56 | 精确匹配✅ → 对齐失败❌ | "Now look, Sam,//I won a trip, ..."
[16:49:09] [DEBUG] 🔍   #57 | 精确匹配✅ → 对齐成功✅ | "to see the world by myself,//w..."
[16:49:09] [DEBUG] 🔍   #58 | 精确匹配✅ → 对齐成功✅ | "To enjoy things, people and pl..."
[16:49:09] [DEBUG] 🔍   #59 | 精确匹配✅ → 对齐成功✅ | "not to sell goddamn candy bars..."
[16:49:09] [DEBUG] 🔍   #60 | 精确匹配✅ → 对齐成功✅ | "Listen, Miss Laura Lewis."
[16:49:09] [DEBUG] 🔍   #61 | 精确匹配✅ → 对齐成功✅ | "Yeah?"
[16:49:09] [DEBUG] 🔍   #62 | 精确匹配✅ → 对齐成功✅ | "You agreed to be under our aus..."
[16:49:09] [DEBUG] 🔍   #63 | 精确匹配✅ → 对齐失败❌ | "No, no, no, Sam."
[16:49:09] [DEBUG] 🔍   #64 | 精确匹配✅ → 对齐失败❌ | "... and to cooperate."
[16:49:09] [DEBUG] 🔍   #65 | 精确匹配✅ → 对齐失败❌ | "No, I never agreed to no such ..."
[16:49:09] [DEBUG] 🔍   #66 | 精确匹配✅ → 对齐成功✅ | "Yes, you did."
[16:49:09] [DEBUG] 🔍   #67 | 精确匹配✅ → 对齐成功✅ | "Look will ya, get out of my fa..."
[16:49:09] [DEBUG] 🔍   #68 | 精确匹配✅ → 对齐成功✅ | "Herb. Herb."
[16:49:09] [DEBUG] 🔍   #69 | 精确匹配✅ → 对齐失败❌ | "Yes, you did."
[16:49:09] [DEBUG] 🔍   #70 | 精确匹配✅ → 对齐成功✅ | "When? When?"
[16:49:09] [DEBUG] 🔍   #71 | 精确匹配✅ → 对齐成功✅ | "On the original entry form."
[16:49:09] [DEBUG] 🔍   #72 | 精确匹配✅ → 对齐成功✅ | "I never sent that in."
[16:49:09] [DEBUG] 🔍   #73 | 精确匹配✅ → 对齐成功✅ | "Oh, no? Who did?"
[16:49:09] [DEBUG] 🔍   #74 | 精确匹配✅ → 对齐失败❌ | "Number over here."
[16:49:09] [DEBUG] 🔍   #75 | 精确匹配✅ → 对齐失败❌ | "It was his idea."
[16:49:09] [DEBUG] 🔍   #76 | 精确匹配✅ → 对齐失败❌ | "Nonetheless, you're still boun..."
[16:49:09] [DEBUG] 🔍   #77 | 精确匹配✅ → 对齐失败❌ | "Listen, Laura, Laura I'm,"
[16:49:09] [DEBUG] 🔍   #78 | 精确匹配✅ → 对齐失败❌ | "I'm gonna give you//all the fr..."
[16:49:09] [DEBUG] 🔍   #79 | 精确匹配✅ → 对齐失败❌ | "Damn."
[16:49:09] [DEBUG] 🔍   #80 | 精确匹配✅ → 对齐失败❌ | "You're such a big person."
[16:49:09] [DEBUG] 🔍   #81 | 精确匹配✅ → 对齐失败❌ | "Laura?"
[16:49:09] [DEBUG] 🔍   #82 | 精确匹配✅ → 对齐失败❌ | "Okay?"
[16:49:09] [DEBUG] 🔍   #83 | 精确匹配✅ → 对齐成功✅ | "What can I say?"
[16:49:09] [DEBUG] 🔍   #84 | 精确匹配✅ → 对齐成功✅ | "What'd they say?"
[16:49:09] [DEBUG] 🔍   #85 | 精确匹配✅ → 对齐成功✅ | "What do you mean what did they..."
[16:49:09] [DEBUG] 🔍   #86 | 精确匹配✅ → 对齐失败❌ | "You just heard what they just ..."
[16:49:09] [DEBUG] 🔍   #87 | 精确匹配✅ → 对齐成功✅ | "They said, they said,"
[16:49:09] [DEBUG] 🔍   #88 | 精确匹配✅ → 对齐成功✅ | ""We're here to settle.//We're ..."
[16:49:09] [DEBUG] 🔍   #89 | 精确匹配✅ → 对齐成功✅ | "Talking about the cops."
[16:49:09] [DEBUG] 🔍   #90 | 精确匹配✅ → 对齐失败❌ | "Oh, the cops. Oh."
[16:49:09] [DEBUG] 🔍   #91 | 精确匹配✅ → 对齐失败❌ | "Well they said, they said//the..."
[16:49:09] [DEBUG] 🔍   #92 | 精确匹配✅ → 对齐成功✅ | "they never heard of any Chines..."
[16:49:09] [DEBUG] 🔍   #93 | 精确匹配✅ → 对齐失败❌ | "and they thought I was just//a..."
[16:49:09] [DEBUG] 🔍   #94 | 精确匹配✅ → 对齐失败❌ | "That's crazy."
[16:49:09] [DEBUG] 🔍   #95 | 精确匹配✅ → 对齐失败❌ | "We saw them take off the boat."
[16:49:09] [DEBUG] 🔍   #96 | 精确匹配✅ → 对齐失败❌ | "Yeah, well the man checked tho..."
[16:49:09] [DEBUG] 🔍   #97 | 精确匹配✅ → 对齐失败❌ | "They pulling a little game."
[16:49:09] [DEBUG] 🔍   #98 | 精确匹配✅ → 对齐失败❌ | "They may very well be doing//j..."
[16:49:09] [DEBUG] 🔍   #99 | 精确匹配✅ → 对齐成功✅ | "Now you go into your room//and..."
[16:49:09] [DEBUG] 🔍   #100 | 精确匹配✅ → 对齐失败❌ | "and I'll meet you downstairs//..."
[16:49:09] [DEBUG] 🔍   #101 | 精确匹配✅ → 对齐失败❌ | "If she doesn't exist,//then th..."
[16:49:09] [DEBUG] 🔍   #102 | 精确匹配✅ → 对齐失败❌ | "So that puts us in the clear, ..."
[16:49:09] [DEBUG] 🔍   #103 | 精确匹配✅ → 对齐成功✅ | "Yeah, something like that."
[16:49:09] [DEBUG] 🔍   #104 | 精确匹配✅ → 对齐成功✅ | "If you wanna look at it that w..."
[16:49:09] [DEBUG] 🔍   #105 | 精确匹配✅ → 对齐成功✅ | "Hey, Herb, let's check out the..."
[16:49:09] [DEBUG] 🔍   #106 | 精确匹配✅ → 对齐成功✅ | "Yeah."
[16:49:09] [DEBUG] 🔍   #107 | 精确匹配✅ → 对齐成功✅ | "Hey, Herb, take our picture."
[16:49:09] [DEBUG] 🔍   #108 | 精确匹配✅ → 对齐成功✅ | "They are beautiful, aren't the..."
[16:49:09] [DEBUG] 🔍   #109 | 精确匹配✅ → 对齐成功✅ | "Yes, yes, they really are."
[16:49:09] [DEBUG] 🔍   #110 | 精确匹配✅ → 对齐成功✅ | "I believe they give concerts h..."
[16:49:09] [DEBUG] 🔍   #111 | 精确匹配✅ → 对齐成功✅ | "Really?"
[16:49:09] [DEBUG] 🔍   #112 | 精确匹配✅ → 对齐成功✅ | "Must be quite a show."
[16:49:09] [DEBUG] 🔍   #113 | 精确匹配✅ → 对齐成功✅ | "Okay."
[16:49:09] [DEBUG] 🔍   #114 | 精确匹配✅ → 对齐成功✅ | "Hey, Laura, I gotta get outta ..."
[16:49:09] [DEBUG] 🔍   #115 | 精确匹配✅ → 对齐成功✅ | "We'll see you outside. Okay?"
[16:49:09] [DEBUG] 🔍   #116 | 精确匹配✅ → 对齐成功✅ | "Oh, okay."
[16:49:09] [DEBUG] 🔍   #117 | 精确匹配✅ → 对齐成功✅ | "Last question, men."
[16:49:09] [DEBUG] 🔍   #118 | 精确匹配✅ → 对齐成功✅ | "Miss Lewis?"
[16:49:09] [DEBUG] 🔍   #119 | 精确匹配✅ → 对齐成功✅ | "Yes?"
[16:49:09] [DEBUG] 🔍   #120 | 精确匹配✅ → 对齐成功✅ | "Can, can I call you on your ho..."
[16:49:09] [DEBUG] 🔍   #121 | 精确匹配✅ → 对齐成功✅ | "I'd like to talk to you alone."
[16:49:09] [DEBUG] 🔍   #122 | 精确匹配✅ → 对齐成功✅ | "Why?"
[16:49:09] [DEBUG] 🔍   #123 | 精确匹配✅ → 对齐成功✅ | "Madam Chiang, the Chinese woma..."
[16:49:09] [DEBUG] 🔍   #124 | 精确匹配✅ → 对齐成功✅ | "Do you know where she is?"
[16:49:09] ℹ️ API2: 成功对齐 91 个文本分段
[16:49:09] ℹ️ 处理 API2 的字幕条目...
[16:49:09] [DEBUG] 🔍 开始处理 91 个字幕条目的时间间隔分割...
[16:49:09] ℹ️ 时间间隔分割完成：91 个条目 → 95 个条目
[16:49:09] [DEBUG] 🔍 过滤短时长条目: 'He'll pardon me.' (00:02:29,579 --> 00:02:29,579, 持续时间: 0.000s)
[16:49:09] [DEBUG] 🔍 过滤短时长条目: 'Where were you?' (00:02:30,619 --> 00:02:30,639, 持续时间: 0.020s)
[16:49:09] [DEBUG] 🔍 过滤短时长条目: 'What museum?' (00:02:36,179 --> 00:02:36,179, 持续时间: 0.000s)
[16:49:09] [DEBUG] 🔍 过滤短时长条目: 'Ha. Herb.' (00:02:36,179 --> 00:02:36,199, 持续时间: 0.020s)
[16:49:09] [DEBUG] 🔍 过滤短时长条目: 'that we send evidence of your trip back home//for the people to see on the show.' (00:02:36,379 --> 00:02:36,379, 持续时间: 0.000s)
[16:49:09] [DEBUG] 🔍 过滤短时长条目: 'Oh, I understand.' (00:02:36,379 --> 00:02:36,379, 持续时间: 0.000s)
[16:49:09] [DEBUG] 🔍 过滤短时长条目: 'but processed and projected,//then it has not happened, right?' (00:02:36,379 --> 00:02:36,379, 持续时间: 0.000s)
[16:49:10] [DEBUG] 🔍 过滤短时长条目: 'We know that you went to the museum, right?' (00:02:36,379 --> 00:02:36,379, 持续时间: 0.000s)
[16:49:10] [DEBUG] 🔍 过滤短时长条目: 'not to sell goddamn candy bars//or whatever it is that buys you time.' (00:02:38,839 --> 00:02:38,839, 持续时间: 0.000s)
[16:49:10] [DEBUG] 🔍 过滤短时长条目: 'Listen, Miss Laura Lewis.' (00:02:38,839 --> 00:02:38,839, 持续时间: 0.000s)
[16:49:10] [DEBUG] 🔍 过滤短时长条目: 'Yeah?' (00:02:38,839 --> 00:02:38,839, 持续时间: 0.000s)
[16:49:10] [DEBUG] 🔍 过滤短时长条目: 'You agreed to be under our auspices//and guidance-' (00:02:38,839 --> 00:02:38,839, 持续时间: 0.000s)
[16:49:10] [DEBUG] 🔍 过滤短时长条目: 'Herb. Herb.' (00:02:40,419 --> 00:02:40,419, 持续时间: 0.000s)
[16:49:10] [DEBUG] 🔍 过滤短时长条目: 'When? When?' (00:02:40,419 --> 00:02:40,419, 持续时间: 0.000s)
[16:49:10] [DEBUG] 🔍 过滤短时长条目: 'I never sent that in.' (00:02:41,299 --> 00:02:41,299, 持续时间: 0.000s)
[16:49:10] [DEBUG] 🔍 过滤短时长条目: 'Oh, no? Who did?' (00:02:41,299 --> 00:02:41,299, 持续时间: 0.000s)
[16:49:10] [DEBUG] 🔍 过滤短时长条目: 'What can I say?' (00:02:41,299 --> 00:02:41,299, 持续时间: 0.000s)
[16:49:10] [DEBUG] 🔍 过滤短时长条目: 'What'd they say?' (00:02:41,299 --> 00:02:41,299, 持续时间: 0.000s)
[16:49:10] [DEBUG] 🔍 过滤短时长条目: 'What do you mean what did they say?' (00:02:41,299 --> 00:02:41,299, 持续时间: 0.000s)
[16:49:10] [DEBUG] 🔍 过滤短时长条目: 'They said, they said,' (00:02:41,299 --> 00:02:41,299, 持续时间: 0.000s)
[16:49:10] [DEBUG] 🔍 过滤短时长条目: '"We're here to settle.//We're here to promote."' (00:02:41,299 --> 00:02:41,299, 持续时间: 0.000s)
[16:49:10] [DEBUG] 🔍 过滤短时长条目: 'Talking about the cops.' (00:02:41,299 --> 00:02:41,299, 持续时间: 0.000s)
[16:49:10] [DEBUG] 🔍 过滤短时长条目: 'they never heard of any Chinese women,' (00:02:41,299 --> 00:02:41,299, 持续时间: 0.000s)
[16:49:10] [DEBUG] 🔍 过滤短时长条目: 'Now you go into your room//and put your clothes on' (00:02:41,299 --> 00:02:41,299, 持续时间: 0.000s)
[16:49:10] [DEBUG] 🔍 过滤短时长条目: 'men.' (00:06:48,819 --> 00:06:48,859, 持续时间: 0.040s)
[16:49:10] ℹ️ 短时长过滤完成：95 个条目 → 70 个条目（过滤掉 25 个短时长条目）
[16:49:10] ℹ️ 正在裁剪字幕静音区域...
[16:49:10] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:49:10] [DEBUG] 🔍 第一轮裁剪: 处理70个, 去除静音17.620秒
[16:49:10] [DEBUG] 🔍 开始第二轮静音裁剪...
[16:49:10] [DEBUG] 🔍 第二轮裁剪: 处理70个条目
[16:49:10] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:49:10] [DEBUG] 🔍 第二轮-开始时间: 在100ms偏移找到44个有效调整，剩余26个条目
[16:49:10] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:49:10] [DEBUG] 🔍 第二轮-开始时间: 200ms偏移无有效调整，剩余26个条目
[16:49:10] [DEBUG] 🔍 第二轮-开始时间: 总计处理44个条目，26个条目保持原样
[16:49:10] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:49:10] [DEBUG] 🔍 第二轮-结束时间: 在100ms偏移找到1个有效调整，剩余69个条目
[16:49:10] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:49:10] [DEBUG] 🔍 第二轮-结束时间: 在200ms偏移找到1个有效调整，剩余68个条目
[16:49:10] [DEBUG] 🔍 第二轮-结束时间: 总计处理2个条目，68个条目保持原样
[16:49:10] [DEBUG] 🔍 第二轮裁剪: 处理70个, 总计调整5个条目
[16:49:10] [DEBUG] 🔍 开始第三轮静音裁剪...
[16:49:10] [DEBUG] 🔍 第三轮裁剪: 处理70个条目
[16:49:10] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:49:10] [DEBUG] 🔍 第三轮-开始时间: 在100ms偏移找到1个有效调整，剩余69个条目
[16:49:10] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:49:10] [DEBUG] 🔍 第三轮-开始时间: 在200ms偏移找到3个有效调整，剩余66个条目
[16:49:10] [DEBUG] 🔍 第三轮-开始时间: 总计处理4个条目，66个条目保持原样
[16:49:10] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:49:10] [DEBUG] 🔍 第三轮-结束时间: 在100ms偏移找到47个有效调整，剩余23个条目
[16:49:10] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:49:10] [DEBUG] 🔍 第三轮-结束时间: 在200ms偏移找到1个有效调整，剩余22个条目
[16:49:10] [DEBUG] 🔍 第三轮-结束时间: 总计处理48个条目，22个条目保持原样
[16:49:10] [DEBUG] 🔍 第三轮裁剪: 处理70个, 总计调整6个条目
[16:49:10] ℹ️ 标准扩充: 增加显示 20.777 秒
[16:49:10] [DEBUG] 🔍 标准扩充详情: 完全34个, 部分35个
[16:49:10] [DEBUG] 🔍 解决扩充冲突: 22个
[16:49:10] ℹ️ 智能扩充: 处理19个短字幕, 增加显示 4.385 秒
[16:49:10] [DEBUG] 🔍 智能扩充详情: 完全14个, 部分5个, 目标1000ms
[16:49:10] ℹ️ 桥接处理: 填补间隔 2.319 秒
[16:49:10] [DEBUG] 🔍 桥接详情: 处理13个间隔, 阈值300ms
[16:49:10] ℹ️ API2: 字幕生成完成 - trimmed_Honey Baby, Honey Baby (1974)_part6_00-23-39-ElevenLabs-API2.srt
[16:49:10] ℹ️ 字幕生成完成，共生成 1 个字幕文件
[16:49:10] ℹ️ 文本处理统计:
[16:49:10] ℹ️   总LLM分段数: 124
[16:49:10] ℹ️   第一轮精确匹配: 124/124 (100.0%)
[16:49:10] ℹ️   第二轮锚点匹配: 0/124 (0.0%)
[16:49:10] ℹ️   文本匹配成功: 124/124 (100.0%)
[16:49:10] ℹ️   文本对齐成功: 91/124 (73.4%)
[16:49:10] [TaskFlow] 分段字幕生成成功: trimmed_Honey Baby, Honey Baby (1974)_part6_00-23-39 - ElevenLabs-API2
[16:49:10] [TaskFlow] 处理分段字幕 7/19: trimmed_Honey Baby, Honey Baby (1974)_part7_00-30-41
[16:49:10] [TaskFlow] 开始生成分段字幕: trimmed_Honey Baby, Honey Baby (1974)_part7_00-30-41 - ElevenLabs-API2
[16:49:10] ℹ️ 开始字幕生成: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part7_00-30-41-ElevenLabs-parsed.json
[16:49:10] [DEBUG] 🔍 提取服务名称，输入文件: trimmed_Honey Baby, Honey Baby (1974)_part7_00-30-41-ElevenLabs-parsed.json
[16:49:10] [DEBUG] 🔍 正则匹配成功，服务名称: ElevenLabs
[16:49:10] ℹ️ 检测到服务名称: ElevenLabs
[16:49:10] ℹ️ 检测已存在的字幕文件...
[16:49:10] [DEBUG] 🔍 🔍 扫描目录: ./projects\trimmed_Honey Baby, Honey Baby (1974)
[16:49:10] [DEBUG] 🔍 🔍 查找模式: trimmed_Honey Baby, Honey Baby (1974)_part7_00-30-41-ElevenLabs-*.srt
[16:49:10] [DEBUG] 🔍 🔍 找到 0 个匹配文件
[16:49:10] ℹ️ 未发现已存在字幕文件
[16:49:10] ℹ️ 全部启用的API: ['API2']
[16:49:10] ℹ️ 已存在字幕的API: []
[16:49:10] ℹ️ 需要处理的API: ['API2']
[16:49:10] ℹ️ 正在分析音频静音段...
[16:49:10] ℹ️ 正在检测音频静音段: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part7_00-30-41.mp3
[16:49:11] ℹ️ 检测到 282 个静音段
[16:49:11] ℹ️ 静音检测完成，缓存结果用于后续处理
[16:49:11] ℹ️ 开始处理 API2...
[16:49:11] ℹ️ API2: 启用分块处理
[16:49:11] ℹ️ API2: 分割为 2 个块
[16:49:11] ℹ️ API2: 正在处理第 1/2 块 (971 字符)
[16:49:11] [DEBUG] 🔍 API2: 开始API调用处理第 1 块...
[16:49:11] [DEBUG] 🔍 API2: 开始API调用...
[16:49:36] [DEBUG] 🔍 API2: API调用成功，返回 976 字符
[16:49:36] ℹ️ API2: 第 1 块处理成功 ✅ (耗时: 25.2秒, 差异: 5 字符)
[16:49:36] ℹ️ API2: 正在处理第 2/2 块 (420 字符)
[16:49:36] ℹ️ API2: 等待请求间隔 10.0 秒...
[16:49:46] [DEBUG] 🔍 API2: 开始API调用处理第 2 块...
[16:49:46] [DEBUG] 🔍 API2: 开始API调用...
[16:50:06] [DEBUG] 🔍 API2: API调用成功，返回 422 字符
[16:50:06] ℹ️ API2: 第 2 块处理成功 ✅ (耗时: 19.5秒, 差异: 2 字符)
[16:50:06] ℹ️ API2: 初始处理完成，成功 2/2 块
[16:50:06] ℹ️ API2: LLM调用成功，开始生成字幕...
[16:50:06] ℹ️ 对齐 API2 的文本分段...
[16:50:06] ℹ️ LLM分段调试数据已保存到: ./projects\trimmed_Honey Baby, Honey Baby (1974)\llm_segments_debug_20250803_165006.json
[16:50:06] ℹ️ 开始文本对齐处理（顺序约束），共 63 个片段
[16:50:06] [DEBUG] 🔍 片段处理进度:
[16:50:06] [DEBUG] 🔍   #1 | 精确匹配✅ → 对齐成功✅ | "Yeah."
[16:50:06] [DEBUG] 🔍   #2 | 精确匹配✅ → 对齐成功✅ | "Look straight up."
[16:50:06] [DEBUG] 🔍   #3 | 精确匹配✅ → 对齐成功✅ | "Look straight up."
[16:50:06] [DEBUG] 🔍   #4 | 精确匹配✅ → 对齐成功✅ | "Uh-huh."
[16:50:06] [DEBUG] 🔍   #5 | 精确匹配✅ → 对齐成功✅ | "All right?"
[16:50:06] [DEBUG] 🔍   #6 | 精确匹配✅ → 对齐成功✅ | "Yeah."
[16:50:06] [DEBUG] 🔍   #7 | 精确匹配✅ → 对齐成功✅ | "Look straight up in the air."
[16:50:06] [DEBUG] 🔍   #8 | 精确匹配✅ → 对齐成功✅ | "Now turn around real slow."
[16:50:06] [DEBUG] 🔍   #9 | 精确匹配✅ → 对齐成功✅ | "Turn around real slow."
[16:50:06] [DEBUG] 🔍   #10 | 精确匹配✅ → 对齐成功✅ | "Keep turning."
[16:50:06] [DEBUG] 🔍   #11 | 精确匹配✅ → 对齐成功✅ | "Aah."
[16:50:06] [DEBUG] 🔍   #12 | 精确匹配✅ → 对齐成功✅ | "Wait."
[16:50:06] [DEBUG] 🔍   #13 | 精确匹配✅ → 对齐成功✅ | "Up there."
[16:50:06] [DEBUG] 🔍   #14 | 精确匹配✅ → 对齐成功✅ | "Up there."
[16:50:06] [DEBUG] 🔍   #15 | 精确匹配✅ → 对齐成功✅ | "You see it?"
[16:50:06] [DEBUG] 🔍   #16 | 精确匹配✅ → 对齐成功✅ | "You see-"
[16:50:06] [DEBUG] 🔍   #17 | 精确匹配✅ → 对齐成功✅ | "I do see."
[16:50:06] [DEBUG] 🔍   #18 | 精确匹配✅ → 对齐成功✅ | "You see it?"
[16:50:06] [DEBUG] 🔍   #19 | 精确匹配✅ → 对齐成功✅ | "Oh, the..."
[16:50:06] [DEBUG] 🔍   #20 | 精确匹配✅ → 对齐成功✅ | "Fabulous."
[16:50:06] [DEBUG] 🔍   #21 | 精确匹配✅ → 对齐成功✅ | "It's like an illusion."
[16:50:06] [DEBUG] 🔍   #22 | 精确匹配✅ → 对齐成功✅ | "Oh!"
[16:50:06] [DEBUG] 🔍   #23 | 精确匹配✅ → 对齐成功✅ | "Oh my gosh."
[16:50:06] [DEBUG] 🔍   #24 | 精确匹配✅ → 对齐成功✅ | "You know what I was thinking, ..."
[16:50:06] [DEBUG] 🔍   #25 | 精确匹配✅ → 对齐成功✅ | "That, uh..."
[16:50:06] [DEBUG] 🔍   #26 | 精确匹配✅ → 对齐成功✅ | "The one thing, uh, that..."
[16:50:06] [DEBUG] 🔍   #27 | 精确匹配✅ → 对齐成功✅ | "You know, with Herb//in his pi..."
[16:50:06] [DEBUG] 🔍   #28 | 精确匹配✅ → 对齐成功✅ | "The angle we're missing//is wo..."
[16:50:06] [DEBUG] 🔍   #29 | 精确匹配✅ → 对齐成功✅ | "Now, wait just a minute."
[16:50:06] [DEBUG] 🔍   #30 | 精确匹配✅ → 对齐成功✅ | "If we get you on a horse//in a..."
[16:50:06] [DEBUG] 🔍   #31 | 精确匹配✅ → 对齐成功✅ | "Now that says it."
[16:50:06] [DEBUG] 🔍   #32 | 精确匹配✅ → 对齐成功✅ | "That says it."
[16:50:06] [DEBUG] 🔍   #33 | 精确匹配✅ → 对齐成功✅ | "Sam, I am me."
[16:50:06] [DEBUG] 🔍   #34 | 精确匹配✅ → 对齐成功✅ | "I'm not a fashion..."
[16:50:06] [DEBUG] 🔍   #35 | 精确匹配✅ → 对齐成功✅ | "I'm not a symbol."
[16:50:06] [DEBUG] 🔍   #36 | 精确匹配✅ → 对齐成功✅ | "I'm just playing me."
[16:50:06] [DEBUG] 🔍   #37 | 精确匹配✅ → 对齐成功✅ | "Oh, wait, wait, wait, wait, wa..."
[16:50:06] [DEBUG] 🔍   #38 | 精确匹配✅ → 对齐成功✅ | "I didn't mean that."
[16:50:06] [DEBUG] 🔍   #39 | 精确匹配✅ → 对齐成功✅ | "I didn't-"
[16:50:06] [DEBUG] 🔍   #40 | 精确匹配✅ → 对齐成功✅ | "Yes, yes, yes, you did."
[16:50:06] [DEBUG] 🔍   #41 | 精确匹配✅ → 对齐成功✅ | "Yes."
[16:50:06] [DEBUG] 🔍   #42 | 精确匹配✅ → 对齐成功✅ | "You're looking for an image, S..."
[16:50:06] [DEBUG] 🔍   #43 | 精确匹配✅ → 对齐成功✅ | "And look, this is not the time..."
[16:50:06] [DEBUG] 🔍   #44 | 精确匹配✅ → 对齐成功✅ | "Not even for a quiz program."
[16:50:06] [DEBUG] 🔍   #45 | 精确匹配✅ → 对齐成功✅ | "Hey, Sam."
[16:50:06] [DEBUG] 🔍   #46 | 精确匹配✅ → 对齐成功✅ | "What?"
[16:50:06] [DEBUG] 🔍   #47 | 精确匹配✅ → 对齐成功✅ | "Don't you think that people ba..."
[16:50:06] [DEBUG] 🔍   #48 | 精确匹配✅ → 对齐成功✅ | "Very big day."
[16:50:06] [DEBUG] 🔍   #49 | 精确匹配✅ → 对齐成功✅ | "Why, tonight,//tomorrow's a wh..."
[16:50:06] [DEBUG] 🔍   #50 | 精确匹配✅ → 对齐成功✅ | "That's more like it, Sam."
[16:50:06] [DEBUG] 🔍   #51 | 精确匹配✅ → 对齐成功✅ | "That's a nice prize."
[16:50:06] [DEBUG] 🔍   #52 | 精确匹配✅ → 对齐成功✅ | "Thank you, Sam."
[16:50:06] [DEBUG] 🔍   #53 | 精确匹配✅ → 对齐成功✅ | "Uh, wait. Wait a second."
[16:50:06] [DEBUG] 🔍   #54 | 精确匹配✅ → 对齐成功✅ | "I really, uh..."
[16:50:06] [DEBUG] 🔍   #55 | 精确匹配✅ → 对齐成功✅ | "As much as I'd like//to take t..."
[16:50:06] [DEBUG] 🔍   #56 | 精确匹配✅ → 对齐成功✅ | "I gotta confess that, uh,//it ..."
[16:50:06] [DEBUG] 🔍   #57 | 精确匹配✅ → 对齐成功✅ | "Herb, that was really beautifu..."
[16:50:06] [DEBUG] 🔍   #58 | 精确匹配✅ → 对齐成功✅ | "Thanks, Herb."
[16:50:06] [DEBUG] 🔍   #59 | 精确匹配✅ → 对齐成功✅ | "Oh, no, not me."
[16:50:06] [DEBUG] 🔍   #60 | 精确匹配✅ → 对齐成功✅ | "Who do you have to thank?"
[16:50:06] [DEBUG] 🔍   #61 | 精确匹配✅ → 对齐成功✅ | "A king of ancient Egypt."
[16:50:06] [DEBUG] 🔍   #62 | 精确匹配✅ → 对齐成功✅ | "Oh, ho, ho, ho."
[16:50:06] [DEBUG] 🔍   #63 | 精确匹配✅ → 对齐成功✅ | "You got a secret admirer in Le..."
[16:50:06] ℹ️ API2: 成功对齐 63 个文本分段
[16:50:06] ℹ️ 处理 API2 的字幕条目...
[16:50:06] [DEBUG] 🔍 开始处理 63 个字幕条目的时间间隔分割...
[16:50:06] ℹ️ 时间间隔分割完成：63 个条目 → 63 个条目
[16:50:06] [DEBUG] 🔍 过滤短时长条目: 'Yeah.' (00:00:57,219 --> 00:00:57,340, 持续时间: 0.121s)
[16:50:06] [DEBUG] 🔍 过滤短时长条目: 'Aah.' (00:01:06,319 --> 00:01:06,339, 持续时间: 0.020s)
[16:50:06] [DEBUG] 🔍 过滤短时长条目: 'You see-' (00:01:15,860 --> 00:01:15,879, 持续时间: 0.019s)
[16:50:06] ℹ️ 短时长过滤完成：63 个条目 → 60 个条目（过滤掉 3 个短时长条目）
[16:50:06] ℹ️ 正在裁剪字幕静音区域...
[16:50:06] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:50:06] [DEBUG] 🔍 第一轮裁剪: 处理60个, 去除静音22.763秒
[16:50:06] [DEBUG] 🔍 开始第二轮静音裁剪...
[16:50:06] [DEBUG] 🔍 第二轮裁剪: 处理60个条目
[16:50:06] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:50:07] [DEBUG] 🔍 第二轮-开始时间: 在100ms偏移找到30个有效调整，剩余30个条目
[16:50:07] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:50:07] [DEBUG] 🔍 第二轮-开始时间: 在200ms偏移找到1个有效调整，剩余29个条目
[16:50:07] [DEBUG] 🔍 第二轮-开始时间: 总计处理31个条目，29个条目保持原样
[16:50:07] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:50:07] [DEBUG] 🔍 第二轮-结束时间: 在100ms偏移找到3个有效调整，剩余57个条目
[16:50:07] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:50:07] [DEBUG] 🔍 第二轮-结束时间: 200ms偏移无有效调整，剩余57个条目
[16:50:07] [DEBUG] 🔍 第二轮-结束时间: 总计处理3个条目，57个条目保持原样
[16:50:07] [DEBUG] 🔍 第二轮裁剪: 处理60个, 总计调整16个条目
[16:50:07] [DEBUG] 🔍 开始第三轮静音裁剪...
[16:50:07] [DEBUG] 🔍 第三轮裁剪: 处理60个条目
[16:50:07] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:50:07] [DEBUG] 🔍 第三轮-开始时间: 100ms偏移无有效调整，剩余60个条目
[16:50:07] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:50:07] [DEBUG] 🔍 第三轮-开始时间: 200ms偏移无有效调整，剩余60个条目
[16:50:07] [DEBUG] 🔍 第三轮-开始时间: 总计处理0个条目，60个条目保持原样
[16:50:07] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:50:07] [DEBUG] 🔍 第三轮-结束时间: 在100ms偏移找到31个有效调整，剩余29个条目
[16:50:07] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:50:07] [DEBUG] 🔍 第三轮-结束时间: 在200ms偏移找到1个有效调整，剩余28个条目
[16:50:07] [DEBUG] 🔍 第三轮-结束时间: 总计处理32个条目，28个条目保持原样
[16:50:07] [DEBUG] 🔍 第三轮裁剪: 处理60个, 总计调整2个条目
[16:50:07] ℹ️ 标准扩充: 增加显示 14.279 秒
[16:50:07] [DEBUG] 🔍 标准扩充详情: 完全14个, 部分44个
[16:50:07] [DEBUG] 🔍 解决扩充冲突: 33个
[16:50:07] ℹ️ 智能扩充: 处理26个短字幕, 增加显示 2.100 秒
[16:50:07] [DEBUG] 🔍 智能扩充详情: 完全8个, 部分18个, 目标1000ms
[16:50:07] ℹ️ 桥接处理: 填补间隔 1.080 秒
[16:50:07] [DEBUG] 🔍 桥接详情: 处理7个间隔, 阈值300ms
[16:50:07] ℹ️ API2: 字幕生成完成 - trimmed_Honey Baby, Honey Baby (1974)_part7_00-30-41-ElevenLabs-API2.srt
[16:50:07] ℹ️ 字幕生成完成，共生成 1 个字幕文件
[16:50:07] ℹ️ 文本处理统计:
[16:50:07] ℹ️   总LLM分段数: 63
[16:50:07] ℹ️   第一轮精确匹配: 63/63 (100.0%)
[16:50:07] ℹ️   第二轮锚点匹配: 0/63 (0.0%)
[16:50:07] ℹ️   文本匹配成功: 63/63 (100.0%)
[16:50:07] ℹ️   文本对齐成功: 63/63 (100.0%)
[16:50:07] [TaskFlow] 分段字幕生成成功: trimmed_Honey Baby, Honey Baby (1974)_part7_00-30-41 - ElevenLabs-API2
[16:50:07] [TaskFlow] 处理分段字幕 8/19: trimmed_Honey Baby, Honey Baby (1974)_part8_00-34-17
[16:50:07] [TaskFlow] 开始生成分段字幕: trimmed_Honey Baby, Honey Baby (1974)_part8_00-34-17 - ElevenLabs-API2
[16:50:07] ℹ️ 开始字幕生成: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part8_00-34-17-ElevenLabs-parsed.json
[16:50:07] [DEBUG] 🔍 提取服务名称，输入文件: trimmed_Honey Baby, Honey Baby (1974)_part8_00-34-17-ElevenLabs-parsed.json
[16:50:07] [DEBUG] 🔍 正则匹配成功，服务名称: ElevenLabs
[16:50:07] ℹ️ 检测到服务名称: ElevenLabs
[16:50:07] ℹ️ 检测已存在的字幕文件...
[16:50:07] [DEBUG] 🔍 🔍 扫描目录: ./projects\trimmed_Honey Baby, Honey Baby (1974)
[16:50:07] [DEBUG] 🔍 🔍 查找模式: trimmed_Honey Baby, Honey Baby (1974)_part8_00-34-17-ElevenLabs-*.srt
[16:50:07] [DEBUG] 🔍 🔍 找到 0 个匹配文件
[16:50:07] ℹ️ 未发现已存在字幕文件
[16:50:07] ℹ️ 全部启用的API: ['API2']
[16:50:07] ℹ️ 已存在字幕的API: []
[16:50:07] ℹ️ 需要处理的API: ['API2']
[16:50:07] ℹ️ 正在分析音频静音段...
[16:50:07] ℹ️ 正在检测音频静音段: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part8_00-34-17.mp3
[16:50:08] ℹ️ 检测到 475 个静音段
[16:50:08] ℹ️ 静音检测完成，缓存结果用于后续处理
[16:50:08] ℹ️ 开始处理 API2...
[16:50:08] ℹ️ API2: 启用分块处理
[16:50:08] ℹ️ API2: 分割为 3 个块
[16:50:08] ℹ️ API2: 正在处理第 1/3 块 (979 字符)
[16:50:08] [DEBUG] 🔍 API2: 开始API调用处理第 1 块...
[16:50:08] [DEBUG] 🔍 API2: 开始API调用...
[16:50:48] [DEBUG] 🔍 API2: API调用成功，返回 984 字符
[16:50:48] ℹ️ API2: 第 1 块处理成功 ✅ (耗时: 40.5秒, 差异: 5 字符)
[16:50:48] ℹ️ API2: 正在处理第 2/3 块 (984 字符)
[16:50:48] ℹ️ API2: 等待请求间隔 10.0 秒...
[16:50:58] [DEBUG] 🔍 API2: 开始API调用处理第 2 块...
[16:50:58] [DEBUG] 🔍 API2: 开始API调用...
[16:51:30] [DEBUG] 🔍 API2: API调用成功，返回 988 字符
[16:51:30] ℹ️ API2: 第 2 块处理成功 ✅ (耗时: 31.7秒, 差异: 4 字符)
[16:51:30] ℹ️ API2: 正在处理第 3/3 块 (491 字符)
[16:51:30] ℹ️ API2: 等待请求间隔 10.0 秒...
[16:51:40] [DEBUG] 🔍 API2: 开始API调用处理第 3 块...
[16:51:40] [DEBUG] 🔍 API2: 开始API调用...
[16:52:11] [DEBUG] 🔍 API2: API调用成功，返回 494 字符
[16:52:11] ℹ️ API2: 第 3 块处理成功 ✅ (耗时: 31.1秒, 差异: 3 字符)
[16:52:11] ℹ️ API2: 初始处理完成，成功 3/3 块
[16:52:11] ℹ️ API2: LLM调用成功，开始生成字幕...
[16:52:11] ℹ️ 对齐 API2 的文本分段...
[16:52:11] ℹ️ LLM分段调试数据已保存到: ./projects\trimmed_Honey Baby, Honey Baby (1974)\llm_segments_debug_20250803_165211.json
[16:52:11] ℹ️ 开始文本对齐处理（顺序约束），共 81 个片段
[16:52:11] [DEBUG] 🔍 片段处理进度:
[16:52:11] [DEBUG] 🔍   #1 | 精确匹配✅ → 对齐成功✅ | "Over against the pillar. Natur..."
[16:52:11] [DEBUG] 🔍   #2 | 精确匹配✅ → 对齐成功✅ | "Yeah, yeah. Oh. Oh. Keep getti..."
[16:52:11] [DEBUG] 🔍   #3 | 精确匹配✅ → 对齐成功✅ | "Uh, like, like, uh, Nefertiti."
[16:52:11] [DEBUG] 🔍   #4 | 精确匹配✅ → 对齐成功✅ | "Nefertiti and they're ruined. ..."
[16:52:11] [DEBUG] 🔍   #5 | 精确匹配✅ → 对齐成功✅ | "Look at it. Oh, oh. That's tro..."
[16:52:11] [DEBUG] 🔍   #6 | 精确匹配✅ → 对齐成功✅ | "Oh, no, it's perfect."
[16:52:11] [DEBUG] 🔍   #7 | 精确匹配✅ → 对齐成功✅ | "That's the champagne sender."
[16:52:11] [DEBUG] 🔍   #8 | 精确匹配✅ → 对齐成功✅ | "It's gotta be."
[16:52:11] [DEBUG] 🔍   #9 | 精确匹配✅ → 对齐成功✅ | "Oh, why, it's just what we nee..."
[16:52:11] [DEBUG] 🔍   #10 | 精确匹配✅ → 对齐成功✅ | "Oh, look at that guy."
[16:52:11] [DEBUG] 🔍   #11 | 精确匹配✅ → 对齐成功✅ | "Oh, he's probably an African p..."
[16:52:11] [DEBUG] 🔍   #12 | 精确匹配✅ → 对齐成功✅ | "Oh, your flower. You're crazy."
[16:52:11] [DEBUG] 🔍   #13 | 精确匹配✅ → 对齐成功✅ | "Oh, it's death in Venice."
[16:52:11] [DEBUG] 🔍   #14 | 精确匹配✅ → 对齐成功✅ | "It's death in Venice."
[16:52:11] [DEBUG] 🔍   #15 | 精确匹配✅ → 对齐成功✅ | "You know, what was that pictur..."
[16:52:11] [DEBUG] 🔍   #16 | 精确匹配✅ → 对齐成功✅ | "You know, which... N- Never mi..."
[16:52:11] [DEBUG] 🔍   #17 | 精确匹配✅ → 对齐成功✅ | "Get a picture, Herb. Get a pic..."
[16:52:11] [DEBUG] 🔍   #18 | 精确匹配✅ → 对齐成功✅ | "Get a, get... Get a dinner."
[16:52:11] [DEBUG] 🔍   #19 | 精确匹配✅ → 对齐成功✅ | "I've got the load. Jesus Chris..."
[16:52:11] [DEBUG] 🔍   #20 | 精确匹配✅ → 对齐成功✅ | "Will you have dinner with me t..."
[16:52:11] [DEBUG] 🔍   #21 | 精确匹配✅ → 对齐成功✅ | "I can't, um, um, I'm tired."
[16:52:11] [DEBUG] 🔍   #22 | 精确匹配✅ → 对齐成功✅ | "Oh, we will make it an early d..."
[16:52:11] [DEBUG] 🔍   #23 | 精确匹配✅ → 对齐成功✅ | "Pick you up at your hotel."
[16:52:11] [DEBUG] 🔍   #24 | 精确匹配✅ → 对齐成功✅ | "If you're taking any pictures ..."
[16:52:11] [DEBUG] 🔍   #25 | 精确匹配✅ → 对齐成功✅ | "African prince."
[16:52:11] [DEBUG] 🔍   #26 | 精确匹配✅ → 对齐成功✅ | "He's from the 127th Street."
[16:52:11] [DEBUG] 🔍   #27 | 精确匹配✅ → 对齐成功✅ | "We found it under her breast."
[16:52:11] [DEBUG] 🔍   #28 | 精确匹配✅ → 对齐成功✅ | "You see, my good woman, it was..."
[16:52:11] [DEBUG] 🔍   #29 | 精确匹配✅ → 对齐成功✅ | "An undergraduate's joke,//whic..."
[16:52:11] [DEBUG] 🔍   #30 | 精确匹配✅ → 对齐成功✅ | "It's a faker."
[16:52:11] [DEBUG] 🔍   #31 | 精确匹配✅ → 对齐成功✅ | "The American woman//must have ..."
[16:52:11] [DEBUG] 🔍   #32 | 精确匹配✅ → 对齐成功✅ | "We will still play the whole n..."
[16:52:11] [DEBUG] 🔍   #33 | 精确匹配✅ → 对齐成功✅ | "That depend."
[16:52:11] [DEBUG] 🔍   #34 | 精确匹配✅ → 对齐成功✅ | "Depends on what?"
[16:52:11] [DEBUG] 🔍   #35 | 精确匹配✅ → 对齐成功✅ | "If I get my collar together."
[16:52:12] [DEBUG] 🔍   #36 | 精确匹配✅ → 对齐成功✅ | "Behave yourself."
[16:52:12] [DEBUG] 🔍   #37 | 精确匹配✅ → 对齐成功✅ | "You know, I need some money."
[16:52:12] [DEBUG] 🔍   #38 | 精确匹配✅ → 对齐成功✅ | "Right, dear. ."
[16:52:12] [DEBUG] 🔍   #39 | 精确匹配✅ → 对齐成功✅ | "Here you are."
[16:52:12] [DEBUG] 🔍   #40 | 精确匹配✅ → 对齐成功✅ | "That's for you."
[16:52:12] [DEBUG] 🔍   #41 | 精确匹配✅ → 对齐成功✅ | "£10?"
[16:52:12] [DEBUG] 🔍   #42 | 精确匹配✅ → 对齐成功✅ | "This ain't even $3."
[16:52:12] [DEBUG] 🔍   #43 | 精确匹配✅ → 对齐成功✅ | "Honey, baby, look."
[16:52:12] [DEBUG] 🔍   #44 | 精确匹配✅ → 对齐成功✅ | "Come on, get up off of some of..."
[16:52:12] [DEBUG] 🔍   #45 | 精确匹配✅ → 对齐成功✅ | "I need more than this."
[16:52:12] [DEBUG] 🔍   #46 | 精确匹配✅ → 对齐成功✅ | "Give me this little,//little b..."
[16:52:12] [DEBUG] 🔍   #47 | 精确匹配✅ → 对齐成功✅ | "Hey, look, honey baby."
[16:52:12] [DEBUG] 🔍   #48 | 精确匹配✅ → 对齐成功✅ | "This won't even get me into ca..."
[16:52:12] [DEBUG] 🔍   #49 | 精确匹配✅ → 对齐成功✅ | "Come on, honey baby."
[16:52:12] [DEBUG] 🔍   #50 | 精确匹配✅ → 对齐成功✅ | "You got to give me some money."
[16:52:12] [DEBUG] 🔍   #51 | 精确匹配✅ → 对齐成功✅ | "Thanks now."
[16:52:12] [DEBUG] 🔍   #52 | 精确匹配✅ → 对齐成功✅ | "Now be careful."
[16:52:12] [DEBUG] 🔍   #53 | 精确匹配✅ → 对齐成功✅ | "Well, I'm pushing up and on yo..."
[16:52:12] [DEBUG] 🔍   #54 | 精确匹配✅ → 对齐成功✅ | "As you talk to them, they talk..."
[16:52:12] [DEBUG] 🔍   #55 | 精确匹配✅ → 对齐成功✅ | "Watch it."
[16:52:12] [DEBUG] 🔍   #56 | 精确匹配✅ → 对齐成功✅ | "Hmm."
[16:52:12] [DEBUG] 🔍   #57 | 精确匹配✅ → 对齐成功✅ | "I'm cool."
[16:52:12] [DEBUG] 🔍   #58 | 精确匹配✅ → 对齐成功✅ | "I think you're late."
[16:52:12] [DEBUG] 🔍   #59 | 精确匹配✅ → 对齐成功✅ | "All right."
[16:52:12] [DEBUG] 🔍   #60 | 精确匹配✅ → 对齐成功✅ | "You have a nice time, you hear..."
[16:52:12] [DEBUG] 🔍   #61 | 精确匹配✅ → 对齐成功✅ | "He shouldn't be carrying that ..."
[16:52:12] [DEBUG] 🔍   #62 | 精确匹配✅ → 对齐成功✅ | "Oh, well, he won't for long."
[16:52:12] [DEBUG] 🔍   #63 | 精确匹配✅ → 对齐成功✅ | "Look, he's not like you."
[16:52:12] [DEBUG] 🔍   #64 | 精确匹配✅ → 对齐成功✅ | "He's not like you either."
[16:52:12] [DEBUG] 🔍   #65 | 精确匹配✅ → 对齐成功✅ | "Stop mothering him."
[16:52:12] [DEBUG] 🔍   #66 | 精确匹配✅ → 对齐成功✅ | "Oh, is that what I'm doing?"
[16:52:12] [DEBUG] 🔍   #67 | 精确匹配✅ → 对齐成功✅ | "Oh, it's so beautiful here."
[16:52:12] [DEBUG] 🔍   #68 | 精确匹配✅ → 对齐成功✅ | "I plan all beautiful things fo..."
[16:52:12] [DEBUG] 🔍   #69 | 精确匹配✅ → 对齐成功✅ | "That's very kind of-"
[16:52:12] [DEBUG] 🔍   #70 | 精确匹配✅ → 对齐成功✅ | "You are a beautiful woman."
[16:52:12] [DEBUG] 🔍   #71 | 精确匹配✅ → 对齐成功✅ | "Well,//that's very sweet of yo..."
[16:52:12] [DEBUG] 🔍   #72 | 精确匹配✅ → 对齐成功✅ | "That takes you up to Our Lady ..."
[16:52:12] [DEBUG] 🔍   #73 | 精确匹配✅ → 对齐成功✅ | "Oh,//I'll be doing a show up t..."
[16:52:12] [DEBUG] 🔍   #74 | 精确匹配✅ → 对齐成功✅ | "By the way, thank you for the ..."
[16:52:12] [DEBUG] 🔍   #75 | 精确匹配✅ → 对齐成功✅ | "That was nothing, ma'am."
[16:52:12] [DEBUG] 🔍   #76 | 精确匹配✅ → 对齐成功✅ | "Close your eyes."
[16:52:12] [DEBUG] 🔍   #77 | 精确匹配✅ → 对齐成功✅ | "Why?"
[16:52:12] [DEBUG] 🔍   #78 | 精确匹配✅ → 对齐成功✅ | "Because we're almost there"
[16:52:12] [DEBUG] 🔍   #79 | 精确匹配✅ → 对齐成功✅ | "and I don't want you to see//a..."
[16:52:12] [DEBUG] 🔍   #80 | 精确匹配✅ → 对齐成功✅ | "Oh, all right, all right."
[16:52:12] [DEBUG] 🔍   #81 | 精确匹配✅ → 对齐成功✅ | "My eyes are closed."
[16:52:12] ℹ️ API2: 成功对齐 81 个文本分段
[16:52:12] ℹ️ 处理 API2 的字幕条目...
[16:52:12] [DEBUG] 🔍 开始处理 81 个字幕条目的时间间隔分割...
[16:52:12] ℹ️ 时间间隔分割完成：81 个条目 → 86 个条目
[16:52:12] [DEBUG] 🔍 短时长过滤完成：86 个条目，无需过滤
[16:52:12] ℹ️ 正在裁剪字幕静音区域...
[16:52:12] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:52:12] [DEBUG] 🔍 第一轮裁剪: 处理86个, 去除静音36.455秒
[16:52:12] [DEBUG] 🔍 开始第二轮静音裁剪...
[16:52:12] [DEBUG] 🔍 第二轮裁剪: 处理86个条目
[16:52:12] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:52:12] [DEBUG] 🔍 第二轮-开始时间: 在100ms偏移找到63个有效调整，剩余23个条目
[16:52:12] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:52:12] [DEBUG] 🔍 第二轮-开始时间: 在200ms偏移找到3个有效调整，剩余20个条目
[16:52:12] [DEBUG] 🔍 第二轮-开始时间: 总计处理66个条目，20个条目保持原样
[16:52:12] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:52:12] [DEBUG] 🔍 第二轮-结束时间: 在100ms偏移找到2个有效调整，剩余84个条目
[16:52:12] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:52:12] [DEBUG] 🔍 第二轮-结束时间: 200ms偏移无有效调整，剩余84个条目
[16:52:12] [DEBUG] 🔍 第二轮-结束时间: 总计处理2个条目，84个条目保持原样
[16:52:12] [DEBUG] 🔍 第二轮裁剪: 处理86个, 总计调整21个条目
[16:52:12] [DEBUG] 🔍 开始第三轮静音裁剪...
[16:52:12] [DEBUG] 🔍 第三轮裁剪: 处理86个条目
[16:52:12] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:52:12] [DEBUG] 🔍 第三轮-开始时间: 100ms偏移无有效调整，剩余86个条目
[16:52:12] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:52:12] [DEBUG] 🔍 第三轮-开始时间: 在200ms偏移找到2个有效调整，剩余84个条目
[16:52:12] [DEBUG] 🔍 第三轮-开始时间: 总计处理2个条目，84个条目保持原样
[16:52:12] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:52:12] [DEBUG] 🔍 第三轮-结束时间: 在100ms偏移找到64个有效调整，剩余22个条目
[16:52:12] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:52:12] [DEBUG] 🔍 第三轮-结束时间: 在200ms偏移找到1个有效调整，剩余21个条目
[16:52:12] [DEBUG] 🔍 第三轮-结束时间: 总计处理65个条目，21个条目保持原样
[16:52:12] [DEBUG] 🔍 第三轮裁剪: 处理86个, 总计调整5个条目
[16:52:12] ℹ️ 标准扩充: 增加显示 27.796 秒
[16:52:12] [DEBUG] 🔍 标准扩充详情: 完全45个, 部分41个
[16:52:12] [DEBUG] 🔍 解决扩充冲突: 23个
[16:52:12] ℹ️ 智能扩充: 处理14个短字幕, 增加显示 3.044 秒
[16:52:12] [DEBUG] 🔍 智能扩充详情: 完全12个, 部分2个, 目标1000ms
[16:52:12] ℹ️ 桥接处理: 填补间隔 1.244 秒
[16:52:13] [DEBUG] 🔍 桥接详情: 处理10个间隔, 阈值300ms
[16:52:13] ℹ️ API2: 字幕生成完成 - trimmed_Honey Baby, Honey Baby (1974)_part8_00-34-17-ElevenLabs-API2.srt
[16:52:13] ℹ️ 字幕生成完成，共生成 1 个字幕文件
[16:52:13] ℹ️ 文本处理统计:
[16:52:13] ℹ️   总LLM分段数: 81
[16:52:13] ℹ️   第一轮精确匹配: 81/81 (100.0%)
[16:52:13] ℹ️   第二轮锚点匹配: 0/81 (0.0%)
[16:52:13] ℹ️   文本匹配成功: 81/81 (100.0%)
[16:52:13] ℹ️   文本对齐成功: 81/81 (100.0%)
[16:52:13] [TaskFlow] 分段字幕生成成功: trimmed_Honey Baby, Honey Baby (1974)_part8_00-34-17 - ElevenLabs-API2
[16:52:13] [TaskFlow] 处理分段字幕 9/19: trimmed_Honey Baby, Honey Baby (1974)_part9_00-40-29
[16:52:13] [TaskFlow] 开始生成分段字幕: trimmed_Honey Baby, Honey Baby (1974)_part9_00-40-29 - ElevenLabs-API2
[16:52:13] ℹ️ 开始字幕生成: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part9_00-40-29-ElevenLabs-parsed.json
[16:52:13] [DEBUG] 🔍 提取服务名称，输入文件: trimmed_Honey Baby, Honey Baby (1974)_part9_00-40-29-ElevenLabs-parsed.json
[16:52:13] [DEBUG] 🔍 正则匹配成功，服务名称: ElevenLabs
[16:52:13] ℹ️ 检测到服务名称: ElevenLabs
[16:52:13] ℹ️ 检测已存在的字幕文件...
[16:52:13] [DEBUG] 🔍 🔍 扫描目录: ./projects\trimmed_Honey Baby, Honey Baby (1974)
[16:52:13] [DEBUG] 🔍 🔍 查找模式: trimmed_Honey Baby, Honey Baby (1974)_part9_00-40-29-ElevenLabs-*.srt
[16:52:13] [DEBUG] 🔍 🔍 找到 0 个匹配文件
[16:52:13] ℹ️ 未发现已存在字幕文件
[16:52:13] ℹ️ 全部启用的API: ['API2']
[16:52:13] ℹ️ 已存在字幕的API: []
[16:52:13] ℹ️ 需要处理的API: ['API2']
[16:52:13] ℹ️ 正在分析音频静音段...
[16:52:13] ℹ️ 正在检测音频静音段: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part9_00-40-29.mp3
[16:52:13] ℹ️ 检测到 434 个静音段
[16:52:13] ℹ️ 静音检测完成，缓存结果用于后续处理
[16:52:13] ℹ️ 开始处理 API2...
[16:52:13] ℹ️ API2: 启用分块处理
[16:52:13] ℹ️ API2: 分割为 2 个块
[16:52:13] ℹ️ API2: 正在处理第 1/2 块 (988 字符)
[16:52:13] [DEBUG] 🔍 API2: 开始API调用处理第 1 块...
[16:52:13] [DEBUG] 🔍 API2: 开始API调用...
[16:52:48] [DEBUG] 🔍 API2: API调用成功，返回 991 字符
[16:52:48] ℹ️ API2: 第 1 块处理成功 ✅ (耗时: 34.2秒, 差异: 3 字符)
[16:52:48] ℹ️ API2: 正在处理第 2/2 块 (355 字符)
[16:52:48] ℹ️ API2: 等待请求间隔 10.0 秒...
[16:52:58] [DEBUG] 🔍 API2: 开始API调用处理第 2 块...
[16:52:58] [DEBUG] 🔍 API2: 开始API调用...
[16:53:16] [DEBUG] 🔍 API2: API调用成功，返回 357 字符
[16:53:16] ℹ️ API2: 第 2 块处理成功 ✅ (耗时: 18.0秒, 差异: 2 字符)
[16:53:16] ℹ️ API2: 初始处理完成，成功 2/2 块
[16:53:16] ℹ️ API2: LLM调用成功，开始生成字幕...
[16:53:16] ℹ️ 对齐 API2 的文本分段...
[16:53:16] ℹ️ LLM分段调试数据已保存到: ./projects\trimmed_Honey Baby, Honey Baby (1974)\llm_segments_debug_20250803_165316.json
[16:53:16] ℹ️ 开始文本对齐处理（顺序约束），共 67 个片段
[16:53:16] [DEBUG] 🔍 片段处理进度:
[16:53:16] [DEBUG] 🔍   #1 | 精确匹配✅ → 对齐成功✅ | "Hello,"
[16:53:16] [DEBUG] 🔍   #2 | 精确匹配✅ → 对齐成功✅ | "mon cher."
[16:53:16] [DEBUG] 🔍   #3 | 精确匹配✅ → 对齐成功✅ | "Come back to-"
[16:53:16] [DEBUG] 🔍   #4 | 精确匹配✅ → 对齐成功✅ | "Hello."
[16:53:16] [DEBUG] 🔍   #5 | 精确匹配✅ → 对齐成功✅ | "... your mother."
[16:53:16] [DEBUG] 🔍   #6 | 精确匹配✅ → 对齐成功✅ | "My mother."
[16:53:16] [DEBUG] 🔍   #7 | 精确匹配✅ → 对齐成功✅ | "You're going to be a dear."
[16:53:16] [DEBUG] 🔍   #8 | 精确匹配✅ → 对齐成功✅ | "Ma- ma- ma mademoiselle."
[16:53:16] [DEBUG] 🔍   #9 | 精确匹配✅ → 对齐成功✅ | "Dora?"
[16:53:16] [DEBUG] 🔍   #10 | 精确匹配✅ → 对齐成功✅ | "Ah."
[16:53:16] [DEBUG] 🔍   #11 | 精确匹配✅ → 对齐成功✅ | "Se prepele?"
[16:53:16] [DEBUG] 🔍   #12 | 精确匹配✅ → 对齐成功✅ | "En train?"
[16:53:16] [DEBUG] 🔍   #13 | 精确匹配✅ → 对齐成功✅ | "Oui."
[16:53:16] [DEBUG] 🔍   #14 | 精确匹配✅ → 对齐成功✅ | "More traditionally,"
[16:53:16] [DEBUG] 🔍   #15 | 精确匹配✅ → 对齐成功✅ | "mon cher."
[16:53:16] [DEBUG] 🔍   #16 | 精确匹配✅ → 对齐成功✅ | "Oh, yeah."
[16:53:16] [DEBUG] 🔍   #17 | 精确匹配✅ → 对齐成功✅ | "Classy."
[16:53:16] [DEBUG] 🔍   #18 | 精确匹配✅ → 对齐成功✅ | "You give up?"
[16:53:16] [DEBUG] 🔍   #19 | 精确匹配✅ → 对齐成功✅ | "No."
[16:53:16] [DEBUG] 🔍   #20 | 精确匹配✅ → 对齐成功✅ | "We iron-"
[16:53:16] [DEBUG] 🔍   #21 | 精确匹配✅ → 对齐成功✅ | "You tell me, I will kill you."
[16:53:16] [DEBUG] 🔍   #22 | 精确匹配✅ → 对齐成功✅ | "I've seen a picture of that ca..."
[16:53:16] [DEBUG] 🔍   #23 | 精确匹配✅ → 对齐成功✅ | "It's 13th century."
[16:53:16] [DEBUG] 🔍   #24 | 精确匹配✅ → 对齐成功✅ | "I know that much."
[16:53:16] [DEBUG] 🔍   #25 | 精确匹配✅ → 对齐成功✅ | "Oh, I, I don't know."
[16:53:16] [DEBUG] 🔍   #26 | 精确匹配✅ → 对齐成功✅ | "I give up."
[16:53:16] [DEBUG] 🔍   #27 | 精确匹配✅ → 对齐成功✅ | "No, no, no."
[16:53:16] [DEBUG] 🔍   #28 | 精确匹配✅ → 对齐成功✅ | "I don't give up."
[16:53:16] [DEBUG] 🔍   #29 | 精确匹配✅ → 对齐成功✅ | "Give me a clue."
[16:53:16] [DEBUG] 🔍   #30 | 精确匹配✅ → 对齐成功✅ | "What, am I my brother's keeper..."
[16:53:16] [DEBUG] 🔍   #31 | 精确匹配✅ → 对齐成功✅ | "That's a clue?"
[16:53:16] [DEBUG] 🔍   #32 | 精确匹配✅ → 对齐成功✅ | "Mm-hmm."
[16:53:16] [DEBUG] 🔍   #33 | 精确匹配✅ → 对齐成功✅ | "Am I my brother's keeper?"
[16:53:16] [DEBUG] 🔍   #34 | 精确匹配✅ → 对齐成功✅ | "Cain?"
[16:53:16] [DEBUG] 🔍   #35 | 精确匹配✅ → 对齐成功✅ | "Abel?"
[16:53:16] [DEBUG] 🔍   #36 | 精确匹配✅ → 对齐成功✅ | "Take the I out of Cain."
[16:53:16] [DEBUG] 🔍   #37 | 精确匹配✅ → 对齐成功✅ | "Cain?"
[16:53:16] [DEBUG] 🔍   #38 | 精确匹配✅ → 对齐成功✅ | "And Egyptian funeral boats."
[16:53:16] [DEBUG] 🔍   #39 | 精确匹配✅ → 对齐成功✅ | "You're deliberately trying to ..."
[16:53:16] [DEBUG] 🔍   #40 | 精确匹配✅ → 对齐成功✅ | "Oh, no."
[16:53:16] [DEBUG] 🔍   #41 | 精确匹配✅ → 对齐成功✅ | "It's also a Greek word."
[16:53:16] [DEBUG] 🔍   #42 | 精确匹配✅ → 对齐成功✅ | "Well, where's the historical c..."
[16:53:16] [DEBUG] 🔍   #43 | 精确匹配✅ → 对齐成功✅ | "It's all on paper."
[16:53:16] [DEBUG] 🔍   #44 | 精确匹配✅ → 对齐成功✅ | "Papyrus."
[16:53:16] [DEBUG] 🔍   #45 | 精确匹配✅ → 对齐成功✅ | "The pharaoh's funeral boats//w..."
[16:53:16] [DEBUG] 🔍   #46 | 精确匹配✅ → 对齐成功✅ | "The Greeks called paper byblos..."
[16:53:16] [DEBUG] 🔍   #47 | 精确匹配✅ → 对齐成功✅ | "The I out of Cain, Canaanites,"
[16:53:16] [DEBUG] 🔍   #48 | 精确匹配✅ → 对齐成功✅ | "known as Phoenicians,"
[16:53:16] [DEBUG] 🔍   #49 | 精确匹配✅ → 对齐成功✅ | "who had three city states://Ty..."
[16:53:16] [DEBUG] 🔍   #50 | 精确匹配✅ → 对齐成功✅ | "Byblos, hence this is Byblos."
[16:53:16] [DEBUG] 🔍   #51 | 精确匹配✅ → 对齐成功✅ | "Right on, sister."
[16:53:16] [DEBUG] 🔍   #52 | 精确匹配✅ → 对齐成功✅ | "You're brilliant."
[16:53:16] [DEBUG] 🔍   #53 | 精确匹配✅ → 对齐成功✅ | "Listen, honey,//I wasn't born ..."
[16:53:16] [DEBUG] 🔍   #54 | 精确匹配✅ → 对齐成功✅ | "you know?"
[16:53:16] [DEBUG] 🔍   #55 | 精确匹配✅ → 对齐成功✅ | "Yeah."
[16:53:16] [DEBUG] 🔍   #56 | 精确匹配✅ → 对齐成功✅ | "Danessa."
[16:53:16] [DEBUG] 🔍   #57 | 精确匹配✅ → 对齐成功✅ | "What?"
[16:53:16] [DEBUG] 🔍   #58 | 精确匹配✅ → 对齐成功✅ | "I have another surprise for yo..."
[16:53:16] [DEBUG] 🔍   #59 | 精确匹配✅ → 对齐成功✅ | "I hope I do as well."
[16:53:16] [DEBUG] 🔍   #60 | 精确匹配✅ → 对齐成功✅ | "I'm sure you will."
[16:53:16] [DEBUG] 🔍   #61 | 精确匹配✅ → 对齐成功✅ | "Just a little something//I had..."
[16:53:16] [DEBUG] 🔍   #62 | 精确匹配✅ → 对齐成功✅ | "They did a very good job."
[16:53:16] [DEBUG] 🔍   #63 | 精确匹配✅ → 对齐成功✅ | "We do the best we can."
[16:53:16] [DEBUG] 🔍   #64 | 精确匹配✅ → 对齐成功✅ | "Shall we sit down?"
[16:53:16] [DEBUG] 🔍   #65 | 精确匹配✅ → 对齐成功✅ | "Yes."
[16:53:16] [DEBUG] 🔍   #66 | 精确匹配✅ → 对齐成功✅ | "And about something, mademoise..."
[16:53:16] [DEBUG] 🔍   #67 | 精确匹配✅ → 对齐成功✅ | "Everything?"
[16:53:16] ℹ️ API2: 成功对齐 67 个文本分段
[16:53:16] ℹ️ 处理 API2 的字幕条目...
[16:53:16] [DEBUG] 🔍 开始处理 67 个字幕条目的时间间隔分割...
[16:53:17] ℹ️ 时间间隔分割完成：67 个条目 → 69 个条目
[16:53:17] [DEBUG] 🔍 过滤短时长条目: 'My mother.' (00:01:23,519 --> 00:01:23,519, 持续时间: 0.000s)
[16:53:17] [DEBUG] 🔍 过滤短时长条目: 'Ah.' (00:01:27,599 --> 00:01:27,719, 持续时间: 0.120s)
[16:53:17] [DEBUG] 🔍 过滤短时长条目: 'Oui.' (00:01:30,000 --> 00:01:30,019, 持续时间: 0.019s)
[16:53:17] ℹ️ 短时长过滤完成：69 个条目 → 66 个条目（过滤掉 3 个短时长条目）
[16:53:17] ℹ️ 正在裁剪字幕静音区域...
[16:53:17] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:53:17] [DEBUG] 🔍 第一轮裁剪: 处理66个, 去除静音48.791秒
[16:53:17] [DEBUG] 🔍 开始第二轮静音裁剪...
[16:53:17] [DEBUG] 🔍 第二轮裁剪: 处理66个条目
[16:53:17] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:53:17] [DEBUG] 🔍 第二轮-开始时间: 在100ms偏移找到43个有效调整，剩余23个条目
[16:53:17] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:53:17] [DEBUG] 🔍 第二轮-开始时间: 在200ms偏移找到1个有效调整，剩余22个条目
[16:53:17] [DEBUG] 🔍 第二轮-开始时间: 总计处理44个条目，22个条目保持原样
[16:53:17] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:53:17] [DEBUG] 🔍 第二轮-结束时间: 在100ms偏移找到1个有效调整，剩余65个条目
[16:53:17] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:53:17] [DEBUG] 🔍 第二轮-结束时间: 200ms偏移无有效调整，剩余65个条目
[16:53:17] [DEBUG] 🔍 第二轮-结束时间: 总计处理1个条目，65个条目保持原样
[16:53:17] [DEBUG] 🔍 第二轮裁剪: 处理66个, 总计调整9个条目
[16:53:17] [DEBUG] 🔍 开始第三轮静音裁剪...
[16:53:17] [DEBUG] 🔍 第三轮裁剪: 处理66个条目
[16:53:17] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:53:17] [DEBUG] 🔍 第三轮-开始时间: 100ms偏移无有效调整，剩余66个条目
[16:53:17] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:53:17] [DEBUG] 🔍 第三轮-开始时间: 200ms偏移无有效调整，剩余66个条目
[16:53:17] [DEBUG] 🔍 第三轮-开始时间: 总计处理0个条目，66个条目保持原样
[16:53:17] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:53:17] [DEBUG] 🔍 第三轮-结束时间: 在100ms偏移找到44个有效调整，剩余22个条目
[16:53:17] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:53:17] [DEBUG] 🔍 第三轮-结束时间: 在200ms偏移找到3个有效调整，剩余19个条目
[16:53:17] [DEBUG] 🔍 第三轮-结束时间: 总计处理47个条目，19个条目保持原样
[16:53:17] [DEBUG] 🔍 第三轮裁剪: 处理66个, 总计调整4个条目
[16:53:17] ℹ️ 标准扩充: 增加显示 21.397 秒
[16:53:17] [DEBUG] 🔍 标准扩充详情: 完全40个, 部分24个
[16:53:17] [DEBUG] 🔍 解决扩充冲突: 16个
[16:53:17] ℹ️ 智能扩充: 处理24个短字幕, 增加显示 3.913 秒
[16:53:17] [DEBUG] 🔍 智能扩充详情: 完全18个, 部分6个, 目标1000ms
[16:53:17] ℹ️ 桥接处理: 填补间隔 1.631 秒
[16:53:17] [DEBUG] 🔍 桥接详情: 处理10个间隔, 阈值300ms
[16:53:17] ℹ️ API2: 字幕生成完成 - trimmed_Honey Baby, Honey Baby (1974)_part9_00-40-29-ElevenLabs-API2.srt
[16:53:17] ℹ️ 字幕生成完成，共生成 1 个字幕文件
[16:53:17] ℹ️ 文本处理统计:
[16:53:17] ℹ️   总LLM分段数: 67
[16:53:17] ℹ️   第一轮精确匹配: 67/67 (100.0%)
[16:53:17] ℹ️   第二轮锚点匹配: 0/67 (0.0%)
[16:53:17] ℹ️   文本匹配成功: 67/67 (100.0%)
[16:53:17] ℹ️   文本对齐成功: 67/67 (100.0%)
[16:53:17] [TaskFlow] 分段字幕生成成功: trimmed_Honey Baby, Honey Baby (1974)_part9_00-40-29 - ElevenLabs-API2
[16:53:17] [TaskFlow] 处理分段字幕 10/19: trimmed_Honey Baby, Honey Baby (1974)_part10_00-45-50
[16:53:17] [TaskFlow] 开始生成分段字幕: trimmed_Honey Baby, Honey Baby (1974)_part10_00-45-50 - ElevenLabs-API2
[16:53:17] ℹ️ 开始字幕生成: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part10_00-45-50-ElevenLabs-parsed.json
[16:53:17] [DEBUG] 🔍 提取服务名称，输入文件: trimmed_Honey Baby, Honey Baby (1974)_part10_00-45-50-ElevenLabs-parsed.json
[16:53:17] [DEBUG] 🔍 正则匹配成功，服务名称: ElevenLabs
[16:53:17] ℹ️ 检测到服务名称: ElevenLabs
[16:53:17] ℹ️ 检测已存在的字幕文件...
[16:53:17] [DEBUG] 🔍 🔍 扫描目录: ./projects\trimmed_Honey Baby, Honey Baby (1974)
[16:53:17] [DEBUG] 🔍 🔍 查找模式: trimmed_Honey Baby, Honey Baby (1974)_part10_00-45-50-ElevenLabs-*.srt
[16:53:17] [DEBUG] 🔍 🔍 找到 0 个匹配文件
[16:53:17] ℹ️ 未发现已存在字幕文件
[16:53:17] ℹ️ 全部启用的API: ['API2']
[16:53:17] ℹ️ 已存在字幕的API: []
[16:53:17] ℹ️ 需要处理的API: ['API2']
[16:53:17] ℹ️ 正在分析音频静音段...
[16:53:17] ℹ️ 正在检测音频静音段: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part10_00-45-50.mp3
[16:53:18] ℹ️ 检测到 454 个静音段
[16:53:18] ℹ️ 静音检测完成，缓存结果用于后续处理
[16:53:18] ℹ️ 开始处理 API2...
[16:53:18] ℹ️ API2: 启用分块处理
[16:53:18] ℹ️ API2: 分割为 2 个块
[16:53:18] ℹ️ API2: 正在处理第 1/2 块 (962 字符)
[16:53:18] [DEBUG] 🔍 API2: 开始API调用处理第 1 块...
[16:53:18] [DEBUG] 🔍 API2: 开始API调用...
[16:53:44] [DEBUG] 🔍 API2: API调用成功，返回 968 字符
[16:53:44] ℹ️ API2: 第 1 块处理成功 ✅ (耗时: 25.8秒, 差异: 6 字符)
[16:53:44] ℹ️ API2: 正在处理第 2/2 块 (805 字符)
[16:53:44] ℹ️ API2: 等待请求间隔 10.0 秒...
[16:53:54] [DEBUG] 🔍 API2: 开始API调用处理第 2 块...
[16:53:54] [DEBUG] 🔍 API2: 开始API调用...
[16:54:24] [DEBUG] 🔍 API2: API调用成功，返回 807 字符
[16:54:24] ℹ️ API2: 第 2 块处理成功 ✅ (耗时: 30.6秒, 差异: 2 字符)
[16:54:24] ℹ️ API2: 初始处理完成，成功 2/2 块
[16:54:24] ℹ️ API2: LLM调用成功，开始生成字幕...
[16:54:24] ℹ️ 对齐 API2 的文本分段...
[16:54:24] ℹ️ LLM分段调试数据已保存到: ./projects\trimmed_Honey Baby, Honey Baby (1974)\llm_segments_debug_20250803_165424.json
[16:54:24] ℹ️ 开始文本对齐处理（顺序约束），共 64 个片段
[16:54:24] [DEBUG] 🔍 片段处理进度:
[16:54:24] [DEBUG] 🔍   #1 | 精确匹配✅ → 对齐成功✅ | "You know? Hm?"
[16:54:24] [DEBUG] 🔍   #2 | 精确匹配✅ → 对齐成功✅ | "I saw a man die yesterday//and..."
[16:54:25] [DEBUG] 🔍   #3 | 精确匹配✅ → 对齐成功✅ | "What's that?"
[16:54:25] [DEBUG] 🔍   #4 | 精确匹配✅ → 对齐成功✅ | "Please don't let this spoil my..."
[16:54:25] [DEBUG] 🔍   #5 | 精确匹配✅ → 对齐成功✅ | "A man just died and all I coul..."
[16:54:25] [DEBUG] 🔍   #6 | 精确匹配✅ → 对齐成功✅ | "Who was that involved?"
[16:54:25] [DEBUG] 🔍   #7 | 精确匹配✅ → 对齐成功✅ | "Well, there was this Chinese w..."
[16:54:25] [DEBUG] 🔍   #8 | 精确匹配✅ → 对齐成功✅ | "Well, what happened to the Chi..."
[16:54:25] [DEBUG] 🔍   #9 | 精确匹配✅ → 对齐成功✅ | ". . . . ."
[16:54:25] [DEBUG] 🔍   #10 | 精确匹配✅ → 对齐成功✅ | "What did he just say?"
[16:54:25] [DEBUG] 🔍   #11 | 精确匹配✅ → 对齐成功✅ | "Uh, he was just talking about/..."
[16:54:25] [DEBUG] 🔍   #12 | 精确匹配✅ → 对齐成功✅ | "Well, what about this Chinese ..."
[16:54:25] [DEBUG] 🔍   #13 | 精确匹配✅ → 对齐成功✅ | "You didn't have to leave like ..."
[16:54:25] [DEBUG] 🔍   #14 | 精确匹配✅ → 对齐成功✅ | "You must be joking."
[16:54:25] [DEBUG] 🔍   #15 | 精确匹配✅ → 对齐成功✅ | "Look, Laura, I was only trying..."
[16:54:25] [DEBUG] 🔍   #16 | 精确匹配✅ → 对齐成功✅ | "No, please. Just wait."
[16:54:25] [DEBUG] 🔍   #17 | 精确匹配✅ → 对齐成功✅ | "Look, I am no 17-year-old scho..."
[16:54:25] [DEBUG] 🔍   #18 | 精确匹配✅ → 对齐成功✅ | "I never thought you were."
[16:54:25] [DEBUG] 🔍   #19 | 精确匹配✅ → 对齐成功✅ | "Well, you acted like you did."
[16:54:25] [DEBUG] 🔍   #20 | 精确匹配✅ → 对齐成功✅ | "With all those stories and tha..."
[16:54:25] [DEBUG] 🔍   #21 | 精确匹配✅ → 对齐成功✅ | "Laura, will you let me say som..."
[16:54:25] [DEBUG] 🔍   #22 | 精确匹配✅ → 对齐成功✅ | "Look, look. I did not come all..."
[16:54:25] [DEBUG] 🔍   #23 | 精确匹配✅ → 对齐成功✅ | "You know what I mean?"
[16:54:25] [DEBUG] 🔍   #24 | 精确匹配✅ → 对齐成功✅ | "Uh, uh, just a minute."
[16:54:25] [DEBUG] 🔍   #25 | 精确匹配✅ → 对齐成功✅ | "Take it easy now."
[16:54:25] [DEBUG] 🔍   #26 | 精确匹配✅ → 对齐成功✅ | "We've been through that before..."
[16:54:25] [DEBUG] 🔍   #27 | 精确匹配✅ → 对齐成功✅ | "I understood every word your f..."
[16:54:25] [DEBUG] 🔍   #28 | 精确匹配✅ → 对齐成功✅ | "Okay."
[16:54:25] [DEBUG] 🔍   #29 | 精确匹配✅ → 对齐成功✅ | "Pepe was wrong."
[16:54:25] [DEBUG] 🔍   #30 | 精确匹配✅ → 对齐成功✅ | "Okay."
[16:54:25] [DEBUG] 🔍   #31 | 精确匹配✅ → 对齐成功✅ | "And what about you?"
[16:54:25] [DEBUG] 🔍   #32 | 精确匹配✅ → 对齐成功✅ | "I was wrong, too."
[16:54:25] [DEBUG] 🔍   #33 | 精确匹配✅ → 对齐成功✅ | "And I'm sorry."
[16:54:25] [DEBUG] 🔍   #34 | 精确匹配✅ → 对齐成功✅ | "I'm sorry about the whole thin..."
[16:54:25] [DEBUG] 🔍   #35 | 精确匹配✅ → 对齐成功✅ | "Okay?"
[16:54:25] [DEBUG] 🔍   #36 | 精确匹配✅ → 对齐成功✅ | "See, the real reason I brought..."
[16:54:25] [DEBUG] 🔍   #37 | 精确匹配✅ → 对齐成功✅ | "was because I wanted to find o..."
[16:54:25] [DEBUG] 🔍   #38 | 精确匹配✅ → 对齐成功✅ | "Madam Chan?"
[16:54:25] [DEBUG] 🔍   #39 | 精确匹配✅ → 对齐成功✅ | "Yeah, with the Chinese woman//..."
[16:54:25] [DEBUG] 🔍   #40 | 精确匹配✅ → 对齐成功✅ | "What's your interest in her?"
[16:54:25] [DEBUG] 🔍   #41 | 精确匹配✅ → 对齐成功✅ | "I'll tell you about that later..."
[16:54:25] [DEBUG] 🔍   #42 | 精确匹配✅ → 对齐成功✅ | "Where'd you like to go now?"
[16:54:25] [DEBUG] 🔍   #43 | 精确匹配✅ → 对齐成功✅ | "I'd like to go for a long driv..."
[16:54:25] [DEBUG] 🔍   #44 | 精确匹配✅ → 对齐成功✅ | "Some place cool."
[16:54:25] [DEBUG] 🔍   #45 | 精确匹配✅ → 对齐成功✅ | "You know, I know just the plac..."
[16:54:25] [DEBUG] 🔍   #46 | 精确匹配✅ → 对齐成功✅ | "I know you'll love it."
[16:54:25] [DEBUG] 🔍   #47 | 精确匹配✅ → 对齐成功✅ | "Okay, but, um, do me a favor."
[16:54:25] [DEBUG] 🔍   #48 | 精确匹配✅ → 对齐成功✅ | "What's that?"
[16:54:25] [DEBUG] 🔍   #49 | 精确匹配✅ → 对齐成功✅ | "No more surprises."
[16:54:25] [DEBUG] 🔍   #50 | 精确匹配✅ → 对齐成功✅ | "I didn't realize you had such ..."
[16:54:25] [DEBUG] 🔍   #51 | 精确匹配✅ → 对齐成功✅ | "I don't have a temper."
[16:54:25] [DEBUG] 🔍   #52 | 精确匹配✅ → 对齐成功✅ | "You don't?"
[16:54:25] [DEBUG] 🔍   #53 | 精确匹配✅ → 对齐成功✅ | "No."
[16:54:25] [DEBUG] 🔍   #54 | 精确匹配✅ → 对齐成功✅ | "I thought you did-"
[16:54:25] [DEBUG] 🔍   #55 | 精确匹配✅ → 对齐成功✅ | "Aren't you gonna look fast?"
[16:54:25] [DEBUG] 🔍   #56 | 精确匹配✅ → 对齐成功✅ | "No."
[16:54:25] [DEBUG] 🔍   #57 | 精确匹配✅ → 对齐成功✅ | "No."
[16:54:25] [DEBUG] 🔍   #58 | 精确匹配✅ → 对齐成功✅ | "Hm."
[16:54:25] [DEBUG] 🔍   #59 | 精确匹配✅ → 对齐成功✅ | "I don't imagine anything frigh..."
[16:54:25] [DEBUG] 🔍   #60 | 精确匹配✅ → 对齐成功✅ | "No."
[16:54:25] [DEBUG] 🔍   #61 | 精确匹配✅ → 对齐成功✅ | "Not much, not much."
[16:54:25] [DEBUG] 🔍   #62 | 精确匹配✅ → 对齐成功✅ | "No."
[16:54:25] [DEBUG] 🔍   #63 | 精确匹配✅ → 对齐成功✅ | "No, seriously."
[16:54:25] [DEBUG] 🔍   #64 | 精确匹配✅ → 对齐成功✅ | "Very little."
[16:54:25] ℹ️ API2: 成功对齐 64 个文本分段
[16:54:25] ℹ️ 处理 API2 的字幕条目...
[16:54:25] [DEBUG] 🔍 开始处理 64 个字幕条目的时间间隔分割...
[16:54:25] ℹ️ 时间间隔分割完成：64 个条目 → 68 个条目
[16:54:25] [DEBUG] 🔍 过滤短时长条目: '. . . . .' (00:00:50,059 --> 00:00:50,059, 持续时间: 0.000s)
[16:54:25] [DEBUG] 🔍 过滤短时长条目: 'I' (00:04:14,639 --> 00:04:14,660, 持续时间: 0.021s)
[16:54:25] [DEBUG] 🔍 过滤短时长条目: 'No.' (00:04:38,759 --> 00:04:38,779, 持续时间: 0.020s)
[16:54:25] ℹ️ 短时长过滤完成：68 个条目 → 65 个条目（过滤掉 3 个短时长条目）
[16:54:26] ℹ️ 正在裁剪字幕静音区域...
[16:54:26] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:54:26] [DEBUG] 🔍 第一轮裁剪: 处理65个, 去除静音12.697秒
[16:54:26] [DEBUG] 🔍 开始第二轮静音裁剪...
[16:54:26] [DEBUG] 🔍 第二轮裁剪: 处理65个条目
[16:54:26] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:54:26] [DEBUG] 🔍 第二轮-开始时间: 在100ms偏移找到42个有效调整，剩余23个条目
[16:54:26] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:54:26] [DEBUG] 🔍 第二轮-开始时间: 在200ms偏移找到6个有效调整，剩余17个条目
[16:54:26] [DEBUG] 🔍 第二轮-开始时间: 总计处理48个条目，17个条目保持原样
[16:54:26] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:54:26] [DEBUG] 🔍 第二轮-结束时间: 在100ms偏移找到1个有效调整，剩余64个条目
[16:54:26] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:54:26] [DEBUG] 🔍 第二轮-结束时间: 200ms偏移无有效调整，剩余64个条目
[16:54:26] [DEBUG] 🔍 第二轮-结束时间: 总计处理1个条目，64个条目保持原样
[16:54:26] [DEBUG] 🔍 第二轮裁剪: 处理65个, 总计调整18个条目
[16:54:26] [DEBUG] 🔍 开始第三轮静音裁剪...
[16:54:26] [DEBUG] 🔍 第三轮裁剪: 处理65个条目
[16:54:26] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:54:26] [DEBUG] 🔍 第三轮-开始时间: 100ms偏移无有效调整，剩余65个条目
[16:54:26] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:54:26] [DEBUG] 🔍 第三轮-开始时间: 在200ms偏移找到1个有效调整，剩余64个条目
[16:54:26] [DEBUG] 🔍 第三轮-开始时间: 总计处理1个条目，64个条目保持原样
[16:54:26] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:54:26] [DEBUG] 🔍 第三轮-结束时间: 在100ms偏移找到48个有效调整，剩余17个条目
[16:54:26] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:54:26] [DEBUG] 🔍 第三轮-结束时间: 200ms偏移无有效调整，剩余17个条目
[16:54:26] [DEBUG] 🔍 第三轮-结束时间: 总计处理48个条目，17个条目保持原样
[16:54:26] [DEBUG] 🔍 第三轮裁剪: 处理65个, 总计调整1个条目
[16:54:26] ℹ️ 标准扩充: 增加显示 19.644 秒
[16:54:26] [DEBUG] 🔍 标准扩充详情: 完全23个, 部分42个
[16:54:26] [DEBUG] 🔍 解决扩充冲突: 28个
[16:54:26] ℹ️ 智能扩充: 处理22个短字幕, 增加显示 2.457 秒
[16:54:26] [DEBUG] 🔍 智能扩充详情: 完全12个, 部分10个, 目标1000ms
[16:54:26] ℹ️ 桥接处理: 填补间隔 0.757 秒
[16:54:26] [DEBUG] 🔍 桥接详情: 处理7个间隔, 阈值300ms
[16:54:26] ℹ️ API2: 字幕生成完成 - trimmed_Honey Baby, Honey Baby (1974)_part10_00-45-50-ElevenLabs-API2.srt
[16:54:26] ℹ️ 字幕生成完成，共生成 1 个字幕文件
[16:54:26] ℹ️ 文本处理统计:
[16:54:26] ℹ️   总LLM分段数: 64
[16:54:26] ℹ️   第一轮精确匹配: 64/64 (100.0%)
[16:54:26] ℹ️   第二轮锚点匹配: 0/64 (0.0%)
[16:54:26] ℹ️   文本匹配成功: 64/64 (100.0%)
[16:54:26] ℹ️   文本对齐成功: 64/64 (100.0%)
[16:54:26] [TaskFlow] 分段字幕生成成功: trimmed_Honey Baby, Honey Baby (1974)_part10_00-45-50 - ElevenLabs-API2
[16:54:26] [TaskFlow] 处理分段字幕 11/19: trimmed_Honey Baby, Honey Baby (1974)_part11_00-50-41
[16:54:26] [TaskFlow] 开始生成分段字幕: trimmed_Honey Baby, Honey Baby (1974)_part11_00-50-41 - ElevenLabs-API2
[16:54:26] ℹ️ 开始字幕生成: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part11_00-50-41-ElevenLabs-parsed.json
[16:54:26] [DEBUG] 🔍 提取服务名称，输入文件: trimmed_Honey Baby, Honey Baby (1974)_part11_00-50-41-ElevenLabs-parsed.json
[16:54:26] [DEBUG] 🔍 正则匹配成功，服务名称: ElevenLabs
[16:54:26] ℹ️ 检测到服务名称: ElevenLabs
[16:54:26] ℹ️ 检测已存在的字幕文件...
[16:54:26] [DEBUG] 🔍 🔍 扫描目录: ./projects\trimmed_Honey Baby, Honey Baby (1974)
[16:54:26] [DEBUG] 🔍 🔍 查找模式: trimmed_Honey Baby, Honey Baby (1974)_part11_00-50-41-ElevenLabs-*.srt
[16:54:26] [DEBUG] 🔍 🔍 找到 0 个匹配文件
[16:54:26] ℹ️ 未发现已存在字幕文件
[16:54:26] ℹ️ 全部启用的API: ['API2']
[16:54:26] ℹ️ 已存在字幕的API: []
[16:54:26] ℹ️ 需要处理的API: ['API2']
[16:54:26] ℹ️ 正在分析音频静音段...
[16:54:26] ℹ️ 正在检测音频静音段: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part11_00-50-41.mp3
[16:54:27] ℹ️ 检测到 230 个静音段
[16:54:27] ℹ️ 静音检测完成，缓存结果用于后续处理
[16:54:27] ℹ️ 开始处理 API2...
[16:54:27] ℹ️ API2: 启用分块处理
[16:54:27] ℹ️ API2: 分割为 2 个块
[16:54:27] ℹ️ API2: 正在处理第 1/2 块 (992 字符)
[16:54:27] [DEBUG] 🔍 API2: 开始API调用处理第 1 块...
[16:54:27] [DEBUG] 🔍 API2: 开始API调用...
[16:55:02] [DEBUG] 🔍 API2: API调用成功，返回 997 字符
[16:55:02] ℹ️ API2: 第 1 块处理成功 ✅ (耗时: 34.8秒, 差异: 5 字符)
[16:55:02] ℹ️ API2: 正在处理第 2/2 块 (90 字符)
[16:55:02] ℹ️ API2: 等待请求间隔 10.0 秒...
[16:55:12] [DEBUG] 🔍 API2: 开始API调用处理第 2 块...
[16:55:12] [DEBUG] 🔍 API2: 开始API调用...
[16:55:28] [DEBUG] 🔍 API2: API调用成功，返回 88 字符
[16:55:28] ℹ️ API2: 第 2 块处理成功 ✅ (耗时: 16.4秒, 差异: 2 字符)
[16:55:28] ℹ️ API2: 初始处理完成，成功 2/2 块
[16:55:28] ℹ️ API2: LLM调用成功，开始生成字幕...
[16:55:28] ℹ️ 对齐 API2 的文本分段...
[16:55:28] ℹ️ LLM分段调试数据已保存到: ./projects\trimmed_Honey Baby, Honey Baby (1974)\llm_segments_debug_20250803_165528.json
[16:55:28] ℹ️ 开始文本对齐处理（顺序约束），共 60 个片段
[16:55:28] [DEBUG] 🔍 片段处理进度:
[16:55:28] [DEBUG] 🔍   #1 | 精确匹配✅ → 对齐成功✅ | "I told you."
[16:55:28] [DEBUG] 🔍   #2 | 精确匹配✅ → 对齐成功✅ | "They don't so much drive cars ..."
[16:55:28] [DEBUG] 🔍   #3 | 精确匹配✅ → 对齐成功✅ | "I can see."
[16:55:28] [DEBUG] 🔍   #4 | 精确匹配✅ → 对齐成功✅ | "And if you're gonna live here,..."
[16:55:28] [DEBUG] 🔍   #5 | 精确匹配✅ → 对齐成功✅ | "Nerves of steel."
[16:55:28] [DEBUG] 🔍   #6 | 精确匹配✅ → 对齐成功✅ | "That's you, isn't it?"
[16:55:28] [DEBUG] 🔍   #7 | 精确匹配✅ → 对齐成功✅ | "Truly."
[16:55:28] [DEBUG] 🔍   #8 | 精确匹配✅ → 对齐成功✅ | "You know, most people when the..."
[16:55:28] [DEBUG] 🔍   #9 | 精确匹配✅ → 对齐成功✅ | "You mean you weren't afraid-"
[16:55:28] [DEBUG] 🔍   #10 | 精确匹配✅ → 对齐成功✅ | "... put them in the middle of ..."
[16:55:28] [DEBUG] 🔍   #11 | 精确匹配✅ → 对齐成功✅ | "... of the friz, that, uh,//yo..."
[16:55:28] [DEBUG] 🔍   #12 | 精确匹配✅ → 对齐成功✅ | "No, no."
[16:55:28] [DEBUG] 🔍   #13 | 精确匹配✅ → 对齐成功✅ | "All I did was to use my, my, m..."
[16:55:28] [DEBUG] 🔍   #14 | 精确匹配✅ → 对齐成功✅ | "I thought, you know, most peop..."
[16:55:28] [DEBUG] 🔍   #15 | 精确匹配✅ → 对齐成功✅ | "But under stress,//I am one of..."
[16:55:28] [DEBUG] 🔍   #16 | 精确匹配✅ → 对齐成功✅ | "I believe it."
[16:55:28] [DEBUG] 🔍   #17 | 精确匹配✅ → 对齐成功✅ | "I know myself very well."
[16:55:28] [DEBUG] 🔍   #18 | 精确匹配✅ → 对齐成功✅ | "What's the matter?"
[16:55:28] [DEBUG] 🔍   #19 | 精确匹配✅ → 对齐成功✅ | "Nothing."
[16:55:28] [DEBUG] 🔍   #20 | 精确匹配✅ → 对齐成功✅ | "What's wrong?"
[16:55:28] [DEBUG] 🔍   #21 | 精确匹配✅ → 对齐成功✅ | "Nothing."
[16:55:28] [DEBUG] 🔍   #22 | 精确匹配✅ → 对齐成功✅ | "No."
[16:55:28] [DEBUG] 🔍   #23 | 精确匹配✅ → 对齐成功✅ | "Oh."
[16:55:28] [DEBUG] 🔍   #24 | 精确匹配✅ → 对齐成功✅ | "Get out."
[16:55:28] [DEBUG] 🔍   #25 | 精确匹配✅ → 对齐成功✅ | "Get out."
[16:55:28] [DEBUG] 🔍   #26 | 精确匹配✅ → 对齐成功✅ | "Don't tell on me."
[16:55:28] [DEBUG] 🔍   #27 | 精确匹配✅ → 对齐成功✅ | "Oh."
[16:55:28] [DEBUG] 🔍   #28 | 精确匹配✅ → 对齐成功✅ | "Oh."
[16:55:28] [DEBUG] 🔍   #29 | 精确匹配✅ → 对齐成功✅ | "Oh."
[16:55:28] [DEBUG] 🔍   #30 | 精确匹配✅ → 对齐成功✅ | "Take this gun."
[16:55:28] [DEBUG] 🔍   #31 | 精确匹配✅ → 对齐成功✅ | "And why do I have-"
[16:55:28] [DEBUG] 🔍   #32 | 精确匹配✅ → 对齐成功✅ | "Oh, no. . Oh, no."
[16:55:28] [DEBUG] 🔍   #33 | 精确匹配✅ → 对齐成功✅ | "What?"
[16:55:28] [DEBUG] 🔍   #34 | 精确匹配✅ → 对齐成功✅ | "What's going on?"
[16:55:28] [DEBUG] 🔍   #35 | 精确匹配✅ → 对齐成功✅ | "I gotta take off my shoes."
[16:55:28] [DEBUG] 🔍   #36 | 精确匹配✅ → 对齐成功✅ | "I can't-"
[16:55:28] [DEBUG] 🔍   #37 | 精确匹配✅ → 对齐成功✅ | "Oh, goddamn it."
[16:55:28] [DEBUG] 🔍   #38 | 精确匹配✅ → 对齐成功✅ | "Now?"
[16:55:29] [DEBUG] 🔍   #39 | 精确匹配✅ → 对齐成功✅ | "Yes."
[16:55:29] [DEBUG] 🔍   #40 | 精确匹配✅ → 对齐成功✅ | "Oh."
[16:55:29] [DEBUG] 🔍   #41 | 精确匹配✅ → 对齐失败❌ | "Oh."
[16:55:29] [DEBUG] 🔍   #42 | 精确匹配✅ → 对齐失败❌ | "Oh."
[16:55:29] [DEBUG] 🔍   #43 | 精确匹配✅ → 对齐成功✅ | "Now, listen."
[16:55:29] [DEBUG] 🔍   #44 | 精确匹配✅ → 对齐成功✅ | "Yes?"
[16:55:29] [DEBUG] 🔍   #45 | 精确匹配✅ → 对齐成功✅ | "Look here, now."
[16:55:29] [DEBUG] 🔍   #46 | 精确匹配✅ → 对齐成功✅ | "All right."
[16:55:29] [DEBUG] 🔍   #47 | 精确匹配✅ → 对齐成功✅ | "Shh."
[16:55:29] [DEBUG] 🔍   #48 | 精确匹配✅ → 对齐成功✅ | "They're coming."
[16:55:29] [DEBUG] 🔍   #49 | 精确匹配✅ → 对齐成功✅ | "All right."
[16:55:29] [DEBUG] 🔍   #50 | 精确匹配✅ → 对齐成功✅ | "Now, look."
[16:55:29] [DEBUG] 🔍   #51 | 精确匹配✅ → 对齐成功✅ | "If they get too close, use tha..."
[16:55:29] [DEBUG] 🔍   #52 | 精确匹配✅ → 对齐成功✅ | "The gun?"
[16:55:29] [DEBUG] 🔍   #53 | 精确匹配✅ → 对齐成功✅ | "All right?"
[16:55:29] [DEBUG] 🔍   #54 | 精确匹配✅ → 对齐成功✅ | "Yeah."
[16:55:29] [DEBUG] 🔍   #55 | 精确匹配✅ → 对齐成功✅ | "I'm gonna make my way over her..."
[16:55:29] [DEBUG] 🔍   #56 | 精确匹配✅ → 对齐成功✅ | "You know how to use-"
[16:55:29] [DEBUG] 🔍   #57 | 精确匹配✅ → 对齐成功✅ | "Got it?"
[16:55:29] [DEBUG] 🔍   #58 | 精确匹配✅ → 对齐成功✅ | "Yeah."
[16:55:29] [DEBUG] 🔍   #59 | 精确匹配✅ → 对齐成功✅ | "I've learned how to use it thr..."
[16:55:29] [DEBUG] 🔍   #60 | 精确匹配✅ → 对齐成功✅ | "Shoot! Shoot"
[16:55:29] ℹ️ API2: 成功对齐 58 个文本分段
[16:55:29] ℹ️ 处理 API2 的字幕条目...
[16:55:29] [DEBUG] 🔍 开始处理 58 个字幕条目的时间间隔分割...
[16:55:29] ℹ️ 时间间隔分割完成：58 个条目 → 58 个条目
[16:55:29] [DEBUG] 🔍 过滤短时长条目: 'Oh.' (00:01:11,680 --> 00:01:11,699, 持续时间: 0.019s)
[16:55:29] [DEBUG] 🔍 过滤短时长条目: 'Oh.' (00:02:24,300 --> 00:02:24,320, 持续时间: 0.020s)
[16:55:29] [DEBUG] 🔍 过滤短时长条目: 'Yeah.' (00:02:37,500 --> 00:02:37,639, 持续时间: 0.139s)
[16:55:29] [DEBUG] 🔍 过滤短时长条目: 'Yeah.' (00:02:39,580 --> 00:02:39,720, 持续时间: 0.140s)
[16:55:29] ℹ️ 短时长过滤完成：58 个条目 → 54 个条目（过滤掉 4 个短时长条目）
[16:55:29] ℹ️ 正在裁剪字幕静音区域...
[16:55:29] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:55:29] [DEBUG] 🔍 第一轮裁剪: 处理54个, 去除静音9.457秒
[16:55:29] [DEBUG] 🔍 开始第二轮静音裁剪...
[16:55:29] [DEBUG] 🔍 第二轮裁剪: 处理54个条目
[16:55:29] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:55:29] [DEBUG] 🔍 第二轮-开始时间: 在100ms偏移找到22个有效调整，剩余32个条目
[16:55:29] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:55:29] [DEBUG] 🔍 第二轮-开始时间: 在200ms偏移找到1个有效调整，剩余31个条目
[16:55:29] [DEBUG] 🔍 第二轮-开始时间: 总计处理23个条目，31个条目保持原样
[16:55:29] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:55:29] [DEBUG] 🔍 第二轮-结束时间: 100ms偏移无有效调整，剩余54个条目
[16:55:29] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:55:29] [DEBUG] 🔍 第二轮-结束时间: 200ms偏移无有效调整，剩余54个条目
[16:55:29] [DEBUG] 🔍 第二轮-结束时间: 总计处理0个条目，54个条目保持原样
[16:55:29] [DEBUG] 🔍 第二轮裁剪: 处理54个, 总计调整5个条目
[16:55:29] [DEBUG] 🔍 开始第三轮静音裁剪...
[16:55:29] [DEBUG] 🔍 第三轮裁剪: 处理54个条目
[16:55:29] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:55:29] [DEBUG] 🔍 第三轮-开始时间: 在100ms偏移找到1个有效调整，剩余53个条目
[16:55:29] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:55:29] [DEBUG] 🔍 第三轮-开始时间: 200ms偏移无有效调整，剩余53个条目
[16:55:29] [DEBUG] 🔍 第三轮-开始时间: 总计处理1个条目，53个条目保持原样
[16:55:29] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:55:29] [DEBUG] 🔍 第三轮-结束时间: 在100ms偏移找到22个有效调整，剩余32个条目
[16:55:29] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:55:29] [DEBUG] 🔍 第三轮-结束时间: 在200ms偏移找到2个有效调整，剩余30个条目
[16:55:29] [DEBUG] 🔍 第三轮-结束时间: 总计处理24个条目，30个条目保持原样
[16:55:29] [DEBUG] 🔍 第三轮裁剪: 处理54个, 总计调整3个条目
[16:55:29] ℹ️ 标准扩充: 增加显示 12.933 秒
[16:55:29] [DEBUG] 🔍 标准扩充详情: 完全14个, 部分35个
[16:55:29] [DEBUG] 🔍 解决扩充冲突: 28个
[16:55:29] ℹ️ 智能扩充: 处理32个短字幕, 增加显示 4.367 秒
[16:55:29] [DEBUG] 🔍 智能扩充详情: 完全15个, 部分17个, 目标1000ms
[16:55:29] ℹ️ 桥接处理: 填补间隔 0.596 秒
[16:55:29] [DEBUG] 🔍 桥接详情: 处理5个间隔, 阈值300ms
[16:55:29] ℹ️ API2: 字幕生成完成 - trimmed_Honey Baby, Honey Baby (1974)_part11_00-50-41-ElevenLabs-API2.srt
[16:55:29] ℹ️ 字幕生成完成，共生成 1 个字幕文件
[16:55:29] ℹ️ 文本处理统计:
[16:55:29] ℹ️   总LLM分段数: 60
[16:55:29] ℹ️   第一轮精确匹配: 60/60 (100.0%)
[16:55:29] ℹ️   第二轮锚点匹配: 0/60 (0.0%)
[16:55:29] ℹ️   文本匹配成功: 60/60 (100.0%)
[16:55:29] ℹ️   文本对齐成功: 58/60 (96.7%)
[16:55:30] [TaskFlow] 分段字幕生成成功: trimmed_Honey Baby, Honey Baby (1974)_part11_00-50-41 - ElevenLabs-API2
[16:55:30] [TaskFlow] 处理分段字幕 12/19: trimmed_Honey Baby, Honey Baby (1974)_part12_00-53-55
[16:55:30] [TaskFlow] 开始生成分段字幕: trimmed_Honey Baby, Honey Baby (1974)_part12_00-53-55 - ElevenLabs-API2
[16:55:30] ℹ️ 开始字幕生成: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part12_00-53-55-ElevenLabs-parsed.json
[16:55:30] [DEBUG] 🔍 提取服务名称，输入文件: trimmed_Honey Baby, Honey Baby (1974)_part12_00-53-55-ElevenLabs-parsed.json
[16:55:30] [DEBUG] 🔍 正则匹配成功，服务名称: ElevenLabs
[16:55:30] ℹ️ 检测到服务名称: ElevenLabs
[16:55:30] ℹ️ 检测已存在的字幕文件...
[16:55:30] [DEBUG] 🔍 🔍 扫描目录: ./projects\trimmed_Honey Baby, Honey Baby (1974)
[16:55:30] [DEBUG] 🔍 🔍 查找模式: trimmed_Honey Baby, Honey Baby (1974)_part12_00-53-55-ElevenLabs-*.srt
[16:55:30] [DEBUG] 🔍 🔍 找到 0 个匹配文件
[16:55:30] ℹ️ 未发现已存在字幕文件
[16:55:30] ℹ️ 全部启用的API: ['API2']
[16:55:30] ℹ️ 已存在字幕的API: []
[16:55:30] ℹ️ 需要处理的API: ['API2']
[16:55:30] ℹ️ 正在分析音频静音段...
[16:55:30] ℹ️ 正在检测音频静音段: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part12_00-53-55.mp3
[16:55:30] ℹ️ 检测到 185 个静音段
[16:55:30] ℹ️ 静音检测完成，缓存结果用于后续处理
[16:55:30] ℹ️ 开始处理 API2...
[16:55:30] ℹ️ API2: 直接处理
[16:55:30] [DEBUG] 🔍 API2: 开始API调用...
[16:55:59] [DEBUG] 🔍 API2: API调用成功，返回 979 字符
[16:55:59] ℹ️ API2: LLM调用成功，开始生成字幕...
[16:55:59] ℹ️ 对齐 API2 的文本分段...
[16:55:59] ℹ️ LLM分段调试数据已保存到: ./projects\trimmed_Honey Baby, Honey Baby (1974)\llm_segments_debug_20250803_165559.json
[16:55:59] ℹ️ 开始文本对齐处理（顺序约束），共 32 个片段
[16:55:59] [DEBUG] 🔍 片段处理进度:
[16:55:59] [DEBUG] 🔍   #1 | 精确匹配✅ → 对齐成功✅ | "You wasn't born in Harlem for ..."
[16:55:59] [DEBUG] 🔍   #2 | 精确匹配✅ → 对齐成功✅ | "Ugh."
[16:56:00] [DEBUG] 🔍   #3 | 精确匹配✅ → 对齐成功✅ | "Ah."
[16:56:00] [DEBUG] 🔍   #4 | 精确匹配✅ → 对齐成功✅ | "Don't you have anything beside..."
[16:56:00] [DEBUG] 🔍   #5 | 精确匹配✅ → 对齐成功✅ | "All right."
[16:56:00] [DEBUG] 🔍   #6 | 精确匹配✅ → 对齐成功✅ | "That's it."
[16:56:00] [DEBUG] 🔍   #7 | 精确匹配✅ → 对齐成功✅ | "What about the car?"
[16:56:00] [DEBUG] 🔍   #8 | 精确匹配✅ → 对齐成功✅ | "I've asked Pepe to take care o..."
[16:56:00] [DEBUG] 🔍   #9 | 精确匹配✅ → 对齐成功✅ | "Liv?"
[16:56:00] [DEBUG] 🔍   #10 | 精确匹配✅ → 对齐成功✅ | "Mm."
[16:56:00] [DEBUG] 🔍   #11 | 精确匹配✅ → 对齐成功✅ | "Why were those men trying to k..."
[16:56:00] [DEBUG] 🔍   #12 | 精确匹配✅ → 对齐成功✅ | "I don't think they were trying..."
[16:56:00] [DEBUG] 🔍   #13 | 精确匹配✅ → 对齐成功✅ | "I think they were after you."
[16:56:00] [DEBUG] 🔍   #14 | 精确匹配✅ → 对齐成功✅ | "But why?"
[16:56:00] [DEBUG] 🔍   #15 | 精确匹配✅ → 对齐成功✅ | "That Chinese woman."
[16:56:00] [DEBUG] 🔍   #16 | 精确匹配✅ → 对齐成功✅ | "Chinese woman?"
[16:56:00] [DEBUG] 🔍   #17 | 精确匹配✅ → 对齐成功✅ | "I have nothing to do with her."
[16:56:00] [DEBUG] 🔍   #18 | 精确匹配✅ → 对齐成功✅ | "What would they want with me?"
[16:56:00] [DEBUG] 🔍   #19 | 精确匹配✅ → 对齐成功✅ | "I don't know, but I know we'll..."
[16:56:00] [DEBUG] 🔍   #20 | 精确匹配✅ → 对齐成功✅ | "We're gonna take Pepe's boat//..."
[16:56:00] [DEBUG] 🔍   #21 | 精确匹配✅ → 对齐成功✅ | "just in case those mothers//ar..."
[16:56:00] [DEBUG] 🔍   #22 | 精确匹配✅ → 对齐成功✅ | "Look, look, look, Liv."
[16:56:00] [DEBUG] 🔍   #23 | 精确匹配✅ → 对齐成功✅ | "I, I, I don't wanna get involv..."
[16:56:00] [DEBUG] 🔍   #24 | 精确匹配✅ → 对齐成功✅ | "I know what you're gonna say,/..."
[16:56:00] [DEBUG] 🔍   #25 | 精确匹配✅ → 对齐成功✅ | "But you could get hurt."
[16:56:00] [DEBUG] 🔍   #26 | 精确匹配✅ → 对齐成功✅ | "You think I'm gonna stand here..."
[16:56:00] [DEBUG] 🔍   #27 | 精确匹配✅ → 对齐成功✅ | "And you're gonna stand by and ..."
[16:56:00] [DEBUG] 🔍   #28 | 精确匹配✅ → 对齐成功✅ | "You're strong, but you're not ..."
[16:56:00] [DEBUG] 🔍   #29 | 精确匹配✅ → 对齐成功✅ | "You can't be."
[16:56:00] [DEBUG] 🔍   #30 | 精确匹配✅ → 对齐成功✅ | "I'm not gonna stand here and l..."
[16:56:00] [DEBUG] 🔍   #31 | 精确匹配✅ → 对齐成功✅ | "So, what are you gonna do?"
[16:56:00] [DEBUG] 🔍   #32 | 精确匹配✅ → 对齐成功✅ | "I'll think of something."
[16:56:00] ℹ️ API2: 成功对齐 32 个文本分段
[16:56:00] ℹ️ 处理 API2 的字幕条目...
[16:56:00] [DEBUG] 🔍 开始处理 32 个字幕条目的时间间隔分割...
[16:56:00] ℹ️ 时间间隔分割完成：32 个条目 → 36 个条目
[16:56:00] [DEBUG] 🔍 过滤短时长条目: 'Ugh.' (00:01:06,739 --> 00:01:06,779, 持续时间: 0.040s)
[16:56:00] [DEBUG] 🔍 过滤短时长条目: 'I' (00:01:49,479 --> 00:01:49,499, 持续时间: 0.020s)
[16:56:00] [DEBUG] 🔍 过滤短时长条目: 'I'll think of something.' (00:04:20,940 --> 00:04:20,979, 持续时间: 0.039s)
[16:56:00] ℹ️ 短时长过滤完成：36 个条目 → 33 个条目（过滤掉 3 个短时长条目）
[16:56:00] ℹ️ 正在裁剪字幕静音区域...
[16:56:00] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:56:00] [DEBUG] 🔍 第一轮裁剪: 处理33个, 去除静音25.354秒
[16:56:00] [DEBUG] 🔍 开始第二轮静音裁剪...
[16:56:00] [DEBUG] 🔍 第二轮裁剪: 处理33个条目
[16:56:00] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:56:00] [DEBUG] 🔍 第二轮-开始时间: 在100ms偏移找到23个有效调整，剩余10个条目
[16:56:00] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:56:00] [DEBUG] 🔍 第二轮-开始时间: 200ms偏移无有效调整，剩余10个条目
[16:56:00] [DEBUG] 🔍 第二轮-开始时间: 总计处理23个条目，10个条目保持原样
[16:56:00] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:56:00] [DEBUG] 🔍 第二轮-结束时间: 在100ms偏移找到1个有效调整，剩余32个条目
[16:56:00] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:56:00] [DEBUG] 🔍 第二轮-结束时间: 在200ms偏移找到1个有效调整，剩余31个条目
[16:56:00] [DEBUG] 🔍 第二轮-结束时间: 总计处理2个条目，31个条目保持原样
[16:56:00] [DEBUG] 🔍 第二轮裁剪: 处理33个, 总计调整6个条目
[16:56:00] [DEBUG] 🔍 开始第三轮静音裁剪...
[16:56:00] [DEBUG] 🔍 第三轮裁剪: 处理33个条目
[16:56:00] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:56:00] [DEBUG] 🔍 第三轮-开始时间: 100ms偏移无有效调整，剩余33个条目
[16:56:00] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:56:00] [DEBUG] 🔍 第三轮-开始时间: 200ms偏移无有效调整，剩余33个条目
[16:56:00] [DEBUG] 🔍 第三轮-开始时间: 总计处理0个条目，33个条目保持原样
[16:56:00] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:56:00] [DEBUG] 🔍 第三轮-结束时间: 在100ms偏移找到24个有效调整，剩余9个条目
[16:56:00] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:56:00] [DEBUG] 🔍 第三轮-结束时间: 200ms偏移无有效调整，剩余9个条目
[16:56:00] [DEBUG] 🔍 第三轮-结束时间: 总计处理24个条目，9个条目保持原样
[16:56:00] [DEBUG] 🔍 第三轮裁剪: 处理33个, 总计调整0个条目
[16:56:00] ℹ️ 标准扩充: 增加显示 10.955 秒
[16:56:00] [DEBUG] 🔍 标准扩充详情: 完全20个, 部分12个
[16:56:00] [DEBUG] 🔍 解决扩充冲突: 8个
[16:56:00] ℹ️ 智能扩充: 处理10个短字幕, 增加显示 2.667 秒
[16:56:01] [DEBUG] 🔍 智能扩充详情: 完全9个, 部分1个, 目标1000ms
[16:56:01] ℹ️ 桥接处理: 填补间隔 0.210 秒
[16:56:01] [DEBUG] 🔍 桥接详情: 处理1个间隔, 阈值300ms
[16:56:01] ℹ️ API2: 字幕生成完成 - trimmed_Honey Baby, Honey Baby (1974)_part12_00-53-55-ElevenLabs-API2.srt
[16:56:01] ℹ️ 字幕生成完成，共生成 1 个字幕文件
[16:56:01] ℹ️ 文本处理统计:
[16:56:01] ℹ️   总LLM分段数: 32
[16:56:01] ℹ️   第一轮精确匹配: 32/32 (100.0%)
[16:56:01] ℹ️   第二轮锚点匹配: 0/32 (0.0%)
[16:56:01] ℹ️   文本匹配成功: 32/32 (100.0%)
[16:56:01] ℹ️   文本对齐成功: 32/32 (100.0%)
[16:56:01] [TaskFlow] 分段字幕生成成功: trimmed_Honey Baby, Honey Baby (1974)_part12_00-53-55 - ElevenLabs-API2
[16:56:01] [TaskFlow] 处理分段字幕 13/19: trimmed_Honey Baby, Honey Baby (1974)_part13_00-58-16
[16:56:01] [TaskFlow] 开始生成分段字幕: trimmed_Honey Baby, Honey Baby (1974)_part13_00-58-16 - ElevenLabs-API2
[16:56:01] ℹ️ 开始字幕生成: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part13_00-58-16-ElevenLabs-parsed.json
[16:56:01] [DEBUG] 🔍 提取服务名称，输入文件: trimmed_Honey Baby, Honey Baby (1974)_part13_00-58-16-ElevenLabs-parsed.json
[16:56:01] [DEBUG] 🔍 正则匹配成功，服务名称: ElevenLabs
[16:56:01] ℹ️ 检测到服务名称: ElevenLabs
[16:56:01] ℹ️ 检测已存在的字幕文件...
[16:56:01] [DEBUG] 🔍 🔍 扫描目录: ./projects\trimmed_Honey Baby, Honey Baby (1974)
[16:56:01] [DEBUG] 🔍 🔍 查找模式: trimmed_Honey Baby, Honey Baby (1974)_part13_00-58-16-ElevenLabs-*.srt
[16:56:01] [DEBUG] 🔍 🔍 找到 0 个匹配文件
[16:56:01] ℹ️ 未发现已存在字幕文件
[16:56:01] ℹ️ 全部启用的API: ['API2']
[16:56:01] ℹ️ 已存在字幕的API: []
[16:56:01] ℹ️ 需要处理的API: ['API2']
[16:56:01] ℹ️ 正在分析音频静音段...
[16:56:01] ℹ️ 正在检测音频静音段: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part13_00-58-16.mp3
[16:56:01] ℹ️ 检测到 313 个静音段
[16:56:01] ℹ️ 静音检测完成，缓存结果用于后续处理
[16:56:01] ℹ️ 开始处理 API2...
[16:56:02] ℹ️ API2: 启用分块处理
[16:56:02] ℹ️ API2: 分割为 2 个块
[16:56:02] ℹ️ API2: 正在处理第 1/2 块 (990 字符)
[16:56:02] [DEBUG] 🔍 API2: 开始API调用处理第 1 块...
[16:56:02] [DEBUG] 🔍 API2: 开始API调用...
[16:56:34] [DEBUG] 🔍 API2: API调用成功，返回 997 字符
[16:56:34] ℹ️ API2: 第 1 块处理成功 ✅ (耗时: 32.8秒, 差异: 7 字符)
[16:56:34] ℹ️ API2: 正在处理第 2/2 块 (682 字符)
[16:56:34] ℹ️ API2: 等待请求间隔 10.0 秒...
[16:56:44] [DEBUG] 🔍 API2: 开始API调用处理第 2 块...
[16:56:44] [DEBUG] 🔍 API2: 开始API调用...
[16:57:15] [DEBUG] 🔍 API2: API调用成功，返回 685 字符
[16:57:15] ℹ️ API2: 第 2 块处理成功 ✅ (耗时: 31.0秒, 差异: 3 字符)
[16:57:15] ℹ️ API2: 初始处理完成，成功 2/2 块
[16:57:15] ℹ️ API2: LLM调用成功，开始生成字幕...
[16:57:15] ℹ️ 对齐 API2 的文本分段...
[16:57:15] ℹ️ LLM分段调试数据已保存到: ./projects\trimmed_Honey Baby, Honey Baby (1974)\llm_segments_debug_20250803_165715.json
[16:57:16] ℹ️ 开始文本对齐处理（顺序约束），共 55 个片段
[16:57:16] [DEBUG] 🔍 片段处理进度:
[16:57:16] [DEBUG] 🔍   #1 | 精确匹配✅ → 对齐成功✅ | "Do you know who this is?"
[16:57:16] [DEBUG] 🔍   #2 | 精确匹配✅ → 对齐成功✅ | "This is Quasi Lasufi."
[16:57:16] [DEBUG] 🔍   #3 | 精确匹配✅ → 对齐成功✅ | "I read about him in the papers..."
[16:57:16] [DEBUG] 🔍   #4 | 精确匹配✅ → 对齐成功✅ | "He led his country to independ..."
[16:57:16] [DEBUG] 🔍   #5 | 精确匹配✅ → 对齐成功✅ | "Then his enemies, his new enem..."
[16:57:16] [DEBUG] 🔍   #6 | 精确匹配✅ → 对齐成功✅ | "blamed and executed an innocen..."
[16:57:16] [DEBUG] 🔍   #7 | 精确匹配✅ → 对齐成功✅ | "and displayed his body//as if ..."
[16:57:16] [DEBUG] 🔍   #8 | 精确匹配✅ → 对齐成功✅ | "Well, now we have him."
[16:57:16] [DEBUG] 🔍   #9 | 精确匹配✅ → 对齐成功✅ | "No, Lev. You have him."
[16:57:16] [DEBUG] 🔍   #10 | 精确匹配✅ → 对齐成功✅ | "So, Harry knew what was in her..."
[16:57:16] [DEBUG] 🔍   #11 | 精确匹配✅ → 对齐成功✅ | "Harry? Yeah, that snake."
[16:57:16] [DEBUG] 🔍   #12 | 精确匹配✅ → 对齐成功✅ | "The one who asked me to do thi..."
[16:57:16] [DEBUG] 🔍   #13 | 精确匹配✅ → 对齐成功✅ | "What are we gonna do to you?"
[16:57:16] [DEBUG] 🔍   #14 | 精确匹配✅ → 对齐成功✅ | "That container weighs about ha..."
[16:57:16] [DEBUG] 🔍   #15 | 精确匹配✅ → 对齐成功✅ | "We're gonna have to make your ..."
[16:57:16] [DEBUG] 🔍   #16 | 精确匹配✅ → 对齐成功✅ | "You know I love caviar."
[16:57:16] [DEBUG] 🔍   #17 | 精确匹配✅ → 对齐成功✅ | "And you know I love salmon."
[16:57:16] [DEBUG] 🔍   #18 | 精确匹配✅ → 对齐成功✅ | "Why don't you sing the lament/..."
[16:57:16] [DEBUG] 🔍   #19 | 精确匹配✅ → 对齐成功✅ | "Why don't you stop sending dri..."
[16:57:16] [DEBUG] 🔍   #20 | 精确匹配✅ → 对齐成功✅ | "This is a saloon, not a charit..."
[16:57:16] [DEBUG] 🔍   #21 | 精确匹配✅ → 对齐成功✅ | "Angelo, Angelo."
[16:57:16] [DEBUG] 🔍   #22 | 精确匹配✅ → 对齐成功✅ | "You see that that man over the..."
[16:57:16] [DEBUG] 🔍   #23 | 精确匹配✅ → 对齐成功✅ | "Tu comprends?"
[16:57:16] [DEBUG] 🔍   #24 | 精确匹配✅ → 对齐成功✅ | "Si, signore."
[16:57:16] [DEBUG] 🔍   #25 | 精确匹配✅ → 对齐成功✅ | "Okay."
[16:57:16] [DEBUG] 🔍   #26 | 精确匹配✅ → 对齐成功✅ | "You people have exploited me a..."
[16:57:16] [DEBUG] 🔍   #27 | 精确匹配✅ → 对齐成功✅ | "I like to see you do this."
[16:57:16] [DEBUG] 🔍   #28 | 精确匹配✅ → 对齐成功✅ | "You do it very well."
[16:57:16] [DEBUG] 🔍   #29 | 精确匹配✅ → 对齐成功✅ | "It's where I began,//little ni..."
[16:57:16] [DEBUG] 🔍   #30 | 精确匹配✅ → 对齐成功✅ | "Oh, my God, Skippy."
[16:57:16] [DEBUG] 🔍   #31 | 精确匹配✅ → 对齐成功✅ | "What about him?"
[16:57:16] [DEBUG] 🔍   #32 | 精确匹配✅ → 对齐成功✅ | "I forgot all about Skippy."
[16:57:16] [DEBUG] 🔍   #33 | 精确匹配✅ → 对齐成功✅ | "I gotta find Skippy."
[16:57:16] [DEBUG] 🔍   #34 | 精确匹配✅ → 对齐成功✅ | "What if..."
[16:57:16] [DEBUG] 🔍   #35 | 精确匹配✅ → 对齐成功✅ | "I gotta find-"
[16:57:16] [DEBUG] 🔍   #36 | 精确匹配✅ → 对齐成功✅ | "All right, all right, all righ..."
[16:57:16] [DEBUG] 🔍   #37 | 精确匹配✅ → 对齐成功✅ | "Well, yeah."
[16:57:16] [DEBUG] 🔍   #38 | 精确匹配✅ → 对齐成功✅ | "Yeah, I need some help anyway."
[16:57:16] [DEBUG] 🔍   #39 | 精确匹配✅ → 对齐成功✅ | "Yeah, we can kill two birds//w..."
[16:57:16] [DEBUG] 🔍   #40 | 精确匹配✅ → 对齐成功✅ | "Right."
[16:57:16] [DEBUG] 🔍   #41 | 精确匹配✅ → 对齐成功✅ | "It's not the only place//that'..."
[16:57:16] [DEBUG] 🔍   #42 | 精确匹配✅ → 对齐成功✅ | "It reminds me of home."
[16:57:16] [DEBUG] 🔍   #43 | 精确匹配✅ → 对齐成功✅ | "Well, aren't you gonna come wi..."
[16:57:16] [DEBUG] 🔍   #44 | 精确匹配✅ → 对齐成功✅ | "Let's go-"
[16:57:16] [DEBUG] 🔍   #45 | 精确匹配✅ → 对齐成功✅ | "No, I don't wanna see Harry ye..."
[16:57:16] [DEBUG] 🔍   #46 | 精确匹配✅ → 对齐成功✅ | "And he's bound to be in there,..."
[16:57:16] [DEBUG] 🔍   #47 | 精确匹配✅ → 对齐成功✅ | "Bricktop?"
[16:57:16] [DEBUG] 🔍   #48 | 精确匹配✅ → 对齐成功✅ | "Yeah."
[16:57:16] [DEBUG] 🔍   #49 | 精确匹配✅ → 对齐成功✅ | "You can't miss him."
[16:57:16] [DEBUG] 🔍   #50 | 精确匹配✅ → 对齐成功✅ | "Okay."
[16:57:16] [DEBUG] 🔍   #51 | 精确匹配✅ → 对齐成功✅ | "Why don't you go?"
[16:57:16] [DEBUG] 🔍   #52 | 精确匹配✅ → 对齐成功✅ | "Mm-hmm."
[16:57:16] [DEBUG] 🔍   #53 | 精确匹配✅ → 对齐成功✅ | "I've been wrong for so long."
[16:57:16] [DEBUG] 🔍   #54 | 精确匹配✅ → 对齐成功✅ | "Still we just can't say goodby..."
[16:57:16] [DEBUG] 🔍   #55 | 精确匹配✅ → 对齐成功✅ | "No, we just can't say goodbye."
[16:57:16] ℹ️ API2: 成功对齐 55 个文本分段
[16:57:16] ℹ️ 处理 API2 的字幕条目...
[16:57:16] [DEBUG] 🔍 开始处理 55 个字幕条目的时间间隔分割...
[16:57:16] ℹ️ 时间间隔分割完成：55 个条目 → 60 个条目
[16:57:16] [DEBUG] 🔍 短时长过滤完成：60 个条目，无需过滤
[16:57:16] ℹ️ 正在裁剪字幕静音区域...
[16:57:16] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:57:16] [DEBUG] 🔍 第一轮裁剪: 处理60个, 去除静音21.542秒
[16:57:16] [DEBUG] 🔍 开始第二轮静音裁剪...
[16:57:16] [DEBUG] 🔍 第二轮裁剪: 处理60个条目
[16:57:16] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:57:16] [DEBUG] 🔍 第二轮-开始时间: 在100ms偏移找到49个有效调整，剩余11个条目
[16:57:17] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:57:17] [DEBUG] 🔍 第二轮-开始时间: 在200ms偏移找到1个有效调整，剩余10个条目
[16:57:17] [DEBUG] 🔍 第二轮-开始时间: 总计处理50个条目，10个条目保持原样
[16:57:17] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:57:17] [DEBUG] 🔍 第二轮-结束时间: 在100ms偏移找到3个有效调整，剩余57个条目
[16:57:17] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:57:17] [DEBUG] 🔍 第二轮-结束时间: 在200ms偏移找到1个有效调整，剩余56个条目
[16:57:17] [DEBUG] 🔍 第二轮-结束时间: 总计处理4个条目，56个条目保持原样
[16:57:17] [DEBUG] 🔍 第二轮裁剪: 处理60个, 总计调整15个条目
[16:57:17] [DEBUG] 🔍 开始第三轮静音裁剪...
[16:57:17] [DEBUG] 🔍 第三轮裁剪: 处理60个条目
[16:57:17] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:57:17] [DEBUG] 🔍 第三轮-开始时间: 在100ms偏移找到2个有效调整，剩余58个条目
[16:57:17] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:57:17] [DEBUG] 🔍 第三轮-开始时间: 200ms偏移无有效调整，剩余58个条目
[16:57:17] [DEBUG] 🔍 第三轮-开始时间: 总计处理2个条目，58个条目保持原样
[16:57:17] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:57:17] [DEBUG] 🔍 第三轮-结束时间: 在100ms偏移找到46个有效调整，剩余14个条目
[16:57:17] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:57:17] [DEBUG] 🔍 第三轮-结束时间: 在200ms偏移找到1个有效调整，剩余13个条目
[16:57:17] [DEBUG] 🔍 第三轮-结束时间: 总计处理47个条目，13个条目保持原样
[16:57:17] [DEBUG] 🔍 第三轮裁剪: 处理60个, 总计调整3个条目
[16:57:17] ℹ️ 标准扩充: 增加显示 19.998 秒
[16:57:17] [DEBUG] 🔍 标准扩充详情: 完全38个, 部分22个
[16:57:17] [DEBUG] 🔍 解决扩充冲突: 14个
[16:57:17] ℹ️ 智能扩充: 处理17个短字幕, 增加显示 3.861 秒
[16:57:17] [DEBUG] 🔍 智能扩充详情: 完全14个, 部分3个, 目标1000ms
[16:57:17] ℹ️ 桥接处理: 填补间隔 1.639 秒
[16:57:17] [DEBUG] 🔍 桥接详情: 处理11个间隔, 阈值300ms
[16:57:17] ℹ️ API2: 字幕生成完成 - trimmed_Honey Baby, Honey Baby (1974)_part13_00-58-16-ElevenLabs-API2.srt
[16:57:17] ℹ️ 字幕生成完成，共生成 1 个字幕文件
[16:57:17] ℹ️ 文本处理统计:
[16:57:17] ℹ️   总LLM分段数: 55
[16:57:17] ℹ️   第一轮精确匹配: 55/55 (100.0%)
[16:57:17] ℹ️   第二轮锚点匹配: 0/55 (0.0%)
[16:57:17] ℹ️   文本匹配成功: 55/55 (100.0%)
[16:57:17] ℹ️   文本对齐成功: 55/55 (100.0%)
[16:57:17] [TaskFlow] 分段字幕生成成功: trimmed_Honey Baby, Honey Baby (1974)_part13_00-58-16 - ElevenLabs-API2
[16:57:17] [TaskFlow] 处理分段字幕 14/19: trimmed_Honey Baby, Honey Baby (1974)_part14_01-05-07
[16:57:17] [TaskFlow] 开始生成分段字幕: trimmed_Honey Baby, Honey Baby (1974)_part14_01-05-07 - ElevenLabs-API2
[16:57:17] ℹ️ 开始字幕生成: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part14_01-05-07-ElevenLabs-parsed.json
[16:57:17] [DEBUG] 🔍 提取服务名称，输入文件: trimmed_Honey Baby, Honey Baby (1974)_part14_01-05-07-ElevenLabs-parsed.json
[16:57:17] [DEBUG] 🔍 正则匹配成功，服务名称: ElevenLabs
[16:57:17] ℹ️ 检测到服务名称: ElevenLabs
[16:57:17] ℹ️ 检测已存在的字幕文件...
[16:57:17] [DEBUG] 🔍 🔍 扫描目录: ./projects\trimmed_Honey Baby, Honey Baby (1974)
[16:57:17] [DEBUG] 🔍 🔍 查找模式: trimmed_Honey Baby, Honey Baby (1974)_part14_01-05-07-ElevenLabs-*.srt
[16:57:17] [DEBUG] 🔍 🔍 找到 0 个匹配文件
[16:57:17] ℹ️ 未发现已存在字幕文件
[16:57:17] ℹ️ 全部启用的API: ['API2']
[16:57:17] ℹ️ 已存在字幕的API: []
[16:57:17] ℹ️ 需要处理的API: ['API2']
[16:57:17] ℹ️ 正在分析音频静音段...
[16:57:17] ℹ️ 正在检测音频静音段: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part14_01-05-07.mp3
[16:57:18] ℹ️ 检测到 209 个静音段
[16:57:18] ℹ️ 静音检测完成，缓存结果用于后续处理
[16:57:18] ℹ️ 开始处理 API2...
[16:57:18] ℹ️ API2: 直接处理
[16:57:18] [DEBUG] 🔍 API2: 开始API调用...
[16:57:41] [DEBUG] 🔍 API2: API调用成功，返回 875 字符
[16:57:41] ℹ️ API2: LLM调用成功，开始生成字幕...
[16:57:41] ℹ️ 对齐 API2 的文本分段...
[16:57:41] ℹ️ LLM分段调试数据已保存到: ./projects\trimmed_Honey Baby, Honey Baby (1974)\llm_segments_debug_20250803_165741.json
[16:57:41] ℹ️ 开始文本对齐处理（顺序约束），共 46 个片段
[16:57:41] [DEBUG] 🔍 片段处理进度:
[16:57:41] [DEBUG] 🔍   #1 | 精确匹配✅ → 对齐成功✅ | "Who is she?"
[16:57:41] [DEBUG] 🔍   #2 | 精确匹配✅ → 对齐成功✅ | "I've never seen her//around he..."
[16:57:41] [DEBUG] 🔍   #3 | 精确匹配✅ → 对齐成功✅ | "He's a friend of Liv's."
[16:57:41] [DEBUG] 🔍   #4 | 精确匹配✅ → 对齐成功✅ | "No."
[16:57:41] [DEBUG] 🔍   #5 | 精确匹配✅ → 对齐成功✅ | "Okay, Skippy."
[16:57:41] [DEBUG] 🔍   #6 | 精确匹配✅ → 对齐成功✅ | "Now that I found you,//let's g..."
[16:57:41] [DEBUG] 🔍   #7 | 精确匹配✅ → 对齐成功✅ | "What you doing here?"
[16:57:41] [DEBUG] 🔍   #8 | 精确匹配✅ → 对齐成功✅ | "I don't mind what I'm doing he..."
[16:57:41] [DEBUG] 🔍   #9 | 精确匹配✅ → 对齐成功✅ | "Let's go."
[16:57:41] [DEBUG] 🔍   #10 | 精确匹配✅ → 对齐成功✅ | "Let's move it."
[16:57:41] [DEBUG] 🔍   #11 | 精确匹配✅ → 对齐成功✅ | "But-"
[16:57:41] [DEBUG] 🔍   #12 | 精确匹配✅ → 对齐成功✅ | "Let's just get..."
[16:57:41] [DEBUG] 🔍   #13 | 精确匹配✅ → 对齐成功✅ | "This is very important."
[16:57:41] [DEBUG] 🔍   #14 | 精确匹配✅ → 对齐成功✅ | "I'm not playing with you."
[16:57:41] [DEBUG] 🔍   #15 | 精确匹配✅ → 对齐成功✅ | "You're very important."
[16:57:41] [DEBUG] 🔍   #16 | 精确匹配✅ → 对齐成功✅ | "Let's move it."
[16:57:41] [DEBUG] 🔍   #17 | 精确匹配✅ → 对齐成功✅ | "But you mean..."
[16:57:41] [DEBUG] 🔍   #18 | 精确匹配✅ → 对齐成功✅ | "Listen, I got a girl here."
[16:57:41] [DEBUG] 🔍   #19 | 精确匹配✅ → 对齐成功✅ | "Tell her you'll be back."
[16:57:41] [DEBUG] 🔍   #20 | 精确匹配✅ → 对齐成功✅ | "Can't do that."
[16:57:41] [DEBUG] 🔍   #21 | 精确匹配✅ → 对齐成功✅ | "All right."
[16:57:41] [DEBUG] 🔍   #22 | 精确匹配✅ → 对齐成功✅ | "Hey, now."
[16:57:41] [DEBUG] 🔍   #23 | 精确匹配✅ → 对齐成功✅ | "That's my cousin."
[16:57:41] [DEBUG] 🔍   #24 | 精确匹配✅ → 对齐成功✅ | "Something very heavy went down..."
[16:57:41] [DEBUG] 🔍   #25 | 精确匹配✅ → 对齐成功✅ | "and she wants me to go with he..."
[16:57:41] [DEBUG] 🔍   #26 | 精确匹配✅ → 对齐成功✅ | "Look, I'll meet you//at your h..."
[16:57:41] [DEBUG] 🔍   #27 | 精确匹配✅ → 对齐成功✅ | "All right?"
[16:57:41] [DEBUG] 🔍   #28 | 精确匹配✅ → 对齐成功✅ | "Skippy."
[16:57:41] [DEBUG] 🔍   #29 | 精确匹配✅ → 对齐成功✅ | "All right."
[16:57:41] [DEBUG] 🔍   #30 | 精确匹配✅ → 对齐成功✅ | "I'll see you later."
[16:57:41] [DEBUG] 🔍   #31 | 精确匹配✅ → 对齐成功✅ | "Out, out, out, out, out."
[16:57:41] [DEBUG] 🔍   #32 | 精确匹配✅ → 对齐成功✅ | "Hmm."
[16:57:41] [DEBUG] 🔍   #33 | 精确匹配✅ → 对齐成功✅ | "Okay, hold it."
[16:57:41] [DEBUG] 🔍   #34 | 精确匹配✅ → 对齐成功✅ | "Okay."
[16:57:42] [DEBUG] 🔍   #35 | 精确匹配✅ → 对齐成功✅ | "Y'all can split now."
[16:57:42] [DEBUG] 🔍   #36 | 精确匹配✅ → 对齐成功✅ | "Now, let's get going//before w..."
[16:57:42] [DEBUG] 🔍   #37 | 精确匹配✅ → 对齐成功✅ | "Later on."
[16:57:42] [DEBUG] 🔍   #38 | 精确匹配✅ → 对齐成功✅ | "Right on, baby."
[16:57:42] [DEBUG] 🔍   #39 | 精确匹配✅ → 对齐成功✅ | "Don't wait up on me."
[16:57:42] [DEBUG] 🔍   #40 | 精确匹配✅ → 对齐成功✅ | "No."
[16:57:42] [DEBUG] 🔍   #41 | 精确匹配✅ → 对齐成功✅ | "All right?"
[16:57:42] [DEBUG] 🔍   #42 | 精确匹配✅ → 对齐成功✅ | "All right."
[16:57:42] [DEBUG] 🔍   #43 | 精确匹配✅ → 对齐成功✅ | "Ciao."
[16:57:42] [DEBUG] 🔍   #44 | 精确匹配✅ → 对齐成功✅ | "Be careful."
[16:57:42] [DEBUG] 🔍   #45 | 精确匹配✅ → 对齐成功✅ | "Ah, Skippy's all gone up now."
[16:57:42] [DEBUG] 🔍   #46 | 精确匹配✅ → 对齐成功✅ | "Now we'll go to my bed, huh?"
[16:57:42] ℹ️ API2: 成功对齐 46 个文本分段
[16:57:42] ℹ️ 处理 API2 的字幕条目...
[16:57:42] [DEBUG] 🔍 开始处理 46 个字幕条目的时间间隔分割...
[16:57:42] ℹ️ 时间间隔分割完成：46 个条目 → 46 个条目
[16:57:42] [DEBUG] 🔍 过滤短时长条目: 'But-' (00:01:18,580 --> 00:01:18,599, 持续时间: 0.019s)
[16:57:42] [DEBUG] 🔍 过滤短时长条目: 'No.' (00:04:11,919 --> 00:04:11,940, 持续时间: 0.021s)
[16:57:42] ℹ️ 短时长过滤完成：46 个条目 → 44 个条目（过滤掉 2 个短时长条目）
[16:57:42] ℹ️ 正在裁剪字幕静音区域...
[16:57:42] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:57:42] [DEBUG] 🔍 第一轮裁剪: 处理44个, 去除静音12.838秒
[16:57:42] [DEBUG] 🔍 开始第二轮静音裁剪...
[16:57:42] [DEBUG] 🔍 第二轮裁剪: 处理44个条目
[16:57:42] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:57:42] [DEBUG] 🔍 第二轮-开始时间: 在100ms偏移找到26个有效调整，剩余18个条目
[16:57:42] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:57:42] [DEBUG] 🔍 第二轮-开始时间: 200ms偏移无有效调整，剩余18个条目
[16:57:42] [DEBUG] 🔍 第二轮-开始时间: 总计处理26个条目，18个条目保持原样
[16:57:42] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:57:42] [DEBUG] 🔍 第二轮-结束时间: 在100ms偏移找到2个有效调整，剩余42个条目
[16:57:42] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:57:42] [DEBUG] 🔍 第二轮-结束时间: 在200ms偏移找到1个有效调整，剩余41个条目
[16:57:42] [DEBUG] 🔍 第二轮-结束时间: 总计处理3个条目，41个条目保持原样
[16:57:42] [DEBUG] 🔍 第二轮裁剪: 处理44个, 总计调整9个条目
[16:57:42] [DEBUG] 🔍 开始第三轮静音裁剪...
[16:57:42] [DEBUG] 🔍 第三轮裁剪: 处理44个条目
[16:57:42] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:57:42] [DEBUG] 🔍 第三轮-开始时间: 100ms偏移无有效调整，剩余44个条目
[16:57:42] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:57:42] [DEBUG] 🔍 第三轮-开始时间: 200ms偏移无有效调整，剩余44个条目
[16:57:42] [DEBUG] 🔍 第三轮-开始时间: 总计处理0个条目，44个条目保持原样
[16:57:42] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:57:42] [DEBUG] 🔍 第三轮-结束时间: 在100ms偏移找到26个有效调整，剩余18个条目
[16:57:42] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:57:42] [DEBUG] 🔍 第三轮-结束时间: 200ms偏移无有效调整，剩余18个条目
[16:57:42] [DEBUG] 🔍 第三轮-结束时间: 总计处理26个条目，18个条目保持原样
[16:57:42] [DEBUG] 🔍 第三轮裁剪: 处理44个, 总计调整1个条目
[16:57:42] ℹ️ 标准扩充: 增加显示 11.306 秒
[16:57:42] [DEBUG] 🔍 标准扩充详情: 完全17个, 部分21个
[16:57:42] [DEBUG] 🔍 解决扩充冲突: 19个
[16:57:42] ℹ️ 智能扩充: 处理21个短字幕, 增加显示 2.317 秒
[16:57:42] [DEBUG] 🔍 智能扩充详情: 完全11个, 部分10个, 目标1000ms
[16:57:42] ℹ️ 桥接处理: 填补间隔 0.396 秒
[16:57:42] [DEBUG] 🔍 桥接详情: 处理3个间隔, 阈值300ms
[16:57:42] ℹ️ API2: 字幕生成完成 - trimmed_Honey Baby, Honey Baby (1974)_part14_01-05-07-ElevenLabs-API2.srt
[16:57:42] ℹ️ 字幕生成完成，共生成 1 个字幕文件
[16:57:42] ℹ️ 文本处理统计:
[16:57:42] ℹ️   总LLM分段数: 46
[16:57:42] ℹ️   第一轮精确匹配: 46/46 (100.0%)
[16:57:42] ℹ️   第二轮锚点匹配: 0/46 (0.0%)
[16:57:42] ℹ️   文本匹配成功: 46/46 (100.0%)
[16:57:42] ℹ️   文本对齐成功: 46/46 (100.0%)
[16:57:42] [TaskFlow] 分段字幕生成成功: trimmed_Honey Baby, Honey Baby (1974)_part14_01-05-07 - ElevenLabs-API2
[16:57:42] [TaskFlow] 处理分段字幕 15/19: trimmed_Honey Baby, Honey Baby (1974)_part15_01-09-51
[16:57:42] [TaskFlow] 开始生成分段字幕: trimmed_Honey Baby, Honey Baby (1974)_part15_01-09-51 - ElevenLabs-API2
[16:57:42] ℹ️ 开始字幕生成: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part15_01-09-51-ElevenLabs-parsed.json
[16:57:42] [DEBUG] 🔍 提取服务名称，输入文件: trimmed_Honey Baby, Honey Baby (1974)_part15_01-09-51-ElevenLabs-parsed.json
[16:57:42] [DEBUG] 🔍 正则匹配成功，服务名称: ElevenLabs
[16:57:42] ℹ️ 检测到服务名称: ElevenLabs
[16:57:42] ℹ️ 检测已存在的字幕文件...
[16:57:42] [DEBUG] 🔍 🔍 扫描目录: ./projects\trimmed_Honey Baby, Honey Baby (1974)
[16:57:42] [DEBUG] 🔍 🔍 查找模式: trimmed_Honey Baby, Honey Baby (1974)_part15_01-09-51-ElevenLabs-*.srt
[16:57:42] [DEBUG] 🔍 🔍 找到 0 个匹配文件
[16:57:42] ℹ️ 未发现已存在字幕文件
[16:57:42] ℹ️ 全部启用的API: ['API2']
[16:57:42] ℹ️ 已存在字幕的API: []
[16:57:42] ℹ️ 需要处理的API: ['API2']
[16:57:43] ℹ️ 正在分析音频静音段...
[16:57:43] ℹ️ 正在检测音频静音段: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part15_01-09-51.mp3
[16:57:43] ℹ️ 检测到 406 个静音段
[16:57:43] ℹ️ 静音检测完成，缓存结果用于后续处理
[16:57:43] ℹ️ 开始处理 API2...
[16:57:43] ℹ️ API2: 启用分块处理
[16:57:43] ℹ️ API2: 分割为 3 个块
[16:57:43] ℹ️ API2: 正在处理第 1/3 块 (982 字符)
[16:57:43] [DEBUG] 🔍 API2: 开始API调用处理第 1 块...
[16:57:43] [DEBUG] 🔍 API2: 开始API调用...
[16:58:25] [DEBUG] 🔍 API2: API调用成功，返回 991 字符
[16:58:25] ℹ️ API2: 第 1 块处理成功 ✅ (耗时: 42.3秒, 差异: 9 字符)
[16:58:25] ℹ️ API2: 正在处理第 2/3 块 (968 字符)
[16:58:25] ℹ️ API2: 等待请求间隔 10.0 秒...
[16:58:35] [DEBUG] 🔍 API2: 开始API调用处理第 2 块...
[16:58:35] [DEBUG] 🔍 API2: 开始API调用...
[16:59:01] [DEBUG] 🔍 API2: API调用成功，返回 973 字符
[16:59:01] ℹ️ API2: 第 2 块处理成功 ✅ (耗时: 26.0秒, 差异: 5 字符)
[16:59:01] ℹ️ API2: 正在处理第 3/3 块 (537 字符)
[16:59:01] ℹ️ API2: 等待请求间隔 10.0 秒...
[16:59:12] [DEBUG] 🔍 API2: 开始API调用处理第 3 块...
[16:59:12] [DEBUG] 🔍 API2: 开始API调用...
[16:59:35] [DEBUG] 🔍 API2: API调用成功，返回 539 字符
[16:59:35] ℹ️ API2: 第 3 块处理成功 ✅ (耗时: 23.6秒, 差异: 2 字符)
[16:59:35] ℹ️ API2: 初始处理完成，成功 3/3 块
[16:59:35] ℹ️ API2: LLM调用成功，开始生成字幕...
[16:59:35] ℹ️ 对齐 API2 的文本分段...
[16:59:35] ℹ️ LLM分段调试数据已保存到: ./projects\trimmed_Honey Baby, Honey Baby (1974)\llm_segments_debug_20250803_165935.json
[16:59:35] ℹ️ 开始文本对齐处理（顺序约束），共 82 个片段
[16:59:35] [DEBUG] 🔍 片段处理进度:
[16:59:35] [DEBUG] 🔍   #1 | 精确匹配✅ → 对齐成功✅ | "Liv, come on down here//and ha..."
[16:59:35] [DEBUG] 🔍   #2 | 精确匹配✅ → 对齐成功✅ | "Oh, flower."
[16:59:35] [DEBUG] 🔍   #3 | 精确匹配✅ → 对齐成功✅ | "Come on."
[16:59:35] [DEBUG] 🔍   #4 | 精确匹配✅ → 对齐成功✅ | "Have some tea."
[16:59:35] [DEBUG] 🔍   #5 | 精确匹配✅ → 对齐成功✅ | "I opened the container, Harry."
[16:59:35] [DEBUG] 🔍   #6 | 精确匹配✅ → 对齐成功✅ | "You did what?"
[16:59:35] [DEBUG] 🔍   #7 | 精确匹配✅ → 对齐成功✅ | "You crossed me, Harry."
[16:59:35] [DEBUG] 🔍   #8 | 精确匹配✅ → 对齐成功✅ | "Was that your idea too?"
[16:59:35] [DEBUG] 🔍   #9 | 精确匹配✅ → 对齐成功✅ | "No, that wasn't my idea."
[16:59:35] [DEBUG] 🔍   #10 | 精确匹配✅ → 对齐成功✅ | "You're a bloody fool, Liv."
[16:59:35] [DEBUG] 🔍   #11 | 精确匹配✅ → 对齐成功✅ | "If you have opened the contain..."
[16:59:35] [DEBUG] 🔍   #12 | 精确匹配✅ → 对齐成功✅ | "and the thing will disintegrat..."
[16:59:35] [DEBUG] 🔍   #13 | 精确匹配✅ → 对齐成功✅ | "Dust to dust!"
[16:59:35] [DEBUG] 🔍   #14 | 精确匹配✅ → 对齐成功✅ | "Hey, what's going on down ther..."
[16:59:35] [DEBUG] 🔍   #15 | 精确匹配✅ → 对齐成功✅ | "I'm trying to sleep!"
[16:59:35] [DEBUG] 🔍   #16 | 精确匹配✅ → 对齐成功✅ | "Take a pill, woman!"
[16:59:35] [DEBUG] 🔍   #17 | 精确匹配✅ → 对齐成功✅ | "Take a pill!"
[16:59:35] [DEBUG] 🔍   #18 | 精确匹配✅ → 对齐成功✅ | "Why, you go."
[16:59:35] [DEBUG] 🔍   #19 | 精确匹配✅ → 对齐成功✅ | "All right now, Liv."
[16:59:35] [DEBUG] 🔍   #20 | 精确匹配✅ → 对齐成功✅ | "Now look, I wasn't entirely//s..."
[16:59:35] [DEBUG] 🔍   #21 | 精确匹配✅ → 对齐成功✅ | "Your goons tried to kill me la..."
[16:59:35] [DEBUG] 🔍   #22 | 精确匹配✅ → 对齐成功✅ | "Oh, for Jesus' sake,//you know..."
[16:59:35] [DEBUG] 🔍   #23 | 精确匹配✅ → 对齐成功✅ | "I want the full story, Harry."
[16:59:36] [DEBUG] 🔍   #24 | 精确匹配✅ → 对齐成功✅ | "All right, all right."
[16:59:36] [DEBUG] 🔍   #25 | 精确匹配✅ → 对齐成功✅ | "Now look, the liberal oppositi..."
[16:59:36] [DEBUG] 🔍   #26 | 精确匹配✅ → 对齐成功✅ | "who now run that man's country..."
[16:59:36] [DEBUG] 🔍   #27 | 精确匹配✅ → 对齐成功✅ | "A, to remove the body//and so ..."
[16:59:36] [DEBUG] 🔍   #28 | 精确匹配✅ → 对齐成功✅ | "and B, to remove the support//..."
[16:59:36] [DEBUG] 🔍   #29 | 精确匹配✅ → 对齐成功✅ | "And Madam Chas?"
[16:59:36] [DEBUG] 🔍   #30 | 精确匹配✅ → 对齐成功✅ | "She was carrying the formula//..."
[16:59:36] [DEBUG] 🔍   #31 | 精确匹配✅ → 对齐成功✅ | "And a microdot."
[16:59:36] [DEBUG] 🔍   #32 | 精确匹配✅ → 对齐成功✅ | "Whose was that stuff?"
[16:59:36] [DEBUG] 🔍   #33 | 精确匹配✅ → 对齐成功✅ | "How the hell do I know?"
[16:59:36] [DEBUG] 🔍   #34 | 精确匹配✅ → 对齐成功✅ | "Agents of the junta, I suppose..."
[16:59:36] [DEBUG] 🔍   #35 | 精确匹配✅ → 对齐成功✅ | "But now listen, look, Liv,"
[16:59:36] [DEBUG] 🔍   #36 | 精确匹配✅ → 对齐成功✅ | "Mr. Mokuba has said that he'll..."
[16:59:36] [DEBUG] 🔍   #37 | 精确匹配✅ → 对齐成功✅ | "No."
[16:59:36] [DEBUG] 🔍   #38 | 精确匹配✅ → 对齐成功✅ | "Oh, now come on, Liv."
[16:59:36] [DEBUG] 🔍   #39 | 精确匹配✅ → 对齐成功✅ | "Half a loaf, half a loaf!"
[16:59:36] [DEBUG] 🔍   #40 | 精确匹配✅ → 对齐成功✅ | "If the body disintegrates,//we..."
[16:59:36] [DEBUG] 🔍   #41 | 精确匹配✅ → 对齐成功✅ | "Then I will have half of Afric..."
[16:59:36] [DEBUG] 🔍   #42 | 精确匹配✅ → 对齐成功✅ | "Exactly."
[16:59:36] [DEBUG] 🔍   #43 | 精确匹配✅ → 对齐成功✅ | "So let's get rid of the bloody..."
[16:59:36] [DEBUG] 🔍   #44 | 精确匹配✅ → 对齐成功✅ | "No."
[16:59:36] [DEBUG] 🔍   #45 | 精确匹配✅ → 对齐成功✅ | "But you must, sir."
[16:59:36] [DEBUG] 🔍   #46 | 精确匹配✅ → 对齐成功✅ | "We need it."
[16:59:36] [DEBUG] 🔍   #47 | 精确匹配✅ → 对齐成功✅ | "And we will have it."
[16:59:36] [DEBUG] 🔍   #48 | 精确匹配✅ → 对齐成功✅ | "Uh, this is Mr. Mokuba."
[16:59:36] [DEBUG] 🔍   #49 | 精确匹配✅ → 对齐成功✅ | "Uh, this is Liv."
[16:59:36] [DEBUG] 🔍   #50 | 精确匹配✅ → 对齐成功✅ | "What good is the body to you//..."
[16:59:36] [DEBUG] 🔍   #51 | 精确匹配✅ → 对齐成功✅ | "We'll bury it decently."
[16:59:36] [DEBUG] 🔍   #52 | 精确匹配✅ → 对齐成功✅ | "I don't like the feel of it."
[16:59:36] [DEBUG] 🔍   #53 | 精确匹配✅ → 对齐成功✅ | "Something stinks."
[16:59:36] [DEBUG] 🔍   #54 | 精确匹配✅ → 对齐成功✅ | "Where is the body?"
[16:59:36] [DEBUG] 🔍   #55 | 精确匹配✅ → 对齐成功✅ | "It's in a blacksmith's shop in..."
[16:59:36] [DEBUG] 🔍   #56 | 精确匹配✅ → 对齐成功✅ | "Oh, not anymore."
[16:59:36] [DEBUG] 🔍   #57 | 精确匹配✅ → 对齐成功✅ | "I moved it."
[16:59:36] [DEBUG] 🔍   #58 | 精确匹配✅ → 对齐成功✅ | "You bastard."
[16:59:36] [DEBUG] 🔍   #59 | 精确匹配✅ → 对齐成功✅ | "Please tell us where the body ..."
[16:59:36] [DEBUG] 🔍   #60 | 精确匹配✅ → 对齐成功✅ | "No way, baby. No way."
[16:59:36] [DEBUG] 🔍   #61 | 精确匹配✅ → 对齐成功✅ | "Oh, come on, Liv."
[16:59:36] [DEBUG] 🔍   #62 | 精确匹配✅ → 对齐成功✅ | "At least this way we'll cover ..."
[16:59:36] [DEBUG] 🔍   #63 | 精确匹配✅ → 对齐成功✅ | "Hand over the wretched thing!"
[16:59:36] [DEBUG] 🔍   #64 | 精确匹配✅ → 对齐成功✅ | "Not until I find out//who's tr..."
[16:59:36] [DEBUG] 🔍   #65 | 精确匹配✅ → 对齐成功✅ | "He'll never tell you where it ..."
[16:59:36] [DEBUG] 🔍   #66 | 精确匹配✅ → 对齐成功✅ | "He was with the American woman..."
[16:59:36] [DEBUG] 🔍   #67 | 精确匹配✅ → 对齐成功✅ | "Perhaps she helped him move th..."
[16:59:36] [DEBUG] 🔍   #68 | 精确匹配✅ → 对齐成功✅ | "Then we must frighten her a li..."
[16:59:36] [DEBUG] 🔍   #69 | 精确匹配✅ → 对齐成功✅ | "Okay, that's enough time//with..."
[16:59:36] [DEBUG] 🔍   #70 | 精确匹配✅ → 对齐成功✅ | "Thanks a lot."
[16:59:36] [DEBUG] 🔍   #71 | 精确匹配✅ → 对齐成功✅ | "Okay, that's all your study ti..."
[16:59:36] [DEBUG] 🔍   #72 | 精确匹配✅ → 对齐成功✅ | "Thanks, Midge."
[16:59:36] [DEBUG] 🔍   #73 | 精确匹配✅ → 对齐成功✅ | "Here, uh, take this."
[16:59:36] [DEBUG] 🔍   #74 | 精确匹配✅ → 对齐成功✅ | "You don't look your best, I'll..."
[16:59:36] [DEBUG] 🔍   #75 | 精确匹配✅ → 对齐成功✅ | "Build my confidence, Sam."
[16:59:36] [DEBUG] 🔍   #76 | 精确匹配✅ → 对齐成功✅ | "Build my confidence."
[16:59:36] [DEBUG] 🔍   #77 | 精确匹配✅ → 对齐成功✅ | "I thought the African prince//..."
[16:59:36] [DEBUG] 🔍   #78 | 精确匹配✅ → 对齐成功✅ | "Kept you out all night,//and y..."
[16:59:36] [DEBUG] 🔍   #79 | 精确匹配✅ → 对齐成功✅ | "Good luck to you."
[16:59:36] [DEBUG] 🔍   #80 | 精确匹配✅ → 对齐成功✅ | "Bonne chance."
[16:59:36] [DEBUG] 🔍   #81 | 精确匹配✅ → 对齐成功✅ | "Next week's it."
[16:59:36] [DEBUG] 🔍   #82 | 精确匹配✅ → 对齐成功✅ | "Okay, everybody.//Now let's ma..."
[16:59:36] ℹ️ API2: 成功对齐 82 个文本分段
[16:59:36] ℹ️ 处理 API2 的字幕条目...
[16:59:36] [DEBUG] 🔍 开始处理 82 个字幕条目的时间间隔分割...
[16:59:36] ℹ️ 时间间隔分割完成：82 个条目 → 84 个条目
[16:59:36] [DEBUG] 🔍 过滤短时长条目: 'I' (00:01:07,459 --> 00:01:07,479, 持续时间: 0.020s)
[16:59:36] [DEBUG] 🔍 过滤短时长条目: 'I thought the African prince' (00:04:09,720 --> 00:04:09,740, 持续时间: 0.020s)
[16:59:36] ℹ️ 短时长过滤完成：84 个条目 → 82 个条目（过滤掉 2 个短时长条目）
[16:59:36] ℹ️ 正在裁剪字幕静音区域...
[16:59:36] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:59:36] [DEBUG] 🔍 第一轮裁剪: 处理82个, 去除静音16.895秒
[16:59:36] [DEBUG] 🔍 开始第二轮静音裁剪...
[16:59:36] [DEBUG] 🔍 第二轮裁剪: 处理82个条目
[16:59:36] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:59:37] [DEBUG] 🔍 第二轮-开始时间: 在100ms偏移找到57个有效调整，剩余25个条目
[16:59:37] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:59:37] [DEBUG] 🔍 第二轮-开始时间: 在200ms偏移找到2个有效调整，剩余23个条目
[16:59:37] [DEBUG] 🔍 第二轮-开始时间: 总计处理59个条目，23个条目保持原样
[16:59:37] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:59:37] [DEBUG] 🔍 第二轮-结束时间: 在100ms偏移找到1个有效调整，剩余81个条目
[16:59:37] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:59:37] [DEBUG] 🔍 第二轮-结束时间: 200ms偏移无有效调整，剩余81个条目
[16:59:37] [DEBUG] 🔍 第二轮-结束时间: 总计处理1个条目，81个条目保持原样
[16:59:37] [DEBUG] 🔍 第二轮裁剪: 处理82个, 总计调整15个条目
[16:59:37] [DEBUG] 🔍 开始第三轮静音裁剪...
[16:59:37] [DEBUG] 🔍 第三轮裁剪: 处理82个条目
[16:59:37] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:59:37] [DEBUG] 🔍 第三轮-开始时间: 100ms偏移无有效调整，剩余82个条目
[16:59:37] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:59:37] [DEBUG] 🔍 第三轮-开始时间: 在200ms偏移找到1个有效调整，剩余81个条目
[16:59:37] [DEBUG] 🔍 第三轮-开始时间: 总计处理1个条目，81个条目保持原样
[16:59:37] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:59:37] [DEBUG] 🔍 第三轮-结束时间: 在100ms偏移找到59个有效调整，剩余23个条目
[16:59:37] [DEBUG] 🔍 正在裁剪字幕静音区域...
[16:59:37] [DEBUG] 🔍 第三轮-结束时间: 200ms偏移无有效调整，剩余23个条目
[16:59:37] [DEBUG] 🔍 第三轮-结束时间: 总计处理59个条目，23个条目保持原样
[16:59:37] [DEBUG] 🔍 第三轮裁剪: 处理82个, 总计调整2个条目
[16:59:37] ℹ️ 标准扩充: 增加显示 24.247 秒
[16:59:37] [DEBUG] 🔍 标准扩充详情: 完全32个, 部分49个
[16:59:37] [DEBUG] 🔍 解决扩充冲突: 33个
[16:59:37] ℹ️ 智能扩充: 处理13个短字幕, 增加显示 1.362 秒
[16:59:37] [DEBUG] 🔍 智能扩充详情: 完全9个, 部分4个, 目标1000ms
[16:59:37] ℹ️ 桥接处理: 填补间隔 1.090 秒
[16:59:37] [DEBUG] 🔍 桥接详情: 处理10个间隔, 阈值300ms
[16:59:37] ℹ️ API2: 字幕生成完成 - trimmed_Honey Baby, Honey Baby (1974)_part15_01-09-51-ElevenLabs-API2.srt
[16:59:37] ℹ️ 字幕生成完成，共生成 1 个字幕文件
[16:59:37] ℹ️ 文本处理统计:
[16:59:37] ℹ️   总LLM分段数: 82
[16:59:37] ℹ️   第一轮精确匹配: 82/82 (100.0%)
[16:59:37] ℹ️   第二轮锚点匹配: 0/82 (0.0%)
[16:59:37] ℹ️   文本匹配成功: 82/82 (100.0%)
[16:59:37] ℹ️   文本对齐成功: 82/82 (100.0%)
[16:59:37] [TaskFlow] 分段字幕生成成功: trimmed_Honey Baby, Honey Baby (1974)_part15_01-09-51 - ElevenLabs-API2
[16:59:37] [TaskFlow] 处理分段字幕 16/19: trimmed_Honey Baby, Honey Baby (1974)_part16_01-14-29
[16:59:37] [TaskFlow] 开始生成分段字幕: trimmed_Honey Baby, Honey Baby (1974)_part16_01-14-29 - ElevenLabs-API2
[16:59:37] ℹ️ 开始字幕生成: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part16_01-14-29-ElevenLabs-parsed.json
[16:59:37] [DEBUG] 🔍 提取服务名称，输入文件: trimmed_Honey Baby, Honey Baby (1974)_part16_01-14-29-ElevenLabs-parsed.json
[16:59:37] [DEBUG] 🔍 正则匹配成功，服务名称: ElevenLabs
[16:59:37] ℹ️ 检测到服务名称: ElevenLabs
[16:59:37] ℹ️ 检测已存在的字幕文件...
[16:59:37] [DEBUG] 🔍 🔍 扫描目录: ./projects\trimmed_Honey Baby, Honey Baby (1974)
[16:59:37] [DEBUG] 🔍 🔍 查找模式: trimmed_Honey Baby, Honey Baby (1974)_part16_01-14-29-ElevenLabs-*.srt
[16:59:37] [DEBUG] 🔍 🔍 找到 0 个匹配文件
[16:59:37] ℹ️ 未发现已存在字幕文件
[16:59:37] ℹ️ 全部启用的API: ['API2']
[16:59:37] ℹ️ 已存在字幕的API: []
[16:59:37] ℹ️ 需要处理的API: ['API2']
[16:59:37] ℹ️ 正在分析音频静音段...
[16:59:37] ℹ️ 正在检测音频静音段: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part16_01-14-29.mp3
[16:59:38] ℹ️ 检测到 612 个静音段
[16:59:38] ℹ️ 静音检测完成，缓存结果用于后续处理
[16:59:38] ℹ️ 开始处理 API2...
[16:59:38] ℹ️ API2: 启用分块处理
[16:59:38] ℹ️ API2: 分割为 3 个块
[16:59:38] ℹ️ API2: 正在处理第 1/3 块 (979 字符)
[16:59:38] [DEBUG] 🔍 API2: 开始API调用处理第 1 块...
[16:59:38] [DEBUG] 🔍 API2: 开始API调用...
[17:00:11] [DEBUG] 🔍 API2: API调用成功，返回 989 字符
[17:00:11] ℹ️ API2: 第 1 块处理成功 ✅ (耗时: 32.9秒, 差异: 10 字符)
[17:00:11] ℹ️ API2: 正在处理第 2/3 块 (989 字符)
[17:00:11] ℹ️ API2: 等待请求间隔 10.0 秒...
[17:00:21] [DEBUG] 🔍 API2: 开始API调用处理第 2 块...
[17:00:21] [DEBUG] 🔍 API2: 开始API调用...
[17:00:48] [DEBUG] 🔍 API2: API调用成功，返回 997 字符
[17:00:48] ℹ️ API2: 第 2 块处理成功 ✅ (耗时: 27.4秒, 差异: 8 字符)
[17:00:48] ℹ️ API2: 正在处理第 3/3 块 (449 字符)
[17:00:48] ℹ️ API2: 等待请求间隔 10.0 秒...
[17:00:58] [DEBUG] 🔍 API2: 开始API调用处理第 3 块...
[17:00:58] [DEBUG] 🔍 API2: 开始API调用...
[17:01:17] [DEBUG] 🔍 API2: API调用成功，返回 451 字符
[17:01:17] ℹ️ API2: 第 3 块处理成功 ✅ (耗时: 18.5秒, 差异: 2 字符)
[17:01:17] ℹ️ API2: 初始处理完成，成功 3/3 块
[17:01:17] ℹ️ API2: LLM调用成功，开始生成字幕...
[17:01:17] ℹ️ 对齐 API2 的文本分段...
[17:01:17] ℹ️ LLM分段调试数据已保存到: ./projects\trimmed_Honey Baby, Honey Baby (1974)\llm_segments_debug_20250803_170117.json
[17:01:17] ℹ️ 开始文本对齐处理（顺序约束），共 80 个片段
[17:01:17] [DEBUG] 🔍 片段处理进度:
[17:01:17] [DEBUG] 🔍   #1 | 精确匹配✅ → 对齐成功✅ | "Hold it there."
[17:01:17] [DEBUG] 🔍   #2 | 精确匹配✅ → 对齐成功✅ | "Can you hear me okay, Laura?"
[17:01:17] [DEBUG] 🔍   #3 | 精确匹配✅ → 对齐成功✅ | "Yes, I can hear you, Sam."
[17:01:17] [DEBUG] 🔍   #4 | 精确匹配✅ → 对齐成功✅ | "Is Laura on yet?"
[17:01:17] [DEBUG] 🔍   #5 | 精确匹配✅ → 对齐成功✅ | "No, she's coming on now."
[17:01:17] [DEBUG] 🔍   #6 | 精确匹配✅ → 对齐成功✅ | "Come on."
[17:01:17] [DEBUG] 🔍   #7 | 精确匹配✅ → 对齐成功✅ | "And now Laura will answer//fou..."
[17:01:17] [DEBUG] 🔍   #8 | 精确匹配✅ → 对齐成功✅ | "And the answers to those quest..."
[17:01:17] [DEBUG] 🔍   #9 | 精确匹配✅ → 对齐成功✅ | "she continues on this trip//of..."
[17:01:17] [DEBUG] 🔍   #10 | 精确匹配✅ → 对齐成功✅ | "Are you ready, Laura?"
[17:01:17] [DEBUG] 🔍   #11 | 精确匹配✅ → 对齐成功✅ | "Yes, I'm ready, Sam."
[17:01:17] [DEBUG] 🔍   #12 | 精确匹配✅ → 对齐成功✅ | "Okay."
[17:01:17] [DEBUG] 🔍   #13 | 精确匹配✅ → 对齐成功✅ | "Now is there anything you woul..."
[17:01:17] [DEBUG] 🔍   #14 | 精确匹配✅ → 对齐成功✅ | "Oh, yes."
[17:01:17] [DEBUG] 🔍   #15 | 精确匹配✅ → 对齐成功✅ | "Yes, I'd, um, I'd like to say ..."
[17:01:17] [DEBUG] 🔍   #16 | 精确匹配✅ → 对齐成功✅ | "at Kazdulan."
[17:01:17] [DEBUG] 🔍   #17 | 精确匹配✅ → 对齐成功✅ | "Aha."
[17:01:17] [DEBUG] 🔍   #18 | 精确匹配✅ → 对齐成功✅ | "Laura says hi to all her frien..."
[17:01:17] [DEBUG] 🔍   #19 | 精确匹配✅ → 对齐成功✅ | "You know, uh, it- it's a beaut..."
[17:01:17] [DEBUG] 🔍   #20 | 精确匹配✅ → 对齐成功✅ | "See?"
[17:01:17] [DEBUG] 🔍   #21 | 精确匹配✅ → 对齐成功✅ | "What'd I tell you?"
[17:01:17] [DEBUG] 🔍   #22 | 精确匹配✅ → 对齐成功✅ | "That bastard."
[17:01:17] [DEBUG] 🔍   #23 | 精确匹配✅ → 对齐成功✅ | "Now I..."
[17:01:17] [DEBUG] 🔍   #24 | 精确匹配✅ → 对齐成功✅ | "Ah, don't Skiki show and tell/..."
[17:01:17] [DEBUG] 🔍   #25 | 精确匹配✅ → 对齐成功✅ | "I would like you to tell the f..."
[17:01:17] [DEBUG] 🔍   #26 | 精确匹配✅ → 对齐成功✅ | "here in the Middle East."
[17:01:17] [DEBUG] 🔍   #27 | 精确匹配✅ → 对齐成功✅ | "Better yet, let 'em see."
[17:01:17] [DEBUG] 🔍   #28 | 精确匹配✅ → 对齐成功✅ | "Let 'em see what winning our//..."
[17:01:17] [DEBUG] 🔍   #29 | 精确匹配✅ → 对齐成功✅ | "the lucky Laura Lewis to."
[17:01:17] [DEBUG] 🔍   #30 | 精确匹配✅ → 对齐成功✅ | "And now, question number one."
[17:01:17] [DEBUG] 🔍   #31 | 精确匹配✅ → 对齐成功✅ | "It's moving."
[17:01:17] [DEBUG] 🔍   #32 | 精确匹配✅ → 对齐成功✅ | "Why is this thing..."
[17:01:17] [DEBUG] 🔍   #33 | 精确匹配✅ → 对齐成功✅ | "Someone shut off the machine!"
[17:01:18] [DEBUG] 🔍   #34 | 精确匹配✅ → 对齐成功✅ | "<< I'll Wear Your Clothes Toda..."
[17:01:18] [DEBUG] 🔍   #35 | 精确匹配✅ → 对齐成功✅ | "Due to an accident,//the show ..."
[17:01:18] [DEBUG] 🔍   #36 | 精确匹配✅ → 对齐成功✅ | "Ms. Lewis gets the rest of the..."
[17:01:18] [DEBUG] 🔍   #37 | 精确匹配✅ → 对齐成功✅ | "See that? Sabotage."
[17:01:18] [DEBUG] 🔍   #38 | 精确匹配✅ → 对齐成功✅ | "We gotta do something about th..."
[17:01:18] [DEBUG] 🔍   #39 | 精确匹配✅ → 对齐成功✅ | "Nigga, sit down."
[17:01:18] [DEBUG] 🔍   #40 | 精确匹配✅ → 对齐成功✅ | "Ain't no A train going to no L..."
[17:01:18] [DEBUG] 🔍   #41 | 精确匹配✅ → 对齐成功✅ | "Hello?"
[17:01:18] [DEBUG] 🔍   #42 | 精确匹配✅ → 对齐成功✅ | "Ms. Lewis?"
[17:01:18] [DEBUG] 🔍   #43 | 精确匹配✅ → 对齐成功✅ | "Yes?"
[17:01:18] [DEBUG] 🔍   #44 | 精确匹配✅ → 对齐成功✅ | "We already have your friend, L..."
[17:01:18] [DEBUG] 🔍   #45 | 精确匹配✅ → 对齐成功✅ | "We will not hesitate to kill y..."
[17:01:18] [DEBUG] 🔍   #46 | 精确匹配✅ → 对齐成功✅ | "Will you tell us where the bod..."
[17:01:18] [DEBUG] 🔍   #47 | 精确匹配✅ → 对齐成功✅ | "No."
[17:01:18] [DEBUG] 🔍   #48 | 精确匹配✅ → 对齐成功✅ | "I suggest to make my point,//b..."
[17:01:18] [DEBUG] 🔍   #49 | 精确匹配✅ → 对齐成功✅ | "I will hold on."
[17:01:18] [DEBUG] 🔍   #50 | 精确匹配✅ → 对齐成功✅ | "Ms. Lewis."
[17:01:18] [DEBUG] 🔍   #51 | 精确匹配✅ → 对齐成功✅ | "Ms. Lewis?"
[17:01:18] [DEBUG] 🔍   #52 | 精确匹配✅ → 对齐成功✅ | "Yes?"
[17:01:18] [DEBUG] 🔍   #53 | 精确匹配✅ → 对齐成功✅ | "Oh, you see, Ms. Lewis,//we ar..."
[17:01:18] [DEBUG] 🔍   #54 | 精确匹配✅ → 对齐成功✅ | "And without your friend,//ther..."
[17:01:18] [DEBUG] 🔍   #55 | 精确匹配✅ → 对齐成功✅ | "Ms. Lewis?"
[17:01:18] [DEBUG] 🔍   #56 | 精确匹配✅ → 对齐成功✅ | "He was at the house this morni..."
[17:01:18] [DEBUG] 🔍   #57 | 精确匹配✅ → 对齐成功✅ | "I heard him and Harry arguing/..."
[17:01:18] [DEBUG] 🔍   #58 | 精确匹配✅ → 对齐成功✅ | "You know, you can't tell//abou..."
[17:01:18] [DEBUG] 🔍   #59 | 精确匹配✅ → 对齐成功✅ | "Well, thank you very much."
[17:01:18] [DEBUG] 🔍   #60 | 精确匹配✅ → 对齐成功✅ | "Come on, Skiki, let's go."
[17:01:18] [DEBUG] 🔍   #61 | 精确匹配✅ → 对齐成功✅ | "No, but wait, wait, wait a min..."
[17:01:18] [DEBUG] 🔍   #62 | 精确匹配✅ → 对齐成功✅ | "Now if you're going up to the ..."
[17:01:18] [DEBUG] 🔍   #63 | 精确匹配✅ → 对齐成功✅ | "be very, very careful//of thos..."
[17:01:18] [DEBUG] 🔍   #64 | 精确匹配✅ → 对齐成功✅ | "Every night, he lets 'em loose..."
[17:01:18] [DEBUG] 🔍   #65 | 精确匹配✅ → 对齐成功✅ | "Why don't you just stay in the..."
[17:01:18] [DEBUG] 🔍   #66 | 精确匹配✅ → 对齐成功✅ | "What if he's not home?"
[17:01:18] [DEBUG] 🔍   #67 | 精确匹配✅ → 对齐成功✅ | "Feed them some marshmallows."
[17:01:18] [DEBUG] 🔍   #68 | 精确匹配✅ → 对齐成功✅ | "Marshmallows?"
[17:01:18] [DEBUG] 🔍   #69 | 精确匹配✅ → 对齐成功✅ | "Yes."
[17:01:18] [DEBUG] 🔍   #70 | 精确匹配✅ → 对齐成功✅ | "They're addicted to marshmallo..."
[17:01:18] [DEBUG] 🔍   #71 | 精确匹配✅ → 对齐成功✅ | "That's what I feed them//when ..."
[17:01:18] [DEBUG] 🔍   #72 | 精确匹配✅ → 对齐成功✅ | "Mm-hmm."
[17:01:18] [DEBUG] 🔍   #73 | 精确匹配✅ → 对齐成功✅ | "But don't forget to get the be..."
[17:01:18] [DEBUG] 🔍   #74 | 精确匹配✅ → 对齐成功✅ | "Gotcha."
[17:01:18] [DEBUG] 🔍   #75 | 精确匹配✅ → 对齐成功✅ | "... big as them dogs is//they ..."
[17:01:18] [DEBUG] 🔍   #76 | 精确匹配✅ → 对齐成功✅ | "Skiki, nothing be a try but a ..."
[17:01:18] [DEBUG] 🔍   #77 | 精确匹配✅ → 对齐成功✅ | "Throw it."
[17:01:18] [DEBUG] 🔍   #78 | 精确匹配✅ → 对齐成功✅ | "Aw, Shugs they getting down no..."
[17:01:18] [DEBUG] 🔍   #79 | 精确匹配✅ → 对齐成功✅ | "Until tomorrow."
[17:01:18] [DEBUG] 🔍   #80 | 精确匹配✅ → 对齐成功✅ | "Yes."
[17:01:18] ℹ️ API2: 成功对齐 80 个文本分段
[17:01:18] ℹ️ 处理 API2 的字幕条目...
[17:01:18] [DEBUG] 🔍 开始处理 80 个字幕条目的时间间隔分割...
[17:01:18] ℹ️ 时间间隔分割完成：80 个条目 → 82 个条目
[17:01:18] [DEBUG] 🔍 短时长过滤完成：82 个条目，无需过滤
[17:01:18] ℹ️ 正在裁剪字幕静音区域...
[17:01:18] [DEBUG] 🔍 正在裁剪字幕静音区域...
[17:01:18] [DEBUG] 🔍 第一轮裁剪: 处理82个, 去除静音25.120秒
[17:01:18] [DEBUG] 🔍 开始第二轮静音裁剪...
[17:01:18] [DEBUG] 🔍 第二轮裁剪: 处理82个条目
[17:01:18] [DEBUG] 🔍 正在裁剪字幕静音区域...
[17:01:18] [DEBUG] 🔍 第二轮-开始时间: 在100ms偏移找到61个有效调整，剩余21个条目
[17:01:18] [DEBUG] 🔍 正在裁剪字幕静音区域...
[17:01:18] [DEBUG] 🔍 第二轮-开始时间: 在200ms偏移找到3个有效调整，剩余18个条目
[17:01:18] [DEBUG] 🔍 第二轮-开始时间: 总计处理64个条目，18个条目保持原样
[17:01:18] [DEBUG] 🔍 正在裁剪字幕静音区域...
[17:01:19] [DEBUG] 🔍 第二轮-结束时间: 在100ms偏移找到2个有效调整，剩余80个条目
[17:01:19] [DEBUG] 🔍 正在裁剪字幕静音区域...
[17:01:19] [DEBUG] 🔍 第二轮-结束时间: 在200ms偏移找到2个有效调整，剩余78个条目
[17:01:19] [DEBUG] 🔍 第二轮-结束时间: 总计处理4个条目，78个条目保持原样
[17:01:19] [DEBUG] 🔍 第二轮裁剪: 处理82个, 总计调整27个条目
[17:01:19] [DEBUG] 🔍 开始第三轮静音裁剪...
[17:01:19] [DEBUG] 🔍 第三轮裁剪: 处理82个条目
[17:01:19] [DEBUG] 🔍 正在裁剪字幕静音区域...
[17:01:19] [DEBUG] 🔍 第三轮-开始时间: 100ms偏移无有效调整，剩余82个条目
[17:01:19] [DEBUG] 🔍 正在裁剪字幕静音区域...
[17:01:19] [DEBUG] 🔍 第三轮-开始时间: 在200ms偏移找到1个有效调整，剩余81个条目
[17:01:19] [DEBUG] 🔍 第三轮-开始时间: 总计处理1个条目，81个条目保持原样
[17:01:19] [DEBUG] 🔍 正在裁剪字幕静音区域...
[17:01:19] [DEBUG] 🔍 第三轮-结束时间: 在100ms偏移找到64个有效调整，剩余18个条目
[17:01:19] [DEBUG] 🔍 正在裁剪字幕静音区域...
[17:01:19] [DEBUG] 🔍 第三轮-结束时间: 200ms偏移无有效调整，剩余18个条目
[17:01:19] [DEBUG] 🔍 第三轮-结束时间: 总计处理64个条目，18个条目保持原样
[17:01:19] [DEBUG] 🔍 第三轮裁剪: 处理82个, 总计调整2个条目
[17:01:19] ℹ️ 标准扩充: 增加显示 27.144 秒
[17:01:19] [DEBUG] 🔍 标准扩充详情: 完全48个, 部分34个
[17:01:19] [DEBUG] 🔍 解决扩充冲突: 20个
[17:01:19] ℹ️ 智能扩充: 处理22个短字幕, 增加显示 5.172 秒
[17:01:19] [DEBUG] 🔍 智能扩充详情: 完全19个, 部分3个, 目标1000ms
[17:01:19] ℹ️ 桥接处理: 填补间隔 1.486 秒
[17:01:19] [DEBUG] 🔍 桥接详情: 处理12个间隔, 阈值300ms
[17:01:19] ℹ️ API2: 字幕生成完成 - trimmed_Honey Baby, Honey Baby (1974)_part16_01-14-29-ElevenLabs-API2.srt
[17:01:19] ℹ️ 字幕生成完成，共生成 1 个字幕文件
[17:01:19] ℹ️ 文本处理统计:
[17:01:19] ℹ️   总LLM分段数: 80
[17:01:19] ℹ️   第一轮精确匹配: 80/80 (100.0%)
[17:01:19] ℹ️   第二轮锚点匹配: 0/80 (0.0%)
[17:01:19] ℹ️   文本匹配成功: 80/80 (100.0%)
[17:01:19] ℹ️   文本对齐成功: 80/80 (100.0%)
[17:01:19] [TaskFlow] 分段字幕生成成功: trimmed_Honey Baby, Honey Baby (1974)_part16_01-14-29 - ElevenLabs-API2
[17:01:19] [TaskFlow] 处理分段字幕 17/19: trimmed_Honey Baby, Honey Baby (1974)_part17_01-20-54
[17:01:19] [TaskFlow] 开始生成分段字幕: trimmed_Honey Baby, Honey Baby (1974)_part17_01-20-54 - ElevenLabs-API2
[17:01:19] ℹ️ 开始字幕生成: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part17_01-20-54-ElevenLabs-parsed.json
[17:01:19] [DEBUG] 🔍 提取服务名称，输入文件: trimmed_Honey Baby, Honey Baby (1974)_part17_01-20-54-ElevenLabs-parsed.json
[17:01:19] [DEBUG] 🔍 正则匹配成功，服务名称: ElevenLabs
[17:01:19] ℹ️ 检测到服务名称: ElevenLabs
[17:01:19] ℹ️ 检测已存在的字幕文件...
[17:01:19] [DEBUG] 🔍 🔍 扫描目录: ./projects\trimmed_Honey Baby, Honey Baby (1974)
[17:01:19] [DEBUG] 🔍 🔍 查找模式: trimmed_Honey Baby, Honey Baby (1974)_part17_01-20-54-ElevenLabs-*.srt
[17:01:19] [DEBUG] 🔍 🔍 找到 0 个匹配文件
[17:01:19] ℹ️ 未发现已存在字幕文件
[17:01:19] ℹ️ 全部启用的API: ['API2']
[17:01:19] ℹ️ 已存在字幕的API: []
[17:01:19] ℹ️ 需要处理的API: ['API2']
[17:01:19] ℹ️ 正在分析音频静音段...
[17:01:19] ℹ️ 正在检测音频静音段: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part17_01-20-54.mp3
[17:01:20] ℹ️ 检测到 209 个静音段
[17:01:20] ℹ️ 静音检测完成，缓存结果用于后续处理
[17:01:20] ℹ️ 开始处理 API2...
[17:01:20] ℹ️ API2: 启用分块处理
[17:01:20] ℹ️ API2: 分割为 2 个块
[17:01:20] ℹ️ API2: 正在处理第 1/2 块 (964 字符)
[17:01:20] [DEBUG] 🔍 API2: 开始API调用处理第 1 块...
[17:01:20] [DEBUG] 🔍 API2: 开始API调用...
[17:01:42] [DEBUG] 🔍 API2: API调用成功，返回 973 字符
[17:01:42] ℹ️ API2: 第 1 块处理成功 ✅ (耗时: 22.6秒, 差异: 9 字符)
[17:01:42] ℹ️ API2: 正在处理第 2/2 块 (65 字符)
[17:01:42] ℹ️ API2: 等待请求间隔 10.0 秒...
[17:01:52] [DEBUG] 🔍 API2: 开始API调用处理第 2 块...
[17:01:52] [DEBUG] 🔍 API2: 开始API调用...
[17:02:14] 已打开项目目录: E:\github\EvaTrans\projects
[17:02:26] [DEBUG] 🔍 API2: API调用成功，返回 66 字符
[17:02:26] ℹ️ API2: 第 2 块处理成功 ✅ (耗时: 33.2秒, 差异: 1 字符)
[17:02:26] ℹ️ API2: 初始处理完成，成功 2/2 块
[17:02:26] ℹ️ API2: LLM调用成功，开始生成字幕...
[17:02:26] ℹ️ 对齐 API2 的文本分段...
[17:02:26] ℹ️ LLM分段调试数据已保存到: ./projects\trimmed_Honey Baby, Honey Baby (1974)\llm_segments_debug_20250803_170226.json
[17:02:26] ℹ️ 开始文本对齐处理（顺序约束），共 40 个片段
[17:02:26] [DEBUG] 🔍 片段处理进度:
[17:02:26] [DEBUG] 🔍   #1 | 精确匹配✅ → 对齐成功✅ | "Believe me, Lynn, if you knew/..."
[17:02:26] [DEBUG] 🔍   #2 | 精确匹配✅ → 对齐成功✅ | "Now come on, isn't a percentag..."
[17:02:26] [DEBUG] 🔍   #3 | 精确匹配✅ → 对齐成功✅ | "Oh, you poor baby."
[17:02:26] [DEBUG] 🔍   #4 | 精确匹配✅ → 对齐成功✅ | "Are you okay?"
[17:02:26] [DEBUG] 🔍   #5 | 精确匹配✅ → 对齐成功✅ | "You're used..."
[17:02:26] [DEBUG] 🔍   #6 | 精确匹配✅ → 对齐成功✅ | "Huh?"
[17:02:26] [DEBUG] 🔍   #7 | 精确匹配✅ → 对齐成功✅ | "All right, okay, oh."
[17:02:26] [DEBUG] 🔍   #8 | 精确匹配✅ → 对齐成功✅ | "Hmm."
[17:02:26] [DEBUG] 🔍   #9 | 精确匹配✅ → 对齐成功✅ | "Oh, thanks for coming."
[17:02:26] [DEBUG] 🔍   #10 | 精确匹配✅ → 对齐成功✅ | "Ah."
[17:02:26] [DEBUG] 🔍   #11 | 精确匹配✅ → 对齐成功✅ | "Ah."
[17:02:26] [DEBUG] 🔍   #12 | 精确匹配✅ → 对齐成功✅ | "Listen, uh, what was that//Gen..."
[17:02:26] [DEBUG] 🔍   #13 | 精确匹配✅ → 对齐成功✅ | "Hmm?"
[17:02:26] [DEBUG] 🔍   #14 | 精确匹配✅ → 对齐成功✅ | "General Awani, the African."
[17:02:26] [DEBUG] 🔍   #15 | 精确匹配✅ → 对齐成功✅ | "He was at the nightclub, too."
[17:02:26] [DEBUG] 🔍   #16 | 精确匹配✅ → 对齐成功✅ | "I just remembered who he is."
[17:02:26] [DEBUG] 🔍   #17 | 精确匹配✅ → 对齐成功✅ | "That's Mkhuwa."
[17:02:26] [DEBUG] 🔍   #18 | 精确匹配✅ → 对齐成功✅ | "No."
[17:02:26] [DEBUG] 🔍   #19 | 精确匹配✅ → 对齐成功✅ | "That's General Christian Awani..."
[17:02:26] [DEBUG] 🔍   #20 | 精确匹配✅ → 对齐成功✅ | "How do you know?"
[17:02:26] [DEBUG] 🔍   #21 | 精确匹配✅ → 对齐成功✅ | "He's one of the higher-ups//in..."
[17:02:26] [DEBUG] 🔍   #22 | 精确匹配✅ → 对齐成功✅ | "How do you know?"
[17:02:26] [DEBUG] 🔍   #23 | 精确匹配✅ → 对齐成功✅ | "Because he headed an aid deleg..."
[17:02:26] [DEBUG] 🔍   #24 | 精确匹配✅ → 对齐成功✅ | "That's why he wants the body."
[17:02:26] [DEBUG] 🔍   #25 | 精确匹配✅ → 对齐成功✅ | "Hmm, we've got to win//and get..."
[17:02:26] [DEBUG] 🔍   #26 | 精确匹配✅ → 对齐成功✅ | "Compromising."
[17:02:26] [DEBUG] 🔍   #27 | 精确匹配✅ → 对齐成功✅ | "What?"
[17:02:26] [DEBUG] 🔍   #28 | 精确匹配✅ → 对齐成功✅ | "Make it look as if he and the ..."
[17:02:26] [DEBUG] 🔍   #29 | 精确匹配✅ → 对齐成功✅ | "then his own people//will take..."
[17:02:26] [DEBUG] 🔍   #30 | 精确匹配✅ → 对齐成功✅ | "Right, let's set him up."
[17:02:26] [DEBUG] 🔍   #31 | 精确匹配✅ → 对齐成功✅ | "Okay."
[17:02:26] [DEBUG] 🔍   #32 | 精确匹配✅ → 对齐成功✅ | "And we'll even be bait."
[17:02:26] [DEBUG] 🔍   #33 | 精确匹配✅ → 对齐成功✅ | "Why not?"
[17:02:26] [DEBUG] 🔍   #34 | 精确匹配✅ → 对齐成功✅ | "Harry tells me that he still h..."
[17:02:26] [DEBUG] 🔍   #35 | 精确匹配✅ → 对齐成功✅ | "I, uh, don't necessarily belie..."
[17:02:26] [DEBUG] 🔍   #36 | 精确匹配✅ → 对齐成功✅ | "Oh."
[17:02:26] [DEBUG] 🔍   #37 | 精确匹配✅ → 对齐成功✅ | "Shh, shh, shh."
[17:02:26] [DEBUG] 🔍   #38 | 精确匹配✅ → 对齐成功✅ | "Oh."
[17:02:26] [DEBUG] 🔍   #39 | 精确匹配✅ → 对齐成功✅ | "You cannot escape, Miss Lewis."
[17:02:26] [DEBUG] 🔍   #40 | 精确匹配✅ → 对齐成功✅ | "If you play games with me,// I..."
[17:02:26] ℹ️ API2: 成功对齐 40 个文本分段
[17:02:26] ℹ️ 处理 API2 的字幕条目...
[17:02:26] [DEBUG] 🔍 开始处理 40 个字幕条目的时间间隔分割...
[17:02:26] ℹ️ 时间间隔分割完成：40 个条目 → 41 个条目
[17:02:26] [DEBUG] 🔍 过滤短时长条目: 'Hmm.' (00:00:48,119 --> 00:00:48,159, 持续时间: 0.040s)
[17:02:26] ℹ️ 短时长过滤完成：41 个条目 → 40 个条目（过滤掉 1 个短时长条目）
[17:02:26] ℹ️ 正在裁剪字幕静音区域...
[17:02:26] [DEBUG] 🔍 正在裁剪字幕静音区域...
[17:02:26] [DEBUG] 🔍 第一轮裁剪: 处理40个, 去除静音6.041秒
[17:02:26] [DEBUG] 🔍 开始第二轮静音裁剪...
[17:02:26] [DEBUG] 🔍 第二轮裁剪: 处理40个条目
[17:02:26] [DEBUG] 🔍 正在裁剪字幕静音区域...
[17:02:26] [DEBUG] 🔍 第二轮-开始时间: 在100ms偏移找到30个有效调整，剩余10个条目
[17:02:26] [DEBUG] 🔍 正在裁剪字幕静音区域...
[17:02:26] [DEBUG] 🔍 第二轮-开始时间: 在200ms偏移找到1个有效调整，剩余9个条目
[17:02:26] [DEBUG] 🔍 第二轮-开始时间: 总计处理31个条目，9个条目保持原样
[17:02:26] [DEBUG] 🔍 正在裁剪字幕静音区域...
[17:02:26] [DEBUG] 🔍 第二轮-结束时间: 在100ms偏移找到1个有效调整，剩余39个条目
[17:02:26] [DEBUG] 🔍 正在裁剪字幕静音区域...
[17:02:26] [DEBUG] 🔍 第二轮-结束时间: 200ms偏移无有效调整，剩余39个条目
[17:02:26] [DEBUG] 🔍 第二轮-结束时间: 总计处理1个条目，39个条目保持原样
[17:02:26] [DEBUG] 🔍 第二轮裁剪: 处理40个, 总计调整11个条目
[17:02:26] [DEBUG] 🔍 开始第三轮静音裁剪...
[17:02:26] [DEBUG] 🔍 第三轮裁剪: 处理40个条目
[17:02:26] [DEBUG] 🔍 正在裁剪字幕静音区域...
[17:02:26] [DEBUG] 🔍 第三轮-开始时间: 100ms偏移无有效调整，剩余40个条目
[17:02:26] [DEBUG] 🔍 正在裁剪字幕静音区域...
[17:02:26] [DEBUG] 🔍 第三轮-开始时间: 200ms偏移无有效调整，剩余40个条目
[17:02:26] [DEBUG] 🔍 第三轮-开始时间: 总计处理0个条目，40个条目保持原样
[17:02:27] [DEBUG] 🔍 正在裁剪字幕静音区域...
[17:02:27] [DEBUG] 🔍 第三轮-结束时间: 在100ms偏移找到31个有效调整，剩余9个条目
[17:02:27] [DEBUG] 🔍 正在裁剪字幕静音区域...
[17:02:27] [DEBUG] 🔍 第三轮-结束时间: 200ms偏移无有效调整，剩余9个条目
[17:02:27] [DEBUG] 🔍 第三轮-结束时间: 总计处理31个条目，9个条目保持原样
[17:02:27] [DEBUG] 🔍 第三轮裁剪: 处理40个, 总计调整3个条目
[17:02:27] ℹ️ 标准扩充: 增加显示 13.557 秒
[17:02:27] [DEBUG] 🔍 标准扩充详情: 完全24个, 部分16个
[17:02:27] [DEBUG] 🔍 解决扩充冲突: 10个
[17:02:27] ℹ️ 智能扩充: 处理12个短字幕, 增加显示 2.705 秒
[17:02:27] [DEBUG] 🔍 智能扩充详情: 完全6个, 部分6个, 目标1000ms
[17:02:27] ℹ️ 桥接处理: 填补间隔 0.365 秒
[17:02:27] [DEBUG] 🔍 桥接详情: 处理3个间隔, 阈值300ms
[17:02:27] ℹ️ API2: 字幕生成完成 - trimmed_Honey Baby, Honey Baby (1974)_part17_01-20-54-ElevenLabs-API2.srt
[17:02:27] ℹ️ 字幕生成完成，共生成 1 个字幕文件
[17:02:27] ℹ️ 文本处理统计:
[17:02:27] ℹ️   总LLM分段数: 40
[17:02:27] ℹ️   第一轮精确匹配: 40/40 (100.0%)
[17:02:27] ℹ️   第二轮锚点匹配: 0/40 (0.0%)
[17:02:27] ℹ️   文本匹配成功: 40/40 (100.0%)
[17:02:27] ℹ️   文本对齐成功: 40/40 (100.0%)
[17:02:27] [TaskFlow] 分段字幕生成成功: trimmed_Honey Baby, Honey Baby (1974)_part17_01-20-54 - ElevenLabs-API2
[17:02:27] [TaskFlow] 处理分段字幕 18/19: trimmed_Honey Baby, Honey Baby (1974)_part18_01-24-10
[17:02:27] [TaskFlow] 开始生成分段字幕: trimmed_Honey Baby, Honey Baby (1974)_part18_01-24-10 - ElevenLabs-API2
[17:02:27] ℹ️ 开始字幕生成: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part18_01-24-10-ElevenLabs-parsed.json
[17:02:27] [DEBUG] 🔍 提取服务名称，输入文件: trimmed_Honey Baby, Honey Baby (1974)_part18_01-24-10-ElevenLabs-parsed.json
[17:02:27] [DEBUG] 🔍 正则匹配成功，服务名称: ElevenLabs
[17:02:27] ℹ️ 检测到服务名称: ElevenLabs
[17:02:27] ℹ️ 检测已存在的字幕文件...
[17:02:27] [DEBUG] 🔍 🔍 扫描目录: ./projects\trimmed_Honey Baby, Honey Baby (1974)
[17:02:27] [DEBUG] 🔍 🔍 查找模式: trimmed_Honey Baby, Honey Baby (1974)_part18_01-24-10-ElevenLabs-*.srt
[17:02:27] [DEBUG] 🔍 🔍 找到 0 个匹配文件
[17:02:27] ℹ️ 未发现已存在字幕文件
[17:02:27] ℹ️ 全部启用的API: ['API2']
[17:02:27] ℹ️ 已存在字幕的API: []
[17:02:27] ℹ️ 需要处理的API: ['API2']
[17:02:27] ℹ️ 正在分析音频静音段...
[17:02:27] ℹ️ 正在检测音频静音段: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part18_01-24-10.mp3
[17:02:27] ℹ️ 检测到 138 个静音段
[17:02:27] ℹ️ 静音检测完成，缓存结果用于后续处理
[17:02:27] ℹ️ 开始处理 API2...
[17:02:27] ℹ️ API2: 直接处理
[17:02:28] [DEBUG] 🔍 API2: 开始API调用...
[17:02:56] [DEBUG] 🔍 API2: API调用成功，返回 501 字符
[17:02:56] ℹ️ API2: LLM调用成功，开始生成字幕...
[17:02:56] ℹ️ 对齐 API2 的文本分段...
[17:02:56] ℹ️ LLM分段调试数据已保存到: ./projects\trimmed_Honey Baby, Honey Baby (1974)\llm_segments_debug_20250803_170256.json
[17:02:56] ℹ️ 开始文本对齐处理（顺序约束），共 17 个片段
[17:02:56] [DEBUG] 🔍 片段处理进度:
[17:02:56] [DEBUG] 🔍   #1 | 精确匹配✅ → 对齐成功✅ | "... he's got the key."
[17:02:56] [DEBUG] 🔍   #2 | 精确匹配✅ → 对齐成功✅ | "Don't bribe for the key."
[17:02:56] [DEBUG] 🔍   #3 | 精确匹配✅ → 对齐成功✅ | "I'm not going to bribe you."
[17:02:56] [DEBUG] 🔍   #4 | 精确匹配✅ → 对齐成功✅ | "All right."
[17:02:56] [DEBUG] 🔍   #5 | 精确匹配✅ → 对齐成功✅ | "Now, you can't get the keys ei..."
[17:02:56] [DEBUG] 🔍   #6 | 精确匹配✅ → 对齐成功✅ | "And you know, of course,//if e..."
[17:02:56] [DEBUG] 🔍   #7 | 精确匹配✅ → 对齐成功✅ | "we'll have the entire police f..."
[17:02:56] [DEBUG] 🔍   #8 | 精确匹配✅ → 对齐成功✅ | "So, why don't we make a deal?"
[17:02:56] [DEBUG] 🔍   #9 | 精确匹配✅ → 对齐成功✅ | "What are your terms?"
[17:02:56] [DEBUG] 🔍   #10 | 精确匹配✅ → 对齐成功✅ | "Money."
[17:02:56] [DEBUG] 🔍   #11 | 精确匹配✅ → 对齐成功✅ | "Harry with you?"
[17:02:56] [DEBUG] 🔍   #12 | 精确匹配✅ → 对齐成功✅ | "Harry's at home with a sore he..."
[17:02:56] [DEBUG] 🔍   #13 | 精确匹配✅ → 对齐成功✅ | "Tell me where Nabili is."
[17:02:56] [DEBUG] 🔍   #14 | 精确匹配✅ → 对齐成功✅ | "He's upstairs."
[17:02:56] [DEBUG] 🔍   #15 | 精确匹配✅ → 对齐成功✅ | "Where's the money?"
[17:02:56] [DEBUG] 🔍   #16 | 精确匹配✅ → 对齐成功✅ | "Don't do any more of that."
[17:02:56] [DEBUG] 🔍   #17 | 精确匹配✅ → 对齐成功✅ | "By the way, if you want to see..."
[17:02:56] ℹ️ API2: 成功对齐 17 个文本分段
[17:02:56] ℹ️ 处理 API2 的字幕条目...
[17:02:56] [DEBUG] 🔍 开始处理 17 个字幕条目的时间间隔分割...
[17:02:56] ℹ️ 时间间隔分割完成：17 个条目 → 21 个条目
[17:02:56] [DEBUG] 🔍 过滤短时长条目: '... he's' (00:00:00,079 --> 00:00:00,179, 持续时间: 0.100s)
[17:02:56] ℹ️ 短时长过滤完成：21 个条目 → 20 个条目（过滤掉 1 个短时长条目）
[17:02:57] ℹ️ 正在裁剪字幕静音区域...
[17:02:57] [DEBUG] 🔍 正在裁剪字幕静音区域...
[17:02:57] [DEBUG] 🔍 第一轮裁剪: 处理20个, 去除静音25.949秒
[17:02:57] [DEBUG] 🔍 开始第二轮静音裁剪...
[17:02:57] [DEBUG] 🔍 第二轮裁剪: 处理20个条目
[17:02:57] [DEBUG] 🔍 正在裁剪字幕静音区域...
[17:02:57] [DEBUG] 🔍 第二轮-开始时间: 在100ms偏移找到19个有效调整，剩余1个条目
[17:02:57] [DEBUG] 🔍 正在裁剪字幕静音区域...
[17:02:57] [DEBUG] 🔍 第二轮-开始时间: 200ms偏移无有效调整，剩余1个条目
[17:02:57] [DEBUG] 🔍 第二轮-开始时间: 总计处理19个条目，1个条目保持原样
[17:02:57] [DEBUG] 🔍 正在裁剪字幕静音区域...
[17:02:57] [DEBUG] 🔍 第二轮-结束时间: 100ms偏移无有效调整，剩余20个条目
[17:02:57] [DEBUG] 🔍 正在裁剪字幕静音区域...
[17:02:57] [DEBUG] 🔍 第二轮-结束时间: 200ms偏移无有效调整，剩余20个条目
[17:02:57] [DEBUG] 🔍 第二轮-结束时间: 总计处理0个条目，20个条目保持原样
[17:02:57] [DEBUG] 🔍 第二轮裁剪: 处理20个, 总计调整3个条目
[17:02:57] [DEBUG] 🔍 开始第三轮静音裁剪...
[17:02:57] [DEBUG] 🔍 第三轮裁剪: 处理20个条目
[17:02:57] [DEBUG] 🔍 正在裁剪字幕静音区域...
[17:02:57] [DEBUG] 🔍 第三轮-开始时间: 100ms偏移无有效调整，剩余20个条目
[17:02:57] [DEBUG] 🔍 正在裁剪字幕静音区域...
[17:02:57] [DEBUG] 🔍 第三轮-开始时间: 200ms偏移无有效调整，剩余20个条目
[17:02:57] [DEBUG] 🔍 第三轮-开始时间: 总计处理0个条目，20个条目保持原样
[17:02:57] [DEBUG] 🔍 正在裁剪字幕静音区域...
[17:02:57] [DEBUG] 🔍 第三轮-结束时间: 在100ms偏移找到19个有效调整，剩余1个条目
[17:02:57] [DEBUG] 🔍 正在裁剪字幕静音区域...
[17:02:57] [DEBUG] 🔍 第三轮-结束时间: 200ms偏移无有效调整，剩余1个条目
[17:02:57] [DEBUG] 🔍 第三轮-结束时间: 总计处理19个条目，1个条目保持原样
[17:02:57] [DEBUG] 🔍 第三轮裁剪: 处理20个, 总计调整0个条目
[17:02:57] ℹ️ 标准扩充: 增加显示 7.416 秒
[17:02:57] [DEBUG] 🔍 标准扩充详情: 完全16个, 部分4个
[17:02:57] [DEBUG] 🔍 解决扩充冲突: 2个
[17:02:57] ℹ️ 智能扩充: 处理4个短字幕, 增加显示 0.732 秒
[17:02:57] [DEBUG] 🔍 智能扩充详情: 完全4个, 部分0个, 目标1000ms
[17:02:57] ℹ️ 桥接处理: 填补间隔 0.146 秒
[17:02:57] [DEBUG] 🔍 桥接详情: 处理1个间隔, 阈值300ms
[17:02:57] ℹ️ API2: 字幕生成完成 - trimmed_Honey Baby, Honey Baby (1974)_part18_01-24-10-ElevenLabs-API2.srt
[17:02:57] ℹ️ 字幕生成完成，共生成 1 个字幕文件
[17:02:57] ℹ️ 文本处理统计:
[17:02:57] ℹ️   总LLM分段数: 17
[17:02:57] ℹ️   第一轮精确匹配: 17/17 (100.0%)
[17:02:57] ℹ️   第二轮锚点匹配: 0/17 (0.0%)
[17:02:57] ℹ️   文本匹配成功: 17/17 (100.0%)
[17:02:57] ℹ️   文本对齐成功: 17/17 (100.0%)
[17:02:57] [TaskFlow] 分段字幕生成成功: trimmed_Honey Baby, Honey Baby (1974)_part18_01-24-10 - ElevenLabs-API2
[17:02:57] [TaskFlow] 处理分段字幕 19/19: trimmed_Honey Baby, Honey Baby (1974)_part19_01-28-57
[17:02:57] [TaskFlow] 开始生成分段字幕: trimmed_Honey Baby, Honey Baby (1974)_part19_01-28-57 - ElevenLabs-API2
[17:02:57] ℹ️ 开始字幕生成: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part19_01-28-57-ElevenLabs-parsed.json
[17:02:57] [DEBUG] 🔍 提取服务名称，输入文件: trimmed_Honey Baby, Honey Baby (1974)_part19_01-28-57-ElevenLabs-parsed.json
[17:02:57] [DEBUG] 🔍 正则匹配成功，服务名称: ElevenLabs
[17:02:57] ℹ️ 检测到服务名称: ElevenLabs
[17:02:57] ℹ️ 检测已存在的字幕文件...
[17:02:57] [DEBUG] 🔍 🔍 扫描目录: ./projects\trimmed_Honey Baby, Honey Baby (1974)
[17:02:57] [DEBUG] 🔍 🔍 查找模式: trimmed_Honey Baby, Honey Baby (1974)_part19_01-28-57-ElevenLabs-*.srt
[17:02:57] [DEBUG] 🔍 🔍 找到 0 个匹配文件
[17:02:57] ℹ️ 未发现已存在字幕文件
[17:02:57] ℹ️ 全部启用的API: ['API2']
[17:02:57] ℹ️ 已存在字幕的API: []
[17:02:57] ℹ️ 需要处理的API: ['API2']
[17:02:57] ℹ️ 正在分析音频静音段...
[17:02:57] ℹ️ 正在检测音频静音段: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part19_01-28-57.mp3
[17:02:58] ℹ️ 检测到 331 个静音段
[17:02:58] ℹ️ 静音检测完成，缓存结果用于后续处理
[17:02:58] ℹ️ 开始处理 API2...
[17:02:58] ℹ️ API2: 启用分块处理
[17:02:58] ℹ️ API2: 分割为 2 个块
[17:02:58] ℹ️ API2: 正在处理第 1/2 块 (990 字符)
[17:02:58] [DEBUG] 🔍 API2: 开始API调用处理第 1 块...
[17:02:58] [DEBUG] 🔍 API2: 开始API调用...
[17:03:34] [DEBUG] 🔍 API2: API调用成功，返回 995 字符
[17:03:34] ℹ️ API2: 第 1 块处理成功 ✅ (耗时: 36.3秒, 差异: 5 字符)
[17:03:34] ℹ️ API2: 正在处理第 2/2 块 (312 字符)
[17:03:34] ℹ️ API2: 等待请求间隔 10.0 秒...
[17:03:44] [DEBUG] 🔍 API2: 开始API调用处理第 2 块...
[17:03:44] [DEBUG] 🔍 API2: 开始API调用...
[17:04:03] [DEBUG] 🔍 API2: API调用成功，返回 312 字符
[17:04:03] ℹ️ API2: 第 2 块处理成功 ✅ (耗时: 18.3秒, 差异: 0 字符)
[17:04:03] ℹ️ API2: 初始处理完成，成功 2/2 块
[17:04:03] ℹ️ API2: LLM调用成功，开始生成字幕...
[17:04:03] ℹ️ 对齐 API2 的文本分段...
[17:04:03] ℹ️ LLM分段调试数据已保存到: ./projects\trimmed_Honey Baby, Honey Baby (1974)\llm_segments_debug_20250803_170403.json
[17:04:03] ℹ️ 开始文本对齐处理（顺序约束），共 48 个片段
[17:04:03] [DEBUG] 🔍 片段处理进度:
[17:04:03] [DEBUG] 🔍   #1 | 精确匹配✅ → 对齐成功✅ | "... now let's go finish the jo..."
[17:04:03] [DEBUG] 🔍   #2 | 精确匹配✅ → 对齐成功✅ | "Kemetist Kiki is a rock specia..."
[17:04:03] [DEBUG] 🔍   #3 | 精确匹配✅ → 对齐成功✅ | "Time to get on."
[17:04:03] [DEBUG] 🔍   #4 | 精确匹配✅ → 对齐成功✅ | "Yeah."
[17:04:03] [DEBUG] 🔍   #5 | 精确匹配✅ → 对齐成功✅ | "All right, all right."
[17:04:03] [DEBUG] 🔍   #6 | 精确匹配✅ → 对齐成功✅ | "Now his own people will take c..."
[17:04:03] [DEBUG] 🔍   #7 | 精确匹配✅ → 对齐成功✅ | "All right."
[17:04:03] [DEBUG] 🔍   #8 | 精确匹配✅ → 对齐成功✅ | "General Christian Awani was fo..."
[17:04:03] [DEBUG] 🔍   #9 | 精确匹配✅ → 对齐成功✅ | "two hours after his arrest."
[17:04:03] [DEBUG] 🔍   #10 | 精确匹配✅ → 对齐成功✅ | "No details are available."
[17:04:03] [DEBUG] 🔍   #11 | 精确匹配✅ → 对齐成功✅ | "A new government has taken pow..."
[17:04:03] [DEBUG] 🔍   #12 | 精确匹配✅ → 对齐成功✅ | "of the body of the beloved Kwa..."
[17:04:03] [DEBUG] 🔍   #13 | 精确匹配✅ → 对齐成功✅ | "Take care of yourself, Neferti..."
[17:04:03] [DEBUG] 🔍   #14 | 精确匹配✅ → 对齐成功✅ | "Rah-Rah!"
[17:04:03] [DEBUG] 🔍   #15 | 精确匹配✅ → 对齐成功✅ | "Come on,//let's get this thing..."
[17:04:03] [DEBUG] 🔍   #16 | 精确匹配✅ → 对齐成功✅ | "Well."
[17:04:03] [DEBUG] 🔍   #17 | 精确匹配✅ → 对齐成功✅ | "Hey, you're not going?"
[17:04:03] [DEBUG] 🔍   #18 | 精确匹配✅ → 对齐成功✅ | "I'll see you."
[17:04:03] [DEBUG] 🔍   #19 | 精确匹配✅ → 对齐成功✅ | "I gotta stay home//and make so..."
[17:04:03] [DEBUG] 🔍   #20 | 精确匹配✅ → 对齐成功✅ | "That's cool."
[17:04:03] [DEBUG] 🔍   #21 | 精确匹配✅ → 对齐成功✅ | "I'll pick you later, yeah?"
[17:04:03] [DEBUG] 🔍   #22 | 精确匹配✅ → 对齐成功✅ | "Do you know where you're going..."
[17:04:03] [DEBUG] 🔍   #23 | 精确匹配✅ → 对齐成功✅ | "No, I haven't made up my mind."
[17:04:03] [DEBUG] 🔍   #24 | 精确匹配✅ → 对齐成功✅ | "Ooh."
[17:04:03] [DEBUG] 🔍   #25 | 精确匹配✅ → 对齐成功✅ | "As long as you do, let me know..."
[17:04:03] [DEBUG] 🔍   #26 | 精确匹配✅ → 对齐成功✅ | "Wherever it is."
[17:04:03] [DEBUG] 🔍   #27 | 精确匹配✅ → 对齐成功✅ | "I might."
[17:04:03] [DEBUG] 🔍   #28 | 精确匹配✅ → 对齐成功✅ | "Honey baby."
[17:04:03] [DEBUG] 🔍   #29 | 精确匹配✅ → 对齐成功✅ | "Honey baby."
[17:04:03] [DEBUG] 🔍   #30 | 精确匹配✅ → 对齐成功✅ | "Honey baby."
[17:04:03] [DEBUG] 🔍   #31 | 精确匹配✅ → 对齐成功✅ | "Oh, honey baby."
[17:04:03] [DEBUG] 🔍   #32 | 精确匹配✅ → 对齐成功✅ | "Honey baby, you learned from t..."
[17:04:03] [DEBUG] 🔍   #33 | 精确匹配✅ → 对齐成功✅ | "That the world is living aroun..."
[17:04:03] [DEBUG] 🔍   #34 | 精确匹配✅ → 对齐成功✅ | "Honey baby, you think that you..."
[17:04:03] [DEBUG] 🔍   #35 | 精确匹配✅ → 对齐成功✅ | "But that just ain't enough."
[17:04:03] [DEBUG] 🔍   #36 | 精确匹配✅ → 对齐成功✅ | "Oh, honey baby."
[17:04:03] [DEBUG] 🔍   #37 | 精确匹配✅ → 对齐成功✅ | "Honey baby."
[17:04:03] [DEBUG] 🔍   #38 | 精确匹配✅ → 对齐成功✅ | "Honey baby."
[17:04:03] [DEBUG] 🔍   #39 | 精确匹配✅ → 对齐成功✅ | "Oh, honey baby."
[17:04:03] [DEBUG] 🔍   #40 | 精确匹配✅ → 对齐成功✅ | "Well, honey baby."
[17:04:03] [DEBUG] 🔍   #41 | 精确匹配✅ → 对齐成功✅ | "You did it this time, didn't y..."
[17:04:03] [DEBUG] 🔍   #42 | 精确匹配✅ → 对齐成功✅ | "You did it this time, didn't y..."
[17:04:03] [DEBUG] 🔍   #43 | 精确匹配✅ → 对齐成功✅ | "Now, as I was telling you befo..."
[17:04:03] [DEBUG] 🔍   #44 | 精确匹配✅ → 对齐成功✅ | "And every guarantee ain't gott..."
[17:04:03] [DEBUG] 🔍   #45 | 精确匹配✅ → 对齐成功✅ | "And every shut-eye ain't sleep..."
[17:04:03] [DEBUG] 🔍   #46 | 精确匹配✅ → 对齐成功✅ | "And every goodbye ain't gone."
[17:04:03] [DEBUG] 🔍   #47 | 精确匹配✅ → 对齐成功✅ | "And every Mohawk ain't a Mohic..."
[17:04:03] [DEBUG] 🔍   #48 | 精确匹配✅ → 对齐成功✅ | "Honey-"
[17:04:03] ℹ️ API2: 成功对齐 48 个文本分段
[17:04:03] ℹ️ 处理 API2 的字幕条目...
[17:04:03] [DEBUG] 🔍 开始处理 48 个字幕条目的时间间隔分割...
[17:04:03] ℹ️ 时间间隔分割完成：48 个条目 → 50 个条目
[17:04:03] [DEBUG] 🔍 过滤短时长条目: '...' (00:00:00,099 --> 00:00:00,199, 持续时间: 0.100s)
[17:04:03] ℹ️ 短时长过滤完成：50 个条目 → 49 个条目（过滤掉 1 个短时长条目）
[17:04:03] ℹ️ 正在裁剪字幕静音区域...
[17:04:03] [DEBUG] 🔍 正在裁剪字幕静音区域...
[17:04:03] [DEBUG] 🔍 第一轮裁剪: 处理49个, 去除静音6.449秒
[17:04:03] [DEBUG] 🔍 开始第二轮静音裁剪...
[17:04:03] [DEBUG] 🔍 第二轮裁剪: 处理49个条目
[17:04:03] [DEBUG] 🔍 正在裁剪字幕静音区域...
[17:04:03] [DEBUG] 🔍 第二轮-开始时间: 在100ms偏移找到34个有效调整，剩余15个条目
[17:04:03] [DEBUG] 🔍 正在裁剪字幕静音区域...
[17:04:03] [DEBUG] 🔍 第二轮-开始时间: 在200ms偏移找到2个有效调整，剩余13个条目
[17:04:03] [DEBUG] 🔍 第二轮-开始时间: 总计处理36个条目，13个条目保持原样
[17:04:03] [DEBUG] 🔍 正在裁剪字幕静音区域...
[17:04:03] [DEBUG] 🔍 第二轮-结束时间: 在100ms偏移找到1个有效调整，剩余48个条目
[17:04:03] [DEBUG] 🔍 正在裁剪字幕静音区域...
[17:04:03] [DEBUG] 🔍 第二轮-结束时间: 200ms偏移无有效调整，剩余48个条目
[17:04:03] [DEBUG] 🔍 第二轮-结束时间: 总计处理1个条目，48个条目保持原样
[17:04:04] [DEBUG] 🔍 第二轮裁剪: 处理49个, 总计调整14个条目
[17:04:04] [DEBUG] 🔍 开始第三轮静音裁剪...
[17:04:04] [DEBUG] 🔍 第三轮裁剪: 处理49个条目
[17:04:04] [DEBUG] 🔍 正在裁剪字幕静音区域...
[17:04:04] [DEBUG] 🔍 第三轮-开始时间: 100ms偏移无有效调整，剩余49个条目
[17:04:04] [DEBUG] 🔍 正在裁剪字幕静音区域...
[17:04:04] [DEBUG] 🔍 第三轮-开始时间: 在200ms偏移找到1个有效调整，剩余48个条目
[17:04:04] [DEBUG] 🔍 第三轮-开始时间: 总计处理1个条目，48个条目保持原样
[17:04:04] [DEBUG] 🔍 正在裁剪字幕静音区域...
[17:04:04] [DEBUG] 🔍 第三轮-结束时间: 在100ms偏移找到27个有效调整，剩余22个条目
[17:04:04] [DEBUG] 🔍 正在裁剪字幕静音区域...
[17:04:04] [DEBUG] 🔍 第三轮-结束时间: 200ms偏移无有效调整，剩余22个条目
[17:04:04] [DEBUG] 🔍 第三轮-结束时间: 总计处理27个条目，22个条目保持原样
[17:04:04] [DEBUG] 🔍 第三轮裁剪: 处理49个, 总计调整3个条目
[17:04:04] ℹ️ 标准扩充: 增加显示 15.937 秒
[17:04:04] [DEBUG] 🔍 标准扩充详情: 完全26个, 部分22个
[17:04:04] [DEBUG] 🔍 解决扩充冲突: 15个
[17:04:04] ℹ️ 智能扩充: 处理8个短字幕, 增加显示 1.192 秒
[17:04:04] [DEBUG] 🔍 智能扩充详情: 完全6个, 部分2个, 目标1000ms
[17:04:04] ℹ️ 桥接处理: 填补间隔 0.320 秒
[17:04:04] [DEBUG] 🔍 桥接详情: 处理4个间隔, 阈值300ms
[17:04:04] ℹ️ API2: 字幕生成完成 - trimmed_Honey Baby, Honey Baby (1974)_part19_01-28-57-ElevenLabs-API2.srt
[17:04:04] ℹ️ 字幕生成完成，共生成 1 个字幕文件
[17:04:04] ℹ️ 文本处理统计:
[17:04:04] ℹ️   总LLM分段数: 48
[17:04:04] ℹ️   第一轮精确匹配: 48/48 (100.0%)
[17:04:04] ℹ️   第二轮锚点匹配: 0/48 (0.0%)
[17:04:04] ℹ️   文本匹配成功: 48/48 (100.0%)
[17:04:04] ℹ️   文本对齐成功: 48/48 (100.0%)
[17:04:04] [TaskFlow] 分段字幕生成成功: trimmed_Honey Baby, Honey Baby (1974)_part19_01-28-57 - ElevenLabs-API2
[17:04:04] [TaskFlow] 所有分段LLM处理完成
[17:04:04] [TaskFlow] 分段完成，继续字幕合并复制流程
[17:04:04] [INFO] 开始字幕生成流程: trimmed_Honey Baby, Honey Baby (1974).wav
[17:04:04] [INFO] 开始生成分段字幕: 19 个文件
[17:04:04] [INFO] 处理转录文件 (1/19): trimmed_Honey Baby, Honey Baby (1974)_part10_00-45-50-ElevenLabs-parsed.json
[17:04:04] ℹ️ 开始字幕生成: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part10_00-45-50-ElevenLabs-parsed.json
[17:04:04] [DEBUG] 🔍 提取服务名称，输入文件: trimmed_Honey Baby, Honey Baby (1974)_part10_00-45-50-ElevenLabs-parsed.json
[17:04:04] [DEBUG] 🔍 正则匹配成功，服务名称: ElevenLabs
[17:04:04] ℹ️ 检测到服务名称: ElevenLabs
[17:04:04] ℹ️ 检测已存在的字幕文件...
[17:04:04] [DEBUG] 🔍 🔍 扫描目录: ./projects\trimmed_Honey Baby, Honey Baby (1974)
[17:04:04] [DEBUG] 🔍 🔍 查找模式: trimmed_Honey Baby, Honey Baby (1974)_part10_00-45-50-ElevenLabs-*.srt
[17:04:04] [DEBUG] 🔍 🔍 找到 1 个匹配文件
[17:04:04] [DEBUG] 🔍 ✅ 有效文件: API2 -> trimmed_Honey Baby, Honey Baby (1974)_part10_00-45-50-ElevenLabs-API2.srt
[17:04:04] ℹ️ 发现 1 个已存在字幕文件
[17:04:04] ℹ️ 全部启用的API: ['API2']
[17:04:04] ℹ️ 已存在字幕的API: ['API2']
[17:04:04] ℹ️ 需要处理的API: []
[17:04:04] ℹ️ 添加已存在的字幕文件: API2 -> trimmed_Honey Baby, Honey Baby (1974)_part10_00-45-50-ElevenLabs-API2.srt
[17:04:04] ℹ️ 字幕生成完成，共生成 1 个字幕文件
[17:04:04] [SUCCESS] trimmed_Honey Baby, Honey Baby (1974)_part10_00-45-50-ElevenLabs-parsed.json 字幕生成成功
[17:04:04] [INFO] 处理转录文件 (2/19): trimmed_Honey Baby, Honey Baby (1974)_part11_00-50-41-ElevenLabs-parsed.json
[17:04:04] ℹ️ 开始字幕生成: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part11_00-50-41-ElevenLabs-parsed.json
[17:04:04] [DEBUG] 🔍 提取服务名称，输入文件: trimmed_Honey Baby, Honey Baby (1974)_part11_00-50-41-ElevenLabs-parsed.json
[17:04:04] [DEBUG] 🔍 正则匹配成功，服务名称: ElevenLabs
[17:04:04] ℹ️ 检测到服务名称: ElevenLabs
[17:04:04] ℹ️ 检测已存在的字幕文件...
[17:04:04] [DEBUG] 🔍 🔍 扫描目录: ./projects\trimmed_Honey Baby, Honey Baby (1974)
[17:04:04] [DEBUG] 🔍 🔍 查找模式: trimmed_Honey Baby, Honey Baby (1974)_part11_00-50-41-ElevenLabs-*.srt
[17:04:04] [DEBUG] 🔍 🔍 找到 1 个匹配文件
[17:04:04] [DEBUG] 🔍 ✅ 有效文件: API2 -> trimmed_Honey Baby, Honey Baby (1974)_part11_00-50-41-ElevenLabs-API2.srt
[17:04:04] ℹ️ 发现 1 个已存在字幕文件
[17:04:05] ℹ️ 全部启用的API: ['API2']
[17:04:05] ℹ️ 已存在字幕的API: ['API2']
[17:04:05] ℹ️ 需要处理的API: []
[17:04:05] ℹ️ 添加已存在的字幕文件: API2 -> trimmed_Honey Baby, Honey Baby (1974)_part11_00-50-41-ElevenLabs-API2.srt
[17:04:05] ℹ️ 字幕生成完成，共生成 1 个字幕文件
[17:04:05] [SUCCESS] trimmed_Honey Baby, Honey Baby (1974)_part11_00-50-41-ElevenLabs-parsed.json 字幕生成成功
[17:04:05] [INFO] 处理转录文件 (3/19): trimmed_Honey Baby, Honey Baby (1974)_part12_00-53-55-ElevenLabs-parsed.json
[17:04:05] ℹ️ 开始字幕生成: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part12_00-53-55-ElevenLabs-parsed.json
[17:04:05] [DEBUG] 🔍 提取服务名称，输入文件: trimmed_Honey Baby, Honey Baby (1974)_part12_00-53-55-ElevenLabs-parsed.json
[17:04:05] [DEBUG] 🔍 正则匹配成功，服务名称: ElevenLabs
[17:04:05] ℹ️ 检测到服务名称: ElevenLabs
[17:04:05] ℹ️ 检测已存在的字幕文件...
[17:04:05] [DEBUG] 🔍 🔍 扫描目录: ./projects\trimmed_Honey Baby, Honey Baby (1974)
[17:04:05] [DEBUG] 🔍 🔍 查找模式: trimmed_Honey Baby, Honey Baby (1974)_part12_00-53-55-ElevenLabs-*.srt
[17:04:05] [DEBUG] 🔍 🔍 找到 1 个匹配文件
[17:04:05] [DEBUG] 🔍 ✅ 有效文件: API2 -> trimmed_Honey Baby, Honey Baby (1974)_part12_00-53-55-ElevenLabs-API2.srt
[17:04:05] ℹ️ 发现 1 个已存在字幕文件
[17:04:05] ℹ️ 全部启用的API: ['API2']
[17:04:05] ℹ️ 已存在字幕的API: ['API2']
[17:04:05] ℹ️ 需要处理的API: []
[17:04:05] ℹ️ 添加已存在的字幕文件: API2 -> trimmed_Honey Baby, Honey Baby (1974)_part12_00-53-55-ElevenLabs-API2.srt
[17:04:05] ℹ️ 字幕生成完成，共生成 1 个字幕文件
[17:04:05] [SUCCESS] trimmed_Honey Baby, Honey Baby (1974)_part12_00-53-55-ElevenLabs-parsed.json 字幕生成成功
[17:04:05] [INFO] 处理转录文件 (4/19): trimmed_Honey Baby, Honey Baby (1974)_part13_00-58-16-ElevenLabs-parsed.json
[17:04:05] ℹ️ 开始字幕生成: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part13_00-58-16-ElevenLabs-parsed.json
[17:04:05] [DEBUG] 🔍 提取服务名称，输入文件: trimmed_Honey Baby, Honey Baby (1974)_part13_00-58-16-ElevenLabs-parsed.json
[17:04:05] [DEBUG] 🔍 正则匹配成功，服务名称: ElevenLabs
[17:04:05] ℹ️ 检测到服务名称: ElevenLabs
[17:04:05] ℹ️ 检测已存在的字幕文件...
[17:04:05] [DEBUG] 🔍 🔍 扫描目录: ./projects\trimmed_Honey Baby, Honey Baby (1974)
[17:04:05] [DEBUG] 🔍 🔍 查找模式: trimmed_Honey Baby, Honey Baby (1974)_part13_00-58-16-ElevenLabs-*.srt
[17:04:05] [DEBUG] 🔍 🔍 找到 1 个匹配文件
[17:04:05] [DEBUG] 🔍 ✅ 有效文件: API2 -> trimmed_Honey Baby, Honey Baby (1974)_part13_00-58-16-ElevenLabs-API2.srt
[17:04:05] ℹ️ 发现 1 个已存在字幕文件
[17:04:05] ℹ️ 全部启用的API: ['API2']
[17:04:05] ℹ️ 已存在字幕的API: ['API2']
[17:04:05] ℹ️ 需要处理的API: []
[17:04:05] ℹ️ 添加已存在的字幕文件: API2 -> trimmed_Honey Baby, Honey Baby (1974)_part13_00-58-16-ElevenLabs-API2.srt
[17:04:05] ℹ️ 字幕生成完成，共生成 1 个字幕文件
[17:04:05] [SUCCESS] trimmed_Honey Baby, Honey Baby (1974)_part13_00-58-16-ElevenLabs-parsed.json 字幕生成成功
[17:04:05] [INFO] 处理转录文件 (5/19): trimmed_Honey Baby, Honey Baby (1974)_part14_01-05-07-ElevenLabs-parsed.json
[17:04:05] ℹ️ 开始字幕生成: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part14_01-05-07-ElevenLabs-parsed.json
[17:04:05] [DEBUG] 🔍 提取服务名称，输入文件: trimmed_Honey Baby, Honey Baby (1974)_part14_01-05-07-ElevenLabs-parsed.json
[17:04:05] [DEBUG] 🔍 正则匹配成功，服务名称: ElevenLabs
[17:04:05] ℹ️ 检测到服务名称: ElevenLabs
[17:04:05] ℹ️ 检测已存在的字幕文件...
[17:04:05] [DEBUG] 🔍 🔍 扫描目录: ./projects\trimmed_Honey Baby, Honey Baby (1974)
[17:04:05] [DEBUG] 🔍 🔍 查找模式: trimmed_Honey Baby, Honey Baby (1974)_part14_01-05-07-ElevenLabs-*.srt
[17:04:05] [DEBUG] 🔍 🔍 找到 1 个匹配文件
[17:04:05] [DEBUG] 🔍 ✅ 有效文件: API2 -> trimmed_Honey Baby, Honey Baby (1974)_part14_01-05-07-ElevenLabs-API2.srt
[17:04:05] ℹ️ 发现 1 个已存在字幕文件
[17:04:05] ℹ️ 全部启用的API: ['API2']
[17:04:05] ℹ️ 已存在字幕的API: ['API2']
[17:04:05] ℹ️ 需要处理的API: []
[17:04:05] ℹ️ 添加已存在的字幕文件: API2 -> trimmed_Honey Baby, Honey Baby (1974)_part14_01-05-07-ElevenLabs-API2.srt
[17:04:05] ℹ️ 字幕生成完成，共生成 1 个字幕文件
[17:04:05] [SUCCESS] trimmed_Honey Baby, Honey Baby (1974)_part14_01-05-07-ElevenLabs-parsed.json 字幕生成成功
[17:04:05] [INFO] 处理转录文件 (6/19): trimmed_Honey Baby, Honey Baby (1974)_part15_01-09-51-ElevenLabs-parsed.json
[17:04:05] ℹ️ 开始字幕生成: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part15_01-09-51-ElevenLabs-parsed.json
[17:04:05] [DEBUG] 🔍 提取服务名称，输入文件: trimmed_Honey Baby, Honey Baby (1974)_part15_01-09-51-ElevenLabs-parsed.json
[17:04:06] [DEBUG] 🔍 正则匹配成功，服务名称: ElevenLabs
[17:04:06] ℹ️ 检测到服务名称: ElevenLabs
[17:04:06] ℹ️ 检测已存在的字幕文件...
[17:04:06] [DEBUG] 🔍 🔍 扫描目录: ./projects\trimmed_Honey Baby, Honey Baby (1974)
[17:04:06] [DEBUG] 🔍 🔍 查找模式: trimmed_Honey Baby, Honey Baby (1974)_part15_01-09-51-ElevenLabs-*.srt
[17:04:06] [DEBUG] 🔍 🔍 找到 1 个匹配文件
[17:04:06] [DEBUG] 🔍 ✅ 有效文件: API2 -> trimmed_Honey Baby, Honey Baby (1974)_part15_01-09-51-ElevenLabs-API2.srt
[17:04:06] ℹ️ 发现 1 个已存在字幕文件
[17:04:06] ℹ️ 全部启用的API: ['API2']
[17:04:06] ℹ️ 已存在字幕的API: ['API2']
[17:04:06] ℹ️ 需要处理的API: []
[17:04:06] ℹ️ 添加已存在的字幕文件: API2 -> trimmed_Honey Baby, Honey Baby (1974)_part15_01-09-51-ElevenLabs-API2.srt
[17:04:06] ℹ️ 字幕生成完成，共生成 1 个字幕文件
[17:04:06] [SUCCESS] trimmed_Honey Baby, Honey Baby (1974)_part15_01-09-51-ElevenLabs-parsed.json 字幕生成成功
[17:04:06] [INFO] 处理转录文件 (7/19): trimmed_Honey Baby, Honey Baby (1974)_part16_01-14-29-ElevenLabs-parsed.json
[17:04:06] ℹ️ 开始字幕生成: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part16_01-14-29-ElevenLabs-parsed.json
[17:04:06] [DEBUG] 🔍 提取服务名称，输入文件: trimmed_Honey Baby, Honey Baby (1974)_part16_01-14-29-ElevenLabs-parsed.json
[17:04:06] [DEBUG] 🔍 正则匹配成功，服务名称: ElevenLabs
[17:04:06] ℹ️ 检测到服务名称: ElevenLabs
[17:04:06] ℹ️ 检测已存在的字幕文件...
[17:04:06] [DEBUG] 🔍 🔍 扫描目录: ./projects\trimmed_Honey Baby, Honey Baby (1974)
[17:04:06] [DEBUG] 🔍 🔍 查找模式: trimmed_Honey Baby, Honey Baby (1974)_part16_01-14-29-ElevenLabs-*.srt
[17:04:06] [DEBUG] 🔍 🔍 找到 1 个匹配文件
[17:04:06] [DEBUG] 🔍 ✅ 有效文件: API2 -> trimmed_Honey Baby, Honey Baby (1974)_part16_01-14-29-ElevenLabs-API2.srt
[17:04:06] ℹ️ 发现 1 个已存在字幕文件
[17:04:06] ℹ️ 全部启用的API: ['API2']
[17:04:06] ℹ️ 已存在字幕的API: ['API2']
[17:04:06] ℹ️ 需要处理的API: []
[17:04:06] ℹ️ 添加已存在的字幕文件: API2 -> trimmed_Honey Baby, Honey Baby (1974)_part16_01-14-29-ElevenLabs-API2.srt
[17:04:06] ℹ️ 字幕生成完成，共生成 1 个字幕文件
[17:04:06] [SUCCESS] trimmed_Honey Baby, Honey Baby (1974)_part16_01-14-29-ElevenLabs-parsed.json 字幕生成成功
[17:04:06] [INFO] 处理转录文件 (8/19): trimmed_Honey Baby, Honey Baby (1974)_part17_01-20-54-ElevenLabs-parsed.json
[17:04:06] ℹ️ 开始字幕生成: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part17_01-20-54-ElevenLabs-parsed.json
[17:04:06] [DEBUG] 🔍 提取服务名称，输入文件: trimmed_Honey Baby, Honey Baby (1974)_part17_01-20-54-ElevenLabs-parsed.json
[17:04:06] [DEBUG] 🔍 正则匹配成功，服务名称: ElevenLabs
[17:04:06] ℹ️ 检测到服务名称: ElevenLabs
[17:04:06] ℹ️ 检测已存在的字幕文件...
[17:04:06] [DEBUG] 🔍 🔍 扫描目录: ./projects\trimmed_Honey Baby, Honey Baby (1974)
[17:04:06] [DEBUG] 🔍 🔍 查找模式: trimmed_Honey Baby, Honey Baby (1974)_part17_01-20-54-ElevenLabs-*.srt
[17:04:06] [DEBUG] 🔍 🔍 找到 1 个匹配文件
[17:04:06] [DEBUG] 🔍 ✅ 有效文件: API2 -> trimmed_Honey Baby, Honey Baby (1974)_part17_01-20-54-ElevenLabs-API2.srt
[17:04:06] ℹ️ 发现 1 个已存在字幕文件
[17:04:06] ℹ️ 全部启用的API: ['API2']
[17:04:06] ℹ️ 已存在字幕的API: ['API2']
[17:04:06] ℹ️ 需要处理的API: []
[17:04:06] ℹ️ 添加已存在的字幕文件: API2 -> trimmed_Honey Baby, Honey Baby (1974)_part17_01-20-54-ElevenLabs-API2.srt
[17:04:06] ℹ️ 字幕生成完成，共生成 1 个字幕文件
[17:04:06] [SUCCESS] trimmed_Honey Baby, Honey Baby (1974)_part17_01-20-54-ElevenLabs-parsed.json 字幕生成成功
[17:04:06] [INFO] 处理转录文件 (9/19): trimmed_Honey Baby, Honey Baby (1974)_part18_01-24-10-ElevenLabs-parsed.json
[17:04:06] ℹ️ 开始字幕生成: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part18_01-24-10-ElevenLabs-parsed.json
[17:04:06] [DEBUG] 🔍 提取服务名称，输入文件: trimmed_Honey Baby, Honey Baby (1974)_part18_01-24-10-ElevenLabs-parsed.json
[17:04:06] [DEBUG] 🔍 正则匹配成功，服务名称: ElevenLabs
[17:04:06] ℹ️ 检测到服务名称: ElevenLabs
[17:04:06] ℹ️ 检测已存在的字幕文件...
[17:04:06] [DEBUG] 🔍 🔍 扫描目录: ./projects\trimmed_Honey Baby, Honey Baby (1974)
[17:04:06] [DEBUG] 🔍 🔍 查找模式: trimmed_Honey Baby, Honey Baby (1974)_part18_01-24-10-ElevenLabs-*.srt
[17:04:06] [DEBUG] 🔍 🔍 找到 1 个匹配文件
[17:04:06] [DEBUG] 🔍 ✅ 有效文件: API2 -> trimmed_Honey Baby, Honey Baby (1974)_part18_01-24-10-ElevenLabs-API2.srt
[17:04:06] ℹ️ 发现 1 个已存在字幕文件
[17:04:06] ℹ️ 全部启用的API: ['API2']
[17:04:06] ℹ️ 已存在字幕的API: ['API2']
[17:04:06] ℹ️ 需要处理的API: []
[17:04:06] ℹ️ 添加已存在的字幕文件: API2 -> trimmed_Honey Baby, Honey Baby (1974)_part18_01-24-10-ElevenLabs-API2.srt
[17:04:06] ℹ️ 字幕生成完成，共生成 1 个字幕文件
[17:04:06] [SUCCESS] trimmed_Honey Baby, Honey Baby (1974)_part18_01-24-10-ElevenLabs-parsed.json 字幕生成成功
[17:04:07] [INFO] 处理转录文件 (10/19): trimmed_Honey Baby, Honey Baby (1974)_part19_01-28-57-ElevenLabs-parsed.json
[17:04:07] ℹ️ 开始字幕生成: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part19_01-28-57-ElevenLabs-parsed.json
[17:04:07] [DEBUG] 🔍 提取服务名称，输入文件: trimmed_Honey Baby, Honey Baby (1974)_part19_01-28-57-ElevenLabs-parsed.json
[17:04:07] [DEBUG] 🔍 正则匹配成功，服务名称: ElevenLabs
[17:04:07] ℹ️ 检测到服务名称: ElevenLabs
[17:04:07] ℹ️ 检测已存在的字幕文件...
[17:04:07] [DEBUG] 🔍 🔍 扫描目录: ./projects\trimmed_Honey Baby, Honey Baby (1974)
[17:04:07] [DEBUG] 🔍 🔍 查找模式: trimmed_Honey Baby, Honey Baby (1974)_part19_01-28-57-ElevenLabs-*.srt
[17:04:07] [DEBUG] 🔍 🔍 找到 1 个匹配文件
[17:04:07] [DEBUG] 🔍 ✅ 有效文件: API2 -> trimmed_Honey Baby, Honey Baby (1974)_part19_01-28-57-ElevenLabs-API2.srt
[17:04:07] ℹ️ 发现 1 个已存在字幕文件
[17:04:07] ℹ️ 全部启用的API: ['API2']
[17:04:07] ℹ️ 已存在字幕的API: ['API2']
[17:04:07] ℹ️ 需要处理的API: []
[17:04:07] ℹ️ 添加已存在的字幕文件: API2 -> trimmed_Honey Baby, Honey Baby (1974)_part19_01-28-57-ElevenLabs-API2.srt
[17:04:07] ℹ️ 字幕生成完成，共生成 1 个字幕文件
[17:04:07] [SUCCESS] trimmed_Honey Baby, Honey Baby (1974)_part19_01-28-57-ElevenLabs-parsed.json 字幕生成成功
[17:04:07] [INFO] 处理转录文件 (11/19): trimmed_Honey Baby, Honey Baby (1974)_part1_00-00-00-ElevenLabs-parsed.json
[17:04:07] ℹ️ 开始字幕生成: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part1_00-00-00-ElevenLabs-parsed.json
[17:04:07] [DEBUG] 🔍 提取服务名称，输入文件: trimmed_Honey Baby, Honey Baby (1974)_part1_00-00-00-ElevenLabs-parsed.json
[17:04:07] [DEBUG] 🔍 正则匹配成功，服务名称: ElevenLabs
[17:04:07] ℹ️ 检测到服务名称: ElevenLabs
[17:04:07] ℹ️ 检测已存在的字幕文件...
[17:04:07] [DEBUG] 🔍 🔍 扫描目录: ./projects\trimmed_Honey Baby, Honey Baby (1974)
[17:04:07] [DEBUG] 🔍 🔍 查找模式: trimmed_Honey Baby, Honey Baby (1974)_part1_00-00-00-ElevenLabs-*.srt
[17:04:07] [DEBUG] 🔍 🔍 找到 1 个匹配文件
[17:04:07] [DEBUG] 🔍 ✅ 有效文件: API2 -> trimmed_Honey Baby, Honey Baby (1974)_part1_00-00-00-ElevenLabs-API2.srt
[17:04:07] ℹ️ 发现 1 个已存在字幕文件
[17:04:07] ℹ️ 全部启用的API: ['API2']
[17:04:07] ℹ️ 已存在字幕的API: ['API2']
[17:04:07] ℹ️ 需要处理的API: []
[17:04:07] ℹ️ 添加已存在的字幕文件: API2 -> trimmed_Honey Baby, Honey Baby (1974)_part1_00-00-00-ElevenLabs-API2.srt
[17:04:07] ℹ️ 字幕生成完成，共生成 1 个字幕文件
[17:04:07] [SUCCESS] trimmed_Honey Baby, Honey Baby (1974)_part1_00-00-00-ElevenLabs-parsed.json 字幕生成成功
[17:04:07] [INFO] 处理转录文件 (12/19): trimmed_Honey Baby, Honey Baby (1974)_part2_00-05-18-ElevenLabs-parsed.json
[17:04:07] ℹ️ 开始字幕生成: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part2_00-05-18-ElevenLabs-parsed.json
[17:04:07] [DEBUG] 🔍 提取服务名称，输入文件: trimmed_Honey Baby, Honey Baby (1974)_part2_00-05-18-ElevenLabs-parsed.json
[17:04:07] [DEBUG] 🔍 正则匹配成功，服务名称: ElevenLabs
[17:04:07] ℹ️ 检测到服务名称: ElevenLabs
[17:04:07] ℹ️ 检测已存在的字幕文件...
[17:04:07] [DEBUG] 🔍 🔍 扫描目录: ./projects\trimmed_Honey Baby, Honey Baby (1974)
[17:04:07] [DEBUG] 🔍 🔍 查找模式: trimmed_Honey Baby, Honey Baby (1974)_part2_00-05-18-ElevenLabs-*.srt
[17:04:07] [DEBUG] 🔍 🔍 找到 1 个匹配文件
[17:04:07] [DEBUG] 🔍 ✅ 有效文件: API2 -> trimmed_Honey Baby, Honey Baby (1974)_part2_00-05-18-ElevenLabs-API2.srt
[17:04:07] ℹ️ 发现 1 个已存在字幕文件
[17:04:07] ℹ️ 全部启用的API: ['API2']
[17:04:07] ℹ️ 已存在字幕的API: ['API2']
[17:04:07] ℹ️ 需要处理的API: []
[17:04:07] ℹ️ 添加已存在的字幕文件: API2 -> trimmed_Honey Baby, Honey Baby (1974)_part2_00-05-18-ElevenLabs-API2.srt
[17:04:07] ℹ️ 字幕生成完成，共生成 1 个字幕文件
[17:04:07] [SUCCESS] trimmed_Honey Baby, Honey Baby (1974)_part2_00-05-18-ElevenLabs-parsed.json 字幕生成成功
[17:04:07] [INFO] 处理转录文件 (13/19): trimmed_Honey Baby, Honey Baby (1974)_part3_00-10-14-ElevenLabs-parsed.json
[17:04:07] ℹ️ 开始字幕生成: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part3_00-10-14-ElevenLabs-parsed.json
[17:04:07] [DEBUG] 🔍 提取服务名称，输入文件: trimmed_Honey Baby, Honey Baby (1974)_part3_00-10-14-ElevenLabs-parsed.json
[17:04:07] [DEBUG] 🔍 正则匹配成功，服务名称: ElevenLabs
[17:04:07] ℹ️ 检测到服务名称: ElevenLabs
[17:04:07] ℹ️ 检测已存在的字幕文件...
[17:04:07] [DEBUG] 🔍 🔍 扫描目录: ./projects\trimmed_Honey Baby, Honey Baby (1974)
[17:04:07] [DEBUG] 🔍 🔍 查找模式: trimmed_Honey Baby, Honey Baby (1974)_part3_00-10-14-ElevenLabs-*.srt
[17:04:07] [DEBUG] 🔍 🔍 找到 1 个匹配文件
[17:04:07] [DEBUG] 🔍 ✅ 有效文件: API2 -> trimmed_Honey Baby, Honey Baby (1974)_part3_00-10-14-ElevenLabs-API2.srt
[17:04:08] ℹ️ 发现 1 个已存在字幕文件
[17:04:08] ℹ️ 全部启用的API: ['API2']
[17:04:08] ℹ️ 已存在字幕的API: ['API2']
[17:04:08] ℹ️ 需要处理的API: []
[17:04:08] ℹ️ 添加已存在的字幕文件: API2 -> trimmed_Honey Baby, Honey Baby (1974)_part3_00-10-14-ElevenLabs-API2.srt
[17:04:08] ℹ️ 字幕生成完成，共生成 1 个字幕文件
[17:04:08] [SUCCESS] trimmed_Honey Baby, Honey Baby (1974)_part3_00-10-14-ElevenLabs-parsed.json 字幕生成成功
[17:04:08] [INFO] 处理转录文件 (14/19): trimmed_Honey Baby, Honey Baby (1974)_part4_00-14-25-ElevenLabs-parsed.json
[17:04:08] ℹ️ 开始字幕生成: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part4_00-14-25-ElevenLabs-parsed.json
[17:04:08] [DEBUG] 🔍 提取服务名称，输入文件: trimmed_Honey Baby, Honey Baby (1974)_part4_00-14-25-ElevenLabs-parsed.json
[17:04:08] [DEBUG] 🔍 正则匹配成功，服务名称: ElevenLabs
[17:04:08] ℹ️ 检测到服务名称: ElevenLabs
[17:04:08] ℹ️ 检测已存在的字幕文件...
[17:04:08] [DEBUG] 🔍 🔍 扫描目录: ./projects\trimmed_Honey Baby, Honey Baby (1974)
[17:04:08] [DEBUG] 🔍 🔍 查找模式: trimmed_Honey Baby, Honey Baby (1974)_part4_00-14-25-ElevenLabs-*.srt
[17:04:08] [DEBUG] 🔍 🔍 找到 1 个匹配文件
[17:04:08] [DEBUG] 🔍 ✅ 有效文件: API2 -> trimmed_Honey Baby, Honey Baby (1974)_part4_00-14-25-ElevenLabs-API2.srt
[17:04:08] ℹ️ 发现 1 个已存在字幕文件
[17:04:08] ℹ️ 全部启用的API: ['API2']
[17:04:08] ℹ️ 已存在字幕的API: ['API2']
[17:04:08] ℹ️ 需要处理的API: []
[17:04:08] ℹ️ 添加已存在的字幕文件: API2 -> trimmed_Honey Baby, Honey Baby (1974)_part4_00-14-25-ElevenLabs-API2.srt
[17:04:08] ℹ️ 字幕生成完成，共生成 1 个字幕文件
[17:04:08] [SUCCESS] trimmed_Honey Baby, Honey Baby (1974)_part4_00-14-25-ElevenLabs-parsed.json 字幕生成成功
[17:04:08] [INFO] 处理转录文件 (15/19): trimmed_Honey Baby, Honey Baby (1974)_part5_00-19-51-ElevenLabs-parsed.json
[17:04:08] ℹ️ 开始字幕生成: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part5_00-19-51-ElevenLabs-parsed.json
[17:04:08] [DEBUG] 🔍 提取服务名称，输入文件: trimmed_Honey Baby, Honey Baby (1974)_part5_00-19-51-ElevenLabs-parsed.json
[17:04:08] [DEBUG] 🔍 正则匹配成功，服务名称: ElevenLabs
[17:04:08] ℹ️ 检测到服务名称: ElevenLabs
[17:04:08] ℹ️ 检测已存在的字幕文件...
[17:04:08] [DEBUG] 🔍 🔍 扫描目录: ./projects\trimmed_Honey Baby, Honey Baby (1974)
[17:04:08] [DEBUG] 🔍 🔍 查找模式: trimmed_Honey Baby, Honey Baby (1974)_part5_00-19-51-ElevenLabs-*.srt
[17:04:08] [DEBUG] 🔍 🔍 找到 1 个匹配文件
[17:04:08] [DEBUG] 🔍 ✅ 有效文件: API2 -> trimmed_Honey Baby, Honey Baby (1974)_part5_00-19-51-ElevenLabs-API2.srt
[17:04:08] ℹ️ 发现 1 个已存在字幕文件
[17:04:08] ℹ️ 全部启用的API: ['API2']
[17:04:08] ℹ️ 已存在字幕的API: ['API2']
[17:04:08] ℹ️ 需要处理的API: []
[17:04:08] ℹ️ 添加已存在的字幕文件: API2 -> trimmed_Honey Baby, Honey Baby (1974)_part5_00-19-51-ElevenLabs-API2.srt
[17:04:08] ℹ️ 字幕生成完成，共生成 1 个字幕文件
[17:04:08] [SUCCESS] trimmed_Honey Baby, Honey Baby (1974)_part5_00-19-51-ElevenLabs-parsed.json 字幕生成成功
[17:04:08] [INFO] 处理转录文件 (16/19): trimmed_Honey Baby, Honey Baby (1974)_part6_00-23-39-ElevenLabs-parsed.json
[17:04:08] ℹ️ 开始字幕生成: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part6_00-23-39-ElevenLabs-parsed.json
[17:04:08] [DEBUG] 🔍 提取服务名称，输入文件: trimmed_Honey Baby, Honey Baby (1974)_part6_00-23-39-ElevenLabs-parsed.json
[17:04:08] [DEBUG] 🔍 正则匹配成功，服务名称: ElevenLabs
[17:04:08] ℹ️ 检测到服务名称: ElevenLabs
[17:04:08] ℹ️ 检测已存在的字幕文件...
[17:04:08] [DEBUG] 🔍 🔍 扫描目录: ./projects\trimmed_Honey Baby, Honey Baby (1974)
[17:04:08] [DEBUG] 🔍 🔍 查找模式: trimmed_Honey Baby, Honey Baby (1974)_part6_00-23-39-ElevenLabs-*.srt
[17:04:08] [DEBUG] 🔍 🔍 找到 1 个匹配文件
[17:04:08] [DEBUG] 🔍 ✅ 有效文件: API2 -> trimmed_Honey Baby, Honey Baby (1974)_part6_00-23-39-ElevenLabs-API2.srt
[17:04:08] ℹ️ 发现 1 个已存在字幕文件
[17:04:08] ℹ️ 全部启用的API: ['API2']
[17:04:08] ℹ️ 已存在字幕的API: ['API2']
[17:04:08] ℹ️ 需要处理的API: []
[17:04:08] ℹ️ 添加已存在的字幕文件: API2 -> trimmed_Honey Baby, Honey Baby (1974)_part6_00-23-39-ElevenLabs-API2.srt
[17:04:08] ℹ️ 字幕生成完成，共生成 1 个字幕文件
[17:04:08] [SUCCESS] trimmed_Honey Baby, Honey Baby (1974)_part6_00-23-39-ElevenLabs-parsed.json 字幕生成成功
[17:04:08] [INFO] 处理转录文件 (17/19): trimmed_Honey Baby, Honey Baby (1974)_part7_00-30-41-ElevenLabs-parsed.json
[17:04:08] ℹ️ 开始字幕生成: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part7_00-30-41-ElevenLabs-parsed.json
[17:04:08] [DEBUG] 🔍 提取服务名称，输入文件: trimmed_Honey Baby, Honey Baby (1974)_part7_00-30-41-ElevenLabs-parsed.json
[17:04:08] [DEBUG] 🔍 正则匹配成功，服务名称: ElevenLabs
[17:04:09] ℹ️ 检测到服务名称: ElevenLabs
[17:04:09] ℹ️ 检测已存在的字幕文件...
[17:04:09] [DEBUG] 🔍 🔍 扫描目录: ./projects\trimmed_Honey Baby, Honey Baby (1974)
[17:04:09] [DEBUG] 🔍 🔍 查找模式: trimmed_Honey Baby, Honey Baby (1974)_part7_00-30-41-ElevenLabs-*.srt
[17:04:09] [DEBUG] 🔍 🔍 找到 1 个匹配文件
[17:04:09] [DEBUG] 🔍 ✅ 有效文件: API2 -> trimmed_Honey Baby, Honey Baby (1974)_part7_00-30-41-ElevenLabs-API2.srt
[17:04:09] ℹ️ 发现 1 个已存在字幕文件
[17:04:09] ℹ️ 全部启用的API: ['API2']
[17:04:09] ℹ️ 已存在字幕的API: ['API2']
[17:04:09] ℹ️ 需要处理的API: []
[17:04:09] ℹ️ 添加已存在的字幕文件: API2 -> trimmed_Honey Baby, Honey Baby (1974)_part7_00-30-41-ElevenLabs-API2.srt
[17:04:09] ℹ️ 字幕生成完成，共生成 1 个字幕文件
[17:04:09] [SUCCESS] trimmed_Honey Baby, Honey Baby (1974)_part7_00-30-41-ElevenLabs-parsed.json 字幕生成成功
[17:04:09] [INFO] 处理转录文件 (18/19): trimmed_Honey Baby, Honey Baby (1974)_part8_00-34-17-ElevenLabs-parsed.json
[17:04:09] ℹ️ 开始字幕生成: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part8_00-34-17-ElevenLabs-parsed.json
[17:04:09] [DEBUG] 🔍 提取服务名称，输入文件: trimmed_Honey Baby, Honey Baby (1974)_part8_00-34-17-ElevenLabs-parsed.json
[17:04:09] [DEBUG] 🔍 正则匹配成功，服务名称: ElevenLabs
[17:04:09] ℹ️ 检测到服务名称: ElevenLabs
[17:04:09] ℹ️ 检测已存在的字幕文件...
[17:04:09] [DEBUG] 🔍 🔍 扫描目录: ./projects\trimmed_Honey Baby, Honey Baby (1974)
[17:04:09] [DEBUG] 🔍 🔍 查找模式: trimmed_Honey Baby, Honey Baby (1974)_part8_00-34-17-ElevenLabs-*.srt
[17:04:09] [DEBUG] 🔍 🔍 找到 1 个匹配文件
[17:04:09] [DEBUG] 🔍 ✅ 有效文件: API2 -> trimmed_Honey Baby, Honey Baby (1974)_part8_00-34-17-ElevenLabs-API2.srt
[17:04:09] ℹ️ 发现 1 个已存在字幕文件
[17:04:09] ℹ️ 全部启用的API: ['API2']
[17:04:09] ℹ️ 已存在字幕的API: ['API2']
[17:04:09] ℹ️ 需要处理的API: []
[17:04:09] ℹ️ 添加已存在的字幕文件: API2 -> trimmed_Honey Baby, Honey Baby (1974)_part8_00-34-17-ElevenLabs-API2.srt
[17:04:09] ℹ️ 字幕生成完成，共生成 1 个字幕文件
[17:04:09] [SUCCESS] trimmed_Honey Baby, Honey Baby (1974)_part8_00-34-17-ElevenLabs-parsed.json 字幕生成成功
[17:04:09] [INFO] 处理转录文件 (19/19): trimmed_Honey Baby, Honey Baby (1974)_part9_00-40-29-ElevenLabs-parsed.json
[17:04:09] ℹ️ 开始字幕生成: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)_part9_00-40-29-ElevenLabs-parsed.json
[17:04:09] [DEBUG] 🔍 提取服务名称，输入文件: trimmed_Honey Baby, Honey Baby (1974)_part9_00-40-29-ElevenLabs-parsed.json
[17:04:09] [DEBUG] 🔍 正则匹配成功，服务名称: ElevenLabs
[17:04:09] ℹ️ 检测到服务名称: ElevenLabs
[17:04:09] ℹ️ 检测已存在的字幕文件...
[17:04:09] [DEBUG] 🔍 🔍 扫描目录: ./projects\trimmed_Honey Baby, Honey Baby (1974)
[17:04:09] [DEBUG] 🔍 🔍 查找模式: trimmed_Honey Baby, Honey Baby (1974)_part9_00-40-29-ElevenLabs-*.srt
[17:04:09] [DEBUG] 🔍 🔍 找到 1 个匹配文件
[17:04:09] [DEBUG] 🔍 ✅ 有效文件: API2 -> trimmed_Honey Baby, Honey Baby (1974)_part9_00-40-29-ElevenLabs-API2.srt
[17:04:09] ℹ️ 发现 1 个已存在字幕文件
[17:04:09] ℹ️ 全部启用的API: ['API2']
[17:04:09] ℹ️ 已存在字幕的API: ['API2']
[17:04:09] ℹ️ 需要处理的API: []
[17:04:09] ℹ️ 添加已存在的字幕文件: API2 -> trimmed_Honey Baby, Honey Baby (1974)_part9_00-40-29-ElevenLabs-API2.srt
[17:04:09] ℹ️ 字幕生成完成，共生成 1 个字幕文件
[17:04:09] [SUCCESS] trimmed_Honey Baby, Honey Baby (1974)_part9_00-40-29-ElevenLabs-parsed.json 字幕生成成功
[17:04:09] [INFO] 分段生成完成: 成功 19, 失败 0
[17:04:09] [INFO] 检查合并准备状态: trimmed_Honey Baby, Honey Baby (1974).wav
[17:04:09] [INFO] 分析合并准备状态: 1 ASR × 1 LLM
[17:04:09] [READY] ElevenLabs-API2 准备合并 (19 个分段)
[17:04:09] [INFO] 开始字幕合并: 1 个组合
[17:04:09] [INFO] 开始执行字幕合并: 1 个组合
[17:04:09] [INFO] 合并组合 (1/1): ElevenLabs-API2
[17:04:09] 开始合并 19 个分段字幕文件
[17:04:09] [DEBUG] 解析文件名 trimmed_Honey Baby, Honey Baby (1974)_part10_00-45-50-ElevenLabs-API2.srt: 偏移=2750s, 分段=10
[17:04:09] [DEBUG] 时间戳校正完成，偏移量: 2750.0s
[17:04:09] [DEBUG] 解析SRT条目: 64 个
[17:04:09] [DEBUG] 分段 10: 64 个条目，偏移 2750.0s
[17:04:10] [DEBUG] 解析文件名 trimmed_Honey Baby, Honey Baby (1974)_part11_00-50-41-ElevenLabs-API2.srt: 偏移=3041s, 分段=11
[17:04:10] [DEBUG] 时间戳校正完成，偏移量: 3041.0s
[17:04:10] [DEBUG] 解析SRT条目: 54 个
[17:04:10] [DEBUG] 分段 11: 54 个条目，偏移 3041.0s
[17:04:10] [DEBUG] 解析文件名 trimmed_Honey Baby, Honey Baby (1974)_part12_00-53-55-ElevenLabs-API2.srt: 偏移=3235s, 分段=12
[17:04:10] [DEBUG] 时间戳校正完成，偏移量: 3235.0s
[17:04:10] [DEBUG] 解析SRT条目: 33 个
[17:04:10] [DEBUG] 分段 12: 33 个条目，偏移 3235.0s
[17:04:10] [DEBUG] 解析文件名 trimmed_Honey Baby, Honey Baby (1974)_part13_00-58-16-ElevenLabs-API2.srt: 偏移=3496s, 分段=13
[17:04:10] [DEBUG] 时间戳校正完成，偏移量: 3496.0s
[17:04:10] [DEBUG] 解析SRT条目: 59 个
[17:04:10] [DEBUG] 分段 13: 59 个条目，偏移 3496.0s
[17:04:10] [DEBUG] 解析文件名 trimmed_Honey Baby, Honey Baby (1974)_part14_01-05-07-ElevenLabs-API2.srt: 偏移=3907s, 分段=14
[17:04:10] [DEBUG] 时间戳校正完成，偏移量: 3907.0s
[17:04:10] [DEBUG] 解析SRT条目: 44 个
[17:04:10] [DEBUG] 分段 14: 44 个条目，偏移 3907.0s
[17:04:10] [DEBUG] 解析文件名 trimmed_Honey Baby, Honey Baby (1974)_part15_01-09-51-ElevenLabs-API2.srt: 偏移=4191s, 分段=15
[17:04:10] [DEBUG] 时间戳校正完成，偏移量: 4191.0s
[17:04:10] [DEBUG] 解析SRT条目: 81 个
[17:04:10] [DEBUG] 分段 15: 81 个条目，偏移 4191.0s
[17:04:10] [DEBUG] 解析文件名 trimmed_Honey Baby, Honey Baby (1974)_part16_01-14-29-ElevenLabs-API2.srt: 偏移=4469s, 分段=16
[17:04:10] [DEBUG] 时间戳校正完成，偏移量: 4469.0s
[17:04:10] [DEBUG] 解析SRT条目: 82 个
[17:04:10] [DEBUG] 分段 16: 82 个条目，偏移 4469.0s
[17:04:10] [DEBUG] 解析文件名 trimmed_Honey Baby, Honey Baby (1974)_part17_01-20-54-ElevenLabs-API2.srt: 偏移=4854s, 分段=17
[17:04:10] [DEBUG] 时间戳校正完成，偏移量: 4854.0s
[17:04:10] [DEBUG] 解析SRT条目: 40 个
[17:04:10] [DEBUG] 分段 17: 40 个条目，偏移 4854.0s
[17:04:10] [DEBUG] 解析文件名 trimmed_Honey Baby, Honey Baby (1974)_part18_01-24-10-ElevenLabs-API2.srt: 偏移=5050s, 分段=18
[17:04:10] [DEBUG] 时间戳校正完成，偏移量: 5050.0s
[17:04:10] [DEBUG] 解析SRT条目: 20 个
[17:04:10] [DEBUG] 分段 18: 20 个条目，偏移 5050.0s
[17:04:10] [DEBUG] 解析文件名 trimmed_Honey Baby, Honey Baby (1974)_part19_01-28-57-ElevenLabs-API2.srt: 偏移=5337s, 分段=19
[17:04:10] [DEBUG] 时间戳校正完成，偏移量: 5337.0s
[17:04:10] [DEBUG] 解析SRT条目: 49 个
[17:04:10] [DEBUG] 分段 19: 49 个条目，偏移 5337.0s
[17:04:10] [DEBUG] 解析文件名 trimmed_Honey Baby, Honey Baby (1974)_part1_00-00-00-ElevenLabs-API2.srt: 偏移=0s, 分段=1
[17:04:10] [DEBUG] 时间戳校正完成，偏移量: 0.0s
[17:04:10] [DEBUG] 解析SRT条目: 72 个
[17:04:10] [DEBUG] 分段 1: 72 个条目，偏移 0.0s
[17:04:10] [DEBUG] 解析文件名 trimmed_Honey Baby, Honey Baby (1974)_part2_00-05-18-ElevenLabs-API2.srt: 偏移=318s, 分段=2
[17:04:10] [DEBUG] 时间戳校正完成，偏移量: 318.0s
[17:04:10] [DEBUG] 解析SRT条目: 47 个
[17:04:10] [DEBUG] 分段 2: 47 个条目，偏移 318.0s
[17:04:10] [DEBUG] 解析文件名 trimmed_Honey Baby, Honey Baby (1974)_part3_00-10-14-ElevenLabs-API2.srt: 偏移=614s, 分段=3
[17:04:10] [DEBUG] 时间戳校正完成，偏移量: 614.0s
[17:04:10] [DEBUG] 解析SRT条目: 57 个
[17:04:10] [DEBUG] 分段 3: 57 个条目，偏移 614.0s
[17:04:10] [DEBUG] 解析文件名 trimmed_Honey Baby, Honey Baby (1974)_part4_00-14-25-ElevenLabs-API2.srt: 偏移=865s, 分段=4
[17:04:10] [DEBUG] 时间戳校正完成，偏移量: 865.0s
[17:04:10] [DEBUG] 解析SRT条目: 44 个
[17:04:10] [DEBUG] 分段 4: 44 个条目，偏移 865.0s
[17:04:10] [DEBUG] 解析文件名 trimmed_Honey Baby, Honey Baby (1974)_part5_00-19-51-ElevenLabs-API2.srt: 偏移=1191s, 分段=5
[17:04:10] [DEBUG] 时间戳校正完成，偏移量: 1191.0s
[17:04:10] [DEBUG] 解析SRT条目: 54 个
[17:04:10] [DEBUG] 分段 5: 54 个条目，偏移 1191.0s
[17:04:10] [DEBUG] 解析文件名 trimmed_Honey Baby, Honey Baby (1974)_part6_00-23-39-ElevenLabs-API2.srt: 偏移=1419s, 分段=6
[17:04:10] [DEBUG] 时间戳校正完成，偏移量: 1419.0s
[17:04:10] [DEBUG] 解析SRT条目: 69 个
[17:04:10] [DEBUG] 分段 6: 69 个条目，偏移 1419.0s
[17:04:10] [DEBUG] 解析文件名 trimmed_Honey Baby, Honey Baby (1974)_part7_00-30-41-ElevenLabs-API2.srt: 偏移=1841s, 分段=7
[17:04:10] [DEBUG] 时间戳校正完成，偏移量: 1841.0s
[17:04:10] [DEBUG] 解析SRT条目: 60 个
[17:04:10] [DEBUG] 分段 7: 60 个条目，偏移 1841.0s
[17:04:10] [DEBUG] 解析文件名 trimmed_Honey Baby, Honey Baby (1974)_part8_00-34-17-ElevenLabs-API2.srt: 偏移=2057s, 分段=8
[17:04:10] [DEBUG] 时间戳校正完成，偏移量: 2057.0s
[17:04:10] [DEBUG] 解析SRT条目: 86 个
[17:04:10] [DEBUG] 分段 8: 86 个条目，偏移 2057.0s
[17:04:10] [DEBUG] 解析文件名 trimmed_Honey Baby, Honey Baby (1974)_part9_00-40-29-ElevenLabs-API2.srt: 偏移=2429s, 分段=9
[17:04:10] [DEBUG] 时间戳校正完成，偏移量: 2429.0s
[17:04:10] [DEBUG] 解析SRT条目: 66 个
[17:04:10] [DEBUG] 分段 9: 66 个条目，偏移 2429.0s
[17:04:10] 字幕合并完成: ./projects\trimmed_Honey Baby, Honey Baby (1974)\trimmed_Honey Baby, Honey Baby (1974)-ElevenLabs-API2.srt (1081 个条目)
[17:04:10] [SUCCESS] ElevenLabs-API2 合并完成: trimmed_Honey Baby, Honey Baby (1974)-ElevenLabs-API2.srt
[17:04:10] [INFO] 字幕合并完成: 成功 1, 失败 0
[17:04:11] [INFO] 验证合并结果: trimmed_Honey Baby, Honey Baby (1974).wav
[17:04:11] [VALID] trimmed_Honey Baby, Honey Baby (1974)-ElevenLabs-API2.srt (71735 字节)
[17:04:11] [SUCCESS] 所有合并文件验证通过: 1 个文件
[17:04:11] [COPY] 字幕文件已复制: trimmed_Honey Baby, Honey Baby (1974)-ElevenLabs-API2.srt
[17:04:11] [INFO] ASR-LLM组合完成度: 1/1
[17:04:11] [SUCCESS] 字幕生成完成: trimmed_Honey Baby, Honey Baby (1974).wav (合并: 1/1)
[17:04:11] [TaskFlow] 字幕生成成功
[17:04:11] 任务完成: trimmed_Honey Baby, Honey Baby (1974).wav
[17:04:11] [BUTTON] 按钮状态更新: '进行中...' -> '开始' (禁用: True -> False)
[17:04:11] 转换完成！成功: 1/1
