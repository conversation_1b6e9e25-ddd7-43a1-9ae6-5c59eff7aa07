"""
ASR数据模型
转录相关的标准数据结构
"""

from dataclasses import dataclass, field
from typing import Optional, Dict, Any, List


@dataclass
class TimestampedWord:
    """带时间戳词汇

    转录结果中的单个词汇及时间信息
    """
    word: str                           # 词汇文本
    start_time: float                   # 开始时间
    end_time: float                     # 结束时间
    confidence: Optional[float] = None  # 置信度
    speaker_id: Optional[str] = None    # 说话人ID

    @property
    def duration(self) -> float:
        """持续时间"""
        return self.end_time - self.start_time


@dataclass
class TranscriptionMetadata:
    """转录元数据

    转录过程的基本元信息
    """
    service_name: str                   # 服务名称
    model_used: str                     # 模型名称


@dataclass
class ASRServiceConfig:
    """ASR服务配置基类

    所有ASR服务的通用配置参数
    """
    service_name: str                   # 服务名称
    api_key: str                        # API密钥
    enabled: bool = True                # 是否启用
    model: Optional[str] = None         # 模型名称
    language: str = "auto"              # 语言设置
    enable_diarization: bool = False    # 启用说话人分离
    enable_punctuation: bool = True     # 启用标点符号
    enable_timestamps: bool = True      # 启用时间戳
    timeout: int = 300                  # 超时时间（秒）
    max_retries: int = 5                # 最大重试次数
    custom_settings: Dict[str, Any] = field(default_factory=dict)  # 自定义设置

    @staticmethod
    def _clean_ui_fields(kwargs: dict) -> dict:
        """清理UI专用字段（防御性清理）

        这是最后一道防线，防止配置管理层遗漏的UI字段传递给ASR服务

        Args:
            kwargs: 配置参数字典

        Returns:
            dict: 清理后的参数字典
        """
        # 可能遗漏的UI专用字段
        ui_fields = [
            'id',           # UI配置识别
            'enabled',      # UI开关状态（某些情况下可能被遗漏）
        ]

        # 防御性清理
        cleaned_kwargs = kwargs.copy()
        for field in ui_fields:
            cleaned_kwargs.pop(field, None)

        return cleaned_kwargs

    def validate(self) -> tuple[bool, List[str]]:
        """验证配置 - 简化的单一验证方法

        Returns:
            tuple[bool, List[str]]: (是否有效, 错误信息列表)
        """
        errors = []

        if not self.service_name.strip():
            errors.append("服务名称不能为空")

        if not self.api_key.strip():
            errors.append("API密钥不能为空")

        if self.timeout <= 0:
            errors.append("超时时间必须大于0秒")

        if self.max_retries < 0:
            errors.append("重试次数不能为负数")

        return len(errors) == 0, errors
